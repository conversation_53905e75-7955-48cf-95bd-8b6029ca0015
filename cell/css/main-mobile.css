﻿@charset "UTF-8";

/*
 * Worksheet canvas
 * --------------------------------------------------------
 */

#ws-canvas-outer {
	position: absolute;
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
}

#ws-canvas {
	border: 0;
}

#ws-canvas-overlay, #ws-canvas-graphic, #ws-canvas-graphic-overlay {
	border: 0;
	position: absolute;
	left: 0;
	top: 0;
	z-index: 1;
}

/*
 * Worksheet scroll bars
 * --------------------------------------------------------
 */

#ws-v-scrollbar {
	position: absolute;
	right: 0;
	width: 19px;
	top: -1px;
	bottom: 18px;
	overflow: hidden;
	z-index: 10;
}

#ws-v-scroll-helper {
	width: 1px;
}

#ws-h-scrollbar {
	position: absolute;
	bottom: 0;
	height: 19px;
	left: 0;
	right: 18px;
	overflow: hidden;
	z-index: 10;
}

#ws-h-scroll-helper {
	height: 1px;
}

#ws-scrollbar-corner {
	position: absolute;
	right: 0;
	bottom: 0;
	width: 18px;
	height: 18px;
	background-color: #DCE2E8;
	border: 0;
	z-index: 10;
}

/* Scrollbars common */

#ws-v-scrollbar .jspVerticalBar,
#ws-h-scrollbar .jspHorizontalBar,
#ws-v-scrollbar .jspTrack,
#ws-h-scrollbar .jspTrack {
	background-color: #DCE2E8;
}

#ws-v-scrollbar .jspDrag,
#ws-h-scrollbar .jspDrag {
	background-color: #C0C0C0;
}
#ws-v-scrollbar .jspDrag.jspHover,
#ws-v-scrollbar .jspDrag.jspActive,
#ws-h-scrollbar .jspDrag.jspHover,
#ws-h-scrollbar .jspDrag.jspActive {
	background-color: #808080;
}

/* Vertical scrollbar */

#ws-v-scrollbar .jspVerticalBar {
	width: 7px;
	border-left: 1px solid #C1C6CC;
}
#ws-v-scrollbar .jspTrack {
	width: 8px;
}

/* Horizontal scrollbar */

#ws-h-scrollbar .jspHorizontalBar {
	height: 7px;
	border-top: 1px solid #C1C6CC;
}
#ws-h-scrollbar .jspTrack {
	height: 8px;
}
