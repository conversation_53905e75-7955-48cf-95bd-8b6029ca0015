﻿@charset "UTF-8";

/*
 * Worksheet canvas
 * --------------------------------------------------------
 */

#ws-canvas-outer {
	position: absolute;
	left: 0;
	top: 0;
	right: 14px;
	bottom: 14px;
    overflow: hidden;
}

#ws-canvas {
	border: 0;
    -webkit-user-select: none;
}

#ws-canvas-overlay, #ws-canvas-graphic, #ws-canvas-graphic-overlay {
    -webkit-user-select: none;
	border: 0;
	position: absolute;
	left: 0;
	top: 0;
	z-index: 1;
}

/*
 * Worksheet scroll bars
 * --------------------------------------------------------
 */

#ws-v-scrollbar {
	position: absolute;
	right: 0;
	width: 14px;
	top: 0px;
	bottom: 14px;
	overflow: hidden;
	z-index: 10;
    background-color: #f1f1f1;
}

#ws-v-scroll-helper {
	width: 1px;
}

#ws-h-scrollbar {
	position: absolute;
	bottom: 0;
	height: 14px;
	left: 0px;
	right: 14px;
	overflow: hidden;
	z-index: 10;
    background-color: #f1f1f1;
}

#ws-h-scroll-helper {
	height: 1px;
}

#ws-scrollbar-corner {
	position: absolute;
	right: 0;
	bottom: 0;
	width: 14px;
	height: 14px;
	background-color: #F4F4F4;
	border: 0;
	z-index: 10;
}

/* Scrollbars common */

#ws-v-scrollbar .jspVerticalBar,
#ws-h-scrollbar .jspHorizontalBar,
#ws-v-scrollbar .jspTrack,
#ws-h-scrollbar .jspTrack {
	background-color: #DCE2E8;
}

#ws-v-scrollbar .jspDrag,
#ws-h-scrollbar .jspDrag {
	background-color: #C0C0C0;
}
#ws-v-scrollbar .jspDrag.jspHover,
#ws-v-scrollbar .jspDrag.jspActive,
#ws-h-scrollbar .jspDrag.jspHover,
#ws-h-scrollbar .jspDrag.jspActive {
	background-color: #808080;
}

/* Vertical scrollbar */

#ws-v-scrollbar .jspVerticalBar {
	width: 7px;
	border-left: 1px solid #C1C6CC;
}
#ws-v-scrollbar .jspTrack {
	width: 8px;
}

/* Horizontal scrollbar */

#ws-h-scrollbar .jspHorizontalBar {
	height: 7px;
	border-top: 1px solid #C1C6CC;
}
#ws-h-scrollbar .jspTrack {
	height: 8px;
}

/*
 * Cell editor
 * --------------------------------------------------------
 */

#ce-canvas-outer,
#ce-canvas-outer-menu {
	position: absolute;
	border: 0;
	overflow: hidden;
}

#ce-canvas,
#ce-canvas-overlay,
#ce-canvas-menu,
#ce-canvas-overlay-menu {
	border: 0;
	position: absolute;
	left: 0;
	top: 0;
}

#ce-cursor,
#ce-cursor-menu {
	position: absolute;
	background-color: #000;
	width: 1px;
	height: 11pt;
    cursor: text;
}

#apiPopUpSelector {
    position: absolute;
}
#apiPopUpList {
    width: 100%;
    height: 100%;
    max-height: 210px;
    overflow: hidden;
    position: relative;
}
#apiPopUpList li {
    max-width: 500px;
}