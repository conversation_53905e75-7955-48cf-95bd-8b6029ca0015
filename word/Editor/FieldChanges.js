/*
 * (c) Copyright Ascensio System SIA 2010-2019
 *
 * This program is a free software product. You can redistribute it and/or
 * modify it under the terms of the GNU Affero General Public License (AGPL)
 * version 3 as published by the Free Software Foundation. In accordance with
 * Section 7(a) of the GNU AGPL its Section 15 shall be amended to the effect
 * that Ascensio System SIA expressly excludes the warranty of non-infringement
 * of any third-party rights.
 *
 * This program is distributed WITHOUT ANY WARRANTY; without even the implied
 * warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR  PURPOSE. For
 * details, see the GNU AGPL at: http://www.gnu.org/licenses/agpl-3.0.html
 *
 * You can contact Ascensio System SIA at 20A-12 Ernesta Birznieka-Upisha
 * street, Riga, Latvia, EU, LV-1050.
 *
 * The  interactive user interfaces in modified source and object code versions
 * of the Program must display Appropriate Legal Notices, as required under
 * Section 5 of the GNU AGPL version 3.
 *
 * Pursuant to Section 7(b) of the License you must retain the original Product
 * logo when distributing the program. Pursuant to Section 7(e) we decline to
 * grant you any rights under trademark law for use of our trademarks.
 *
 * All the Product's GUI elements, including illustrations and icon sets, as
 * well as technical writing content are licensed under the terms of the
 * Creative Commons Attribution-ShareAlike 4.0 International. See the License
 * terms at http://creativecommons.org/licenses/by-sa/4.0/legalcode
 *
 */

"use strict";
/**
 * User: Ilja.Kirillov
 * Date: 10.11.2016
 * Time: 18:59
 */

AscDFH.changesFactory[AscDFH.historyitem_Field_AddItem]              = CChangesParaFieldAddItem;
AscDFH.changesFactory[AscDFH.historyitem_Field_RemoveItem]           = CChangesParaFieldRemoveItem;
AscDFH.changesFactory[AscDFH.historyitem_Field_FormFieldName]        = CChangesParaFieldFormFieldName;
AscDFH.changesFactory[AscDFH.historyitem_Field_FormFieldDefaultText] = CChangesParaFieldFormFieldDefaultText;

//----------------------------------------------------------------------------------------------------------------------
// Карта зависимости изменений
//----------------------------------------------------------------------------------------------------------------------
AscDFH.changesRelationMap[AscDFH.historyitem_Field_AddItem]              = [AscDFH.historyitem_Field_AddItem, AscDFH.historyitem_Field_RemoveItem];
AscDFH.changesRelationMap[AscDFH.historyitem_Field_RemoveItem]           = [AscDFH.historyitem_Field_AddItem, AscDFH.historyitem_Field_RemoveItem];
AscDFH.changesRelationMap[AscDFH.historyitem_Field_FormFieldName]        = [AscDFH.historyitem_Field_FormFieldName];
AscDFH.changesRelationMap[AscDFH.historyitem_Field_FormFieldDefaultText] = [AscDFH.historyitem_Field_FormFieldDefaultText];
//----------------------------------------------------------------------------------------------------------------------

/**
 * @constructor
 * @extends {AscDFH.CChangesBaseContentChange}
 */
function CChangesParaFieldAddItem(Class, Pos, Items)
{
	AscDFH.CChangesBaseContentChange.call(this, Class, Pos, Items, true);
}
CChangesParaFieldAddItem.prototype = Object.create(AscDFH.CChangesBaseContentChange.prototype);
CChangesParaFieldAddItem.prototype.constructor = CChangesParaFieldAddItem;
CChangesParaFieldAddItem.prototype.Type = AscDFH.historyitem_Field_AddItem;
CChangesParaFieldAddItem.prototype.Undo = function()
{
	var oField = this.Class;
	oField.Content.splice(this.Pos, this.Items.length);
	oField.OnContentChange();
	oField.private_UpdateTrackRevisions();
	oField.private_CheckUpdateBookmarks(this.Items);
	oField.private_UpdateSelectionPosOnRemove(this.Pos, this.Items.length);
	oField.SetIsRecalculated(false);
};
CChangesParaFieldAddItem.prototype.Redo = function()
{
	var oField = this.Class;

	var Array_start = oField.Content.slice(0, this.Pos);
	var Array_end   = oField.Content.slice(this.Pos);

	oField.Content = Array_start.concat(this.Items, Array_end);
	oField.private_UpdateTrackRevisions();
	oField.private_CheckUpdateBookmarks(this.Items);
	oField.OnContentChange();
	oField.private_UpdateSelectionPosOnAdd(this.Pos, this.Items.length);
	oField.SetIsRecalculated(false);

	for (var nIndex = 0, nCount = this.Items.length; nIndex < nCount; ++nIndex)
	{
		var oItem = this.Items[nIndex];
		if (oItem.SetParagraph)
		{
			if (oField.GetParagraph)
				oItem.SetParagraph(oField.GetParagraph());
			else
				oItem.SetParagraph(null);
		}

		if (oItem.SetParent)
			oItem.SetParent(oField);
	}
};
CChangesParaFieldAddItem.prototype.private_WriteItem = function(Writer, Item)
{
	Writer.WriteString2(Item.Get_Id());
};
CChangesParaFieldAddItem.prototype.private_ReadItem = function(Reader)
{
	return AscCommon.g_oTableId.Get_ById(Reader.GetString2());
};
CChangesParaFieldAddItem.prototype.Load = function(Color)
{
	var oField = this.Class;
	for (var nIndex = 0, nCount = this.Items.length; nIndex < nCount; ++nIndex)
	{
		var Pos     = oField.m_oContentChanges.Check(AscCommon.contentchanges_Add, this.PosArray[nIndex]);
		var Element = this.Items[nIndex];

		if (null != Element)
		{
			if (Element.SetParagraph)
			{
				if (oField.GetParagraph)
					Element.SetParagraph(oField.GetParagraph());
				else
					Element.SetParagraph(null);
			}

			if (Element.SetParent)
				Element.SetParent(oField);

			oField.Content.splice(Pos, 0, Element);
			oField.private_UpdateSelectionPosOnAdd(Pos, 1);
			AscCommon.CollaborativeEditing.Update_DocumentPositionsOnAdd(oField, Pos);
		}
	}

	oField.private_UpdateTrackRevisions();
	oField.private_CheckUpdateBookmarks(this.Items);
	oField.OnContentChange();
	oField.SetIsRecalculated(false);
};
CChangesParaFieldAddItem.prototype.IsRelated = function(oChanges)
{
	if (this.Class === oChanges.Class && (AscDFH.historyitem_Field_AddItem === oChanges.Type || AscDFH.historyitem_Field_RemoveItem === oChanges.Type))
		return true;

	return false;
};
CChangesParaFieldAddItem.prototype.CreateReverseChange = function()
{
	return this.private_CreateReverseChange(CChangesParaFieldRemoveItem);
};
/**
 * @constructor
 * @extends {AscDFH.CChangesBaseContentChange}
 */
function CChangesParaFieldRemoveItem(Class, Pos, Items)
{
	AscDFH.CChangesBaseContentChange.call(this, Class, Pos, Items, false);
}
CChangesParaFieldRemoveItem.prototype = Object.create(AscDFH.CChangesBaseContentChange.prototype);
CChangesParaFieldRemoveItem.prototype.constructor = CChangesParaFieldRemoveItem;
CChangesParaFieldRemoveItem.prototype.Type = AscDFH.historyitem_Field_RemoveItem;
CChangesParaFieldRemoveItem.prototype.Undo = function()
{
	var oField = this.Class;

	var Array_start = oField.Content.slice(0, this.Pos);
	var Array_end   = oField.Content.slice(this.Pos);

	oField.Content = Array_start.concat(this.Items, Array_end);
	oField.OnContentChange();
	oField.private_CheckUpdateBookmarks(this.Items);
	oField.private_UpdateTrackRevisions();
	oField.private_UpdateSelectionPosOnAdd(this.Pos, this.Items.length);
	oField.SetIsRecalculated(false);

	for (var nIndex = 0, nCount = this.Items.length; nIndex < nCount; ++nIndex)
	{
		var oItem = this.Items[nIndex];
		if (oItem.SetParagraph)
		{
			if (oField.GetParagraph)
				oItem.SetParagraph(oField.GetParagraph());
			else
				oItem.SetParagraph(null);
		}

		if (oItem.SetParent)
			oItem.SetParent(oField);
	}
};
CChangesParaFieldRemoveItem.prototype.Redo = function()
{
	var oField = this.Class;
	oField.Content.splice(this.Pos, this.Items.length);
	oField.private_UpdateTrackRevisions();
	oField.private_CheckUpdateBookmarks(this.Items);
	oField.OnContentChange();
	oField.private_UpdateSelectionPosOnRemove(this.Pos, this.Items.length);
	oField.SetIsRecalculated(false);
};
CChangesParaFieldRemoveItem.prototype.private_WriteItem = function(Writer, Item)
{
	Writer.WriteString2(Item.Get_Id());
};
CChangesParaFieldRemoveItem.prototype.private_ReadItem = function(Reader)
{
	return AscCommon.g_oTableId.Get_ById(Reader.GetString2());
};
CChangesParaFieldRemoveItem.prototype.Load = function(Color)
{
	var oField = this.Class;
	for (var nIndex = 0, nCount = this.Items.length; nIndex < nCount; ++nIndex)
	{
		var ChangesPos = oField.m_oContentChanges.Check(AscCommon.contentchanges_Remove, this.PosArray[nIndex]);

		if (false === ChangesPos)
			continue;

		oField.Content.splice(ChangesPos, 1);
		oField.private_UpdateSelectionPosOnRemove(ChangesPos, 1);
		AscCommon.CollaborativeEditing.Update_DocumentPositionsOnRemove(oField, ChangesPos, 1);
	}
	oField.private_UpdateTrackRevisions();
	oField.private_CheckUpdateBookmarks(this.Items);
	oField.OnContentChange();
	oField.SetIsRecalculated(false);
};
CChangesParaFieldRemoveItem.prototype.IsRelated = function(oChanges)
{
	if (this.Class === oChanges.Class && (AscDFH.historyitem_Field_AddItem === oChanges.Type || AscDFH.historyitem_Field_RemoveItem === oChanges.Type))
		return true;

	return false;
};
CChangesParaFieldRemoveItem.prototype.CreateReverseChange = function()
{
	return this.private_CreateReverseChange(CChangesParaFieldAddItem);
};
/**
 * @constructor
 * @extends {AscDFH.CChangesBaseStringProperty}
 */
function CChangesParaFieldFormFieldName(Class, Old, New)
{
	AscDFH.CChangesBaseStringProperty.call(this, Class, Old, New);
}
CChangesParaFieldFormFieldName.prototype = Object.create(AscDFH.CChangesBaseStringProperty.prototype);
CChangesParaFieldFormFieldName.prototype.constructor = CChangesParaFieldFormFieldName;
CChangesParaFieldFormFieldName.prototype.Type = AscDFH.historyitem_Field_FormFieldName;
CChangesParaFieldFormFieldName.prototype.private_SetValue = function(Value)
{
	this.Class.FormFieldName = Value;
};
/**
 * @constructor
 * @extends {AscDFH.CChangesBaseStringProperty}
 */
function CChangesParaFieldFormFieldDefaultText(Class, Old, New)
{
	AscDFH.CChangesBaseStringProperty.call(this, Class, Old, New);
}
CChangesParaFieldFormFieldDefaultText.prototype = Object.create(AscDFH.CChangesBaseStringProperty.prototype);
CChangesParaFieldFormFieldDefaultText.prototype.constructor = CChangesParaFieldFormFieldDefaultText;
CChangesParaFieldFormFieldDefaultText.prototype.Type = AscDFH.historyitem_Field_FormFieldDefaultText;
CChangesParaFieldFormFieldDefaultText.prototype.private_SetValue = function(Value)
{
	this.Class.FormFieldDefaultText = Value;
};
