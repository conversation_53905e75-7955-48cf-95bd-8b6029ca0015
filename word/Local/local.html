﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>Test Docs</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge;chrome=1;" />
    <style type="text/css">
	
		html, body {height:100%;width:100%;-webkit-user-select:none;} 
		body {padding:0px; margin:0px;}

		body,input,textarea,select {
			font-family: 'Open Sans', sans-serif;
		}
		table
		{
			width:100%;
			border-spacing: 0;
			border-collapse:collapse;
			vertical-align:top;
		}
		
	   .tableNames
		{
			background-color:transparent;
			color:#222;
			overflow:hidden;
			padding: 8px 0 4px 8px;
			margin: 20px 20px 5px 20px;	
			font-family: 'Open Sans', sans-serif;
			font-size: 1.2em;
			font-weight: 600;
		}
		
		.tableHeader
		{
			background : transparent;
			color: #333;
			cursor: default;
			font-family: 'Open Sans', sans-serif;
			font-size: 13px;
			-khtml-user-select: none;
			user-select: none;
			-moz-user-select: none;
			-webkit-user-select: none;	
		}
		
		.tableHeaderCell
		{
			padding: 5px 1px 5px 6px;
		}
		
		.contentCells
		{
			padding: 2px 1px 2px 6px;
			font-family: 'Open Sans', sans-serif;
			font-size: 16px;
			border-bottom: 1px solid #e5e5e5;
			white-space: nowrap;
			-khtml-user-select: none;
			user-select: none;
			-moz-user-select: none;
			-webkit-user-select: none;	
		}

		.contentCellsSmall
		{
			padding: 5px 1px 5px 6px;
			font-family: 'Open Sans', sans-serif;
			font-size: 13px;
			border-bottom: 1px solid #e5e5e5;
			white-space: nowrap;
		}
		.contentCellsSmall:hover
		{
			cursor:pointer;
		}

		.contentCellsIcon {
			padding: 5px 6px 5px 10px;
			border-bottom: 1px solid #e5e5e5;
		}
		
		.contentCellsIconRemove {
			background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAABGdBTUEAAK/INwWK6QAAAAlwSFlzAAAdhwAAHYcBj+XxZQAAABh0RVh0U29mdHdhcmUAcGFpbnQubmV0IDQuMC41ZYUyZQAAAsFJREFUOE9tk8tPE1EUxgcfiWkHSqcUEFoIsOaRVIWWSMGkYWEChJ0uXOMK/wATwkOwUJ4CTjEjj76mUCzQREPiShMj4S9w6cK4McQEXF+/7zoTy+MmX3rnnO/87rmPKvY4bG8PfIpEdo/6+yfjgcBNK3xpjCjKteOBgeefI5G9j6HQfSv8bxQCgXuFtrbf6cpKkaupEV8ikdxVMAHIUW+vkff5hAlvoaXlz2Ew+EAm4263K9/aerJZUSFSXq/IVlWJPGBfL8AIOQbkwO8XO/BkAErAn29uPkvU1d1WNvz+JgOQDSiJBFfKwbhfWyuOLJjcDiAFQHarq8U28ux+CzVvPR6Rbmy8I1db07RVBpiggV0RtgcYO+N22AkhdjdclIu/0bQMECUShLZLDE3TmWC7EgaxaBfb5Jlwzk5sCI8Ci2fQ7Q0JscdFGM+LRdwqxTkXKIZsK8p1q/z8sGHrMNK8hSJCbXHrXMjweMxLnVwcXAW38A1meZDrFIr5y++Ez/djvb7+lmW/evB2PnR3G2vYQlzTBC6BhynFuYwB+r6n58p3JgchB4CswPiqvFwsQytut1gtEr8ZXwZwPxy+DCMkHw4b82h9tqxMzLtcYgFahJZQSC1SVnyOAvRdV1curij/YWZn51oUianSUhGFZgCLQYTOulwmfuOcM8bcNDwvKQDNUGiHl6RMe71NUWxnzOkU49ALVRVTkDSqqrwdGrGAzhhzk9AERP8kj6Ch4a4yomllow7HL4Js0TCuquceG2ETTqfOXLF3zOE4HXE4qqUJHwHATuwk5sXvhM9f/gV4lsjrxZBRVQ0zx1FimmarPjwcjQWDpwt9fT8Tm5tzqVRqJplM6pBhSWcskUjElgYHv8c6Os5eDw0tsZYMScpms7UwBdPp9EMYH2H+BIVDmD8rFuJPrdxjejOZTIi1iqIofwHC/qpK8+CH/wAAAABJRU5ErkJggg==);
			padding: 0px;
			border-bottom: 1px solid #e5e5e5;
		}
		.contentCellsIconRemove:hover
		{
			cursor:pointer;
		}

		.primaryRow
		{
			background-color:#e9e9e9;    
		}
		.primaryRow:hover
		{
			background-color:#ffffcc;
		}
		.secondaryRow
		{
			background-color:#F9F9F9;    
		}
		.secondaryRow:hover
		{
			background-color:#ffffcc;
		}
		
	</style>
</head>
<body>
    <div style="min-height:100%;">
        <br />
        <table>
            <tr>
                <td width="10px">
                </td>
                <td width="200px" id="loginMenu" style="background-color: transparent; vertical-align:top; border-right: 1px solid #e5e5e5;" valign="top">
                    <table>
                        <tr>
                            <td class="tableNames">Management</td>
                        </tr>
                        <tr class="ManagmentBorder">
                            <td class="tableNames">
								<a id="id_open_file" style="font-family: Open Sans, serif;font-size: 13px;color: #333;font-decoration: none;cursor: pointer;">Open File</a><br/>
								<a id="id_create_file_1" style="font-family: Open Sans, serif;font-size: 13px;color: #333;font-decoration: none;cursor: pointer;">New Document</a><br/>
								<a id="id_create_file_2" style="font-family: Open Sans, serif;font-size: 13px;color: #333;font-decoration: none;cursor: pointer;">New Spreadsheet</a><br/>
								<a id="id_create_file_3" style="font-family: Open Sans, serif;font-size: 13px;color: #333;font-decoration: none;cursor: pointer;">New Presentation</a><br/>
                            </td>
                        </tr>
                    </table>
                </td>
                <td width="10px">
                </td>
                <td id="filesMenu" valign="top">
                    <table>
                        <tr>
                            <td class="tableNames">
                                Recent
                            </td>
                        </tr>
                        <tr id="tableRow" style="vertical-align:top;">
                            <td>
                                <div>
                                    <table id="tableRecents">
                                        <tr class="tableHeader">
                                            <td class="tableHeaderCell" style="text-align: center; width: 30px">
                                            </td>
                                            <td class="tableHeaderCell">
                                            </td>
											<td class="tableHeaderCell" style="text-align: center; width: 70px">                                                
                                            </td>											
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
						<tr>
                            <td class="tableNames">
                                Recover
                            </td>
                        </tr>
                        <tr id="tableRow" style="vertical-align:top;">
                            <td>
                                <div>
                                    <table id="tableRecovers">
                                        <tr class="tableHeader">
                                            <td class="tableHeaderCell" style="text-align: center; width: 30px">
                                            </td>
                                            <td class="tableHeaderCell">
                                            </td>
											<td class="tableHeaderCell" style="text-align: center; width: 70px">                                                
                                            </td>											
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
                <td width="10px">
                </td>
            </tr>
        </table>
        <div style="height:40px;"></div>
    </div>
    <div id="credits" style="position: absolute; margin: -50px 0 0 0; width: 100%;">
        <div id="bottompart" style="font-family: 'Open Sans', sans-serif; font-size: 13px; color: #333; background: rgb(229,229,229); margin-top: 15px; padding: 5px 16px 0 0;">
            <table style="height: 30px">
                <tr>
                    <td>
                    </td>
                    <td align="right">
                        <span>© Ascensio System SIA 2015. All rights reserved.</span>
                    </td>
                </tr>
            </table>
        </div>
    </div>
	
	<script type="text/javascript">
	
	window.onupdaterecents = function(_params)
	{
		var _len = _params.length;
		var _contents = "<tr class=\"tableHeader\"><td class=\"tableHeaderCell\" style=\"text-align: center; width: 30px\"></td><td class=\"tableHeaderCell\"></td><td class=\"tableHeaderCell\" style=\"text-align: center; width: 70px\"></td></tr>";
		for (var i = 0; i < _len; i++)
		{
			_contents += AddRecent(_params[i].type, _params[i].path + "; date: " + _params[i].modifyed, _params[i].id, false, i);
		}
		document.getElementById("tableRecents").innerHTML = _contents;
		
		var _recents = document.getElementsByClassName("contentCellsSmall");
		for (var i = 0; i < _recents.length; i++)
			_recents[i].onclick = onOpenFileId;
			
		_recents = document.getElementsByClassName("contentCellsIconRemove");
		for (var i = 0; i < _recents.length; i++)
		{
			_recents[i].onclick = onCloseFileId;		
		}
	};
	window.onupdaterecovers = function(_params)
	{
		var _len = _params.length / 3;
		var _contents = "<tr class=\"tableHeader\"><td class=\"tableHeaderCell\" style=\"text-align: center; width: 30px\"></td><td class=\"tableHeaderCell\"></td><td class=\"tableHeaderCell\" style=\"text-align: center; width: 70px\"></td></tr>";
		for (var i = 0; i < _len; i++)
		{
			_contents += AddRecover(_params[i].type, _params[i].path, _params[i].id, false, i);
		}
		document.getElementById("tableRecovers").innerHTML = _contents;
		
		var _recents = document.getElementsByClassName("contentCellsSmall");
		for (var i = 0; i < _recents.length; i++)
			_recents[i].onclick = onOpenFileId;
			
		_recents = document.getElementsByClassName("contentCellsIconRemove");
		for (var i = 0; i < _recents.length; i++)
		{
			_recents[i].onclick = onCloseFileId;		
		}
	};
	function onOpenFile(e) 
	{
		if (window["AscDesktopEditor"] && window["AscDesktopEditor"]["LocalFileOpen"])
		{
			window["AscDesktopEditor"]["LocalFileOpen"]();
		}
		else
		{
			alert("desktop!!! (open)");
		}
		
		e.preventDefault();
		return false;   // cancels the default copy operation
    }
	function onCreateFile(nType) 
	{
		if (window["AscDesktopEditor"] && window["AscDesktopEditor"]["LocalFileCreate"])
		{
			window["AscDesktopEditor"]["LocalFileCreate"](nType);
		}
		else
		{
			alert("desktop!!! (open)");
		}
    }
	function onOpenFileId(e)
	{
		var _methodName = "LocalFileOpenRecent";
		var _id = this.getAttribute("recent_id");
		if (null == _id || "" == _id)
		{
			_id = this.getAttribute("recover_id");
			_methodName = "LocalFileOpenRecover";
		}
				
		if (window["AscDesktopEditor"] && window["AscDesktopEditor"][_methodName])
		{
			window["AscDesktopEditor"][_methodName](parseInt(_id));
		}
		else
		{
			alert("desktop!!! (" + _methodName + ": " + _id + ")");
		}
		
		e.preventDefault();
		return false;   // cancels the default copy operation
	}
	function onCloseFileId(e)
	{
		var _methodName = "LocalFileRemoveRecent";
		var _id = this.parentNode.getAttribute("recent_id");
		if (null == _id || "" == _id)
		{
			_id = this.parentNode.getAttribute("recover_id");
			_methodName = "LocalFileRemoveRecover";
		}
				
		if (window["AscDesktopEditor"] && window["AscDesktopEditor"][_methodName])
		{
			window["AscDesktopEditor"][_methodName](parseInt(_id));
		}
		else
		{
			alert("desktop!!! (" + _methodName + ": " + _id + ")");
		}
		
		e.preventDefault();
		return false;   // cancels the default copy operation
	}
		
	document.getElementById("id_open_file").onclick = onOpenFile;
	
	document.getElementById("id_create_file_1").onclick = function(e){ onCreateFile(0); e.preventDefault(); return false; };
	document.getElementById("id_create_file_2").onclick = function(e){ onCreateFile(1); e.preventDefault(); return false; };
	document.getElementById("id_create_file_3").onclick = function(e){ onCreateFile(2); e.preventDefault(); return false; };
	
	function allIndexOf(str, toSearch) 
	{
		var indices = [];
		for(var pos = str.indexOf(toSearch); pos !== -1; pos = str.indexOf(toSearch, pos + 1)) {
			indices.push(pos);
		}
		return indices;
	}

	function AddRecent(type, filename, id, is_add, index)
	{
		var _contents = document.getElementById("tableRecents").innerHTML;
		var _count = allIndexOf(_contents, "primaryRow").length + allIndexOf(_contents, "secondaryRow").length;
		if (index !== undefined)
			_count = ((index % 2) == 0) ? 0 : 1;
			
		var _class = ((_count % 2) == 0) ? "primaryRow" : "secondaryRow";
		
		var sContent = "<tr title=\""+filename+"\" type=\""+type+"\" class=\"" + _class + "\">";
        sContent += "<td style=\"text-align:center;\" class=\"contentCellsIcon\">";
		sContent += GetIcon( type );
        sContent += "</td>";
        sContent += "<td class=\"contentCellsSmall\" recent_id=\"" + id + "\">";
        sContent += filename;
        sContent += "</td>";
		
		sContent += "<td style=\"text-align:center;\" class=\"contentCellsIcon\" recent_id=\"" + id + "\">";
		sContent += GetIcon2();
        sContent += "</td>";
		
        sContent += "</tr>";
		
		if (is_add)
			document.getElementById("tableRecents").innerHTML += sContent;
		else
			return sContent;
	}
	
	function AddRecover(type, filename, id, is_add, index)
	{
		var _contents = document.getElementById("tableRecovers").innerHTML;
		var _count = allIndexOf(_contents, "primaryRow").length + allIndexOf(_contents, "secondaryRow").length;
		if (index !== undefined)
			_count = ((index % 2) == 0) ? 0 : 1;
		
		var _class = ((_count % 2) == 0) ? "primaryRow" : "secondaryRow";
		
		var sContent = "<tr title=\""+filename+"\" type=\""+type+"\" class=\"" + _class + "\">";
        sContent += "<td style=\"text-align:center;\" class=\"contentCellsIcon\">";
		sContent += GetIcon( type );
        sContent += "</td>";
        sContent += "<td class=\"contentCellsSmall\" recover_id=\"" + id + "\">";
        sContent += filename;
        sContent += "</td>";
		
		sContent += "<td style=\"text-align:center;\" class=\"contentCellsIcon\" recover_id=\"" + id + "\">";
		sContent += GetIcon2();
        sContent += "</td>";
		
        sContent += "</tr>";
		
		if (is_add)
			document.getElementById("tableRecovers").innerHTML += sContent;
		else
			return sContent;
	}
	
	function GetIcon( type )
	{
		var sResult = "<span style=\"display:inline-block;width:18px;height:20px;background-image: url(";
		switch( type ){
			case 0:sResult+="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAUCAYAAACAl21KAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RUZGMjU2NTEwRTdDMTFFNTg3RThEQUFBMzZDREY0N0QiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RUZGMjU2NTIwRTdDMTFFNTg3RThEQUFBMzZDREY0N0QiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpFRkYyNTY0RjBFN0MxMUU1ODdFOERBQUEzNkNERjQ3RCIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpFRkYyNTY1MDBFN0MxMUU1ODdFOERBQUEzNkNERjQ3RCIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Pi/uMyAAAAE3SURBVHjarJQ9aoVAFIXvGBvB8JoU7iBkA4JaaBOX4J6yCmuRtA8rUYQHtmKTBfhTpAkPC/Fn4p3Eh8ITTfSAcJl755szZ0CSpimFFVH6M9J1HauTJHkSBOFqWVaDbezxVVWBLMtrLGjbFnzfB9M0Ydjzmef54wBtxj4/njYqCIK7oL7vIcsyVmuaBnEcX4eS3AYulwtd0wChdV3T8/k8W//dy657c7TkZOqoLMvZmqIozIiqqgSiKKJb1DQN9Tzvbg8ZmxzhDCEEiqIA27ZnPUmSQBRFgDAMNznCnIbnZ84wr+mHDB7vviWjqbOpdF1n+d1AhmHAf8VAW19tSWiAPf9hjv6S0ZKjQzMir2/v9OOrgz16Pj0AtxeCQgYHB4l7OZHdEMZwXZfuFTJ4/H06jrPb1bcAAwCHbKnzAe5v8QAAAABJRU5ErkJggg==";break;
			case 1:sResult+="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAUCAYAAACAl21KAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6MTgyMEVEQkUwRTdEMTFFNUIxQjI5QjQ4QzVDREFGOUMiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6MTgyMEVEQkYwRTdEMTFFNUIxQjI5QjQ4QzVDREFGOUMiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDoxODIwRURCQzBFN0QxMUU1QjFCMjlCNDhDNUNEQUY5QyIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDoxODIwRURCRDBFN0QxMUU1QjFCMjlCNDhDNUNEQUY5QyIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Pohd6b8AAAFMSURBVHjarJQ9boMwFMefKRmQ6JQO3KDqBZCAhSlH4Ay9TW8AM2LtzIdAqVgRSw8QpnaKUIX4cHluHQXSABH9Sxa23/OPv58tkzzPKcyI0p+Utm1ZP8uyB0mSjpZl1RjGmFiWJaiqOseCpmnA933Y7XbQr/koiuK+h9Y8LvK/hWE4Ceq6Dg6HA+sbhgFpmh77Ljkl7Pd7igqCgI7F53oIraqK2rY9iP+uZds9Obrmis+ho7E0TWNGdF0nEMfxrCNUXdcXjriQscgR5hBCWHMcZxBXFAVkWQaIomiRI6wTjtEZ1gtbf4rsiwzxfO9TNeL3KUmS0xgdbjYbVr8ByDTNC8j53F9jhDHQ0lObymHHv9YRvxo31ehazgA0dnOLGOjt6wVevWdYo+3dIwif7TusFTIE+CcJW/K0GsIYnufRtUKGiM+n67qrXX0LMAB8cb1DM7XNPgAAAABJRU5ErkJggg==";break;
			case 2:sResult+="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAUCAYAAACAl21KAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6MDg5Mjk5NzYwRTdEMTFFNUJFQTJEQzI4NEZBNzU0OTciIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6MDg5Mjk5NzcwRTdEMTFFNUJFQTJEQzI4NEZBNzU0OTciPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDowODkyOTk3NDBFN0QxMUU1QkVBMkRDMjg0RkE3NTQ5NyIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDowODkyOTk3NTBFN0QxMUU1QkVBMkRDMjg0RkE3NTQ5NyIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PhKt/cQAAAE/SURBVHjarJQ/aoRQEMbniRaCgRQprMQbLGkU3UIrj7AnygFyA2ux33Kx8F8b2RsExCKBgNlq1RfnhbeYXUSN+8HgwOjvffMpkuPxSGFClP7e0rYt64uieJJlud7tdmcc40w8nU5gGMYUC5qmgcPhAJ7nQf/MR1mWDz30zOcCP23KUdd1rFDb7RY0Tau5G+aID68VRdGldxznz4oo0zQhyzJqWRaZ7WhMPQTSNGUAYczRUIQQSJIEqqq6mdm2DXEcU3FuRgjD8n0fVFW9QLFXFAUuoGEmruveOELpus6gGPZQeZ7PW+3amSRJmA0r7JGxCMSd8SvvkSH+960N10fGIkfX3xnPdfFqY2KM/X5P1woZ5P11Qx+/31Y5+lI2QOoXoHAHCXAnCZ/K82oIY4RhuDpsZIj4+wyCYLWrHwEGAJxONF0vMrGdAAAAAElFTkSuQmCC";break;
			default:sResult+="";
		}
		sResult+=")\">&nbsp;</span>";
		return sResult;
	};
	function GetIcon2( )
	{
		var sResult = "<span class=\"contentCellsIconRemove\" style=\"display:inline-block;width:18px;height:18px;\">&nbsp;</span>";
		return sResult;
	};
	
	/*
	AddRecent(0, "oleg1", 0, true);
	AddRecent(1, "oleg2", 1, true);
	AddRecent(2, "oleg3", 2, true);
	
	AddRecover(0, "oleg1", 0, true);
	AddRecover(1, "oleg2", 1, true);
	AddRecover(2, "oleg3", 2, true);
	*/
	
	if (window["AscDesktopEditor"] && window["AscDesktopEditor"]["LocalFileRecents"])
		window["AscDesktopEditor"]["LocalFileRecents"]();
	if (window["AscDesktopEditor"] && window["AscDesktopEditor"]["LocalFileRecovers"])
		window["AscDesktopEditor"]["LocalFileRecovers"]();
			
	</script>
</body>
</html>
