{"sdk": {"min": ["common/device_scale.js", "common/userinfo_parser.js", "common/browser.js", "common/skin.js", "common/commonDefines.js", "common/docscoapicommon.js", "common/docscoapi.js", "common/spellcheckapi.js", "common/apiCommon.js", "common/SerializeCommonWordExcel.js", "vendor/string.js", "vendor/array.js", "common/editorscommon.js", "common/HistoryCommon.js", "common/TableId.js", "common/TableIdChanges.js", "common/AdvancedOptions.js", "common/libfont/engine.js", "common/libfont/loader.js", "word/apiDefines.js", "common/collaborativeHistory.js", "common/CollaborativeEditingBase.js", "word/Editor/CollaborativeEditing.js", "common/macros.js", "common/apiBase.js", "common/apiBase_plugins.js", "word/apiCommon.js", "word/api.js", "word/api_plugins.js", "common/spell/spell.js", "common/zlib/zlib.js", "word/Editor/Table/TableLook.js", "cell/utils/utils.js"], "common": ["common/api/actionOnTimer.js", "common/api/tableStylesPreviewGenerator.js", "common/api/spellCheckSettings.js", "common/api/addTextSettings.js", "common/api/autoCorrectSettings.js", "common/api/firstLetterExceptions.js", "common/api/restrictionSettings.js", "common/nameMap.js", "common/downloaderfiles.js", "common/NumFormat.js", "common/SerializeChart.js", "common/libfont/common.js", "common/libfont/map.js", "common/libfont/rasterheap.js", "common/libfont/glyphstring.js", "common/libfont/character.js", "common/libfont/grapheme.js", "common/libfont/textshaper.js", "common/libfont/file.js", "common/libfont/manager.js", "common/stringserialize.js", "common/random.js", "common/hash/hash.js", "common/keychainstorage.js", "common/Drawings/Metafile.js", "common/libfont/textmeasurer.js", "common/Drawings/WorkEvents.js", "word/Editor/History.js", "common/Shapes/EditorSettings.js", "common/Shapes/Serialize.js", "common/Shapes/SerializeWriter.js", "common/Drawings/Hit.js", "common/Drawings/ArcTo.js", "common/Drawings/ColorArray.js", "common/Drawings/CommonController.js", "word/Editor/GraphicObjects/DrawingStates.js", "common/Drawings/DrawingsChanges.js", "common/Drawings/Format/Format.js", "common/Drawings/Format/CreateGeometry.js", "common/Drawings/Format/Geometry.js", "common/Drawings/Format/GraphicObjectBase.js", "common/Drawings/Format/Shape.js", "common/Drawings/Format/CnxShape.js", "common/Drawings/Format/Path.js", "common/Drawings/Format/Image.js", "common/Drawings/Format/GroupShape.js", "common/Drawings/Format/Data.js", "common/Drawings/Format/ChartSpace.js", "common/Drawings/Format/ChartFormat.js", "common/Drawings/Format/TextBody.js", "common/Charts/charts.js", "common/Charts/DrawingObjects.js", "common/Charts/3DTransformation.js", "common/Charts/ChartsDrawer.js", "common/Drawings/TrackObjects/AdjustmentTracks.js", "common/Drawings/TrackObjects/MoveTracks.js", "common/Drawings/TrackObjects/NewShapeTracks.js", "common/Drawings/TrackObjects/PolyLine.js", "common/Drawings/TrackObjects/ResizeTracks.js", "common/Drawings/TrackObjects/RotateTracks.js", "common/Drawings/TrackObjects/GeometryEditTrack.js", "common/Drawings/TrackObjects/Spline.js", "common/Drawings/TrackObjects/ConnectorTrack.js", "common/Drawings/DrawingObjectsHandlers.js", "common/Drawings/TextDrawer.js", "common/Drawings/Externals.js", "common/GlobalLoaders.js", "common/Controls.js", "common/Overlay.js", "common/Drawings/HatchPattern.js", "common/scroll.js", "vendor/iscroll.js", "vendor/delta.js", "vendor/minhash.js", "common/Scrolls/mobileTouchManagerBase.js", "word/Drawing/mobileTouchManager.js", "common/wordcopypaste.js", "common/intervalTree.js", "cell/model/WorkbookElems.js", "cell/model/Workbook.js", "cell/model/Serialize.js", "cell/model/CellInfo.js", "word/Editor/Paragraph/Run/FontClassification.js", "word/Editor/Paragraph/Run/FontCalculator.js", "word/Editor/Paragraph/Run/RunAutoCorrect.js", "word/Drawing/translations.js", "word/Editor/GraphicObjects/Format/ShapePrototype.js", "word/Editor/GraphicObjects/Format/ImagePrototype.js", "word/Editor/GraphicObjects/Format/GroupPrototype.js", "word/Editor/GraphicObjects/Format/ChartSpacePrototype.js", "common/Drawings/Format/GraphicFrame.js", "common/Drawings/Format/LockedCanvas.js", "word/Editor/GraphicObjects/GraphicObjects.js", "word/Editor/GraphicObjects/GraphicPage.js", "word/Editor/GraphicObjects/WrapManager.js", "word/Editor/DocumentContentElementBase.js", "word/Editor/ParagraphContentBase.js", "word/Editor/Comments.js", "word/Editor/CommentsChanges.js", "word/Editor/Bookmarks.js", "word/Editor/Styles.js", "word/Editor/StylesChanges.js", "word/Editor/RevisionsChange.js", "word/Editor/FlowObjects.js", "word/Editor/Paragraph/RunContent/Types.js", "word/Editor/Paragraph/RunContent/Base.js", "word/Editor/Paragraph/RunContent/FootnoteReference.js", "word/Editor/Paragraph/RunContent/FootnoteRef.js", "word/Editor/Paragraph/RunContent/EndnoteReference.js", "word/Editor/Paragraph/RunContent/EndnoteRef.js", "word/Editor/Paragraph/RunContent/Separator.js", "word/Editor/Paragraph/RunContent/ContinuationSeparator.js", "word/Editor/Paragraph/RunContent/PageNum.js", "word/Editor/Paragraph/RunContent/PagesCount.js", "word/Editor/Paragraph/RunContent/Break.js", "word/Editor/Paragraph/RunContent/Text.js", "word/Editor/Paragraph/RunContent/Space.js", "word/Editor/Paragraph/RunContent/Tab.js", "word/Editor/Paragraph/RunContent/ParagraphMark.js", "word/Editor/ParagraphContent.js", "word/Editor/Paragraph/ParaTextPr.js", "word/Editor/Paragraph/ParaTextPrChanges.js", "word/Editor/Paragraph/ParaDrawing.js", "word/Editor/Paragraph/ParaDrawingChanges.js", "word/Editor/Paragraph/ParagraphContentPos.js", "word/Editor/Paragraph/ComplexFieldInstruction.js", "word/Editor/Paragraph/ComplexFields/Addin.js", "word/Editor/Paragraph/ComplexFields/AddinData.js", "word/Editor/Paragraph/ComplexFields/FormText.js", "word/Editor/Paragraph/ComplexFields/MergeField.js", "word/Editor/Paragraph/ComplexField.js", "word/Editor/Paragraph/FormulaParser.js", "word/Editor/Paragraph/ParaRevisionMove.js", "word/Editor/Paragraph/GraphemesCounter.js", "word/Editor/Paragraph/TextShaper.js", "word/Editor/Hyperlink.js", "word/Editor/HyperlinkChanges.js", "word/Editor/Field.js", "word/Editor/FieldChanges.js", "word/Editor/FormFieldChanges.js", "word/Editor/Run.js", "word/Editor/PresentationField.js", "word/Editor/RunChanges.js", "word/Editor/Math.js", "word/Editor/MathChanges.js", "word/Editor/Paragraph.js", "word/Editor/ParagraphChanges.js", "word/Editor/Paragraph_Recalculate.js", "word/Editor/Sections.js", "word/Editor/SectionsChanges.js", "word/Editor/Numbering/NumberingApplicator.js", "word/Editor/Numbering/NumberingCommon.js", "word/Editor/Numbering/NumberingLvl.js", "word/Editor/Numbering/AbstractNum.js", "word/Editor/Numbering/AbstractNumChanges.js", "word/Editor/Numbering/Num.js", "word/Editor/Numbering/NumChanges.js", "word/Editor/Numbering/PresentationNumbering.js", "word/Editor/Numbering/Numbering.js", "word/Editor/HeaderFooter.js", "word/Editor/Layout/Base.js", "word/Editor/Layout/PrintView.js", "word/Editor/Layout/ReadView.js", "word/Editor/DocumentContentBase.js", "word/Editor/Document.js", "word/Editor/DocumentSettings.js", "word/Editor/ChangeCase.js", "word/Editor/SelectedContent.js", "word/Editor/DocumentOutline.js", "word/Editor/DocumentChanges.js", "word/Editor/DocumentContent.js", "word/Editor/DocumentContentChanges.js", "word/Editor/DocumentControllerBase.js", "word/Editor/LogicDocumentController.js", "word/Editor/DrawingsController.js", "word/Editor/HeaderFooterController.js", "word/Editor/Common.js", "word/Editor/Table.js", "word/Editor/Table/TableChanges.js", "word/Editor/Table/TableRecalculate.js", "word/Editor/Table/TableDraw.js", "word/Editor/Table/TableRow.js", "word/Editor/Table/TableRowChanges.js", "word/Editor/Table/TableCell.js", "word/Editor/Table/TableCellChanges.js", "word/Editor/StructuredDocumentTags/SdtBase.js", "word/Editor/StructuredDocumentTags/BlockLevel.js", "word/Editor/StructuredDocumentTags/FormKeyGenerator.js", "word/Editor/StructuredDocumentTags/FormsManager.js", "word/Editor/StructuredDocumentTags/FormToJson.js", "word/Editor/StructuredDocumentTags/InlineLevel.js", "word/Editor/StructuredDocumentTags/SdtPr.js", "word/Editor/StructuredDocumentTags/SdtPrChanges.js", "word/Editor/StructuredDocumentTags/Custom/CheckBox.js", "word/Editor/StructuredDocumentTags/Custom/ComboBox.js", "word/Editor/StructuredDocumentTags/Custom/ComplexForm.js", "word/Editor/StructuredDocumentTags/Custom/DatePicker.js", "word/Editor/StructuredDocumentTags/Custom/Form.js", "word/Editor/StructuredDocumentTags/Custom/PictureForm.js", "word/Editor/StructuredDocumentTags/Custom/TextForm.js", "word/Editor/StructuredDocumentTags/Custom/TextFormFormat.js", "word/Editor/StructuredDocumentTags/Custom/TextFormMask.js", "word/Editor/Serialize2.js", "common/api/searchSettings.js", "word/Editor/Search/DocumentSearch.js", "word/Editor/Search/ParagraphSearch.js", "word/Editor/Search/SearchItem.js", "word/Editor/Search/Pattern.js", "word/Editor/SpellChecker/DocumentSpellChecker.js", "word/Editor/SpellChecker/ParagraphCollector.js", "word/Editor/SpellChecker/ParagraphSpellChecker.js", "word/Editor/SpellChecker/ParagraphSpellCheckerElement.js", "word/Editor/FootEndNote.js", "word/Editor/Footnotes.js", "word/Editor/FootnotesChanges.js", "word/Editor/Endnotes.js", "word/Editor/EndnotesChanges.js", "word/Editor/GlossaryDocument.js", "word/Editor/GlossaryDocumentChanges.js", "word/Editor/DocumentProtection.js", "word/Editor/Comparison.js", "word/Editor/Revisions/TrackRevisionsManager.js", "word/Drawing/Graphics.js", "word/Drawing/ShapeDrawer.js", "word/Drawing/buttons.js", "word/Drawing/DrawingDocument.js", "word/Drawing/GraphicsEvents.js", "word/Drawing/Rulers.js", "word/Drawing/printpreview.js", "word/Drawing/HtmlPage.js", "word/Drawing/documentrenderer.js", "pdf/src/thumbnails.js", "pdf/src/viewer.js", "pdf/src/file.js", "word/document/empty.js", "word/Math/NamesOfLiterals.js", "word/Math/LaTeXParser.js", "word/Math/UnicodeParser.js", "word/Math/mathTypes.js", "word/Math/mathText.js", "word/Math/mathContent.js", "word/Math/base.js", "word/Math/fraction.js", "word/Math/degree.js", "word/Math/matrix.js", "word/Math/limit.js", "word/Math/nary.js", "word/Math/radical.js", "word/Math/operators.js", "word/Math/accent.js", "word/Math/borderBox.js", "word/Math/mathTrackHandler.js", "word/Editor/Styles/FixedFormDefaults.js", "word/apiBuilder.js", "word/fromToJSON.js", "common/versionHistory.js", "common/clipboard_base.js", "common/text_input2.js", "common/Drawings/Format/OleObject.js", "common/Drawings/Format/DrawingContent.js", "common/plugins.js", "common/Native/native_graphics.js", "vendor/easysax.js", "common/openxml.js"], "desktop": {"min": ["common/Local/license.js"], "common": ["common/Local/common.js", "word/Local/api.js"]}, "mobile_banners": {"min": ["common/Native/Wrappers/memory.js"], "common": []}, "mobile": ["common/libfont/engine/fonts_native.js", "common/Charts/ChartStyles.js", "common/Native/Wrappers/HtmlPage.js", "common/Native/Wrappers/DrawingStream.js", "common/Native/Wrappers/ShapeDrawer.js", "common/Native/Wrappers/Overlay.js", "common/Native/Wrappers/DrawingDocument.js", "common/Native/Wrappers/api.js"], "exclude_mobile": ["vendor/iscroll.js", "common/Scrolls/mobileTouchManagerBase.js", "common/Overlay.js", "word/Drawing/mobileTouchManager.js", "word/Drawing/HtmlPage.js", "word/Drawing/ShapeDrawer.js", "word/Drawing/DrawingDocument.js"]}}