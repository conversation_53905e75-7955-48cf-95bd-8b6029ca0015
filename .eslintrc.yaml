root: true

env:
  browser: true

rules:
  # Possible Errors
  # http://eslint.org/docs/rules/#possible-errors
  for-direction: error
  no-debugger: error
  no-dupe-args: error
  no-dupe-keys: error
  no-duplicate-case: error
  no-empty-character-class: error
  no-ex-assign: error
  no-extra-semi: error
  no-func-assign: error
  no-invalid-regexp: error
  no-irregular-whitespace: error
  no-obj-calls: error
  no-unreachable: error
  no-unsafe-negation: error

  # Best Practices
  # http://eslint.org/docs/rules/#best-practices
  no-proto: error
  no-redeclare: error
  no-self-assign: error
  no-unused-labels: error
  no-useless-return: error

  # Variables
  # http://eslint.org/docs/rules/#variables
  no-delete-var: error

  # Stylistic Issues
  # http://eslint.org/docs/rules/#stylistic-issues
  linebreak-style: [error, unix]
