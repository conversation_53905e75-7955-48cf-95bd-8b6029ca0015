/*
 * (c) Copyright Ascensio System SIA 2010-2019
 *
 * This program is a free software product. You can redistribute it and/or
 * modify it under the terms of the GNU Affero General Public License (AGPL)
 * version 3 as published by the Free Software Foundation. In accordance with
 * Section 7(a) of the GNU AGPL its Section 15 shall be amended to the effect
 * that Ascensio System SIA expressly excludes the warranty of non-infringement
 * of any third-party rights.
 *
 * This program is distributed WITHOUT ANY WARRANTY; without even the implied
 * warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR  PURPOSE. For
 * details, see the GNU AGPL at: http://www.gnu.org/licenses/agpl-3.0.html
 *
 * You can contact Ascensio System SIA at 20A-12 Ernesta Birznieka-Upisha
 * street, Riga, Latvia, EU, LV-1050.
 *
 * The  interactive user interfaces in modified source and object code versions
 * of the Program must display Appropriate Legal Notices, as required under
 * Section 5 of the GNU AGPL version 3.
 *
 * Pursuant to Section 7(b) of the License you must retain the original Product
 * logo when distributing the program. Pursuant to Section 7(e) we decline to
 * grant you any rights under trademark law for use of our trademarks.
 *
 * All the Product's GUI elements, including illustrations and icon sets, as
 * well as technical writing content are licensed under the terms of the
 * Creative Commons Attribution-ShareAlike 4.0 International. See the License
 * terms at http://creativecommons.org/licenses/by-sa/4.0/legalcode
 *
 */

"use strict";

(
/**
* @param {Window} window
* @param {undefined} undefined
*/
function (window, undefined) {

function CreateGeometry(prst, oGeom){
    var f = oGeom || (new AscFormat.Geometry());
    switch(prst){
        case 'accentBorderCallout1':{
            f.AddAdj('adj1', 15, '18750');
            f.AddAdj('adj2', 15, '-8333');
            f.AddAdj('adj3', 15, '112500');
            f.AddAdj('adj4', 15, '-38333');
            f.AddGuide('y1', 0, 'h', 'adj1', '100000');
            f.AddGuide('x1', 0, 'w', 'adj2', '100000');
            f.AddGuide('y2', 0, 'h', 'adj3', '100000');
            f.AddGuide('x2', 0, 'w', 'adj4', '100000');
            f.AddHandleXY('adj2','-**********','**********','adj1','-**********','**********', 'x1', 'y1');
            f.AddHandleXY('adj4','-**********','**********','adj3','-**********','**********', 'x2', 'y2');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 't');
            f.AddPathCommand(6);
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(2, 'x2', 'y2');
            break;
        }
        case 'accentBorderCallout2':{
            f.AddAdj('adj1', 15, '18750');
            f.AddAdj('adj2', 15, '-8333');
            f.AddAdj('adj3', 15, '18750');
            f.AddAdj('adj4', 15, '-16667');
            f.AddAdj('adj5', 15, '112500');
            f.AddAdj('adj6', 15, '-46667');
            f.AddGuide('y1', 0, 'h', 'adj1', '100000');
            f.AddGuide('x1', 0, 'w', 'adj2', '100000');
            f.AddGuide('y2', 0, 'h', 'adj3', '100000');
            f.AddGuide('x2', 0, 'w', 'adj4', '100000');
            f.AddGuide('y3', 0, 'h', 'adj5', '100000');
            f.AddGuide('x3', 0, 'w', 'adj6', '100000');
            f.AddHandleXY('adj2','-**********','**********','adj1','-**********','**********', 'x1', 'y1');
            f.AddHandleXY('adj4','-**********','**********','adj3','-**********','**********', 'x2', 'y2');
            f.AddHandleXY('adj6','-**********','**********','adj5','-**********','**********', 'x3', 'y3');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 't');
            f.AddPathCommand(6);
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x3', 'y3');
            break;
        }
        case 'accentBorderCallout3':{
            f.AddAdj('adj1', 15, '18750');
            f.AddAdj('adj2', 15, '-8333');
            f.AddAdj('adj3', 15, '18750');
            f.AddAdj('adj4', 15, '-16667');
            f.AddAdj('adj5', 15, '100000');
            f.AddAdj('adj6', 15, '-16667');
            f.AddAdj('adj7', 15, '112963');
            f.AddAdj('adj8', 15, '-8333');
            f.AddGuide('y1', 0, 'h', 'adj1', '100000');
            f.AddGuide('x1', 0, 'w', 'adj2', '100000');
            f.AddGuide('y2', 0, 'h', 'adj3', '100000');
            f.AddGuide('x2', 0, 'w', 'adj4', '100000');
            f.AddGuide('y3', 0, 'h', 'adj5', '100000');
            f.AddGuide('x3', 0, 'w', 'adj6', '100000');
            f.AddGuide('y4', 0, 'h', 'adj7', '100000');
            f.AddGuide('x4', 0, 'w', 'adj8', '100000');
            f.AddHandleXY('adj2','-**********','**********','adj1','-**********','**********', 'x1', 'y1');
            f.AddHandleXY('adj4','-**********','**********','adj3','-**********','**********', 'x2', 'y2');
            f.AddHandleXY('adj6','-**********','**********','adj5','-**********','**********', 'x3', 'y3');
            f.AddHandleXY('adj8','-**********','**********','adj7','-**********','**********', 'x4', 'y4');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 't');
            f.AddPathCommand(6);
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x3', 'y3');
            f.AddPathCommand(2, 'x4', 'y4');
            break;
        }
        case 'accentCallout1':{
            f.AddAdj('adj1', 15, '18750');
            f.AddAdj('adj2', 15, '-8333');
            f.AddAdj('adj3', 15, '112500');
            f.AddAdj('adj4', 15, '-38333');
            f.AddGuide('y1', 0, 'h', 'adj1', '100000');
            f.AddGuide('x1', 0, 'w', 'adj2', '100000');
            f.AddGuide('y2', 0, 'h', 'adj3', '100000');
            f.AddGuide('x2', 0, 'w', 'adj4', '100000');
            f.AddHandleXY('adj2','-**********','**********','adj1','-**********','**********', 'x1', 'y1');
            f.AddHandleXY('adj4','-**********','**********','adj3','-**********','**********', 'x2', 'y2');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 't');
            f.AddPathCommand(6);
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(2, 'x2', 'y2');
            break;
        }
        case 'accentCallout2':{
            f.AddAdj('adj1', 15, '18750');
            f.AddAdj('adj2', 15, '-8333');
            f.AddAdj('adj3', 15, '18750');
            f.AddAdj('adj4', 15, '-16667');
            f.AddAdj('adj5', 15, '112500');
            f.AddAdj('adj6', 15, '-46667');
            f.AddGuide('y1', 0, 'h', 'adj1', '100000');
            f.AddGuide('x1', 0, 'w', 'adj2', '100000');
            f.AddGuide('y2', 0, 'h', 'adj3', '100000');
            f.AddGuide('x2', 0, 'w', 'adj4', '100000');
            f.AddGuide('y3', 0, 'h', 'adj5', '100000');
            f.AddGuide('x3', 0, 'w', 'adj6', '100000');
            f.AddHandleXY('adj2','-**********','**********','adj1','-**********','**********', 'x1', 'y1');
            f.AddHandleXY('adj4','-**********','**********','adj3','-**********','**********', 'x2', 'y2');
            f.AddHandleXY('adj6','-**********','**********','adj5','-**********','**********', 'x3', 'y3');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 't');
            f.AddPathCommand(6);
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x3', 'y3');
            break;
        }
        case 'accentCallout3':{
            f.AddAdj('adj1', 15, '18750');
            f.AddAdj('adj2', 15, '-8333');
            f.AddAdj('adj3', 15, '18750');
            f.AddAdj('adj4', 15, '-16667');
            f.AddAdj('adj5', 15, '100000');
            f.AddAdj('adj6', 15, '-16667');
            f.AddAdj('adj7', 15, '112963');
            f.AddAdj('adj8', 15, '-8333');
            f.AddGuide('y1', 0, 'h', 'adj1', '100000');
            f.AddGuide('x1', 0, 'w', 'adj2', '100000');
            f.AddGuide('y2', 0, 'h', 'adj3', '100000');
            f.AddGuide('x2', 0, 'w', 'adj4', '100000');
            f.AddGuide('y3', 0, 'h', 'adj5', '100000');
            f.AddGuide('x3', 0, 'w', 'adj6', '100000');
            f.AddGuide('y4', 0, 'h', 'adj7', '100000');
            f.AddGuide('x4', 0, 'w', 'adj8', '100000');
            f.AddHandleXY('adj2','-**********','**********','adj1','-**********','**********', 'x1', 'y1');
            f.AddHandleXY('adj4','-**********','**********','adj3','-**********','**********', 'x2', 'y2');
            f.AddHandleXY('adj6','-**********','**********','adj5','-**********','**********', 'x3', 'y3');
            f.AddHandleXY('adj8','-**********','**********','adj7','-**********','**********', 'x4', 'y4');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 't');
            f.AddPathCommand(6);
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x3', 'y3');
            f.AddPathCommand(2, 'x4', 'y4');
            break;
        }
        case 'actionButtonBackPrevious':{
            f.AddGuide('dx2', 0, 'ss', '3', '8');
            f.AddGuide('g9', 1, 'vc', '0', 'dx2');
            f.AddGuide('g10', 1, 'vc', 'dx2', '0');
            f.AddGuide('g11', 1, 'hc', '0', 'dx2');
            f.AddGuide('g12', 1, 'hc', 'dx2', '0');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'g11', 'vc');
            f.AddPathCommand(2, 'g12', 'g9');
            f.AddPathCommand(2, 'g12', 'g10');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darken', false, undefined, undefined);
            f.AddPathCommand(1, 'g11', 'vc');
            f.AddPathCommand(2, 'g12', 'g9');
            f.AddPathCommand(2, 'g12', 'g10');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'g11', 'vc');
            f.AddPathCommand(2, 'g12', 'g9');
            f.AddPathCommand(2, 'g12', 'g10');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'actionButtonBeginning':{
            f.AddGuide('dx2', 0, 'ss', '3', '8');
            f.AddGuide('g9', 1, 'vc', '0', 'dx2');
            f.AddGuide('g10', 1, 'vc', 'dx2', '0');
            f.AddGuide('g11', 1, 'hc', '0', 'dx2');
            f.AddGuide('g12', 1, 'hc', 'dx2', '0');
            f.AddGuide('g13', 0, 'ss', '3', '4');
            f.AddGuide('g14', 0, 'g13', '1', '8');
            f.AddGuide('g15', 0, 'g13', '1', '4');
            f.AddGuide('g16', 1, 'g11', 'g14', '0');
            f.AddGuide('g17', 1, 'g11', 'g15', '0');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'g17', 'vc');
            f.AddPathCommand(2, 'g12', 'g9');
            f.AddPathCommand(2, 'g12', 'g10');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'g16', 'g9');
            f.AddPathCommand(2, 'g11', 'g9');
            f.AddPathCommand(2, 'g11', 'g10');
            f.AddPathCommand(2, 'g16', 'g10');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darken', false, undefined, undefined);
            f.AddPathCommand(1, 'g17', 'vc');
            f.AddPathCommand(2, 'g12', 'g9');
            f.AddPathCommand(2, 'g12', 'g10');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'g16', 'g9');
            f.AddPathCommand(2, 'g11', 'g9');
            f.AddPathCommand(2, 'g11', 'g10');
            f.AddPathCommand(2, 'g16', 'g10');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'g17', 'vc');
            f.AddPathCommand(2, 'g12', 'g9');
            f.AddPathCommand(2, 'g12', 'g10');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'g16', 'g9');
            f.AddPathCommand(2, 'g16', 'g10');
            f.AddPathCommand(2, 'g11', 'g10');
            f.AddPathCommand(2, 'g11', 'g9');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'actionButtonBlank':{
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'actionButtonDocument':{
            f.AddGuide('dx2', 0, 'ss', '3', '8');
            f.AddGuide('g9', 1, 'vc', '0', 'dx2');
            f.AddGuide('g10', 1, 'vc', 'dx2', '0');
            f.AddGuide('dx1', 0, 'ss', '9', '32');
            f.AddGuide('g11', 1, 'hc', '0', 'dx1');
            f.AddGuide('g12', 1, 'hc', 'dx1', '0');
            f.AddGuide('g13', 0, 'ss', '3', '16');
            f.AddGuide('g14', 1, 'g12', '0', 'g13');
            f.AddGuide('g15', 1, 'g9', 'g13', '0');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'g11', 'g9');
            f.AddPathCommand(2, 'g14', 'g9');
            f.AddPathCommand(2, 'g12', 'g15');
            f.AddPathCommand(2, 'g12', 'g10');
            f.AddPathCommand(2, 'g11', 'g10');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darkenLess', false, undefined, undefined);
            f.AddPathCommand(1, 'g11', 'g9');
            f.AddPathCommand(2, 'g14', 'g9');
            f.AddPathCommand(2, 'g14', 'g15');
            f.AddPathCommand(2, 'g12', 'g15');
            f.AddPathCommand(2, 'g12', 'g10');
            f.AddPathCommand(2, 'g11', 'g10');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darken', false, undefined, undefined);
            f.AddPathCommand(1, 'g14', 'g9');
            f.AddPathCommand(2, 'g14', 'g15');
            f.AddPathCommand(2, 'g12', 'g15');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'g11', 'g9');
            f.AddPathCommand(2, 'g14', 'g9');
            f.AddPathCommand(2, 'g12', 'g15');
            f.AddPathCommand(2, 'g12', 'g10');
            f.AddPathCommand(2, 'g11', 'g10');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'g12', 'g15');
            f.AddPathCommand(2, 'g14', 'g15');
            f.AddPathCommand(2, 'g14', 'g9');
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'actionButtonEnd':{
            f.AddGuide('dx2', 0, 'ss', '3', '8');
            f.AddGuide('g9', 1, 'vc', '0', 'dx2');
            f.AddGuide('g10', 1, 'vc', 'dx2', '0');
            f.AddGuide('g11', 1, 'hc', '0', 'dx2');
            f.AddGuide('g12', 1, 'hc', 'dx2', '0');
            f.AddGuide('g13', 0, 'ss', '3', '4');
            f.AddGuide('g14', 0, 'g13', '3', '4');
            f.AddGuide('g15', 0, 'g13', '7', '8');
            f.AddGuide('g16', 1, 'g11', 'g14', '0');
            f.AddGuide('g17', 1, 'g11', 'g15', '0');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'g16', 'vc');
            f.AddPathCommand(2, 'g11', 'g9');
            f.AddPathCommand(2, 'g11', 'g10');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'g17', 'g9');
            f.AddPathCommand(2, 'g12', 'g9');
            f.AddPathCommand(2, 'g12', 'g10');
            f.AddPathCommand(2, 'g17', 'g10');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darken', false, undefined, undefined);
            f.AddPathCommand(1, 'g16', 'vc');
            f.AddPathCommand(2, 'g11', 'g9');
            f.AddPathCommand(2, 'g11', 'g10');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'g17', 'g9');
            f.AddPathCommand(2, 'g12', 'g9');
            f.AddPathCommand(2, 'g12', 'g10');
            f.AddPathCommand(2, 'g17', 'g10');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'g16', 'vc');
            f.AddPathCommand(2, 'g11', 'g10');
            f.AddPathCommand(2, 'g11', 'g9');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'g17', 'g9');
            f.AddPathCommand(2, 'g12', 'g9');
            f.AddPathCommand(2, 'g12', 'g10');
            f.AddPathCommand(2, 'g17', 'g10');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'actionButtonForwardNext':{
            f.AddGuide('dx2', 0, 'ss', '3', '8');
            f.AddGuide('g9', 1, 'vc', '0', 'dx2');
            f.AddGuide('g10', 1, 'vc', 'dx2', '0');
            f.AddGuide('g11', 1, 'hc', '0', 'dx2');
            f.AddGuide('g12', 1, 'hc', 'dx2', '0');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'g12', 'vc');
            f.AddPathCommand(2, 'g11', 'g9');
            f.AddPathCommand(2, 'g11', 'g10');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darken', false, undefined, undefined);
            f.AddPathCommand(1, 'g12', 'vc');
            f.AddPathCommand(2, 'g11', 'g9');
            f.AddPathCommand(2, 'g11', 'g10');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'g12', 'vc');
            f.AddPathCommand(2, 'g11', 'g10');
            f.AddPathCommand(2, 'g11', 'g9');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'actionButtonHelp':{
            f.AddGuide('dx2', 0, 'ss', '3', '8');
            f.AddGuide('g9', 1, 'vc', '0', 'dx2');
            f.AddGuide('g11', 1, 'hc', '0', 'dx2');
            f.AddGuide('g13', 0, 'ss', '3', '4');
            f.AddGuide('g14', 0, 'g13', '1', '7');
            f.AddGuide('g15', 0, 'g13', '3', '14');
            f.AddGuide('g16', 0, 'g13', '2', '7');
            f.AddGuide('g19', 0, 'g13', '3', '7');
            f.AddGuide('g20', 0, 'g13', '4', '7');
            f.AddGuide('g21', 0, 'g13', '17', '28');
            f.AddGuide('g23', 0, 'g13', '21', '28');
            f.AddGuide('g24', 0, 'g13', '11', '14');
            f.AddGuide('g27', 1, 'g9', 'g16', '0');
            f.AddGuide('g29', 1, 'g9', 'g21', '0');
            f.AddGuide('g30', 1, 'g9', 'g23', '0');
            f.AddGuide('g31', 1, 'g9', 'g24', '0');
            f.AddGuide('g33', 1, 'g11', 'g15', '0');
            f.AddGuide('g36', 1, 'g11', 'g19', '0');
            f.AddGuide('g37', 1, 'g11', 'g20', '0');
            f.AddGuide('g41', 0, 'g13', '1', '14');
            f.AddGuide('g42', 0, 'g13', '3', '28');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'g33', 'g27');
            f.AddPathCommand(3, 'g16', 'g16', 'cd2', 'cd2');
            f.AddPathCommand(3, 'g14', 'g15', '0', 'cd4');
            f.AddPathCommand(3, 'g41', 'g42', '_3cd4', '-5400000');
            f.AddPathCommand(2, 'g37', 'g30');
            f.AddPathCommand(2, 'g36', 'g30');
            f.AddPathCommand(2, 'g36', 'g29');
            f.AddPathCommand(3, 'g14', 'g15', 'cd2', 'cd4');
            f.AddPathCommand(3, 'g41', 'g42', 'cd4', '-5400000');
            f.AddPathCommand(3, 'g14', 'g14', '0', '-10800000');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'hc', 'g31');
            f.AddPathCommand(3, 'g42', 'g42', '_3cd4', '21600000');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darken', false, undefined, undefined);
            f.AddPathCommand(1, 'g33', 'g27');
            f.AddPathCommand(3, 'g16', 'g16', 'cd2', 'cd2');
            f.AddPathCommand(3, 'g14', 'g15', '0', 'cd4');
            f.AddPathCommand(3, 'g41', 'g42', '_3cd4', '-5400000');
            f.AddPathCommand(2, 'g37', 'g30');
            f.AddPathCommand(2, 'g36', 'g30');
            f.AddPathCommand(2, 'g36', 'g29');
            f.AddPathCommand(3, 'g14', 'g15', 'cd2', 'cd4');
            f.AddPathCommand(3, 'g41', 'g42', 'cd4', '-5400000');
            f.AddPathCommand(3, 'g14', 'g14', '0', '-10800000');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'hc', 'g31');
            f.AddPathCommand(3, 'g42', 'g42', '_3cd4', '21600000');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'g33', 'g27');
            f.AddPathCommand(3, 'g16', 'g16', 'cd2', 'cd2');
            f.AddPathCommand(3, 'g14', 'g15', '0', 'cd4');
            f.AddPathCommand(3, 'g41', 'g42', '_3cd4', '-5400000');
            f.AddPathCommand(2, 'g37', 'g30');
            f.AddPathCommand(2, 'g36', 'g30');
            f.AddPathCommand(2, 'g36', 'g29');
            f.AddPathCommand(3, 'g14', 'g15', 'cd2', 'cd4');
            f.AddPathCommand(3, 'g41', 'g42', 'cd4', '-5400000');
            f.AddPathCommand(3, 'g14', 'g14', '0', '-10800000');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'hc', 'g31');
            f.AddPathCommand(3, 'g42', 'g42', '_3cd4', '21600000');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'actionButtonHome':{
            f.AddGuide('dx2', 0, 'ss', '3', '8');
            f.AddGuide('g9', 1, 'vc', '0', 'dx2');
            f.AddGuide('g10', 1, 'vc', 'dx2', '0');
            f.AddGuide('g11', 1, 'hc', '0', 'dx2');
            f.AddGuide('g12', 1, 'hc', 'dx2', '0');
            f.AddGuide('g13', 0, 'ss', '3', '4');
            f.AddGuide('g14', 0, 'g13', '1', '16');
            f.AddGuide('g15', 0, 'g13', '1', '8');
            f.AddGuide('g16', 0, 'g13', '3', '16');
            f.AddGuide('g17', 0, 'g13', '5', '16');
            f.AddGuide('g18', 0, 'g13', '7', '16');
            f.AddGuide('g19', 0, 'g13', '9', '16');
            f.AddGuide('g20', 0, 'g13', '11', '16');
            f.AddGuide('g21', 0, 'g13', '3', '4');
            f.AddGuide('g22', 0, 'g13', '13', '16');
            f.AddGuide('g23', 0, 'g13', '7', '8');
            f.AddGuide('g24', 1, 'g9', 'g14', '0');
            f.AddGuide('g25', 1, 'g9', 'g16', '0');
            f.AddGuide('g26', 1, 'g9', 'g17', '0');
            f.AddGuide('g27', 1, 'g9', 'g21', '0');
            f.AddGuide('g28', 1, 'g11', 'g15', '0');
            f.AddGuide('g29', 1, 'g11', 'g18', '0');
            f.AddGuide('g30', 1, 'g11', 'g19', '0');
            f.AddGuide('g31', 1, 'g11', 'g20', '0');
            f.AddGuide('g32', 1, 'g11', 'g22', '0');
            f.AddGuide('g33', 1, 'g11', 'g23', '0');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'hc', 'g9');
            f.AddPathCommand(2, 'g11', 'vc');
            f.AddPathCommand(2, 'g28', 'vc');
            f.AddPathCommand(2, 'g28', 'g10');
            f.AddPathCommand(2, 'g33', 'g10');
            f.AddPathCommand(2, 'g33', 'vc');
            f.AddPathCommand(2, 'g12', 'vc');
            f.AddPathCommand(2, 'g32', 'g26');
            f.AddPathCommand(2, 'g32', 'g24');
            f.AddPathCommand(2, 'g31', 'g24');
            f.AddPathCommand(2, 'g31', 'g25');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darkenLess', false, undefined, undefined);
            f.AddPathCommand(1, 'g32', 'g26');
            f.AddPathCommand(2, 'g32', 'g24');
            f.AddPathCommand(2, 'g31', 'g24');
            f.AddPathCommand(2, 'g31', 'g25');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'g28', 'vc');
            f.AddPathCommand(2, 'g28', 'g10');
            f.AddPathCommand(2, 'g29', 'g10');
            f.AddPathCommand(2, 'g29', 'g27');
            f.AddPathCommand(2, 'g30', 'g27');
            f.AddPathCommand(2, 'g30', 'g10');
            f.AddPathCommand(2, 'g33', 'g10');
            f.AddPathCommand(2, 'g33', 'vc');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darken', false, undefined, undefined);
            f.AddPathCommand(1, 'hc', 'g9');
            f.AddPathCommand(2, 'g11', 'vc');
            f.AddPathCommand(2, 'g12', 'vc');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'g29', 'g27');
            f.AddPathCommand(2, 'g30', 'g27');
            f.AddPathCommand(2, 'g30', 'g10');
            f.AddPathCommand(2, 'g29', 'g10');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'hc', 'g9');
            f.AddPathCommand(2, 'g31', 'g25');
            f.AddPathCommand(2, 'g31', 'g24');
            f.AddPathCommand(2, 'g32', 'g24');
            f.AddPathCommand(2, 'g32', 'g26');
            f.AddPathCommand(2, 'g12', 'vc');
            f.AddPathCommand(2, 'g33', 'vc');
            f.AddPathCommand(2, 'g33', 'g10');
            f.AddPathCommand(2, 'g28', 'g10');
            f.AddPathCommand(2, 'g28', 'vc');
            f.AddPathCommand(2, 'g11', 'vc');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'g31', 'g25');
            f.AddPathCommand(2, 'g32', 'g26');
            f.AddPathCommand(1, 'g33', 'vc');
            f.AddPathCommand(2, 'g28', 'vc');
            f.AddPathCommand(1, 'g29', 'g10');
            f.AddPathCommand(2, 'g29', 'g27');
            f.AddPathCommand(2, 'g30', 'g27');
            f.AddPathCommand(2, 'g30', 'g10');
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'actionButtonInformation':{
            f.AddGuide('dx2', 0, 'ss', '3', '8');
            f.AddGuide('g9', 1, 'vc', '0', 'dx2');
            f.AddGuide('g11', 1, 'hc', '0', 'dx2');
            f.AddGuide('g13', 0, 'ss', '3', '4');
            f.AddGuide('g14', 0, 'g13', '1', '32');
            f.AddGuide('g17', 0, 'g13', '5', '16');
            f.AddGuide('g18', 0, 'g13', '3', '8');
            f.AddGuide('g19', 0, 'g13', '13', '32');
            f.AddGuide('g20', 0, 'g13', '19', '32');
            f.AddGuide('g22', 0, 'g13', '11', '16');
            f.AddGuide('g23', 0, 'g13', '13', '16');
            f.AddGuide('g24', 0, 'g13', '7', '8');
            f.AddGuide('g25', 1, 'g9', 'g14', '0');
            f.AddGuide('g28', 1, 'g9', 'g17', '0');
            f.AddGuide('g29', 1, 'g9', 'g18', '0');
            f.AddGuide('g30', 1, 'g9', 'g23', '0');
            f.AddGuide('g31', 1, 'g9', 'g24', '0');
            f.AddGuide('g32', 1, 'g11', 'g17', '0');
            f.AddGuide('g34', 1, 'g11', 'g19', '0');
            f.AddGuide('g35', 1, 'g11', 'g20', '0');
            f.AddGuide('g37', 1, 'g11', 'g22', '0');
            f.AddGuide('g38', 0, 'g13', '3', '32');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'hc', 'g9');
            f.AddPathCommand(3, 'dx2', 'dx2', '_3cd4', '21600000');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darken', false, undefined, undefined);
            f.AddPathCommand(1, 'hc', 'g9');
            f.AddPathCommand(3, 'dx2', 'dx2', '_3cd4', '21600000');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'hc', 'g25');
            f.AddPathCommand(3, 'g38', 'g38', '_3cd4', '21600000');
            f.AddPathCommand(1, 'g32', 'g28');
            f.AddPathCommand(2, 'g32', 'g29');
            f.AddPathCommand(2, 'g34', 'g29');
            f.AddPathCommand(2, 'g34', 'g30');
            f.AddPathCommand(2, 'g32', 'g30');
            f.AddPathCommand(2, 'g32', 'g31');
            f.AddPathCommand(2, 'g37', 'g31');
            f.AddPathCommand(2, 'g37', 'g30');
            f.AddPathCommand(2, 'g35', 'g30');
            f.AddPathCommand(2, 'g35', 'g28');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'lighten', false, undefined, undefined);
            f.AddPathCommand(1, 'hc', 'g25');
            f.AddPathCommand(3, 'g38', 'g38', '_3cd4', '21600000');
            f.AddPathCommand(1, 'g32', 'g28');
            f.AddPathCommand(2, 'g35', 'g28');
            f.AddPathCommand(2, 'g35', 'g30');
            f.AddPathCommand(2, 'g37', 'g30');
            f.AddPathCommand(2, 'g37', 'g31');
            f.AddPathCommand(2, 'g32', 'g31');
            f.AddPathCommand(2, 'g32', 'g30');
            f.AddPathCommand(2, 'g34', 'g30');
            f.AddPathCommand(2, 'g34', 'g29');
            f.AddPathCommand(2, 'g32', 'g29');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'hc', 'g9');
            f.AddPathCommand(3, 'dx2', 'dx2', '_3cd4', '21600000');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'hc', 'g25');
            f.AddPathCommand(3, 'g38', 'g38', '_3cd4', '21600000');
            f.AddPathCommand(1, 'g32', 'g28');
            f.AddPathCommand(2, 'g35', 'g28');
            f.AddPathCommand(2, 'g35', 'g30');
            f.AddPathCommand(2, 'g37', 'g30');
            f.AddPathCommand(2, 'g37', 'g31');
            f.AddPathCommand(2, 'g32', 'g31');
            f.AddPathCommand(2, 'g32', 'g30');
            f.AddPathCommand(2, 'g34', 'g30');
            f.AddPathCommand(2, 'g34', 'g29');
            f.AddPathCommand(2, 'g32', 'g29');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'actionButtonMovie':{
            f.AddGuide('dx2', 0, 'ss', '3', '8');
            f.AddGuide('g9', 1, 'vc', '0', 'dx2');
            f.AddGuide('g10', 1, 'vc', 'dx2', '0');
            f.AddGuide('g11', 1, 'hc', '0', 'dx2');
            f.AddGuide('g12', 1, 'hc', 'dx2', '0');
            f.AddGuide('g13', 0, 'ss', '3', '4');
            f.AddGuide('g14', 0, 'g13', '1455', '21600');
            f.AddGuide('g15', 0, 'g13', '1905', '21600');
            f.AddGuide('g16', 0, 'g13', '2325', '21600');
            f.AddGuide('g17', 0, 'g13', '16155', '21600');
            f.AddGuide('g18', 0, 'g13', '17010', '21600');
            f.AddGuide('g19', 0, 'g13', '19335', '21600');
            f.AddGuide('g20', 0, 'g13', '19725', '21600');
            f.AddGuide('g21', 0, 'g13', '20595', '21600');
            f.AddGuide('g22', 0, 'g13', '5280', '21600');
            f.AddGuide('g23', 0, 'g13', '5730', '21600');
            f.AddGuide('g24', 0, 'g13', '6630', '21600');
            f.AddGuide('g25', 0, 'g13', '7492', '21600');
            f.AddGuide('g26', 0, 'g13', '9067', '21600');
            f.AddGuide('g27', 0, 'g13', '9555', '21600');
            f.AddGuide('g28', 0, 'g13', '13342', '21600');
            f.AddGuide('g29', 0, 'g13', '14580', '21600');
            f.AddGuide('g30', 0, 'g13', '15592', '21600');
            f.AddGuide('g31', 1, 'g11', 'g14', '0');
            f.AddGuide('g32', 1, 'g11', 'g15', '0');
            f.AddGuide('g33', 1, 'g11', 'g16', '0');
            f.AddGuide('g34', 1, 'g11', 'g17', '0');
            f.AddGuide('g35', 1, 'g11', 'g18', '0');
            f.AddGuide('g36', 1, 'g11', 'g19', '0');
            f.AddGuide('g37', 1, 'g11', 'g20', '0');
            f.AddGuide('g38', 1, 'g11', 'g21', '0');
            f.AddGuide('g39', 1, 'g9', 'g22', '0');
            f.AddGuide('g40', 1, 'g9', 'g23', '0');
            f.AddGuide('g41', 1, 'g9', 'g24', '0');
            f.AddGuide('g42', 1, 'g9', 'g25', '0');
            f.AddGuide('g43', 1, 'g9', 'g26', '0');
            f.AddGuide('g44', 1, 'g9', 'g27', '0');
            f.AddGuide('g45', 1, 'g9', 'g28', '0');
            f.AddGuide('g46', 1, 'g9', 'g29', '0');
            f.AddGuide('g47', 1, 'g9', 'g30', '0');
            f.AddGuide('g48', 1, 'g9', 'g31', '0');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'g11', 'g39');
            f.AddPathCommand(2, 'g11', 'g44');
            f.AddPathCommand(2, 'g31', 'g44');
            f.AddPathCommand(2, 'g32', 'g43');
            f.AddPathCommand(2, 'g33', 'g43');
            f.AddPathCommand(2, 'g33', 'g47');
            f.AddPathCommand(2, 'g35', 'g47');
            f.AddPathCommand(2, 'g35', 'g45');
            f.AddPathCommand(2, 'g36', 'g45');
            f.AddPathCommand(2, 'g38', 'g46');
            f.AddPathCommand(2, 'g12', 'g46');
            f.AddPathCommand(2, 'g12', 'g41');
            f.AddPathCommand(2, 'g38', 'g41');
            f.AddPathCommand(2, 'g37', 'g42');
            f.AddPathCommand(2, 'g35', 'g42');
            f.AddPathCommand(2, 'g35', 'g41');
            f.AddPathCommand(2, 'g34', 'g40');
            f.AddPathCommand(2, 'g32', 'g40');
            f.AddPathCommand(2, 'g31', 'g39');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darken', false, undefined, undefined);
            f.AddPathCommand(1, 'g11', 'g39');
            f.AddPathCommand(2, 'g11', 'g44');
            f.AddPathCommand(2, 'g31', 'g44');
            f.AddPathCommand(2, 'g32', 'g43');
            f.AddPathCommand(2, 'g33', 'g43');
            f.AddPathCommand(2, 'g33', 'g47');
            f.AddPathCommand(2, 'g35', 'g47');
            f.AddPathCommand(2, 'g35', 'g45');
            f.AddPathCommand(2, 'g36', 'g45');
            f.AddPathCommand(2, 'g38', 'g46');
            f.AddPathCommand(2, 'g12', 'g46');
            f.AddPathCommand(2, 'g12', 'g41');
            f.AddPathCommand(2, 'g38', 'g41');
            f.AddPathCommand(2, 'g37', 'g42');
            f.AddPathCommand(2, 'g35', 'g42');
            f.AddPathCommand(2, 'g35', 'g41');
            f.AddPathCommand(2, 'g34', 'g40');
            f.AddPathCommand(2, 'g32', 'g40');
            f.AddPathCommand(2, 'g31', 'g39');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'g11', 'g39');
            f.AddPathCommand(2, 'g31', 'g39');
            f.AddPathCommand(2, 'g32', 'g40');
            f.AddPathCommand(2, 'g34', 'g40');
            f.AddPathCommand(2, 'g35', 'g41');
            f.AddPathCommand(2, 'g35', 'g42');
            f.AddPathCommand(2, 'g37', 'g42');
            f.AddPathCommand(2, 'g38', 'g41');
            f.AddPathCommand(2, 'g12', 'g41');
            f.AddPathCommand(2, 'g12', 'g46');
            f.AddPathCommand(2, 'g38', 'g46');
            f.AddPathCommand(2, 'g36', 'g45');
            f.AddPathCommand(2, 'g35', 'g45');
            f.AddPathCommand(2, 'g35', 'g47');
            f.AddPathCommand(2, 'g33', 'g47');
            f.AddPathCommand(2, 'g33', 'g43');
            f.AddPathCommand(2, 'g32', 'g43');
            f.AddPathCommand(2, 'g31', 'g44');
            f.AddPathCommand(2, 'g11', 'g44');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'actionButtonReturn':{
            f.AddGuide('dx2', 0, 'ss', '3', '8');
            f.AddGuide('g9', 1, 'vc', '0', 'dx2');
            f.AddGuide('g10', 1, 'vc', 'dx2', '0');
            f.AddGuide('g11', 1, 'hc', '0', 'dx2');
            f.AddGuide('g12', 1, 'hc', 'dx2', '0');
            f.AddGuide('g13', 0, 'ss', '3', '4');
            f.AddGuide('g14', 0, 'g13', '7', '8');
            f.AddGuide('g15', 0, 'g13', '3', '4');
            f.AddGuide('g16', 0, 'g13', '5', '8');
            f.AddGuide('g17', 0, 'g13', '3', '8');
            f.AddGuide('g18', 0, 'g13', '1', '4');
            f.AddGuide('g19', 1, 'g9', 'g15', '0');
            f.AddGuide('g20', 1, 'g9', 'g16', '0');
            f.AddGuide('g21', 1, 'g9', 'g18', '0');
            f.AddGuide('g22', 1, 'g11', 'g14', '0');
            f.AddGuide('g23', 1, 'g11', 'g15', '0');
            f.AddGuide('g24', 1, 'g11', 'g16', '0');
            f.AddGuide('g25', 1, 'g11', 'g17', '0');
            f.AddGuide('g26', 1, 'g11', 'g18', '0');
            f.AddGuide('g27', 0, 'g13', '1', '8');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'g12', 'g21');
            f.AddPathCommand(2, 'g23', 'g9');
            f.AddPathCommand(2, 'hc', 'g21');
            f.AddPathCommand(2, 'g24', 'g21');
            f.AddPathCommand(2, 'g24', 'g20');
            f.AddPathCommand(3, 'g27', 'g27', '0', 'cd4');
            f.AddPathCommand(2, 'g25', 'g19');
            f.AddPathCommand(3, 'g27', 'g27', 'cd4', 'cd4');
            f.AddPathCommand(2, 'g26', 'g21');
            f.AddPathCommand(2, 'g11', 'g21');
            f.AddPathCommand(2, 'g11', 'g20');
            f.AddPathCommand(3, 'g17', 'g17', 'cd2', '-5400000');
            f.AddPathCommand(2, 'hc', 'g10');
            f.AddPathCommand(3, 'g17', 'g17', 'cd4', '-5400000');
            f.AddPathCommand(2, 'g22', 'g21');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darken', false, undefined, undefined);
            f.AddPathCommand(1, 'g12', 'g21');
            f.AddPathCommand(2, 'g23', 'g9');
            f.AddPathCommand(2, 'hc', 'g21');
            f.AddPathCommand(2, 'g24', 'g21');
            f.AddPathCommand(2, 'g24', 'g20');
            f.AddPathCommand(3, 'g27', 'g27', '0', 'cd4');
            f.AddPathCommand(2, 'g25', 'g19');
            f.AddPathCommand(3, 'g27', 'g27', 'cd4', 'cd4');
            f.AddPathCommand(2, 'g26', 'g21');
            f.AddPathCommand(2, 'g11', 'g21');
            f.AddPathCommand(2, 'g11', 'g20');
            f.AddPathCommand(3, 'g17', 'g17', 'cd2', '-5400000');
            f.AddPathCommand(2, 'hc', 'g10');
            f.AddPathCommand(3, 'g17', 'g17', 'cd4', '-5400000');
            f.AddPathCommand(2, 'g22', 'g21');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'g12', 'g21');
            f.AddPathCommand(2, 'g22', 'g21');
            f.AddPathCommand(2, 'g22', 'g20');
            f.AddPathCommand(3, 'g17', 'g17', '0', 'cd4');
            f.AddPathCommand(2, 'g25', 'g10');
            f.AddPathCommand(3, 'g17', 'g17', 'cd4', 'cd4');
            f.AddPathCommand(2, 'g11', 'g21');
            f.AddPathCommand(2, 'g26', 'g21');
            f.AddPathCommand(2, 'g26', 'g20');
            f.AddPathCommand(3, 'g27', 'g27', 'cd2', '-5400000');
            f.AddPathCommand(2, 'hc', 'g19');
            f.AddPathCommand(3, 'g27', 'g27', 'cd4', '-5400000');
            f.AddPathCommand(2, 'g24', 'g21');
            f.AddPathCommand(2, 'hc', 'g21');
            f.AddPathCommand(2, 'g23', 'g9');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'actionButtonSound':{
            f.AddGuide('dx2', 0, 'ss', '3', '8');
            f.AddGuide('g9', 1, 'vc', '0', 'dx2');
            f.AddGuide('g10', 1, 'vc', 'dx2', '0');
            f.AddGuide('g11', 1, 'hc', '0', 'dx2');
            f.AddGuide('g12', 1, 'hc', 'dx2', '0');
            f.AddGuide('g13', 0, 'ss', '3', '4');
            f.AddGuide('g14', 0, 'g13', '1', '8');
            f.AddGuide('g15', 0, 'g13', '5', '16');
            f.AddGuide('g16', 0, 'g13', '5', '8');
            f.AddGuide('g17', 0, 'g13', '11', '16');
            f.AddGuide('g18', 0, 'g13', '3', '4');
            f.AddGuide('g19', 0, 'g13', '7', '8');
            f.AddGuide('g20', 1, 'g9', 'g14', '0');
            f.AddGuide('g21', 1, 'g9', 'g15', '0');
            f.AddGuide('g22', 1, 'g9', 'g17', '0');
            f.AddGuide('g23', 1, 'g9', 'g19', '0');
            f.AddGuide('g24', 1, 'g11', 'g15', '0');
            f.AddGuide('g25', 1, 'g11', 'g16', '0');
            f.AddGuide('g26', 1, 'g11', 'g18', '0');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'g11', 'g21');
            f.AddPathCommand(2, 'g11', 'g22');
            f.AddPathCommand(2, 'g24', 'g22');
            f.AddPathCommand(2, 'g25', 'g10');
            f.AddPathCommand(2, 'g25', 'g9');
            f.AddPathCommand(2, 'g24', 'g21');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darken', false, undefined, undefined);
            f.AddPathCommand(1, 'g11', 'g21');
            f.AddPathCommand(2, 'g11', 'g22');
            f.AddPathCommand(2, 'g24', 'g22');
            f.AddPathCommand(2, 'g25', 'g10');
            f.AddPathCommand(2, 'g25', 'g9');
            f.AddPathCommand(2, 'g24', 'g21');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'g11', 'g21');
            f.AddPathCommand(2, 'g24', 'g21');
            f.AddPathCommand(2, 'g25', 'g9');
            f.AddPathCommand(2, 'g25', 'g10');
            f.AddPathCommand(2, 'g24', 'g22');
            f.AddPathCommand(2, 'g11', 'g22');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'g26', 'g21');
            f.AddPathCommand(2, 'g12', 'g20');
            f.AddPathCommand(1, 'g26', 'vc');
            f.AddPathCommand(2, 'g12', 'vc');
            f.AddPathCommand(1, 'g26', 'g22');
            f.AddPathCommand(2, 'g12', 'g23');
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'arc':{
            f.AddAdj('adj1', 15, '16200000');
            f.AddAdj('adj2', 15, '0');
            f.AddGuide('stAng', 10, '0', 'adj1', '21599999');
            f.AddGuide('enAng', 10, '0', 'adj2', '21599999');
            f.AddGuide('sw11', 1, 'enAng', '0', 'stAng');
            f.AddGuide('sw12', 1, 'sw11', '21600000', '0');
            f.AddGuide('swAng', 3, 'sw11', 'sw11', 'sw12');
            f.AddGuide('wt1', 12, 'wd2', 'stAng');
            f.AddGuide('ht1', 7, 'hd2', 'stAng');
            f.AddGuide('dx1', 6, 'wd2', 'ht1', 'wt1');
            f.AddGuide('dy1', 11, 'hd2', 'ht1', 'wt1');
            f.AddGuide('wt2', 12, 'wd2', 'enAng');
            f.AddGuide('ht2', 7, 'hd2', 'enAng');
            f.AddGuide('dx2', 6, 'wd2', 'ht2', 'wt2');
            f.AddGuide('dy2', 11, 'hd2', 'ht2', 'wt2');
            f.AddGuide('x1', 1, 'hc', 'dx1', '0');
            f.AddGuide('y1', 1, 'vc', 'dy1', '0');
            f.AddGuide('x2', 1, 'hc', 'dx2', '0');
            f.AddGuide('y2', 1, 'vc', 'dy2', '0');
            f.AddGuide('sw0', 1, '21600000', '0', 'stAng');
            f.AddGuide('da1', 1, 'swAng', '0', 'sw0');
            f.AddGuide('g1', 8, 'x1', 'x2');
            f.AddGuide('ir', 3, 'da1', 'r', 'g1');
            f.AddGuide('sw1', 1, 'cd4', '0', 'stAng');
            f.AddGuide('sw2', 1, '27000000', '0', 'stAng');
            f.AddGuide('sw3', 3, 'sw1', 'sw1', 'sw2');
            f.AddGuide('da2', 1, 'swAng', '0', 'sw3');
            f.AddGuide('g5', 8, 'y1', 'y2');
            f.AddGuide('ib', 3, 'da2', 'b', 'g5');
            f.AddGuide('sw4', 1, 'cd2', '0', 'stAng');
            f.AddGuide('sw5', 1, '32400000', '0', 'stAng');
            f.AddGuide('sw6', 3, 'sw4', 'sw4', 'sw5');
            f.AddGuide('da3', 1, 'swAng', '0', 'sw6');
            f.AddGuide('g9', 16, 'x1', 'x2');
            f.AddGuide('il', 3, 'da3', 'l', 'g9');
            f.AddGuide('sw7', 1, '_3cd4', '0', 'stAng');
            f.AddGuide('sw8', 1, '37800000', '0', 'stAng');
            f.AddGuide('sw9', 3, 'sw7', 'sw7', 'sw8');
            f.AddGuide('da4', 1, 'swAng', '0', 'sw9');
            f.AddGuide('g13', 16, 'y1', 'y2');
            f.AddGuide('it', 3, 'da4', 't', 'g13');
            f.AddGuide('cang1', 1, 'stAng', '0', 'cd4');
            f.AddGuide('cang2', 1, 'enAng', 'cd4', '0');
            f.AddGuide('cang3', 2, 'cang1', 'cang2', '2');
            f.AddHandlePolar('adj1','0','21599999', undefined, '0', '0', 'x1', 'y1');
            f.AddHandlePolar('adj2','0','21599999', undefined, '0', '0', 'x2', 'y2');
            f.AddCnx('cang1', 'x1', 'y1');
            f.AddCnx('cang3', 'hc', 'vc');
            f.AddCnx('cang2', 'x2', 'y2');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(3, 'wd2', 'hd2', 'stAng', 'swAng');
            f.AddPathCommand(2, 'hc', 'vc');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(3, 'wd2', 'hd2', 'stAng', 'swAng');
            break;
        }
        case 'bentArrow':{
            f.AddAdj('adj1', 15, '25000');
            f.AddAdj('adj2', 15, '25000');
            f.AddAdj('adj3', 15, '25000');
            f.AddAdj('adj4', 15, '43750');
            f.AddGuide('a2', 10, '0', 'adj2', '50000');
            f.AddGuide('maxAdj1', 0, 'a2', '2', '1');
            f.AddGuide('a1', 10, '0', 'adj1', 'maxAdj1');
            f.AddGuide('a3', 10, '0', 'adj3', '50000');
            f.AddGuide('th', 0, 'ss', 'a1', '100000');
            f.AddGuide('aw2', 0, 'ss', 'a2', '100000');
            f.AddGuide('th2', 0, 'th', '1', '2');
            f.AddGuide('dh2', 1, 'aw2', '0', 'th2');
            f.AddGuide('ah', 0, 'ss', 'a3', '100000');
            f.AddGuide('bw', 1, 'r', '0', 'ah');
            f.AddGuide('bh', 1, 'b', '0', 'dh2');
            f.AddGuide('bs', 16, 'bw', 'bh');
            f.AddGuide('maxAdj4', 0, '100000', 'bs', 'ss');
            f.AddGuide('a4', 10, '0', 'adj4', 'maxAdj4');
            f.AddGuide('bd', 0, 'ss', 'a4', '100000');
            f.AddGuide('bd3', 1, 'bd', '0', 'th');
            f.AddGuide('bd2', 8, 'bd3', '0');
            f.AddGuide('x3', 1, 'th', 'bd2', '0');
            f.AddGuide('x4', 1, 'r', '0', 'ah');
            f.AddGuide('y3', 1, 'dh2', 'th', '0');
            f.AddGuide('y4', 1, 'y3', 'dh2', '0');
            f.AddGuide('y5', 1, 'dh2', 'bd', '0');
            f.AddGuide('y6', 1, 'y3', 'bd2', '0');
            f.AddHandleXY('adj1','0','maxAdj1', undefined, '0', '0', 'th', 'b');
            f.AddHandleXY(undefined, '0', '0','adj2','0','50000', 'r', 'y4');
            f.AddHandleXY('adj3','0','50000', undefined, '0', '0', 'x4', 't');
            f.AddHandleXY('adj4','0','maxAdj4', undefined, '0', '0', 'bd', 't');
            f.AddCnx('_3cd4', 'x4', 't');
            f.AddCnx('cd4', 'x4', 'y4');
            f.AddCnx('cd4', 'th2', 'b');
            f.AddCnx('0', 'r', 'aw2');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'b');
            f.AddPathCommand(2, 'l', 'y5');
            f.AddPathCommand(3, 'bd', 'bd', 'cd2', 'cd4');
            f.AddPathCommand(2, 'x4', 'dh2');
            f.AddPathCommand(2, 'x4', 't');
            f.AddPathCommand(2, 'r', 'aw2');
            f.AddPathCommand(2, 'x4', 'y4');
            f.AddPathCommand(2, 'x4', 'y3');
            f.AddPathCommand(2, 'x3', 'y3');
            f.AddPathCommand(3, 'bd2', 'bd2', '_3cd4', '-5400000');
            f.AddPathCommand(2, 'th', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'bentConnector2':{
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            break;
        }
        case 'bentConnector3':{
            f.AddAdj('adj1', 15, '50000');
            f.AddGuide('x1', 0, 'w', 'adj1', '100000');
            f.AddHandleXY('adj1','-**********','**********', undefined, '0', '0', 'x1', 'vc');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'x1', 't');
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(2, 'r', 'b');
            break;
        }
        case 'bentConnector4':{
            f.AddAdj('adj1', 15, '50000');
            f.AddAdj('adj2', 15, '50000');
            f.AddGuide('x1', 0, 'w', 'adj1', '100000');
            f.AddGuide('x2', 2, 'x1', 'r', '2');
            f.AddGuide('y2', 0, 'h', 'adj2', '100000');
            f.AddGuide('y1', 2, 't', 'y2', '2');
            f.AddHandleXY('adj1','-**********','**********', undefined, '0', '0', 'x1', 'y1');
            f.AddHandleXY(undefined, '0', '0','adj2','-**********','**********', 'x2', 'y2');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'x1', 't');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(2, 'r', 'y2');
            f.AddPathCommand(2, 'r', 'b');
            break;
        }
        case 'bentConnector5':{
            f.AddAdj('adj1', 15, '50000');
            f.AddAdj('adj2', 15, '50000');
            f.AddAdj('adj3', 15, '50000');
            f.AddGuide('x1', 0, 'w', 'adj1', '100000');
            f.AddGuide('x3', 0, 'w', 'adj3', '100000');
            f.AddGuide('x2', 2, 'x1', 'x3', '2');
            f.AddGuide('y2', 0, 'h', 'adj2', '100000');
            f.AddGuide('y1', 2, 't', 'y2', '2');
            f.AddGuide('y3', 2, 'b', 'y2', '2');
            f.AddHandleXY('adj1','-**********','**********', undefined, '0', '0', 'x1', 'y1');
            f.AddHandleXY(undefined, '0', '0','adj2','-**********','**********', 'x2', 'y2');
            f.AddHandleXY('adj3','-**********','**********', undefined, '0', '0', 'x3', 'y3');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'x1', 't');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(2, 'x3', 'y2');
            f.AddPathCommand(2, 'x3', 'b');
            f.AddPathCommand(2, 'r', 'b');
            break;
        }
        case 'bentUpArrow':{
            f.AddAdj('adj1', 15, '25000');
            f.AddAdj('adj2', 15, '25000');
            f.AddAdj('adj3', 15, '25000');
            f.AddGuide('a1', 10, '0', 'adj1', '50000');
            f.AddGuide('a2', 10, '0', 'adj2', '50000');
            f.AddGuide('a3', 10, '0', 'adj3', '50000');
            f.AddGuide('y1', 0, 'ss', 'a3', '100000');
            f.AddGuide('dx1', 0, 'ss', 'a2', '50000');
            f.AddGuide('x1', 1, 'r', '0', 'dx1');
            f.AddGuide('dx3', 0, 'ss', 'a2', '100000');
            f.AddGuide('x3', 1, 'r', '0', 'dx3');
            f.AddGuide('dx2', 0, 'ss', 'a1', '200000');
            f.AddGuide('x2', 1, 'x3', '0', 'dx2');
            f.AddGuide('x4', 1, 'x3', 'dx2', '0');
            f.AddGuide('dy2', 0, 'ss', 'a1', '100000');
            f.AddGuide('y2', 1, 'b', '0', 'dy2');
            f.AddGuide('x0', 0, 'x4', '1', '2');
            f.AddGuide('y3', 2, 'y2', 'b', '2');
            f.AddGuide('y15', 2, 'y1', 'b', '2');
            f.AddHandleXY(undefined, '0', '0','adj1','0','50000', 'l', 'y2');
            f.AddHandleXY('adj2','0','50000', undefined, '0', '0', 'x1', 't');
            f.AddHandleXY(undefined, '0', '0','adj3','0','50000', 'x2', 'y1');
            f.AddCnx('_3cd4', 'x3', 't');
            f.AddCnx('cd2', 'x1', 'y1');
            f.AddCnx('cd2', 'l', 'y3');
            f.AddCnx('cd4', 'x0', 'b');
            f.AddCnx('0', 'x4', 'y15');
            f.AddCnx('0', 'r', 'y1');
            f.AddRect('l', 'y2', 'x4', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y2');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x2', 'y1');
            f.AddPathCommand(2, 'x1', 'y1');
            f.AddPathCommand(2, 'x3', 't');
            f.AddPathCommand(2, 'r', 'y1');
            f.AddPathCommand(2, 'x4', 'y1');
            f.AddPathCommand(2, 'x4', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'bevel':{
            f.AddAdj('adj', 15, '12500');
            f.AddGuide('a', 10, '0', 'adj', '50000');
            f.AddGuide('x1', 0, 'ss', 'a', '100000');
            f.AddGuide('x2', 1, 'r', '0', 'x1');
            f.AddGuide('y2', 1, 'b', '0', 'x1');
            f.AddHandleXY('adj','0','50000', undefined, '0', '0', 'x1', 't');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('0', 'x2', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd4', 'hc', 'y2');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd2', 'x1', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('_3cd4', 'hc', 'x1');
            f.AddRect('x1', 'x1', 'x2', 'y2');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'x1');
            f.AddPathCommand(2, 'x2', 'x1');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'lightenLess', false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'x2', 'x1');
            f.AddPathCommand(2, 'x1', 'x1');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darkenLess', false, undefined, undefined);
            f.AddPathCommand(1, 'l', 'b');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'lighten', false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'x1', 'x1');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darken', false, undefined, undefined);
            f.AddPathCommand(1, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x2', 'x1');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'x1', 'x1');
            f.AddPathCommand(2, 'x2', 'x1');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'x1', 'x1');
            f.AddPathCommand(1, 'l', 'b');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(1, 'r', 't');
            f.AddPathCommand(2, 'x2', 'x1');
            f.AddPathCommand(1, 'r', 'b');
            f.AddPathCommand(2, 'x2', 'y2');
            break;
        }
        case 'blockArc':{
            f.AddAdj('adj1', 15, '10800000');
            f.AddAdj('adj2', 15, '0');
            f.AddAdj('adj3', 15, '25000');
            f.AddGuide('stAng', 10, '0', 'adj1', '21599999');
            f.AddGuide('istAng', 10, '0', 'adj2', '21599999');
            f.AddGuide('a3', 10, '0', 'adj3', '50000');
            f.AddGuide('sw11', 1, 'istAng', '0', 'stAng');
            f.AddGuide('sw12', 1, 'sw11', '21600000', '0');
            f.AddGuide('swAng', 3, 'sw11', 'sw11', 'sw12');
            f.AddGuide('iswAng', 1, '0', '0', 'swAng');
            f.AddGuide('wt1', 12, 'wd2', 'stAng');
            f.AddGuide('ht1', 7, 'hd2', 'stAng');
            f.AddGuide('wt3', 12, 'wd2', 'istAng');
            f.AddGuide('ht3', 7, 'hd2', 'istAng');
            f.AddGuide('dx1', 6, 'wd2', 'ht1', 'wt1');
            f.AddGuide('dy1', 11, 'hd2', 'ht1', 'wt1');
            f.AddGuide('dx3', 6, 'wd2', 'ht3', 'wt3');
            f.AddGuide('dy3', 11, 'hd2', 'ht3', 'wt3');
            f.AddGuide('x1', 1, 'hc', 'dx1', '0');
            f.AddGuide('y1', 1, 'vc', 'dy1', '0');
            f.AddGuide('x3', 1, 'hc', 'dx3', '0');
            f.AddGuide('y3', 1, 'vc', 'dy3', '0');
            f.AddGuide('dr', 0, 'ss', 'a3', '100000');
            f.AddGuide('iwd2', 1, 'wd2', '0', 'dr');
            f.AddGuide('ihd2', 1, 'hd2', '0', 'dr');
            f.AddGuide('wt2', 12, 'iwd2', 'istAng');
            f.AddGuide('ht2', 7, 'ihd2', 'istAng');
            f.AddGuide('wt4', 12, 'iwd2', 'stAng');
            f.AddGuide('ht4', 7, 'ihd2', 'stAng');
            f.AddGuide('dx2', 6, 'iwd2', 'ht2', 'wt2');
            f.AddGuide('dy2', 11, 'ihd2', 'ht2', 'wt2');
            f.AddGuide('dx4', 6, 'iwd2', 'ht4', 'wt4');
            f.AddGuide('dy4', 11, 'ihd2', 'ht4', 'wt4');
            f.AddGuide('x2', 1, 'hc', 'dx2', '0');
            f.AddGuide('y2', 1, 'vc', 'dy2', '0');
            f.AddGuide('x4', 1, 'hc', 'dx4', '0');
            f.AddGuide('y4', 1, 'vc', 'dy4', '0');
            f.AddGuide('sw0', 1, '21600000', '0', 'stAng');
            f.AddGuide('da1', 1, 'swAng', '0', 'sw0');
            f.AddGuide('g1', 8, 'x1', 'x2');
            f.AddGuide('g2', 8, 'x3', 'x4');
            f.AddGuide('g3', 8, 'g1', 'g2');
            f.AddGuide('ir', 3, 'da1', 'r', 'g3');
            f.AddGuide('sw1', 1, 'cd4', '0', 'stAng');
            f.AddGuide('sw2', 1, '27000000', '0', 'stAng');
            f.AddGuide('sw3', 3, 'sw1', 'sw1', 'sw2');
            f.AddGuide('da2', 1, 'swAng', '0', 'sw3');
            f.AddGuide('g5', 8, 'y1', 'y2');
            f.AddGuide('g6', 8, 'y3', 'y4');
            f.AddGuide('g7', 8, 'g5', 'g6');
            f.AddGuide('ib', 3, 'da2', 'b', 'g7');
            f.AddGuide('sw4', 1, 'cd2', '0', 'stAng');
            f.AddGuide('sw5', 1, '32400000', '0', 'stAng');
            f.AddGuide('sw6', 3, 'sw4', 'sw4', 'sw5');
            f.AddGuide('da3', 1, 'swAng', '0', 'sw6');
            f.AddGuide('g9', 16, 'x1', 'x2');
            f.AddGuide('g10', 16, 'x3', 'x4');
            f.AddGuide('g11', 16, 'g9', 'g10');
            f.AddGuide('il', 3, 'da3', 'l', 'g11');
            f.AddGuide('sw7', 1, '_3cd4', '0', 'stAng');
            f.AddGuide('sw8', 1, '37800000', '0', 'stAng');
            f.AddGuide('sw9', 3, 'sw7', 'sw7', 'sw8');
            f.AddGuide('da4', 1, 'swAng', '0', 'sw9');
            f.AddGuide('g13', 16, 'y1', 'y2');
            f.AddGuide('g14', 16, 'y3', 'y4');
            f.AddGuide('g15', 16, 'g13', 'g14');
            f.AddGuide('it', 3, 'da4', 't', 'g15');
            f.AddGuide('x5', 2, 'x1', 'x4', '2');
            f.AddGuide('y5', 2, 'y1', 'y4', '2');
            f.AddGuide('x6', 2, 'x3', 'x2', '2');
            f.AddGuide('y6', 2, 'y3', 'y2', '2');
            f.AddGuide('cang1', 1, 'stAng', '0', 'cd4');
            f.AddGuide('cang2', 1, 'istAng', 'cd4', '0');
            f.AddGuide('cang3', 2, 'cang1', 'cang2', '2');
            f.AddHandlePolar('adj1','0','21599999', undefined, '0', '0', 'x1', 'y1');
            f.AddHandlePolar(undefined, '0', '0','adj3','0','50000', 'x2', 'y2');
            f.AddCnx('cang1', 'x5', 'y5');
            f.AddCnx('cang2', 'x6', 'y6');
            f.AddCnx('cang3', 'hc', 'vc');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(3, 'wd2', 'hd2', 'stAng', 'swAng');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(3, 'iwd2', 'ihd2', 'istAng', 'iswAng');
            f.AddPathCommand(6);
            break;
        }
        case 'borderCallout1':{
            f.AddAdj('adj1', 15, '18750');
            f.AddAdj('adj2', 15, '-8333');
            f.AddAdj('adj3', 15, '112500');
            f.AddAdj('adj4', 15, '-38333');
            f.AddGuide('y1', 0, 'h', 'adj1', '100000');
            f.AddGuide('x1', 0, 'w', 'adj2', '100000');
            f.AddGuide('y2', 0, 'h', 'adj3', '100000');
            f.AddGuide('x2', 0, 'w', 'adj4', '100000');
            f.AddHandleXY('adj2','-**********','**********','adj1','-**********','**********', 'x1', 'y1');
            f.AddHandleXY('adj4','-**********','**********','adj3','-**********','**********', 'x2', 'y2');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(2, 'x2', 'y2');
            break;
        }
        case 'borderCallout2':{
            f.AddAdj('adj1', 15, '18750');
            f.AddAdj('adj2', 15, '-8333');
            f.AddAdj('adj3', 15, '18750');
            f.AddAdj('adj4', 15, '-16667');
            f.AddAdj('adj5', 15, '112500');
            f.AddAdj('adj6', 15, '-46667');
            f.AddGuide('y1', 0, 'h', 'adj1', '100000');
            f.AddGuide('x1', 0, 'w', 'adj2', '100000');
            f.AddGuide('y2', 0, 'h', 'adj3', '100000');
            f.AddGuide('x2', 0, 'w', 'adj4', '100000');
            f.AddGuide('y3', 0, 'h', 'adj5', '100000');
            f.AddGuide('x3', 0, 'w', 'adj6', '100000');
            f.AddHandleXY('adj2','-**********','**********','adj1','-**********','**********', 'x1', 'y1');
            f.AddHandleXY('adj4','-**********','**********','adj3','-**********','**********', 'x2', 'y2');
            f.AddHandleXY('adj6','-**********','**********','adj5','-**********','**********', 'x3', 'y3');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x3', 'y3');
            break;
        }
        case 'borderCallout3':{
            f.AddAdj('adj1', 15, '18750');
            f.AddAdj('adj2', 15, '-8333');
            f.AddAdj('adj3', 15, '18750');
            f.AddAdj('adj4', 15, '-16667');
            f.AddAdj('adj5', 15, '100000');
            f.AddAdj('adj6', 15, '-16667');
            f.AddAdj('adj7', 15, '112963');
            f.AddAdj('adj8', 15, '-8333');
            f.AddGuide('y1', 0, 'h', 'adj1', '100000');
            f.AddGuide('x1', 0, 'w', 'adj2', '100000');
            f.AddGuide('y2', 0, 'h', 'adj3', '100000');
            f.AddGuide('x2', 0, 'w', 'adj4', '100000');
            f.AddGuide('y3', 0, 'h', 'adj5', '100000');
            f.AddGuide('x3', 0, 'w', 'adj6', '100000');
            f.AddGuide('y4', 0, 'h', 'adj7', '100000');
            f.AddGuide('x4', 0, 'w', 'adj8', '100000');
            f.AddHandleXY('adj2','-**********','**********','adj1','-**********','**********', 'x1', 'y1');
            f.AddHandleXY('adj4','-**********','**********','adj3','-**********','**********', 'x2', 'y2');
            f.AddHandleXY('adj6','-**********','**********','adj5','-**********','**********', 'x3', 'y3');
            f.AddHandleXY('adj8','-**********','**********','adj7','-**********','**********', 'x4', 'y4');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x3', 'y3');
            f.AddPathCommand(2, 'x4', 'y4');
            break;
        }
        case 'bracePair':{
            f.AddAdj('adj', 15, '8333');
            f.AddGuide('a', 10, '0', 'adj', '25000');
            f.AddGuide('x1', 0, 'ss', 'a', '100000');
            f.AddGuide('x2', 0, 'ss', 'a', '50000');
            f.AddGuide('x3', 1, 'r', '0', 'x2');
            f.AddGuide('x4', 1, 'r', '0', 'x1');
            f.AddGuide('y2', 1, 'vc', '0', 'x1');
            f.AddGuide('y3', 1, 'vc', 'x1', '0');
            f.AddGuide('y4', 1, 'b', '0', 'x1');
            f.AddGuide('it', 0, 'x1', '29289', '100000');
            f.AddGuide('il', 1, 'x1', 'it', '0');
            f.AddGuide('ir', 1, 'r', '0', 'il');
            f.AddGuide('ib', 1, 'b', '0', 'it');
            f.AddHandleXY(undefined, '0', '0','adj','0','25000', 'l', 'x1');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('il', 'il', 'ir', 'ib');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'x2', 'b');
            f.AddPathCommand(3, 'x1', 'x1', 'cd4', 'cd4');
            f.AddPathCommand(2, 'x1', 'y3');
            f.AddPathCommand(3, 'x1', 'x1', '0', '-5400000');
            f.AddPathCommand(3, 'x1', 'x1', 'cd4', '-5400000');
            f.AddPathCommand(2, 'x1', 'x1');
            f.AddPathCommand(3, 'x1', 'x1', 'cd2', 'cd4');
            f.AddPathCommand(2, 'x3', 't');
            f.AddPathCommand(3, 'x1', 'x1', '_3cd4', 'cd4');
            f.AddPathCommand(2, 'x4', 'y2');
            f.AddPathCommand(3, 'x1', 'x1', 'cd2', '-5400000');
            f.AddPathCommand(3, 'x1', 'x1', '_3cd4', '-5400000');
            f.AddPathCommand(2, 'x4', 'y4');
            f.AddPathCommand(3, 'x1', 'x1', '0', 'cd4');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined,  undefined, undefined, undefined);
            f.AddPathCommand(1, 'x2', 'b');
            f.AddPathCommand(3, 'x1', 'x1', 'cd4', 'cd4');
            f.AddPathCommand(2, 'x1', 'y3');
            f.AddPathCommand(3, 'x1', 'x1', '0', '-5400000');
            f.AddPathCommand(3, 'x1', 'x1', 'cd4', '-5400000');
            f.AddPathCommand(2, 'x1', 'x1');
            f.AddPathCommand(3, 'x1', 'x1', 'cd2', 'cd4');
            f.AddPathCommand(1, 'x3', 't');
            f.AddPathCommand(3, 'x1', 'x1', '_3cd4', 'cd4');
            f.AddPathCommand(2, 'x4', 'y2');
            f.AddPathCommand(3, 'x1', 'x1', 'cd2', '-5400000');
            f.AddPathCommand(3, 'x1', 'x1', '_3cd4', '-5400000');
            f.AddPathCommand(2, 'x4', 'y4');
            f.AddPathCommand(3, 'x1', 'x1', '0', 'cd4');
            break;
        }
        case 'bracketPair':{
            f.AddAdj('adj', 15, '16667');
            f.AddGuide('a', 10, '0', 'adj', '50000');
            f.AddGuide('x1', 0, 'ss', 'a', '100000');
            f.AddGuide('x2', 1, 'r', '0', 'x1');
            f.AddGuide('y2', 1, 'b', '0', 'x1');
            f.AddGuide('il', 0, 'x1', '29289', '100000');
            f.AddGuide('ir', 1, 'r', '0', 'il');
            f.AddGuide('ib', 1, 'b', '0', 'il');
            f.AddHandleXY(undefined, '0', '0','adj','0','50000', 'l', 'x1');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('il', 'il', 'ir', 'ib');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 'x1');
            f.AddPathCommand(3, 'x1', 'x1', 'cd2', 'cd4');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(3, 'x1', 'x1', '_3cd4', 'cd4');
            f.AddPathCommand(2, 'r', 'y2');
            f.AddPathCommand(3, 'x1', 'x1', '0', 'cd4');
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(3, 'x1', 'x1', 'cd4', 'cd4');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'b');
            f.AddPathCommand(3, 'x1', 'x1', 'cd4', 'cd4');
            f.AddPathCommand(2, 'l', 'x1');
            f.AddPathCommand(3, 'x1', 'x1', 'cd2', 'cd4');
            f.AddPathCommand(1, 'x2', 't');
            f.AddPathCommand(3, 'x1', 'x1', '_3cd4', 'cd4');
            f.AddPathCommand(2, 'r', 'y2');
            f.AddPathCommand(3, 'x1', 'x1', '0', 'cd4');
            break;
        }
        case 'callout1':{
            f.AddAdj('adj1', 15, '18750');
            f.AddAdj('adj2', 15, '-8333');
            f.AddAdj('adj3', 15, '112500');
            f.AddAdj('adj4', 15, '-38333');
            f.AddGuide('y1', 0, 'h', 'adj1', '100000');
            f.AddGuide('x1', 0, 'w', 'adj2', '100000');
            f.AddGuide('y2', 0, 'h', 'adj3', '100000');
            f.AddGuide('x2', 0, 'w', 'adj4', '100000');
            f.AddHandleXY('adj2','-**********','**********','adj1','-**********','**********', 'x1', 'y1');
            f.AddHandleXY('adj4','-**********','**********','adj3','-**********','**********', 'x2', 'y2');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(2, 'x2', 'y2');
            break;
        }
        case 'callout2':{
            f.AddAdj('adj1', 15, '18750');
            f.AddAdj('adj2', 15, '-8333');
            f.AddAdj('adj3', 15, '18750');
            f.AddAdj('adj4', 15, '-16667');
            f.AddAdj('adj5', 15, '112500');
            f.AddAdj('adj6', 15, '-46667');
            f.AddGuide('y1', 0, 'h', 'adj1', '100000');
            f.AddGuide('x1', 0, 'w', 'adj2', '100000');
            f.AddGuide('y2', 0, 'h', 'adj3', '100000');
            f.AddGuide('x2', 0, 'w', 'adj4', '100000');
            f.AddGuide('y3', 0, 'h', 'adj5', '100000');
            f.AddGuide('x3', 0, 'w', 'adj6', '100000');
            f.AddHandleXY('adj2','-**********','**********','adj1','-**********','**********', 'x1', 'y1');
            f.AddHandleXY('adj4','-**********','**********','adj3','-**********','**********', 'x2', 'y2');
            f.AddHandleXY('adj6','-**********','**********','adj5','-**********','**********', 'x3', 'y3');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x3', 'y3');
            break;
        }
        case 'callout3':{
            f.AddAdj('adj1', 15, '18750');
            f.AddAdj('adj2', 15, '-8333');
            f.AddAdj('adj3', 15, '18750');
            f.AddAdj('adj4', 15, '-16667');
            f.AddAdj('adj5', 15, '100000');
            f.AddAdj('adj6', 15, '-16667');
            f.AddAdj('adj7', 15, '112963');
            f.AddAdj('adj8', 15, '-8333');
            f.AddGuide('y1', 0, 'h', 'adj1', '100000');
            f.AddGuide('x1', 0, 'w', 'adj2', '100000');
            f.AddGuide('y2', 0, 'h', 'adj3', '100000');
            f.AddGuide('x2', 0, 'w', 'adj4', '100000');
            f.AddGuide('y3', 0, 'h', 'adj5', '100000');
            f.AddGuide('x3', 0, 'w', 'adj6', '100000');
            f.AddGuide('y4', 0, 'h', 'adj7', '100000');
            f.AddGuide('x4', 0, 'w', 'adj8', '100000');
            f.AddHandleXY('adj2','-**********','**********','adj1','-**********','**********', 'x1', 'y1');
            f.AddHandleXY('adj4','-**********','**********','adj3','-**********','**********', 'x2', 'y2');
            f.AddHandleXY('adj6','-**********','**********','adj5','-**********','**********', 'x3', 'y3');
            f.AddHandleXY('adj8','-**********','**********','adj7','-**********','**********', 'x4', 'y4');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x3', 'y3');
            f.AddPathCommand(2, 'x4', 'y4');
            break;
        }
        case 'can':{
            f.AddAdj('adj', 15, '25000');
            f.AddGuide('maxAdj', 0, '50000', 'h', 'ss');
            f.AddGuide('a', 10, '0', 'adj', 'maxAdj');
            f.AddGuide('y1', 0, 'ss', 'a', '200000');
            f.AddGuide('y2', 1, 'y1', 'y1', '0');
            f.AddGuide('y3', 1, 'b', '0', 'y1');
            f.AddHandleXY(undefined, '0', '0','adj','0','maxAdj', 'hc', 'y2');
            f.AddCnx('_3cd4', 'hc', 'y2');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('l', 'y2', 'r', 'y3');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y1');
            f.AddPathCommand(3, 'wd2', 'y1', 'cd2', '-10800000');
            f.AddPathCommand(2, 'r', 'y3');
            f.AddPathCommand(3, 'wd2', 'y1', '0', 'cd2');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'lighten', false, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y1');
            f.AddPathCommand(3, 'wd2', 'y1', 'cd2', 'cd2');
            f.AddPathCommand(3, 'wd2', 'y1', '0', 'cd2');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'r', 'y1');
            f.AddPathCommand(3, 'wd2', 'y1', '0', 'cd2');
            f.AddPathCommand(3, 'wd2', 'y1', 'cd2', 'cd2');
            f.AddPathCommand(2, 'r', 'y3');
            f.AddPathCommand(3, 'wd2', 'y1', '0', 'cd2');
            f.AddPathCommand(2, 'l', 'y1');
            break;
        }
        case 'chartPlus':{
            f.AddPathCommand(0,false, 'none', undefined, 10, 10);
            f.AddPathCommand(1, '5', '0');
            f.AddPathCommand(2, '5', '10');
            f.AddPathCommand(1, '0', '5');
            f.AddPathCommand(2, '10', '5');
            f.AddPathCommand(0,undefined, undefined, false, 10, 10);
            f.AddPathCommand(1, '0', '0');
            f.AddPathCommand(2, '0', '10');
            f.AddPathCommand(2, '10', '10');
            f.AddPathCommand(2, '10', '0');
            f.AddPathCommand(6);
            break;
        }
        case 'chartStar':{
            f.AddPathCommand(0,false, 'none', undefined, 10, 10);
            f.AddPathCommand(1, '0', '0');
            f.AddPathCommand(2, '10', '10');
            f.AddPathCommand(1, '0', '10');
            f.AddPathCommand(2, '10', '0');
            f.AddPathCommand(1, '5', '0');
            f.AddPathCommand(2, '5', '10');
            f.AddPathCommand(0,undefined, undefined, false, 10, 10);
            f.AddPathCommand(1, '0', '0');
            f.AddPathCommand(2, '0', '10');
            f.AddPathCommand(2, '10', '10');
            f.AddPathCommand(2, '10', '0');
            f.AddPathCommand(6);
            break;
        }
        case 'chartX':{
            f.AddPathCommand(0,false, 'none', undefined, 10, 10);
            f.AddPathCommand(1, '0', '0');
            f.AddPathCommand(2, '10', '10');
            f.AddPathCommand(1, '0', '10');
            f.AddPathCommand(2, '10', '0');
            f.AddPathCommand(0,undefined, undefined, false, 10, 10);
            f.AddPathCommand(1, '0', '0');
            f.AddPathCommand(2, '0', '10');
            f.AddPathCommand(2, '10', '10');
            f.AddPathCommand(2, '10', '0');
            f.AddPathCommand(6);
            break;
        }
        case 'chevron':{
            f.AddAdj('adj', 15, '50000');
            f.AddGuide('maxAdj', 0, '100000', 'w', 'ss');
            f.AddGuide('a', 10, '0', 'adj', 'maxAdj');
            f.AddGuide('x1', 0, 'ss', 'a', '100000');
            f.AddGuide('x2', 1, 'r', '0', 'x1');
            f.AddGuide('x3', 0, 'x2', '1', '2');
            f.AddGuide('dx', 1, 'x2', '0', 'x1');
            f.AddGuide('il', 3, 'dx', 'x1', 'l');
            f.AddGuide('ir', 3, 'dx', 'x2', 'r');
            f.AddHandleXY('adj','0','maxAdj', undefined, '0', '0', 'x2', 't');
            f.AddCnx('_3cd4', 'x3', 't');
            f.AddCnx('cd2', 'x1', 'vc');
            f.AddCnx('cd4', 'x3', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('il', 't', 'ir', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(2, 'r', 'vc');
            f.AddPathCommand(2, 'x2', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(2, 'x1', 'vc');
            f.AddPathCommand(6);
            break;
        }
        case 'chord':{
            f.AddAdj('adj1', 15, '2700000');
            f.AddAdj('adj2', 15, '16200000');
            f.AddGuide('stAng', 10, '0', 'adj1', '21599999');
            f.AddGuide('enAng', 10, '0', 'adj2', '21599999');
            f.AddGuide('sw1', 1, 'enAng', '0', 'stAng');
            f.AddGuide('sw2', 1, 'sw1', '21600000', '0');
            f.AddGuide('swAng', 3, 'sw1', 'sw1', 'sw2');
            f.AddGuide('wt1', 12, 'wd2', 'stAng');
            f.AddGuide('ht1', 7, 'hd2', 'stAng');
            f.AddGuide('dx1', 6, 'wd2', 'ht1', 'wt1');
            f.AddGuide('dy1', 11, 'hd2', 'ht1', 'wt1');
            f.AddGuide('wt2', 12, 'wd2', 'enAng');
            f.AddGuide('ht2', 7, 'hd2', 'enAng');
            f.AddGuide('dx2', 6, 'wd2', 'ht2', 'wt2');
            f.AddGuide('dy2', 11, 'hd2', 'ht2', 'wt2');
            f.AddGuide('x1', 1, 'hc', 'dx1', '0');
            f.AddGuide('y1', 1, 'vc', 'dy1', '0');
            f.AddGuide('x2', 1, 'hc', 'dx2', '0');
            f.AddGuide('y2', 1, 'vc', 'dy2', '0');
            f.AddGuide('x3', 2, 'x1', 'x2', '2');
            f.AddGuide('y3', 2, 'y1', 'y2', '2');
            f.AddGuide('midAng0', 0, 'swAng', '1', '2');
            f.AddGuide('midAng', 1, 'stAng', 'midAng0', 'cd2');
            f.AddGuide('idx', 7, 'wd2', '2700000');
            f.AddGuide('idy', 12, 'hd2', '2700000');
            f.AddGuide('il', 1, 'hc', '0', 'idx');
            f.AddGuide('ir', 1, 'hc', 'idx', '0');
            f.AddGuide('it', 1, 'vc', '0', 'idy');
            f.AddGuide('ib', 1, 'vc', 'idy', '0');
            f.AddHandlePolar('adj1','0','21599999', undefined, '0', '0', 'x1', 'y1');
            f.AddHandlePolar('adj2','0','21599999', undefined, '0', '0', 'x2', 'y2');
            f.AddCnx('stAng', 'x1', 'y1');
            f.AddCnx('enAng', 'x2', 'y2');
            f.AddCnx('midAng', 'x3', 'y3');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(3, 'wd2', 'hd2', 'stAng', 'swAng');
            f.AddPathCommand(6);
            break;
        }
        case 'circularArrow':{
            f.AddAdj('adj1', 15, '12500');
            f.AddAdj('adj2', 15, '1142319');
            f.AddAdj('adj3', 15, '20457681');
            f.AddAdj('adj4', 15, '10800000');
            f.AddAdj('adj5', 15, '12500');
            f.AddGuide('a5', 10, '0', 'adj5', '25000');
            f.AddGuide('maxAdj1', 0, 'a5', '2', '1');
            f.AddGuide('a1', 10, '0', 'adj1', 'maxAdj1');
            f.AddGuide('enAng', 10, '1', 'adj3', '21599999');
            f.AddGuide('stAng', 10, '0', 'adj4', '21599999');
            f.AddGuide('th', 0, 'ss', 'a1', '100000');
            f.AddGuide('thh', 0, 'ss', 'a5', '100000');
            f.AddGuide('th2', 0, 'th', '1', '2');
            f.AddGuide('rw1', 1, 'wd2', 'th2', 'thh');
            f.AddGuide('rh1', 1, 'hd2', 'th2', 'thh');
            f.AddGuide('rw2', 1, 'rw1', '0', 'th');
            f.AddGuide('rh2', 1, 'rh1', '0', 'th');
            f.AddGuide('rw3', 1, 'rw2', 'th2', '0');
            f.AddGuide('rh3', 1, 'rh2', 'th2', '0');
            f.AddGuide('wtH', 12, 'rw3', 'enAng');
            f.AddGuide('htH', 7, 'rh3', 'enAng');
            f.AddGuide('dxH', 6, 'rw3', 'htH', 'wtH');
            f.AddGuide('dyH', 11, 'rh3', 'htH', 'wtH');
            f.AddGuide('xH', 1, 'hc', 'dxH', '0');
            f.AddGuide('yH', 1, 'vc', 'dyH', '0');
            f.AddGuide('rI', 16, 'rw2', 'rh2');
            f.AddGuide('u1', 0, 'dxH', 'dxH', '1');
            f.AddGuide('u2', 0, 'dyH', 'dyH', '1');
            f.AddGuide('u3', 0, 'rI', 'rI', '1');
            f.AddGuide('u4', 1, 'u1', '0', 'u3');
            f.AddGuide('u5', 1, 'u2', '0', 'u3');
            f.AddGuide('u6', 0, 'u4', 'u5', 'u1');
            f.AddGuide('u7', 0, 'u6', '1', 'u2');
            f.AddGuide('u8', 1, '1', '0', 'u7');
            f.AddGuide('u9', 13, 'u8');
            f.AddGuide('u10', 0, 'u4', '1', 'dxH');
            f.AddGuide('u11', 0, 'u10', '1', 'dyH');
            f.AddGuide('u12', 2, '1', 'u9', 'u11');
            f.AddGuide('u13', 5, '1', 'u12');
            f.AddGuide('u14', 1, 'u13', '21600000', '0');
            f.AddGuide('u15', 3, 'u13', 'u13', 'u14');
            f.AddGuide('u16', 1, 'u15', '0', 'enAng');
            f.AddGuide('u17', 1, 'u16', '21600000', '0');
            f.AddGuide('u18', 3, 'u16', 'u16', 'u17');
            f.AddGuide('u19', 1, 'u18', '0', 'cd2');
            f.AddGuide('u20', 1, 'u18', '0', '21600000');
            f.AddGuide('u21', 3, 'u19', 'u20', 'u18');
            f.AddGuide('maxAng', 4, 'u21');
            f.AddGuide('aAng', 10, '0', 'adj2', 'maxAng');
            f.AddGuide('ptAng', 1, 'enAng', 'aAng', '0');
            f.AddGuide('wtA', 12, 'rw3', 'ptAng');
            f.AddGuide('htA', 7, 'rh3', 'ptAng');
            f.AddGuide('dxA', 6, 'rw3', 'htA', 'wtA');
            f.AddGuide('dyA', 11, 'rh3', 'htA', 'wtA');
            f.AddGuide('xA', 1, 'hc', 'dxA', '0');
            f.AddGuide('yA', 1, 'vc', 'dyA', '0');
            f.AddGuide('wtE', 12, 'rw1', 'stAng');
            f.AddGuide('htE', 7, 'rh1', 'stAng');
            f.AddGuide('dxE', 6, 'rw1', 'htE', 'wtE');
            f.AddGuide('dyE', 11, 'rh1', 'htE', 'wtE');
            f.AddGuide('xE', 1, 'hc', 'dxE', '0');
            f.AddGuide('yE', 1, 'vc', 'dyE', '0');
            f.AddGuide('dxG', 7, 'thh', 'ptAng');
            f.AddGuide('dyG', 12, 'thh', 'ptAng');
            f.AddGuide('xG', 1, 'xH', 'dxG', '0');
            f.AddGuide('yG', 1, 'yH', 'dyG', '0');
            f.AddGuide('dxB', 7, 'thh', 'ptAng');
            f.AddGuide('dyB', 12, 'thh', 'ptAng');
            f.AddGuide('xB', 1, 'xH', '0', 'dxB', '0');
            f.AddGuide('yB', 1, 'yH', '0', 'dyB', '0');
            f.AddGuide('sx1', 1, 'xB', '0', 'hc');
            f.AddGuide('sy1', 1, 'yB', '0', 'vc');
            f.AddGuide('sx2', 1, 'xG', '0', 'hc');
            f.AddGuide('sy2', 1, 'yG', '0', 'vc');
            f.AddGuide('rO', 16, 'rw1', 'rh1');
            f.AddGuide('x1O', 0, 'sx1', 'rO', 'rw1');
            f.AddGuide('y1O', 0, 'sy1', 'rO', 'rh1');
            f.AddGuide('x2O', 0, 'sx2', 'rO', 'rw1');
            f.AddGuide('y2O', 0, 'sy2', 'rO', 'rh1');
            f.AddGuide('dxO', 1, 'x2O', '0', 'x1O');
            f.AddGuide('dyO', 1, 'y2O', '0', 'y1O');
            f.AddGuide('dO', 9, 'dxO', 'dyO', '0');
            f.AddGuide('q1', 0, 'x1O', 'y2O', '1');
            f.AddGuide('q2', 0, 'x2O', 'y1O', '1');
            f.AddGuide('DO', 1, 'q1', '0', 'q2');
            f.AddGuide('q3', 0, 'rO', 'rO', '1');
            f.AddGuide('q4', 0, 'dO', 'dO', '1');
            f.AddGuide('q5', 0, 'q3', 'q4', '1');
            f.AddGuide('q6', 0, 'DO', 'DO', '1');
            f.AddGuide('q7', 1, 'q5', '0', 'q6');
            f.AddGuide('q8', 8, 'q7', '0');
            f.AddGuide('sdelO', 13, 'q8');
            f.AddGuide('ndyO', 0, 'dyO', '-1', '1');
            f.AddGuide('sdyO', 3, 'ndyO', '-1', '1');
            f.AddGuide('q9', 0, 'sdyO', 'dxO', '1');
            f.AddGuide('q10', 0, 'q9', 'sdelO', '1');
            f.AddGuide('q11', 0, 'DO', 'dyO', '1');
            f.AddGuide('dxF1', 2, 'q11', 'q10', 'q4');
            f.AddGuide('q12', 1, 'q11', '0', 'q10');
            f.AddGuide('dxF2', 0, 'q12', '1', 'q4');
            f.AddGuide('adyO', 4, 'dyO');
            f.AddGuide('q13', 0, 'adyO', 'sdelO', '1');
            f.AddGuide('q14', 0, 'DO', 'dxO', '-1');
            f.AddGuide('dyF1', 2, 'q14', 'q13', 'q4');
            f.AddGuide('q15', 1, 'q14', '0', 'q13');
            f.AddGuide('dyF2', 0, 'q15', '1', 'q4');
            f.AddGuide('q16', 1, 'x2O', '0', 'dxF1');
            f.AddGuide('q17', 1, 'x2O', '0', 'dxF2');
            f.AddGuide('q18', 1, 'y2O', '0', 'dyF1');
            f.AddGuide('q19', 1, 'y2O', '0', 'dyF2');
            f.AddGuide('q20', 9, 'q16', 'q18', '0');
            f.AddGuide('q21', 9, 'q17', 'q19', '0');
            f.AddGuide('q22', 1, 'q21', '0', 'q20');
            f.AddGuide('dxF', 3, 'q22', 'dxF1', 'dxF2');
            f.AddGuide('dyF', 3, 'q22', 'dyF1', 'dyF2');
            f.AddGuide('sdxF', 0, 'dxF', 'rw1', 'rO');
            f.AddGuide('sdyF', 0, 'dyF', 'rh1', 'rO');
            f.AddGuide('xF', 1, 'hc', 'sdxF', '0');
            f.AddGuide('yF', 1, 'vc', 'sdyF', '0');
            f.AddGuide('x1I', 0, 'sx1', 'rI', 'rw2');
            f.AddGuide('y1I', 0, 'sy1', 'rI', 'rh2');
            f.AddGuide('x2I', 0, 'sx2', 'rI', 'rw2');
            f.AddGuide('y2I', 0, 'sy2', 'rI', 'rh2');
            f.AddGuide('dxI', 1, 'x2I', '0', 'x1I');
            f.AddGuide('dyI', 1, 'y2I', '0', 'y1I');
            f.AddGuide('dI', 9, 'dxI', 'dyI', '0');
            f.AddGuide('v1', 0, 'x1I', 'y2I', '1');
            f.AddGuide('v2', 0, 'x2I', 'y1I', '1');
            f.AddGuide('DI', 1, 'v1', '0', 'v2');
            f.AddGuide('v3', 0, 'rI', 'rI', '1');
            f.AddGuide('v4', 0, 'dI', 'dI', '1');
            f.AddGuide('v5', 0, 'v3', 'v4', '1');
            f.AddGuide('v6', 0, 'DI', 'DI', '1');
            f.AddGuide('v7', 1, 'v5', '0', 'v6');
            f.AddGuide('v8', 8, 'v7', '0');
            f.AddGuide('sdelI', 13, 'v8');
            f.AddGuide('v9', 0, 'sdyO', 'dxI', '1');
            f.AddGuide('v10', 0, 'v9', 'sdelI', '1');
            f.AddGuide('v11', 0, 'DI', 'dyI', '1');
            f.AddGuide('dxC1', 2, 'v11', 'v10', 'v4');
            f.AddGuide('v12', 1, 'v11', '0', 'v10');
            f.AddGuide('dxC2', 0, 'v12', '1', 'v4');
            f.AddGuide('adyI', 4, 'dyI');
            f.AddGuide('v13', 0, 'adyI', 'sdelI', '1');
            f.AddGuide('v14', 0, 'DI', 'dxI', '-1');
            f.AddGuide('dyC1', 2, 'v14', 'v13', 'v4');
            f.AddGuide('v15', 1, 'v14', '0', 'v13');
            f.AddGuide('dyC2', 0, 'v15', '1', 'v4');
            f.AddGuide('v16', 1, 'x1I', '0', 'dxC1');
            f.AddGuide('v17', 1, 'x1I', '0', 'dxC2');
            f.AddGuide('v18', 1, 'y1I', '0', 'dyC1');
            f.AddGuide('v19', 1, 'y1I', '0', 'dyC2');
            f.AddGuide('v20', 9, 'v16', 'v18', '0');
            f.AddGuide('v21', 9, 'v17', 'v19', '0');
            f.AddGuide('v22', 1, 'v21', '0', 'v20');
            f.AddGuide('dxC', 3, 'v22', 'dxC1', 'dxC2');
            f.AddGuide('dyC', 3, 'v22', 'dyC1', 'dyC2');
            f.AddGuide('sdxC', 0, 'dxC', 'rw2', 'rI');
            f.AddGuide('sdyC', 0, 'dyC', 'rh2', 'rI');
            f.AddGuide('xC', 1, 'hc', 'sdxC', '0');
            f.AddGuide('yC', 1, 'vc', 'sdyC', '0');
            f.AddGuide('ist0', 5, 'sdxC', 'sdyC');
            f.AddGuide('ist1', 1, 'ist0', '21600000', '0');
            f.AddGuide('istAng', 3, 'ist0', 'ist0', 'ist1');
            f.AddGuide('isw1', 1, 'stAng', '0', 'istAng');
            f.AddGuide('isw2', 1, 'isw1', '0', '21600000');
            f.AddGuide('iswAng', 3, 'isw1', 'isw2', 'isw1');
            f.AddGuide('p1', 1, 'xF', '0', 'xC');
            f.AddGuide('p2', 1, 'yF', '0', 'yC');
            f.AddGuide('p3', 9, 'p1', 'p2', '0');
            f.AddGuide('p4', 0, 'p3', '1', '2');
            f.AddGuide('p5', 1, 'p4', '0', 'thh');
            f.AddGuide('xGp', 3, 'p5', 'xF', 'xG');
            f.AddGuide('yGp', 3, 'p5', 'yF', 'yG');
            f.AddGuide('xBp', 3, 'p5', 'xC', 'xB');
            f.AddGuide('yBp', 3, 'p5', 'yC', 'yB');
            f.AddGuide('en0', 5, 'sdxF', 'sdyF');
            f.AddGuide('en1', 1, 'en0', '21600000', '0');
            f.AddGuide('en2', 3, 'en0', 'en0', 'en1');
            f.AddGuide('sw0', 1, 'en2', '0', 'stAng');
            f.AddGuide('sw1', 1, 'sw0', '21600000', '0');
            f.AddGuide('swAng', 3, 'sw0', 'sw0', 'sw1');
            f.AddGuide('wtI', 12, 'rw3', 'stAng');
            f.AddGuide('htI', 7, 'rh3', 'stAng');
            f.AddGuide('dxI', 6, 'rw3', 'htI', 'wtI');
            f.AddGuide('dyI', 11, 'rh3', 'htI', 'wtI');
            f.AddGuide('xI', 1, 'hc', 'dxI', '0');
            f.AddGuide('yI', 1, 'vc', 'dyI', '0');
            f.AddGuide('aI', 1, 'stAng', '0', 'cd4');
            f.AddGuide('aA', 1, 'ptAng', 'cd4', '0');
            f.AddGuide('aB', 1, 'ptAng', 'cd2', '0');
            f.AddGuide('idx', 7, 'rw1', '2700000');
            f.AddGuide('idy', 12, 'rh1', '2700000');
            f.AddGuide('il', 1, 'hc', '0', 'idx');
            f.AddGuide('ir', 1, 'hc', 'idx', '0');
            f.AddGuide('it', 1, 'vc', '0', 'idy');
            f.AddGuide('ib', 1, 'vc', 'idy', '0');
            f.AddHandlePolar('adj2','0','maxAng', undefined, '0', '0', 'xA', 'yA');
            f.AddHandlePolar('adj4','0','21599999', undefined, '0', '0', 'xE', 'yE');
            f.AddHandlePolar(undefined, '0', '0','adj1','0','maxAdj1', 'xF', 'yF');
            f.AddHandlePolar(undefined, '0', '0','adj5','0','25000', 'xB', 'yB');
            f.AddCnx('aI', 'xI', 'yI');
            f.AddCnx('ptAng', 'xGp', 'yGp');
            f.AddCnx('aA', 'xA', 'yA');
            f.AddCnx('aB', 'xBp', 'yBp');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'xE', 'yE');
            f.AddPathCommand(3, 'rw1', 'rh1', 'stAng', 'swAng');
            f.AddPathCommand(2, 'xGp', 'yGp');
            f.AddPathCommand(2, 'xA', 'yA');
            f.AddPathCommand(2, 'xBp', 'yBp');
            f.AddPathCommand(2, 'xC', 'yC');
            f.AddPathCommand(3, 'rw2', 'rh2', 'istAng', 'iswAng');
            f.AddPathCommand(6);
            break;
        }
        case 'cloud':{
            f.AddGuide('il', 0, 'w', '2977', '21600');
            f.AddGuide('it', 0, 'h', '3262', '21600');
            f.AddGuide('ir', 0, 'w', '17087', '21600');
            f.AddGuide('ib', 0, 'h', '17337', '21600');
            f.AddGuide('g27', 0, 'w', '67', '21600');
            f.AddGuide('g28', 0, 'h', '21577', '21600');
            f.AddGuide('g29', 0, 'w', '21582', '21600');
            f.AddGuide('g30', 0, 'h', '1235', '21600');
            f.AddCnx('0', 'g29', 'vc');
            f.AddCnx('cd4', 'hc', 'g28');
            f.AddCnx('cd2', 'g27', 'vc');
            f.AddCnx('_3cd4', 'hc', 'g30');
            f.AddRect('il', 'it', 'ir', 'ib');
             f.AddPathCommand(0,undefined, undefined, undefined, 43200, 43200);
            f.AddPathCommand(1, '3900', '14370');
            f.AddPathCommand(3, '6753', '9190', '-11429249', '7426832');
            f.AddPathCommand(3, '5333', '7267', '-8646143', '5396714');
            f.AddPathCommand(3, '4365', '5945', '-8748475', '5983381');
            f.AddPathCommand(3, '4857', '6595', '-7859164', '7034504');
            f.AddPathCommand(3, '5333', '7273', '-4722533', '6541615');
            f.AddPathCommand(3, '6775', '9220', '-2776035', '7816140');
            f.AddPathCommand(3, '5785', '7867', '37501', '6842000');
            f.AddPathCommand(3, '6752', '9215', '1347096', '6910353');
            f.AddPathCommand(3, '7720', '10543', '3974558', '4542661');
            f.AddPathCommand(3, '4360', '5918', '-16496525', '8804134');
            f.AddPathCommand(3, '4345', '5945', '-14809710', '9151131');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, 43200, 43200);
            f.AddPathCommand(1, '4693', '26177');
            f.AddPathCommand(3, '4345', '5945', '5204520', '1585770');
            f.AddPathCommand(1, '6928', '34899');
            f.AddPathCommand(3, '4360', '5918', '4416628', '686848');
            f.AddPathCommand(1, '16478', '39090');
            f.AddPathCommand(3, '6752', '9215', '8257449', '844866');
            f.AddPathCommand(1, '28827', '34751');
            f.AddPathCommand(3, '6752', '9215', '387196', '959901');
            f.AddPathCommand(1, '34129', '22954');
            f.AddPathCommand(3, '5785', '7867', '-4217541', '4255042');
            f.AddPathCommand(1, '41798', '15354');
            f.AddPathCommand(3, '5333', '7273', '1819082', '1665090');
            f.AddPathCommand(1, '38324', '5426');
            f.AddPathCommand(3, '4857', '6595', '-824660', '891534');
            f.AddPathCommand(1, '29078', '3952');
            f.AddPathCommand(3, '4857', '6595', '-8950887', '1091722');
            f.AddPathCommand(1, '22141', '4720');
            f.AddPathCommand(3, '4365', '5945', '-9809656', '1061181');
            f.AddPathCommand(1, '14000', '5192');
            f.AddPathCommand(3, '6753', '9190', '-4002417', '739161');
            f.AddPathCommand(1, '4127', '15789');
            f.AddPathCommand(3, '6753', '9190', '9459261', '711490');
            f.AddPathCommand(6);
            break;
        }
        case 'cloudCallout':{
            f.AddAdj('adj1', 15, '-20833');
            f.AddAdj('adj2', 15, '62500');
            f.AddGuide('dxPos', 0, 'w', 'adj1', '100000');
            f.AddGuide('dyPos', 0, 'h', 'adj2', '100000');
            f.AddGuide('xPos', 1, 'hc', 'dxPos', '0');
            f.AddGuide('yPos', 1, 'vc', 'dyPos', '0');
            f.AddGuide('ht', 6, 'hd2', 'dxPos', 'dyPos');
            f.AddGuide('wt', 11, 'wd2', 'dxPos', 'dyPos');
            f.AddGuide('g2', 6, 'wd2', 'ht', 'wt');
            f.AddGuide('g3', 11, 'hd2', 'ht', 'wt');
            f.AddGuide('g4', 1, 'hc', 'g2', '0');
            f.AddGuide('g5', 1, 'vc', 'g3', '0');
            f.AddGuide('g6', 1, 'g4', '0', 'xPos');
            f.AddGuide('g7', 1, 'g5', '0', 'yPos');
            f.AddGuide('g8', 9, 'g6', 'g7', '0');
            f.AddGuide('g9', 0, 'ss', '6600', '21600');
            f.AddGuide('g10', 1, 'g8', '0', 'g9');
            f.AddGuide('g11', 0, 'g10', '1', '3');
            f.AddGuide('g12', 0, 'ss', '1800', '21600');
            f.AddGuide('g13', 1, 'g11', 'g12', '0');
            f.AddGuide('g14', 0, 'g13', 'g6', 'g8');
            f.AddGuide('g15', 0, 'g13', 'g7', 'g8');
            f.AddGuide('g16', 1, 'g14', 'xPos', '0');
            f.AddGuide('g17', 1, 'g15', 'yPos', '0');
            f.AddGuide('g18', 0, 'ss', '4800', '21600');
            f.AddGuide('g19', 0, 'g11', '2', '1');
            f.AddGuide('g20', 1, 'g18', 'g19', '0');
            f.AddGuide('g21', 0, 'g20', 'g6', 'g8');
            f.AddGuide('g22', 0, 'g20', 'g7', 'g8');
            f.AddGuide('g23', 1, 'g21', 'xPos', '0');
            f.AddGuide('g24', 1, 'g22', 'yPos', '0');
            f.AddGuide('g25', 0, 'ss', '1200', '21600');
            f.AddGuide('g26', 0, 'ss', '600', '21600');
            f.AddGuide('x23', 1, 'xPos', 'g26', '0');
            f.AddGuide('x24', 1, 'g16', 'g25', '0');
            f.AddGuide('x25', 1, 'g23', 'g12', '0');
            f.AddGuide('il', 0, 'w', '2977', '21600');
            f.AddGuide('it', 0, 'h', '3262', '21600');
            f.AddGuide('ir', 0, 'w', '17087', '21600');
            f.AddGuide('ib', 0, 'h', '17337', '21600');
            f.AddGuide('g27', 0, 'w', '67', '21600');
            f.AddGuide('g28', 0, 'h', '21577', '21600');
            f.AddGuide('g29', 0, 'w', '21582', '21600');
            f.AddGuide('g30', 0, 'h', '1235', '21600');
            f.AddGuide('pang', 5, 'dxPos', 'dyPos');
            f.AddHandleXY('adj1','-**********','**********','adj2','-**********','**********', 'xPos', 'yPos');
            f.AddCnx('cd2', 'g27', 'vc');
            f.AddCnx('cd4', 'hc', 'g28');
            f.AddCnx('0', 'g29', 'vc');
            f.AddCnx('_3cd4', 'hc', 'g30');
            f.AddCnx('pang', 'xPos', 'yPos');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, 43200, 43200);
            f.AddPathCommand(1, '3900', '14370');
            f.AddPathCommand(3, '6753', '9190', '-11429249', '7426832');
            f.AddPathCommand(3, '5333', '7267', '-8646143', '5396714');
            f.AddPathCommand(3, '4365', '5945', '-8748475', '5983381');
            f.AddPathCommand(3, '4857', '6595', '-7859164', '7034504');
            f.AddPathCommand(3, '5333', '7273', '-4722533', '6541615');
            f.AddPathCommand(3, '6775', '9220', '-2776035', '7816140');
            f.AddPathCommand(3, '5785', '7867', '37501', '6842000');
            f.AddPathCommand(3, '6752', '9215', '1347096', '6910353');
            f.AddPathCommand(3, '7720', '10543', '3974558', '4542661');
            f.AddPathCommand(3, '4360', '5918', '-16496525', '8804134');
            f.AddPathCommand(3, '4345', '5945', '-14809710', '9151131');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x23', 'yPos');
            f.AddPathCommand(3, 'g26', 'g26', '0', '21600000');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x24', 'g17');
            f.AddPathCommand(3, 'g25', 'g25', '0', '21600000');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x25', 'g24');
            f.AddPathCommand(3, 'g12', 'g12', '0', '21600000');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, 43200, 43200);
            f.AddPathCommand(1, '4693', '26177');
            f.AddPathCommand(3, '4345', '5945', '5204520', '1585770');
            f.AddPathCommand(1, '6928', '34899');
            f.AddPathCommand(3, '4360', '5918', '4416628', '686848');
            f.AddPathCommand(1, '16478', '39090');
            f.AddPathCommand(3, '6752', '9215', '8257449', '844866');
            f.AddPathCommand(1, '28827', '34751');
            f.AddPathCommand(3, '6752', '9215', '387196', '959901');
            f.AddPathCommand(1, '34129', '22954');
            f.AddPathCommand(3, '5785', '7867', '-4217541', '4255042');
            f.AddPathCommand(1, '41798', '15354');
            f.AddPathCommand(3, '5333', '7273', '1819082', '1665090');
            f.AddPathCommand(1, '38324', '5426');
            f.AddPathCommand(3, '4857', '6595', '-824660', '891534');
            f.AddPathCommand(1, '29078', '3952');
            f.AddPathCommand(3, '4857', '6595', '-8950887', '1091722');
            f.AddPathCommand(1, '22141', '4720');
            f.AddPathCommand(3, '4365', '5945', '-9809656', '1061181');
            f.AddPathCommand(1, '14000', '5192');
            f.AddPathCommand(3, '6753', '9190', '-4002417', '739161');
            f.AddPathCommand(1, '4127', '15789');
            f.AddPathCommand(3, '6753', '9190', '9459261', '711490');
            f.AddPathCommand(6);
            break;
        }
        case 'corner':{
            f.AddAdj('adj1', 15, '50000');
            f.AddAdj('adj2', 15, '50000');
            f.AddGuide('maxAdj1', 0, '100000', 'h', 'ss');
            f.AddGuide('maxAdj2', 0, '100000', 'w', 'ss');
            f.AddGuide('a1', 10, '0', 'adj1', 'maxAdj1');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('x1', 0, 'ss', 'a2', '100000');
            f.AddGuide('dy1', 0, 'ss', 'a1', '100000');
            f.AddGuide('y1', 1, 'b', '0', 'dy1');
            f.AddGuide('cx1', 0, 'x1', '1', '2');
            f.AddGuide('cy1', 2, 'y1', 'b', '2');
            f.AddGuide('d', 1, 'w', '0', 'h');
            f.AddGuide('it', 3, 'd', 'y1', 't');
            f.AddGuide('ir', 3, 'd', 'r', 'x1');
            f.AddHandleXY(undefined, '0', '0','adj1','0','maxAdj1', 'l', 'y1');
            f.AddHandleXY('adj2','0','maxAdj2', undefined, '0', '0', 'x1', 't');
            f.AddCnx('0', 'r', 'cy1');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'cx1', 't');
            f.AddRect('l', 'it', 'ir', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'x1', 't');
            f.AddPathCommand(2, 'x1', 'y1');
            f.AddPathCommand(2, 'r', 'y1');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'cornerTabs':{
            f.AddGuide('md', 9, 'w', 'h', '0');
            f.AddGuide('dx', 0, '1', 'md', '20');
            f.AddGuide('y1', 1, '0', 'b', 'dx');
            f.AddGuide('x1', 1, '0', 'r', 'dx');
            f.AddCnx('cd2', 'l', 't');
            f.AddCnx('cd2', 'l', 'dx');
            f.AddCnx('cd2', 'l', 'y1');
            f.AddCnx('cd2', 'l', 'b');
            f.AddCnx('_3cd4', 'dx', 't');
            f.AddCnx('_3cd4', 'x1', 't');
            f.AddCnx('cd4', 'dx', 'b');
            f.AddCnx('cd4', 'x1', 'b');
            f.AddCnx('0', 'r', 't');
            f.AddCnx('0', 'r', 'dx');
            f.AddCnx('0', 'r', 'y1');
            f.AddCnx('0', 'r', 'b');
            f.AddRect('dx', 'dx', 'x1', 'y1');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'dx', 't');
            f.AddPathCommand(2, 'l', 'dx');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y1');
            f.AddPathCommand(2, 'dx', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'dx');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'r', 'y1');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'cube':{
            f.AddAdj('adj', 15, '25000');
            f.AddGuide('a', 10, '0', 'adj', '100000');
            f.AddGuide('y1', 0, 'ss', 'a', '100000');
            f.AddGuide('y4', 1, 'b', '0', 'y1');
            f.AddGuide('y2', 0, 'y4', '1', '2');
            f.AddGuide('y3', 2, 'y1', 'b', '2');
            f.AddGuide('x4', 1, 'r', '0', 'y1');
            f.AddGuide('x2', 0, 'x4', '1', '2');
            f.AddGuide('x3', 2, 'y1', 'r', '2');
            f.AddHandleXY(undefined, '0', '0','adj','0','100000', 'l', 'y1');
            f.AddCnx('_3cd4', 'x3', 't');
            f.AddCnx('_3cd4', 'x2', 'y1');
            f.AddCnx('cd2', 'l', 'y3');
            f.AddCnx('cd4', 'x2', 'b');
            f.AddCnx('0', 'x4', 'y3');
            f.AddCnx('0', 'r', 'y2');
            f.AddRect('l', 'y1', 'x4', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y1');
            f.AddPathCommand(2, 'x4', 'y1');
            f.AddPathCommand(2, 'x4', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darkenLess', false, undefined, undefined);
            f.AddPathCommand(1, 'x4', 'y1');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'y4');
            f.AddPathCommand(2, 'x4', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'lightenLess', false, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y1');
            f.AddPathCommand(2, 'y1', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'x4', 'y1');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y1');
            f.AddPathCommand(2, 'y1', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'y4');
            f.AddPathCommand(2, 'x4', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'l', 'y1');
            f.AddPathCommand(2, 'x4', 'y1');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(1, 'x4', 'y1');
            f.AddPathCommand(2, 'x4', 'b');
            break;
        }
        case 'curvedConnector2':{
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(5, 'wd2', 't', 'r', 'hd2', 'r', 'b');
            break;
        }
        case 'curvedConnector3':{
            f.AddAdj('adj1', 15, '50000');
            f.AddGuide('x2', 0, 'w', 'adj1', '100000');
            f.AddGuide('x1', 2, 'l', 'x2', '2');
            f.AddGuide('x3', 2, 'r', 'x2', '2');
            f.AddGuide('y3', 0, 'h', '3', '4');
            f.AddHandleXY('adj1','-**********','**********', undefined, '0', '0', 'x2', 'vc');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(5, 'x1', 't', 'x2', 'hd4', 'x2', 'vc');
            f.AddPathCommand(5, 'x2', 'y3', 'x3', 'b', 'r', 'b');
            break;
        }
        case 'curvedConnector4':{
            f.AddAdj('adj1', 15, '50000');
            f.AddAdj('adj2', 15, '50000');
            f.AddGuide('x2', 0, 'w', 'adj1', '100000');
            f.AddGuide('x1', 2, 'l', 'x2', '2');
            f.AddGuide('x3', 2, 'r', 'x2', '2');
            f.AddGuide('x4', 2, 'x2', 'x3', '2');
            f.AddGuide('x5', 2, 'x3', 'r', '2');
            f.AddGuide('y4', 0, 'h', 'adj2', '100000');
            f.AddGuide('y1', 2, 't', 'y4', '2');
            f.AddGuide('y2', 2, 't', 'y1', '2');
            f.AddGuide('y3', 2, 'y1', 'y4', '2');
            f.AddGuide('y5', 2, 'b', 'y4', '2');
            f.AddHandleXY('adj1','-**********','**********', undefined, '0', '0', 'x2', 'y1');
            f.AddHandleXY(undefined, '0', '0','adj2','-**********','**********', 'x3', 'y4');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(5, 'x1', 't', 'x2', 'y2', 'x2', 'y1');
            f.AddPathCommand(5, 'x2', 'y3', 'x4', 'y4', 'x3', 'y4');
            f.AddPathCommand(5, 'x5', 'y4', 'r', 'y5', 'r', 'b');
            break;
        }
        case 'curvedConnector5':{
            f.AddAdj('adj1', 15, '50000');
            f.AddAdj('adj2', 15, '50000');
            f.AddAdj('adj3', 15, '50000');
            f.AddGuide('x3', 0, 'w', 'adj1', '100000');
            f.AddGuide('x6', 0, 'w', 'adj3', '100000');
            f.AddGuide('x1', 2, 'x3', 'x6', '2');
            f.AddGuide('x2', 2, 'l', 'x3', '2');
            f.AddGuide('x4', 2, 'x3', 'x1', '2');
            f.AddGuide('x5', 2, 'x6', 'x1', '2');
            f.AddGuide('x7', 2, 'x6', 'r', '2');
            f.AddGuide('y4', 0, 'h', 'adj2', '100000');
            f.AddGuide('y1', 2, 't', 'y4', '2');
            f.AddGuide('y2', 2, 't', 'y1', '2');
            f.AddGuide('y3', 2, 'y1', 'y4', '2');
            f.AddGuide('y5', 2, 'b', 'y4', '2');
            f.AddGuide('y6', 2, 'y5', 'y4', '2');
            f.AddGuide('y7', 2, 'y5', 'b', '2');
            f.AddHandleXY('adj1','-**********','**********', undefined, '0', '0', 'x3', 'y1');
            f.AddHandleXY(undefined, '0', '0','adj2','-**********','**********', 'x1', 'y4');
            f.AddHandleXY('adj3','-**********','**********', undefined, '0', '0', 'x6', 'y5');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(5, 'x2', 't', 'x3', 'y2', 'x3', 'y1');
            f.AddPathCommand(5, 'x3', 'y3', 'x4', 'y4', 'x1', 'y4');
            f.AddPathCommand(5, 'x5', 'y4', 'x6', 'y6', 'x6', 'y5');
            f.AddPathCommand(5, 'x6', 'y7', 'x7', 'b', 'r', 'b');
            break;
        }
        case 'curvedDownArrow':{
            f.AddAdj('adj1', 15, '25000');
            f.AddAdj('adj2', 15, '50000');
            f.AddAdj('adj3', 15, '25000');
            f.AddGuide('maxAdj2', 0, '50000', 'w', 'ss');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('a1', 10, '0', 'adj1', '100000');
            f.AddGuide('th', 0, 'ss', 'a1', '100000');
            f.AddGuide('aw', 0, 'ss', 'a2', '100000');
            f.AddGuide('q1', 2, 'th', 'aw', '4');
            f.AddGuide('wR', 1, 'wd2', '0', 'q1');
            f.AddGuide('q7', 0, 'wR', '2', '1');
            f.AddGuide('q8', 0, 'q7', 'q7', '1');
            f.AddGuide('q9', 0, 'th', 'th', '1');
            f.AddGuide('q10', 1, 'q8', '0', 'q9');
            f.AddGuide('q11', 13, 'q10');
            f.AddGuide('idy', 0, 'q11', 'h', 'q7');
            f.AddGuide('maxAdj3', 0, '100000', 'idy', 'ss');
            f.AddGuide('a3', 10, '0', 'adj3', 'maxAdj3');
            f.AddGuide('ah', 0, 'ss', 'adj3', '100000');
            f.AddGuide('x3', 1, 'wR', 'th', '0');
            f.AddGuide('q2', 0, 'h', 'h', '1');
            f.AddGuide('q3', 0, 'ah', 'ah', '1');
            f.AddGuide('q4', 1, 'q2', '0', 'q3');
            f.AddGuide('q5', 13, 'q4');
            f.AddGuide('dx', 0, 'q5', 'wR', 'h');
            f.AddGuide('x5', 1, 'wR', 'dx', '0');
            f.AddGuide('x7', 1, 'x3', 'dx', '0');
            f.AddGuide('q6', 1, 'aw', '0', 'th');
            f.AddGuide('dh', 0, 'q6', '1', '2');
            f.AddGuide('x4', 1, 'x5', '0', 'dh');
            f.AddGuide('x8', 1, 'x7', 'dh', '0');
            f.AddGuide('aw2', 0, 'aw', '1', '2');
            f.AddGuide('x6', 1, 'r', '0', 'aw2');
            f.AddGuide('y1', 1, 'b', '0', 'ah');
            f.AddGuide('swAng', 5, 'ah', 'dx');
            f.AddGuide('mswAng', 1, '0', '0', 'swAng');
            f.AddGuide('iy', 1, 'b', '0', 'idy');
            f.AddGuide('ix', 2, 'wR', 'x3', '2');
            f.AddGuide('q12', 0, 'th', '1', '2');
            f.AddGuide('dang2', 5, 'idy', 'q12');
            f.AddGuide('stAng', 1, '_3cd4', 'swAng', '0');
            f.AddGuide('stAng2', 1, '_3cd4', '0', 'dang2');
            f.AddGuide('swAng2', 1, 'dang2', '0', 'cd4');
            f.AddGuide('swAng3', 1, 'cd4', 'dang2', '0');
            f.AddHandleXY('adj1','0','adj2', undefined, '0', '0', 'x7', 'y1');
            f.AddHandleXY('adj2','0','maxAdj2', undefined, '0', '0', 'x4', 'b');
            f.AddHandleXY(undefined, '0', '0','adj3','0','maxAdj3', 'r', 'y1');
            f.AddCnx('_3cd4', 'ix', 't');
            f.AddCnx('cd4', 'q12', 'b');
            f.AddCnx('cd4', 'x4', 'y1');
            f.AddCnx('cd4', 'x6', 'b');
            f.AddCnx('0', 'x8', 'y1');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'x6', 'b');
            f.AddPathCommand(2, 'x4', 'y1');
            f.AddPathCommand(2, 'x5', 'y1');
            f.AddPathCommand(3, 'wR', 'h', 'stAng', 'mswAng');
            f.AddPathCommand(2, 'x3', 't');
            f.AddPathCommand(3, 'wR', 'h', '_3cd4', 'swAng');
            f.AddPathCommand(2, 'x8', 'y1');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darkenLess', false, undefined, undefined);
            f.AddPathCommand(1, 'ix', 'iy');
            f.AddPathCommand(3, 'wR', 'h', 'stAng2', 'swAng2');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(3, 'wR', 'h', 'cd2', 'swAng3');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'ix', 'iy');
            f.AddPathCommand(3, 'wR', 'h', 'stAng2', 'swAng2');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(3, 'wR', 'h', 'cd2', 'cd4');
            f.AddPathCommand(2, 'x3', 't');
            f.AddPathCommand(3, 'wR', 'h', '_3cd4', 'swAng');
            f.AddPathCommand(2, 'x8', 'y1');
            f.AddPathCommand(2, 'x6', 'b');
            f.AddPathCommand(2, 'x4', 'y1');
            f.AddPathCommand(2, 'x5', 'y1');
            f.AddPathCommand(3, 'wR', 'h', 'stAng', 'mswAng');
            break;
        }
        case 'curvedLeftArrow':{
            f.AddAdj('adj1', 15, '25000');
            f.AddAdj('adj2', 15, '50000');
            f.AddAdj('adj3', 15, '25000');
            f.AddGuide('maxAdj2', 0, '50000', 'h', 'ss');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('a1', 10, '0', 'adj1', 'a2');
            f.AddGuide('th', 0, 'ss', 'a1', '100000');
            f.AddGuide('aw', 0, 'ss', 'a2', '100000');
            f.AddGuide('q1', 2, 'th', 'aw', '4');
            f.AddGuide('hR', 1, 'hd2', '0', 'q1');
            f.AddGuide('q7', 0, 'hR', '2', '1');
            f.AddGuide('q8', 0, 'q7', 'q7', '1');
            f.AddGuide('q9', 0, 'th', 'th', '1');
            f.AddGuide('q10', 1, 'q8', '0', 'q9');
            f.AddGuide('q11', 13, 'q10');
            f.AddGuide('idx', 0, 'q11', 'w', 'q7');
            f.AddGuide('maxAdj3', 0, '100000', 'idx', 'ss');
            f.AddGuide('a3', 10, '0', 'adj3', 'maxAdj3');
            f.AddGuide('ah', 0, 'ss', 'a3', '100000');
            f.AddGuide('y3', 1, 'hR', 'th', '0');
            f.AddGuide('q2', 0, 'w', 'w', '1');
            f.AddGuide('q3', 0, 'ah', 'ah', '1');
            f.AddGuide('q4', 1, 'q2', '0', 'q3');
            f.AddGuide('q5', 13, 'q4');
            f.AddGuide('dy', 0, 'q5', 'hR', 'w');
            f.AddGuide('y5', 1, 'hR', 'dy', '0');
            f.AddGuide('y7', 1, 'y3', 'dy', '0');
            f.AddGuide('q6', 1, 'aw', '0', 'th');
            f.AddGuide('dh', 0, 'q6', '1', '2');
            f.AddGuide('y4', 1, 'y5', '0', 'dh');
            f.AddGuide('y8', 1, 'y7', 'dh', '0');
            f.AddGuide('aw2', 0, 'aw', '1', '2');
            f.AddGuide('y6', 1, 'b', '0', 'aw2');
            f.AddGuide('x1', 1, 'l', 'ah', '0');
            f.AddGuide('swAng', 5, 'ah', 'dy');
            f.AddGuide('mswAng', 1, '0', '0', 'swAng');
            f.AddGuide('ix', 1, 'l', 'idx', '0');
            f.AddGuide('iy', 2, 'hR', 'y3', '2');
            f.AddGuide('q12', 0, 'th', '1', '2');
            f.AddGuide('dang2', 5, 'idx', 'q12');
            f.AddGuide('swAng2', 1, 'dang2', '0', 'swAng');
            f.AddGuide('swAng3', 1, 'swAng', 'dang2', '0');
            f.AddGuide('stAng3', 1, '0', '0', 'dang2');
            f.AddHandleXY(undefined, '0', '0','adj1','0','a2', 'x1', 'y5');
            f.AddHandleXY(undefined, '0', '0','adj2','0','maxAdj2', 'r', 'y4');
            f.AddHandleXY('adj3','0','maxAdj3', undefined, '0', '0', 'x1', 'b');
            f.AddCnx('cd2', 'l', 'q12');
            f.AddCnx('cd2', 'x1', 'y4');
            f.AddCnx('cd3', 'l', 'y6');
            f.AddCnx('cd4', 'x1', 'y8');
            f.AddCnx('0', 'r', 'iy');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y6');
            f.AddPathCommand(2, 'x1', 'y4');
            f.AddPathCommand(2, 'x1', 'y5');
            f.AddPathCommand(3, 'w', 'hR', 'swAng', 'swAng2');
            f.AddPathCommand(3, 'w', 'hR', 'stAng3', 'swAng3');
            f.AddPathCommand(2, 'x1', 'y8');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darkenLess', false, undefined, undefined);
            f.AddPathCommand(1, 'r', 'y3');
            f.AddPathCommand(3, 'w', 'hR', '0', '-5400000');
            f.AddPathCommand(2, 'l', 't');
            f.AddPathCommand(3, 'w', 'hR', '_3cd4', 'cd4');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'r', 'y3');
            f.AddPathCommand(3, 'w', 'hR', '0', '-5400000');
            f.AddPathCommand(2, 'l', 't');
            f.AddPathCommand(3, 'w', 'hR', '_3cd4', 'cd4');
            f.AddPathCommand(2, 'r', 'y3');
            f.AddPathCommand(3, 'w', 'hR', '0', 'swAng');
            f.AddPathCommand(2, 'x1', 'y8');
            f.AddPathCommand(2, 'l', 'y6');
            f.AddPathCommand(2, 'x1', 'y4');
            f.AddPathCommand(2, 'x1', 'y5');
            f.AddPathCommand(3, 'w', 'hR', 'swAng', 'swAng2');
            break;
        }
        case 'curvedRightArrow':{

            f.AddAdj('adj1', 15, '25000');
            f.AddAdj('adj2', 15, '50000');
            f.AddAdj('adj3', 15, '25000');
            f.AddGuide('maxAdj2', 0, '50000', 'h', 'ss');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('a1', 10, '0', 'adj1', 'a2');
            f.AddGuide('th', 0, 'ss', 'a1', '100000');
            f.AddGuide('aw', 0, 'ss', 'a2', '100000');
            f.AddGuide('q1', 2, 'th', 'aw', '4');
            f.AddGuide('hR', 1, 'hd2', '0', 'q1');
            f.AddGuide('q7', 0, 'hR', '2', '1');
            f.AddGuide('q8', 0, 'q7', 'q7', '1');
            f.AddGuide('q9', 0, 'th', 'th', '1');
            f.AddGuide('q10', 1, 'q8', '0', 'q9');
            f.AddGuide('q11', 13, 'q10');
            f.AddGuide('idx', 0, 'q11', 'w', 'q7');
            f.AddGuide('maxAdj3', 0, '100000', 'idx', 'ss');
            f.AddGuide('a3', 10, '0', 'adj3', 'maxAdj3');
            f.AddGuide('ah', 0, 'ss', 'a3', '100000');
            f.AddGuide('y3', 1, 'hR', 'th', '0');
            f.AddGuide('q2', 0, 'w', 'w', '1');
            f.AddGuide('q3', 0, 'ah', 'ah', '1');
            f.AddGuide('q4', 1, 'q2', '0', 'q3');
            f.AddGuide('q5', 13, 'q4');
            f.AddGuide('dy', 0, 'q5', 'hR', 'w');
            f.AddGuide('y5', 1, 'hR', 'dy', '0');
            f.AddGuide('y7', 1, 'y3', 'dy', '0');
            f.AddGuide('q6', 1, 'aw', '0', 'th');
            f.AddGuide('dh', 0, 'q6', '1', '2');
            f.AddGuide('y4', 1, 'y5', '0', 'dh');
            f.AddGuide('y8', 1, 'y7', 'dh', '0');
            f.AddGuide('aw2', 0, 'aw', '1', '2');
            f.AddGuide('y6', 1, 'b', '0', 'aw2');
            f.AddGuide('x1', 1, 'r', '0', 'ah');
            f.AddGuide('swAng', 5, 'ah', 'dy');
            f.AddGuide('stAng', 1, 'cd2', '0', 'swAng');
            f.AddGuide('mswAng', 1, '0', '0', 'swAng');
            f.AddGuide('ix', 1, 'r', '0', 'idx');
            f.AddGuide('iy', 2, 'hR', 'y3', '2');
            f.AddGuide('q12', 0, 'th', '1', '2');
            f.AddGuide('dang2', 5, 'idx', 'q12');
            f.AddGuide('swAng2', 1, 'dang2', '0', 'cd4');
            f.AddGuide('swAng3', 1, 'cd4', 'dang2', '0');
            f.AddGuide('stAng3', 1, 'cd2', '0', 'dang2');
            f.AddHandleXY(undefined, '0', '0','adj1','0','a2', 'x1', 'y5');
            f.AddHandleXY(undefined, '0', '0','adj2','0','maxAdj2', 'r', 'y4');
            f.AddHandleXY('adj3','0','maxAdj3', undefined, '0', '0', 'x1', 'b');
            f.AddCnx('cd2', 'l', 'iy');
            f.AddCnx('cd4', 'x1', 'y8');
            f.AddCnx('0', 'r', 'y6');
            f.AddCnx('0', 'x1', 'y4');
            f.AddCnx('0', 'r', 'q12');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 'hR');
            f.AddPathCommand(3, 'w', 'hR', 'cd2', 'mswAng');
            f.AddPathCommand(2, 'x1', 'y4');
            f.AddPathCommand(2, 'r', 'y6');
            f.AddPathCommand(2, 'x1', 'y8');
            f.AddPathCommand(2, 'x1', 'y7');
            f.AddPathCommand(3, 'w', 'hR', 'stAng', 'swAng');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darkenLess', false, undefined, undefined);
            f.AddPathCommand(1, 'r', 'th');
            f.AddPathCommand(3, 'w', 'hR', '_3cd4', 'swAng2');
            f.AddPathCommand(3, 'w', 'hR', 'stAng3', 'swAng3');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'hR');
            f.AddPathCommand(3, 'w', 'hR', 'cd2', 'mswAng');
            f.AddPathCommand(2, 'x1', 'y4');
            f.AddPathCommand(2, 'r', 'y6');
            f.AddPathCommand(2, 'x1', 'y8');
            f.AddPathCommand(2, 'x1', 'y7');
            f.AddPathCommand(3, 'w', 'hR', 'stAng', 'swAng');
            f.AddPathCommand(2, 'l', 'hR');
            f.AddPathCommand(3, 'w', 'hR', 'cd2', 'cd4');
            f.AddPathCommand(2, 'r', 'th');
            f.AddPathCommand(3, 'w', 'hR', '_3cd4', 'swAng2');
            break;
        }
        case 'curvedUpArrow':{
            f.AddAdj('adj1', 15, '25000');
            f.AddAdj('adj2', 15, '50000');
            f.AddAdj('adj3', 15, '25000');
            f.AddGuide('maxAdj2', 0, '50000', 'w', 'ss');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('a1', 10, '0', 'adj1', '100000');
            f.AddGuide('th', 0, 'ss', 'a1', '100000');
            f.AddGuide('aw', 0, 'ss', 'a2', '100000');
            f.AddGuide('q1', 2, 'th', 'aw', '4');
            f.AddGuide('wR', 1, 'wd2', '0', 'q1');
            f.AddGuide('q7', 0, 'wR', '2', '1');
            f.AddGuide('q8', 0, 'q7', 'q7', '1');
            f.AddGuide('q9', 0, 'th', 'th', '1');
            f.AddGuide('q10', 1, 'q8', '0', 'q9');
            f.AddGuide('q11', 13, 'q10');
            f.AddGuide('idy', 0, 'q11', 'h', 'q7');
            f.AddGuide('maxAdj3', 0, '100000', 'idy', 'ss');
            f.AddGuide('a3', 10, '0', 'adj3', 'maxAdj3');
            f.AddGuide('ah', 0, 'ss', 'adj3', '100000');
            f.AddGuide('x3', 1, 'wR', 'th', '0');
            f.AddGuide('q2', 0, 'h', 'h', '1');
            f.AddGuide('q3', 0, 'ah', 'ah', '1');
            f.AddGuide('q4', 1, 'q2', '0', 'q3');
            f.AddGuide('q5', 13, 'q4');
            f.AddGuide('dx', 0, 'q5', 'wR', 'h');
            f.AddGuide('x5', 1, 'wR', 'dx', '0');
            f.AddGuide('x7', 1, 'x3', 'dx', '0');
            f.AddGuide('q6', 1, 'aw', '0', 'th');
            f.AddGuide('dh', 0, 'q6', '1', '2');
            f.AddGuide('x4', 1, 'x5', '0', 'dh');
            f.AddGuide('x8', 1, 'x7', 'dh', '0');
            f.AddGuide('aw2', 0, 'aw', '1', '2');
            f.AddGuide('x6', 1, 'r', '0', 'aw2');
            f.AddGuide('y1', 1, 't', 'ah', '0');
            f.AddGuide('swAng', 5, 'ah', 'dx');
            f.AddGuide('mswAng', 1, '0', '0', 'swAng');
            f.AddGuide('iy', 1, 't', 'idy', '0');
            f.AddGuide('ix', 2, 'wR', 'x3', '2');
            f.AddGuide('q12', 0, 'th', '1', '2');
            f.AddGuide('dang2', 5, 'idy', 'q12');
            f.AddGuide('swAng2', 1, 'dang2', '0', 'swAng');
            f.AddGuide('mswAng2', 1, '0', '0', 'swAng2');
            f.AddGuide('stAng3', 1, 'cd4', '0', 'swAng');
            f.AddGuide('swAng3', 1, 'swAng', 'dang2', '0');
            f.AddGuide('stAng2', 1, 'cd4', '0', 'dang2');
            f.AddHandleXY('adj1','0','a2', undefined, '0', '0', 'x7', 'y1');
            f.AddHandleXY('adj2','0','maxAdj2', undefined, '0', '0', 'x4', 't');
            f.AddHandleXY(undefined, '0', '0','adj3','0','maxAdj3', 'r', 'y1');
            f.AddCnx('_3cd4', 'x6', 't');
            f.AddCnx('_3cd4', 'x4', 'y1');
            f.AddCnx('_3cd4', 'q12', 't');
            f.AddCnx('cd4', 'ix', 'b');
            f.AddCnx('0', 'x8', 'y1');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'x6', 't');
            f.AddPathCommand(2, 'x8', 'y1');
            f.AddPathCommand(2, 'x7', 'y1');
            f.AddPathCommand(3, 'wR', 'h', 'stAng3', 'swAng3');
            f.AddPathCommand(3, 'wR', 'h', 'stAng2', 'swAng2');
            f.AddPathCommand(2, 'x4', 'y1');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darkenLess', false, undefined, undefined);
            f.AddPathCommand(1, 'wR', 'b');
            f.AddPathCommand(3, 'wR', 'h', 'cd4', 'cd4');
            f.AddPathCommand(2, 'th', 't');
            f.AddPathCommand(3, 'wR', 'h', 'cd2', '-5400000');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'ix', 'iy');
            f.AddPathCommand(3, 'wR', 'h', 'stAng2', 'swAng2');
            f.AddPathCommand(2, 'x4', 'y1');
            f.AddPathCommand(2, 'x6', 't');
            f.AddPathCommand(2, 'x8', 'y1');
            f.AddPathCommand(2, 'x7', 'y1');
            f.AddPathCommand(3, 'wR', 'h', 'stAng3', 'swAng');
            f.AddPathCommand(2, 'wR', 'b');
            f.AddPathCommand(3, 'wR', 'h', 'cd4', 'cd4');
            f.AddPathCommand(2, 'th', 't');
            f.AddPathCommand(3, 'wR', 'h', 'cd2', '-5400000');
            break;
        }
        case 'decagon':{
            f.AddAdj('vf', 15, '105146');
            f.AddGuide('shd2', 0, 'hd2', 'vf', '100000');
            f.AddGuide('dx1', 7, 'wd2', '2160000');
            f.AddGuide('dx2', 7, 'wd2', '4320000');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x2', 1, 'hc', '0', 'dx2');
            f.AddGuide('x3', 1, 'hc', 'dx2', '0');
            f.AddGuide('x4', 1, 'hc', 'dx1', '0');
            f.AddGuide('dy1', 12, 'shd2', '4320000');
            f.AddGuide('dy2', 12, 'shd2', '2160000');
            f.AddGuide('y1', 1, 'vc', '0', 'dy1');
            f.AddGuide('y2', 1, 'vc', '0', 'dy2');
            f.AddGuide('y3', 1, 'vc', 'dy2', '0');
            f.AddGuide('y4', 1, 'vc', 'dy1', '0');
            f.AddCnx('0', 'x4', 'y2');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('0', 'x4', 'y3');
            f.AddCnx('cd4', 'x3', 'y4');
            f.AddCnx('cd4', 'x2', 'y4');
            f.AddCnx('cd2', 'x1', 'y3');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd2', 'x1', 'y2');
            f.AddCnx('_3cd4', 'x2', 'y1');
            f.AddCnx('_3cd4', 'x3', 'y1');
            f.AddRect('x1', 'y2', 'x4', 'y3');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(2, 'x2', 'y1');
            f.AddPathCommand(2, 'x3', 'y1');
            f.AddPathCommand(2, 'x4', 'y2');
            f.AddPathCommand(2, 'r', 'vc');
            f.AddPathCommand(2, 'x4', 'y3');
            f.AddPathCommand(2, 'x3', 'y4');
            f.AddPathCommand(2, 'x2', 'y4');
            f.AddPathCommand(2, 'x1', 'y3');
            f.AddPathCommand(6);
            break;
        }
        case 'diagStripe':{
            f.AddAdj('adj', 15, '50000');
            f.AddGuide('a', 10, '0', 'adj', '100000');
            f.AddGuide('x2', 0, 'w', 'a', '100000');
            f.AddGuide('x1', 0, 'x2', '1', '2');
            f.AddGuide('x3', 2, 'x2', 'r', '2');
            f.AddGuide('y2', 0, 'h', 'a', '100000');
            f.AddGuide('y1', 0, 'y2', '1', '2');
            f.AddGuide('y3', 2, 'y2', 'b', '2');
            f.AddHandleXY(undefined, '0', '0','adj','0','100000', 'l', 'y2');
            f.AddCnx('0', 'hc', 'vc');
            f.AddCnx('cd2', 'l', 'y3');
            f.AddCnx('cd2', 'x1', 'y1');
            f.AddCnx('_3cd4', 'x3', 't');
            f.AddRect('l', 't', 'x3', 'y3');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y2');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'diamond':{
            f.AddGuide('ir', 0, 'w', '3', '4');
            f.AddGuide('ib', 0, 'h', '3', '4');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('wd4', 'hd4', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(2, 'hc', 't');
            f.AddPathCommand(2, 'r', 'vc');
            f.AddPathCommand(2, 'hc', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'dodecagon':{
            f.AddGuide('x1', 0, 'w', '2894', '21600');
            f.AddGuide('x2', 0, 'w', '7906', '21600');
            f.AddGuide('x3', 0, 'w', '13694', '21600');
            f.AddGuide('x4', 0, 'w', '18706', '21600');
            f.AddGuide('y1', 0, 'h', '2894', '21600');
            f.AddGuide('y2', 0, 'h', '7906', '21600');
            f.AddGuide('y3', 0, 'h', '13694', '21600');
            f.AddGuide('y4', 0, 'h', '18706', '21600');
            f.AddCnx('0', 'x4', 'y1');
            f.AddCnx('0', 'r', 'y2');
            f.AddCnx('0', 'r', 'y3');
            f.AddCnx('0', 'x4', 'y4');
            f.AddCnx('cd4', 'x3', 'b');
            f.AddCnx('cd4', 'x2', 'b');
            f.AddCnx('cd2', 'x1', 'y4');
            f.AddCnx('cd2', 'l', 'y3');
            f.AddCnx('cd2', 'l', 'y2');
            f.AddCnx('cd2', 'x1', 'y1');
            f.AddCnx('_3cd4', 'x2', 't');
            f.AddCnx('_3cd4', 'x3', 't');
            f.AddRect('x1', 'y1', 'x4', 'y4');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y2');
            f.AddPathCommand(2, 'x1', 'y1');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(2, 'x3', 't');
            f.AddPathCommand(2, 'x4', 'y1');
            f.AddPathCommand(2, 'r', 'y2');
            f.AddPathCommand(2, 'r', 'y3');
            f.AddPathCommand(2, 'x4', 'y4');
            f.AddPathCommand(2, 'x3', 'b');
            f.AddPathCommand(2, 'x2', 'b');
            f.AddPathCommand(2, 'x1', 'y4');
            f.AddPathCommand(2, 'l', 'y3');
            f.AddPathCommand(6);
            break;
        }
        case 'donut':{
            f.AddAdj('adj', 15, '25000');
            f.AddGuide('a', 10, '0', 'adj', '50000');
            f.AddGuide('dr', 0, 'ss', 'a', '100000');
            f.AddGuide('iwd2', 1, 'wd2', '0', 'dr');
            f.AddGuide('ihd2', 1, 'hd2', '0', 'dr');
            f.AddGuide('idx', 7, 'wd2', '2700000');
            f.AddGuide('idy', 12, 'hd2', '2700000');
            f.AddGuide('il', 1, 'hc', '0', 'idx');
            f.AddGuide('ir', 1, 'hc', 'idx', '0');
            f.AddGuide('it', 1, 'vc', '0', 'idy');
            f.AddGuide('ib', 1, 'vc', 'idy', '0');
            f.AddHandlePolar(undefined, '0', '0','adj','0','50000', 'dr', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('_3cd4', 'il', 'it');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'il', 'ib');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd4', 'ir', 'ib');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('_3cd4', 'ir', 'it');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(3, 'wd2', 'hd2', 'cd2', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', '_3cd4', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', '0', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', 'cd4', 'cd4');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'dr', 'vc');
            f.AddPathCommand(3, 'iwd2', 'ihd2', 'cd2', '-5400000');
            f.AddPathCommand(3, 'iwd2', 'ihd2', 'cd4', '-5400000');
            f.AddPathCommand(3, 'iwd2', 'ihd2', '0', '-5400000');
            f.AddPathCommand(3, 'iwd2', 'ihd2', '_3cd4', '-5400000');
            f.AddPathCommand(6);
            break;
        }
        case 'doubleWave':{
            f.AddAdj('adj1', 15, '6250');
            f.AddAdj('adj2', 15, '0');
            f.AddGuide('a1', 10, '0', 'adj1', '12500');
            f.AddGuide('a2', 10, '-10000', 'adj2', '10000');
            f.AddGuide('y1', 0, 'h', 'a1', '100000');
            f.AddGuide('dy2', 0, 'y1', '10', '3');
            f.AddGuide('y2', 1, 'y1', '0', 'dy2');
            f.AddGuide('y3', 1, 'y1', 'dy2', '0');
            f.AddGuide('y4', 1, 'b', '0', 'y1');
            f.AddGuide('y5', 1, 'y4', '0', 'dy2');
            f.AddGuide('y6', 1, 'y4', 'dy2', '0');
            f.AddGuide('dx1', 0, 'w', 'a2', '100000');
            f.AddGuide('of2', 0, 'w', 'a2', '50000');
            f.AddGuide('x1', 4, 'dx1');
            f.AddGuide('dx2', 3, 'of2', '0', 'of2');
            f.AddGuide('x2', 1, 'l', '0', 'dx2');
            f.AddGuide('dx8', 3, 'of2', 'of2', '0');
            f.AddGuide('x8', 1, 'r', '0', 'dx8');
            f.AddGuide('dx3', 2, 'dx2', 'x8', '6');
            f.AddGuide('x3', 1, 'x2', 'dx3', '0');
            f.AddGuide('dx4', 2, 'dx2', 'x8', '3');
            f.AddGuide('x4', 1, 'x2', 'dx4', '0');
            f.AddGuide('x5', 2, 'x2', 'x8', '2');
            f.AddGuide('x6', 1, 'x5', 'dx3', '0');
            f.AddGuide('x7', 2, 'x6', 'x8', '2');
            f.AddGuide('x9', 1, 'l', 'dx8', '0');
            f.AddGuide('x15', 1, 'r', 'dx2', '0');
            f.AddGuide('x10', 1, 'x9', 'dx3', '0');
            f.AddGuide('x11', 1, 'x9', 'dx4', '0');
            f.AddGuide('x12', 2, 'x9', 'x15', '2');
            f.AddGuide('x13', 1, 'x12', 'dx3', '0');
            f.AddGuide('x14', 2, 'x13', 'x15', '2');
            f.AddGuide('x16', 1, 'r', '0', 'x1');
            f.AddGuide('xAdj', 1, 'hc', 'dx1', '0');
            f.AddGuide('il', 8, 'x2', 'x9');
            f.AddGuide('ir', 16, 'x8', 'x15');
            f.AddGuide('it', 0, 'h', 'a1', '50000');
            f.AddGuide('ib', 1, 'b', '0', 'it');
            f.AddHandleXY(undefined, '0', '0','adj1','0','12500', 'l', 'y1');
            f.AddHandleXY('adj2','-10000','10000', undefined, '0', '0', 'xAdj', 'b');
            f.AddCnx('cd4', 'x12', 'y1');
            f.AddCnx('cd2', 'x1', 'vc');
            f.AddCnx('_3cd4', 'x5', 'y4');
            f.AddCnx('0', 'x16', 'vc');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x2', 'y1');
            f.AddPathCommand(5, 'x3', 'y2', 'x4', 'y3', 'x5', 'y1');
            f.AddPathCommand(5, 'x6', 'y2', 'x7', 'y3', 'x8', 'y1');
            f.AddPathCommand(2, 'x15', 'y4');
            f.AddPathCommand(5, 'x14', 'y6', 'x13', 'y5', 'x12', 'y4');
            f.AddPathCommand(5, 'x11', 'y6', 'x10', 'y5', 'x9', 'y4');
            f.AddPathCommand(6);
            break;
        }
        case 'downArrow':{
            f.AddAdj('adj1', 15, '50000');
            f.AddAdj('adj2', 15, '50000');
            f.AddGuide('maxAdj2', 0, '100000', 'h', 'ss');
            f.AddGuide('a1', 10, '0', 'adj1', '100000');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('dy1', 0, 'ss', 'a2', '100000');
            f.AddGuide('y1', 1, 'b', '0', 'dy1');
            f.AddGuide('dx1', 0, 'w', 'a1', '200000');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x2', 1, 'hc', 'dx1', '0');
            f.AddGuide('dy2', 0, 'x1', 'dy1', 'wd2');
            f.AddGuide('y2', 1, 'y1', 'dy2', '0');
            f.AddHandleXY('adj1','0','100000', undefined, '0', '0', 'x1', 't');
            f.AddHandleXY(undefined, '0', '0','adj2','0','maxAdj2', 'l', 'y1');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'y1');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'y1');
            f.AddRect('x1', 't', 'x2', 'y2');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y1');
            f.AddPathCommand(2, 'x1', 'y1');
            f.AddPathCommand(2, 'x1', 't');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(2, 'x2', 'y1');
            f.AddPathCommand(2, 'r', 'y1');
            f.AddPathCommand(2, 'hc', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'downArrowCallout':{
            f.AddAdj('adj1', 15, '25000');
            f.AddAdj('adj2', 15, '25000');
            f.AddAdj('adj3', 15, '25000');
            f.AddAdj('adj4', 15, '64977');
            f.AddGuide('maxAdj2', 0, '50000', 'w', 'ss');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('maxAdj1', 0, 'a2', '2', '1');
            f.AddGuide('a1', 10, '0', 'adj1', 'maxAdj1');
            f.AddGuide('maxAdj3', 0, '100000', 'h', 'ss');
            f.AddGuide('a3', 10, '0', 'adj3', 'maxAdj3');
            f.AddGuide('q2', 0, 'a3', 'ss', 'h');
            f.AddGuide('maxAdj4', 1, '100000', '0', 'q2');
            f.AddGuide('a4', 10, '0', 'adj4', 'maxAdj4');
            f.AddGuide('dx1', 0, 'ss', 'a2', '100000');
            f.AddGuide('dx2', 0, 'ss', 'a1', '200000');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x2', 1, 'hc', '0', 'dx2');
            f.AddGuide('x3', 1, 'hc', 'dx2', '0');
            f.AddGuide('x4', 1, 'hc', 'dx1', '0');
            f.AddGuide('dy3', 0, 'ss', 'a3', '100000');
            f.AddGuide('y3', 1, 'b', '0', 'dy3');
            f.AddGuide('y2', 0, 'h', 'a4', '100000');
            f.AddGuide('y1', 0, 'y2', '1', '2');
            f.AddHandleXY('adj1','0','maxAdj1', undefined, '0', '0', 'x2', 'y3');
            f.AddHandleXY('adj2','0','maxAdj2', undefined, '0', '0', 'x1', 'b');
            f.AddHandleXY(undefined, '0', '0','adj3','0','maxAdj3', 'r', 'y3');
            f.AddHandleXY(undefined, '0', '0','adj4','0','maxAdj4', 'l', 'y2');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'y1');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'y1');
            f.AddRect('l', 't', 'r', 'y2');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'y2');
            f.AddPathCommand(2, 'x3', 'y2');
            f.AddPathCommand(2, 'x3', 'y3');
            f.AddPathCommand(2, 'x4', 'y3');
            f.AddPathCommand(2, 'hc', 'b');
            f.AddPathCommand(2, 'x1', 'y3');
            f.AddPathCommand(2, 'x2', 'y3');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'l', 'y2');
            f.AddPathCommand(6);
            break;
        }
        case 'ellipse':{
            f.AddGuide('idx', 7, 'wd2', '2700000');
            f.AddGuide('idy', 12, 'hd2', '2700000');
            f.AddGuide('il', 1, 'hc', '0', 'idx');
            f.AddGuide('ir', 1, 'hc', 'idx', '0');
            f.AddGuide('it', 1, 'vc', '0', 'idy');
            f.AddGuide('ib', 1, 'vc', 'idy', '0');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('_3cd4', 'il', 'it');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'il', 'ib');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd4', 'ir', 'ib');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('_3cd4', 'ir', 'it');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(3, 'wd2', 'hd2', 'cd2', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', '_3cd4', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', '0', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', 'cd4', 'cd4');
            f.AddPathCommand(6);
            break;
        }
        case 'ellipseRibbon':{
            f.AddAdj('adj1', 15, '25000');
            f.AddAdj('adj2', 15, '50000');
            f.AddAdj('adj3', 15, '12500');
            f.AddGuide('a1', 10, '0', 'adj1', '100000');
            f.AddGuide('a2', 10, '25000', 'adj2', '75000');
            f.AddGuide('q10', 1, '100000', '0', 'a1');
            f.AddGuide('q11', 0, 'q10', '1', '2');
            f.AddGuide('q12', 1, 'a1', '0', 'q11');
            f.AddGuide('minAdj3', 8, '0', 'q12');
            f.AddGuide('a3', 10, 'minAdj3', 'adj3', 'a1');
            f.AddGuide('dx2', 0, 'w', 'a2', '200000');
            f.AddGuide('x2', 1, 'hc', '0', 'dx2');
            f.AddGuide('x3', 1, 'x2', 'wd8', '0');
            f.AddGuide('x4', 1, 'r', '0', 'x3');
            f.AddGuide('x5', 1, 'r', '0', 'x2');
            f.AddGuide('x6', 1, 'r', '0', 'wd8');
            f.AddGuide('dy1', 0, 'h', 'a3', '100000');
            f.AddGuide('f1', 0, '4', 'dy1', 'w');
            f.AddGuide('q1', 0, 'x3', 'x3', 'w');
            f.AddGuide('q2', 1, 'x3', '0', 'q1');
            f.AddGuide('y1', 0, 'f1', 'q2', '1');
            f.AddGuide('cx1', 0, 'x3', '1', '2');
            f.AddGuide('cy1', 0, 'f1', 'cx1', '1');
            f.AddGuide('cx2', 1, 'r', '0', 'cx1');
            f.AddGuide('q1', 0, 'h', 'a1', '100000');
            f.AddGuide('dy3', 1, 'q1', '0', 'dy1');
            f.AddGuide('q3', 0, 'x2', 'x2', 'w');
            f.AddGuide('q4', 1, 'x2', '0', 'q3');
            f.AddGuide('q5', 0, 'f1', 'q4', '1');
            f.AddGuide('y3', 1, 'q5', 'dy3', '0');
            f.AddGuide('q6', 1, 'dy1', 'dy3', 'y3');
            f.AddGuide('q7', 1, 'q6', 'dy1', '0');
            f.AddGuide('cy3', 1, 'q7', 'dy3', '0');
            f.AddGuide('rh', 1, 'b', '0', 'q1');
            f.AddGuide('q8', 0, 'dy1', '14', '16');
            f.AddGuide('y2', 2, 'q8', 'rh', '2');
            f.AddGuide('y5', 1, 'q5', 'rh', '0');
            f.AddGuide('y6', 1, 'y3', 'rh', '0');
            f.AddGuide('cx4', 0, 'x2', '1', '2');
            f.AddGuide('q9', 0, 'f1', 'cx4', '1');
            f.AddGuide('cy4', 1, 'q9', 'rh', '0');
            f.AddGuide('cx5', 1, 'r', '0', 'cx4');
            f.AddGuide('cy6', 1, 'cy3', 'rh', '0');
            f.AddGuide('y7', 1, 'y1', 'dy3', '0');
            f.AddGuide('cy7', 1, 'q1', 'q1', 'y7');
            f.AddGuide('y8', 1, 'b', '0', 'dy1');
            f.AddHandleXY(undefined, '0', '0','adj1','0','100000', 'hc', 'q1');
            f.AddHandleXY('adj2','25000','75000', undefined, '0', '0', 'x2', 'b');
            f.AddHandleXY(undefined, '0', '0','adj3','minAdj3','a1', 'l', 'y8');
            f.AddCnx('_3cd4', 'hc', 'q1');
            f.AddCnx('cd2', 'wd8', 'y2');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'x6', 'y2');
            f.AddRect('x2', 'q1', 'x5', 'y6');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(4, 'cx1', 'cy1', 'x3', 'y1');
            f.AddPathCommand(2, 'x2', 'y3');
            f.AddPathCommand(4, 'hc', 'cy3', 'x5', 'y3');
            f.AddPathCommand(2, 'x4', 'y1');
            f.AddPathCommand(4, 'cx2', 'cy1', 'r', 't');
            f.AddPathCommand(2, 'x6', 'y2');
            f.AddPathCommand(2, 'r', 'rh');
            f.AddPathCommand(4, 'cx5', 'cy4', 'x5', 'y5');
            f.AddPathCommand(2, 'x5', 'y6');
            f.AddPathCommand(4, 'hc', 'cy6', 'x2', 'y6');
            f.AddPathCommand(2, 'x2', 'y5');
            f.AddPathCommand(4, 'cx4', 'cy4', 'l', 'rh');
            f.AddPathCommand(2, 'wd8', 'y2');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darkenLess', false, undefined, undefined);
            f.AddPathCommand(1, 'x3', 'y7');
            f.AddPathCommand(2, 'x3', 'y1');
            f.AddPathCommand(2, 'x2', 'y3');
            f.AddPathCommand(4, 'hc', 'cy3', 'x5', 'y3');
            f.AddPathCommand(2, 'x4', 'y1');
            f.AddPathCommand(2, 'x4', 'y7');
            f.AddPathCommand(4, 'hc', 'cy7', 'x3', 'y7');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(4, 'cx1', 'cy1', 'x3', 'y1');
            f.AddPathCommand(2, 'x2', 'y3');
            f.AddPathCommand(4, 'hc', 'cy3', 'x5', 'y3');
            f.AddPathCommand(2, 'x4', 'y1');
            f.AddPathCommand(4, 'cx2', 'cy1', 'r', 't');
            f.AddPathCommand(2, 'x6', 'y2');
            f.AddPathCommand(2, 'r', 'rh');
            f.AddPathCommand(4, 'cx5', 'cy4', 'x5', 'y5');
            f.AddPathCommand(2, 'x5', 'y6');
            f.AddPathCommand(4, 'hc', 'cy6', 'x2', 'y6');
            f.AddPathCommand(2, 'x2', 'y5');
            f.AddPathCommand(4, 'cx4', 'cy4', 'l', 'rh');
            f.AddPathCommand(2, 'wd8', 'y2');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'x2', 'y5');
            f.AddPathCommand(2, 'x2', 'y3');
            f.AddPathCommand(1, 'x5', 'y3');
            f.AddPathCommand(2, 'x5', 'y5');
            f.AddPathCommand(1, 'x3', 'y1');
            f.AddPathCommand(2, 'x3', 'y7');
            f.AddPathCommand(1, 'x4', 'y7');
            f.AddPathCommand(2, 'x4', 'y1');
            break;
        }
        case 'ellipseRibbon2':{
            f.AddAdj('adj1', 15, '25000');
            f.AddAdj('adj2', 15, '50000');
            f.AddAdj('adj3', 15, '12500');
            f.AddGuide('a1', 10, '0', 'adj1', '100000');
            f.AddGuide('a2', 10, '25000', 'adj2', '75000');
            f.AddGuide('q10', 1, '100000', '0', 'a1');
            f.AddGuide('q11', 0, 'q10', '1', '2');
            f.AddGuide('q12', 1, 'a1', '0', 'q11');
            f.AddGuide('minAdj3', 8, '0', 'q12');
            f.AddGuide('a3', 10, 'minAdj3', 'adj3', 'a1');
            f.AddGuide('dx2', 0, 'w', 'a2', '200000');
            f.AddGuide('x2', 1, 'hc', '0', 'dx2');
            f.AddGuide('x3', 1, 'x2', 'wd8', '0');
            f.AddGuide('x4', 1, 'r', '0', 'x3');
            f.AddGuide('x5', 1, 'r', '0', 'x2');
            f.AddGuide('x6', 1, 'r', '0', 'wd8');
            f.AddGuide('dy1', 0, 'h', 'a3', '100000');
            f.AddGuide('f1', 0, '4', 'dy1', 'w');
            f.AddGuide('q1', 0, 'x3', 'x3', 'w');
            f.AddGuide('q2', 1, 'x3', '0', 'q1');
            f.AddGuide('u1', 0, 'f1', 'q2', '1');
            f.AddGuide('y1', 1, 'b', '0', 'u1');
            f.AddGuide('cx1', 0, 'x3', '1', '2');
            f.AddGuide('cu1', 0, 'f1', 'cx1', '1');
            f.AddGuide('cy1', 1, 'b', '0', 'cu1');
            f.AddGuide('cx2', 1, 'r', '0', 'cx1');
            f.AddGuide('q1', 0, 'h', 'a1', '100000');
            f.AddGuide('dy3', 1, 'q1', '0', 'dy1');
            f.AddGuide('q3', 0, 'x2', 'x2', 'w');
            f.AddGuide('q4', 1, 'x2', '0', 'q3');
            f.AddGuide('q5', 0, 'f1', 'q4', '1');
            f.AddGuide('u3', 1, 'q5', 'dy3', '0');
            f.AddGuide('y3', 1, 'b', '0', 'u3');
            f.AddGuide('q6', 1, 'dy1', 'dy3', 'u3');
            f.AddGuide('q7', 1, 'q6', 'dy1', '0');
            f.AddGuide('cu3', 1, 'q7', 'dy3', '0');
            f.AddGuide('cy3', 1, 'b', '0', 'cu3');
            f.AddGuide('rh', 1, 'b', '0', 'q1');
            f.AddGuide('q8', 0, 'dy1', '14', '16');
            f.AddGuide('u2', 2, 'q8', 'rh', '2');
            f.AddGuide('y2', 1, 'b', '0', 'u2');
            f.AddGuide('u5', 1, 'q5', 'rh', '0');
            f.AddGuide('y5', 1, 'b', '0', 'u5');
            f.AddGuide('u6', 1, 'u3', 'rh', '0');
            f.AddGuide('y6', 1, 'b', '0', 'u6');
            f.AddGuide('cx4', 0, 'x2', '1', '2');
            f.AddGuide('q9', 0, 'f1', 'cx4', '1');
            f.AddGuide('cu4', 1, 'q9', 'rh', '0');
            f.AddGuide('cy4', 1, 'b', '0', 'cu4');
            f.AddGuide('cx5', 1, 'r', '0', 'cx4');
            f.AddGuide('cu6', 1, 'cu3', 'rh', '0');
            f.AddGuide('cy6', 1, 'b', '0', 'cu6');
            f.AddGuide('u7', 1, 'u1', 'dy3', '0');
            f.AddGuide('y7', 1, 'b', '0', 'u7');
            f.AddGuide('cu7', 1, 'q1', 'q1', 'u7');
            f.AddGuide('cy7', 1, 'b', '0', 'cu7');
            f.AddHandleXY(undefined, '0', '0','adj1','0','100000', 'hc', 'rh');
            f.AddHandleXY('adj2','25000','100000', undefined, '0', '0', 'x2', 't');
            f.AddHandleXY(undefined, '0', '0','adj3','minAdj3','a1', 'l', 'dy1');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'wd8', 'y2');
            f.AddCnx('cd4', 'hc', 'rh');
            f.AddCnx('0', 'x6', 'y2');
            f.AddRect('x2', 'y6', 'x5', 'rh');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 'b');
            f.AddPathCommand(4, 'cx1', 'cy1', 'x3', 'y1');
            f.AddPathCommand(2, 'x2', 'y3');
            f.AddPathCommand(4, 'hc', 'cy3', 'x5', 'y3');
            f.AddPathCommand(2, 'x4', 'y1');
            f.AddPathCommand(4, 'cx2', 'cy1', 'r', 'b');
            f.AddPathCommand(2, 'x6', 'y2');
            f.AddPathCommand(2, 'r', 'q1');
            f.AddPathCommand(4, 'cx5', 'cy4', 'x5', 'y5');
            f.AddPathCommand(2, 'x5', 'y6');
            f.AddPathCommand(4, 'hc', 'cy6', 'x2', 'y6');
            f.AddPathCommand(2, 'x2', 'y5');
            f.AddPathCommand(4, 'cx4', 'cy4', 'l', 'q1');
            f.AddPathCommand(2, 'wd8', 'y2');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darkenLess', false, undefined, undefined);
            f.AddPathCommand(1, 'x3', 'y7');
            f.AddPathCommand(2, 'x3', 'y1');
            f.AddPathCommand(2, 'x2', 'y3');
            f.AddPathCommand(4, 'hc', 'cy3', 'x5', 'y3');
            f.AddPathCommand(2, 'x4', 'y1');
            f.AddPathCommand(2, 'x4', 'y7');
            f.AddPathCommand(4, 'hc', 'cy7', 'x3', 'y7');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'b');
            f.AddPathCommand(2, 'wd8', 'y2');
            f.AddPathCommand(2, 'l', 'q1');
            f.AddPathCommand(4, 'cx4', 'cy4', 'x2', 'y5');
            f.AddPathCommand(2, 'x2', 'y6');
            f.AddPathCommand(4, 'hc', 'cy6', 'x5', 'y6');
            f.AddPathCommand(2, 'x5', 'y5');
            f.AddPathCommand(4, 'cx5', 'cy4', 'r', 'q1');
            f.AddPathCommand(2, 'x6', 'y2');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(4, 'cx2', 'cy1', 'x4', 'y1');
            f.AddPathCommand(2, 'x5', 'y3');
            f.AddPathCommand(4, 'hc', 'cy3', 'x2', 'y3');
            f.AddPathCommand(2, 'x3', 'y1');
            f.AddPathCommand(4, 'cx1', 'cy1', 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'x2', 'y3');
            f.AddPathCommand(2, 'x2', 'y5');
            f.AddPathCommand(1, 'x5', 'y5');
            f.AddPathCommand(2, 'x5', 'y3');
            f.AddPathCommand(1, 'x3', 'y7');
            f.AddPathCommand(2, 'x3', 'y1');
            f.AddPathCommand(1, 'x4', 'y1');
            f.AddPathCommand(2, 'x4', 'y7');
            break;
        }
        case 'flowChartAlternateProcess':{
            f.AddGuide('x2', 1, 'r', '0', 'ssd6');
            f.AddGuide('y2', 1, 'b', '0', 'ssd6');
            f.AddGuide('il', 0, 'ssd6', '29289', '100000');
            f.AddGuide('ir', 1, 'r', '0', 'il');
            f.AddGuide('ib', 1, 'b', '0', 'il');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('il', 'il', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'ssd6');
            f.AddPathCommand(3, 'ssd6', 'ssd6', 'cd2', 'cd4');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(3, 'ssd6', 'ssd6', '_3cd4', 'cd4');
            f.AddPathCommand(2, 'r', 'y2');
            f.AddPathCommand(3, 'ssd6', 'ssd6', '0', 'cd4');
            f.AddPathCommand(2, 'ssd6', 'b');
            f.AddPathCommand(3, 'ssd6', 'ssd6', 'cd4', 'cd4');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartCollate':{
            f.AddGuide('ir', 0, 'w', '3', '4');
            f.AddGuide('ib', 0, 'h', '3', '4');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('_3cd4', 'hc', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddRect('wd4', 'hd4', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, 2, 2);
            f.AddPathCommand(1, '0', '0');
            f.AddPathCommand(2, '2', '0');
            f.AddPathCommand(2, '1', '1');
            f.AddPathCommand(2, '2', '2');
            f.AddPathCommand(2, '0', '2');
            f.AddPathCommand(2, '1', '1');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartConnector':{
            f.AddGuide('idx', 7, 'wd2', '2700000');
            f.AddGuide('idy', 12, 'hd2', '2700000');
            f.AddGuide('il', 1, 'hc', '0', 'idx');
            f.AddGuide('ir', 1, 'hc', 'idx', '0');
            f.AddGuide('it', 1, 'vc', '0', 'idy');
            f.AddGuide('ib', 1, 'vc', 'idy', '0');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('_3cd4', 'il', 'it');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'il', 'ib');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd4', 'ir', 'ib');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('_3cd4', 'ir', 'it');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(3, 'wd2', 'hd2', 'cd2', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', '_3cd4', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', '0', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', 'cd4', 'cd4');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartDecision':{
            f.AddGuide('ir', 0, 'w', '3', '4');
            f.AddGuide('ib', 0, 'h', '3', '4');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('wd4', 'hd4', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, 2, 2);
            f.AddPathCommand(1, '0', '1');
            f.AddPathCommand(2, '1', '0');
            f.AddPathCommand(2, '2', '1');
            f.AddPathCommand(2, '1', '2');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartDelay':{
            f.AddGuide('idx', 7, 'wd2', '2700000');
            f.AddGuide('idy', 12, 'hd2', '2700000');
            f.AddGuide('ir', 1, 'hc', 'idx', '0');
            f.AddGuide('it', 1, 'vc', '0', 'idy');
            f.AddGuide('ib', 1, 'vc', 'idy', '0');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('l', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'hc', 't');
            f.AddPathCommand(3, 'wd2', 'hd2', '_3cd4', 'cd2');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartDisplay':{
            f.AddGuide('x2', 0, 'w', '5', '6');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('wd6', 't', 'x2', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, 6, 6);
            f.AddPathCommand(1, '0', '3');
            f.AddPathCommand(2, '1', '0');
            f.AddPathCommand(2, '5', '0');
            f.AddPathCommand(3, '1', '3', '_3cd4', 'cd2');
            f.AddPathCommand(2, '1', '6');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartDocument':{
            f.AddGuide('y1', 0, 'h', '17322', '21600');
            f.AddGuide('y2', 0, 'h', '20172', '21600');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'y2');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('l', 't', 'r', 'y1');
            f.AddPathCommand(0,undefined, undefined, undefined, 21600, 21600);
            f.AddPathCommand(1, '0', '0');
            f.AddPathCommand(2, '21600', '0');
            f.AddPathCommand(2, '21600', '17322');
            f.AddPathCommand(5, '10800', '17322', '10800', '23922', '0', '20172');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartExtract':{
            f.AddGuide('x2', 0, 'w', '3', '4');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'wd4', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'x2', 'vc');
            f.AddRect('wd4', 'vc', 'x2', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, 2, 2);
            f.AddPathCommand(1, '0', '2');
            f.AddPathCommand(2, '1', '0');
            f.AddPathCommand(2, '2', '2');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartInputOutput':{
            f.AddGuide('x3', 0, 'w', '2', '5');
            f.AddGuide('x4', 0, 'w', '3', '5');
            f.AddGuide('x5', 0, 'w', '4', '5');
            f.AddGuide('x6', 0, 'w', '9', '10');
            f.AddCnx('_3cd4', 'x4', 't');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'wd10', 'vc');
            f.AddCnx('cd4', 'x3', 'b');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'x6', 'vc');
            f.AddRect('wd5', 't', 'x5', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, 5, 5);
            f.AddPathCommand(1, '0', '5');
            f.AddPathCommand(2, '1', '0');
            f.AddPathCommand(2, '5', '0');
            f.AddPathCommand(2, '4', '5');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartInternalStorage':{
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('wd8', 'hd8', 'r', 'b');
            f.AddPathCommand(0,false, undefined, false, 1, 1);
            f.AddPathCommand(1, '0', '0');
            f.AddPathCommand(2, '1', '0');
            f.AddPathCommand(2, '1', '1');
            f.AddPathCommand(2, '0', '1');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, 8, 8);
            f.AddPathCommand(1, '1', '0');
            f.AddPathCommand(2, '1', '8');
            f.AddPathCommand(1, '0', '1');
            f.AddPathCommand(2, '8', '1');
            f.AddPathCommand(0,undefined, 'none', undefined, 1, 1);
            f.AddPathCommand(1, '0', '0');
            f.AddPathCommand(2, '1', '0');
            f.AddPathCommand(2, '1', '1');
            f.AddPathCommand(2, '0', '1');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartMagneticDisk':{
            f.AddGuide('y3', 0, 'h', '5', '6');
            f.AddCnx('_3cd4', 'hc', 'hd3');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('l', 'hd3', 'r', 'y3');
            f.AddPathCommand(0,false, undefined, false, 6, 6);
            f.AddPathCommand(1, '0', '1');
            f.AddPathCommand(3, '3', '1', 'cd2', 'cd2');
            f.AddPathCommand(2, '6', '5');
            f.AddPathCommand(3, '3', '1', '0', 'cd2');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, 6, 6);
            f.AddPathCommand(1, '6', '1');
            f.AddPathCommand(3, '3', '1', '0', 'cd2');
            f.AddPathCommand(0,undefined, 'none', undefined, 6, 6);
            f.AddPathCommand(1, '0', '1');
            f.AddPathCommand(3, '3', '1', 'cd2', 'cd2');
            f.AddPathCommand(2, '6', '5');
            f.AddPathCommand(3, '3', '1', '0', 'cd2');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartMagneticDrum':{
            f.AddGuide('x2', 0, 'w', '2', '3');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'x2', 'vc');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('wd6', 't', 'x2', 'b');
            f.AddPathCommand(0,false, undefined, false, 6, 6);
            f.AddPathCommand(1, '1', '0');
            f.AddPathCommand(2, '5', '0');
            f.AddPathCommand(3, '1', '3', '_3cd4', 'cd2');
            f.AddPathCommand(2, '1', '6');
            f.AddPathCommand(3, '1', '3', 'cd4', 'cd2');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, 6, 6);
            f.AddPathCommand(1, '5', '6');
            f.AddPathCommand(3, '1', '3', 'cd4', 'cd2');
            f.AddPathCommand(0,undefined, 'none', undefined, 6, 6);
            f.AddPathCommand(1, '1', '0');
            f.AddPathCommand(2, '5', '0');
            f.AddPathCommand(3, '1', '3', '_3cd4', 'cd2');
            f.AddPathCommand(2, '1', '6');
            f.AddPathCommand(3, '1', '3', 'cd4', 'cd2');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartMagneticTape':{
            f.AddGuide('idx', 7, 'wd2', '2700000');
            f.AddGuide('idy', 12, 'hd2', '2700000');
            f.AddGuide('il', 1, 'hc', '0', 'idx');
            f.AddGuide('ir', 1, 'hc', 'idx', '0');
            f.AddGuide('it', 1, 'vc', '0', 'idy');
            f.AddGuide('ib', 1, 'vc', 'idy', '0');
            f.AddGuide('ang1', 5, '1', '1');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'hc', 'b');
            f.AddPathCommand(3, 'wd2', 'hd2', 'cd4', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', 'cd2', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', '_3cd4', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', '0', 'ang1');
            f.AddPathCommand(2, 'r', 'ib');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartManualInput':{
            f.AddCnx('_3cd4', 'hc', 'hd10');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('l', 'hd5', 'r', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, 5, 5);
            f.AddPathCommand(1, '0', '1');
            f.AddPathCommand(2, '5', '0');
            f.AddPathCommand(2, '5', '5');
            f.AddPathCommand(2, '0', '5');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartManualOperation':{
            f.AddGuide('x3', 0, 'w', '4', '5');
            f.AddGuide('x4', 0, 'w', '9', '10');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'wd10', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'x4', 'vc');
            f.AddRect('wd5', 't', 'x3', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, 5, 5);
            f.AddPathCommand(1, '0', '0');
            f.AddPathCommand(2, '5', '0');
            f.AddPathCommand(2, '4', '5');
            f.AddPathCommand(2, '1', '5');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartMerge':{
            f.AddGuide('x2', 0, 'w', '3', '4');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'wd4', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'x2', 'vc');
            f.AddRect('wd4', 't', 'x2', 'vc');
            f.AddPathCommand(0,undefined, undefined, undefined, 2, 2);
            f.AddPathCommand(1, '0', '0');
            f.AddPathCommand(2, '2', '0');
            f.AddPathCommand(2, '1', '2');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartMultidocument':{
            f.AddGuide('y2', 0, 'h', '3675', '21600');
            f.AddGuide('y8', 0, 'h', '20782', '21600');
            f.AddGuide('x3', 0, 'w', '9298', '21600');
            f.AddGuide('x4', 0, 'w', '12286', '21600');
            f.AddGuide('x5', 0, 'w', '18595', '21600');
            f.AddCnx('_3cd4', 'x4', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'x3', 'y8');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('l', 'y2', 'x5', 'y8');
            f.AddPathCommand(0,false, undefined, false, 21600, 21600);
            f.AddPathCommand(1, '0', '20782');
            f.AddPathCommand(5, '9298', '23542', '9298', '18022', '18595', '18022');
            f.AddPathCommand(2, '18595', '3675');
            f.AddPathCommand(2, '0', '3675');
            f.AddPathCommand(6);
            f.AddPathCommand(1, '1532', '3675');
            f.AddPathCommand(2, '1532', '1815');
            f.AddPathCommand(2, '20000', '1815');
            f.AddPathCommand(2, '20000', '16252');
            f.AddPathCommand(5, '19298', '16252', '18595', '16352', '18595', '16352');
            f.AddPathCommand(2, '18595', '3675');
            f.AddPathCommand(6);
            f.AddPathCommand(1, '2972', '1815');
            f.AddPathCommand(2, '2972', '0');
            f.AddPathCommand(2, '21600', '0');
            f.AddPathCommand(2, '21600', '14392');
            f.AddPathCommand(5, '20800', '14392', '20000', '14467', '20000', '14467');
            f.AddPathCommand(2, '20000', '1815');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, 21600, 21600);
            f.AddPathCommand(1, '0', '3675');
            f.AddPathCommand(2, '18595', '3675');
            f.AddPathCommand(2, '18595', '18022');
            f.AddPathCommand(5, '9298', '18022', '9298', '23542', '0', '20782');
            f.AddPathCommand(6);
            f.AddPathCommand(1, '1532', '3675');
            f.AddPathCommand(2, '1532', '1815');
            f.AddPathCommand(2, '20000', '1815');
            f.AddPathCommand(2, '20000', '16252');
            f.AddPathCommand(5, '19298', '16252', '18595', '16352', '18595', '16352');
            f.AddPathCommand(1, '2972', '1815');
            f.AddPathCommand(2, '2972', '0');
            f.AddPathCommand(2, '21600', '0');
            f.AddPathCommand(2, '21600', '14392');
            f.AddPathCommand(5, '20800', '14392', '20000', '14467', '20000', '14467');
            f.AddPathCommand(0,undefined, 'none', false, 21600, 21600);
            f.AddPathCommand(1, '0', '20782');
            f.AddPathCommand(5, '9298', '23542', '9298', '18022', '18595', '18022');
            f.AddPathCommand(2, '18595', '16352');
            f.AddPathCommand(5, '18595', '16352', '19298', '16252', '20000', '16252');
            f.AddPathCommand(2, '20000', '14467');
            f.AddPathCommand(5, '20000', '14467', '20800', '14392', '21600', '14392');
            f.AddPathCommand(2, '21600', '0');
            f.AddPathCommand(2, '2972', '0');
            f.AddPathCommand(2, '2972', '1815');
            f.AddPathCommand(2, '1532', '1815');
            f.AddPathCommand(2, '1532', '3675');
            f.AddPathCommand(2, '0', '3675');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartOfflineStorage':{
            f.AddGuide('x4', 0, 'w', '3', '4');
            f.AddCnx('0', 'x4', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'wd4', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('wd4', 't', 'x4', 'vc');
            f.AddPathCommand(0,false, undefined, false, 2, 2);
            f.AddPathCommand(1, '0', '0');
            f.AddPathCommand(2, '2', '0');
            f.AddPathCommand(2, '1', '2');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, 5, 5);
            f.AddPathCommand(1, '2', '4');
            f.AddPathCommand(2, '3', '4');
            f.AddPathCommand(0,true, 'none', undefined, 2, 2);
            f.AddPathCommand(1, '0', '0');
            f.AddPathCommand(2, '2', '0');
            f.AddPathCommand(2, '1', '2');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartOffpageConnector':{
            f.AddGuide('y1', 0, 'h', '4', '5');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('l', 't', 'r', 'y1');
            f.AddPathCommand(0,undefined, undefined, undefined, 10, 10);
            f.AddPathCommand(1, '0', '0');
            f.AddPathCommand(2, '10', '0');
            f.AddPathCommand(2, '10', '8');
            f.AddPathCommand(2, '5', '10');
            f.AddPathCommand(2, '0', '8');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartOnlineStorage':{
            f.AddGuide('x2', 0, 'w', '5', '6');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'x2', 'vc');
            f.AddRect('wd6', 't', 'x2', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, 6, 6);
            f.AddPathCommand(1, '1', '0');
            f.AddPathCommand(2, '6', '0');
            f.AddPathCommand(3, '1', '3', '_3cd4', '-10800000');
            f.AddPathCommand(2, '1', '6');
            f.AddPathCommand(3, '1', '3', 'cd4', 'cd2');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartOr':{
            f.AddGuide('idx', 7, 'wd2', '2700000');
            f.AddGuide('idy', 12, 'hd2', '2700000');
            f.AddGuide('il', 1, 'hc', '0', 'idx');
            f.AddGuide('ir', 1, 'hc', 'idx', '0');
            f.AddGuide('it', 1, 'vc', '0', 'idy');
            f.AddGuide('ib', 1, 'vc', 'idy', '0');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('_3cd4', 'il', 'it');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'il', 'ib');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd4', 'ir', 'ib');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('_3cd4', 'ir', 'it');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(3, 'wd2', 'hd2', 'cd2', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', '_3cd4', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', '0', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', 'cd4', 'cd4');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'hc', 't');
            f.AddPathCommand(2, 'hc', 'b');
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(2, 'r', 'vc');
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(3, 'wd2', 'hd2', 'cd2', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', '_3cd4', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', '0', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', 'cd4', 'cd4');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartPredefinedProcess':{
            f.AddGuide('x2', 0, 'w', '7', '8');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('wd8', 't', 'x2', 'b');
            f.AddPathCommand(0,false, undefined, false, 1, 1);
            f.AddPathCommand(1, '0', '0');
            f.AddPathCommand(2, '1', '0');
            f.AddPathCommand(2, '1', '1');
            f.AddPathCommand(2, '0', '1');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, 8, 8);
            f.AddPathCommand(1, '1', '0');
            f.AddPathCommand(2, '1', '8');
            f.AddPathCommand(1, '7', '0');
            f.AddPathCommand(2, '7', '8');
            f.AddPathCommand(0,undefined, 'none', undefined, 1, 1);
            f.AddPathCommand(1, '0', '0');
            f.AddPathCommand(2, '1', '0');
            f.AddPathCommand(2, '1', '1');
            f.AddPathCommand(2, '0', '1');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartPreparation':{
            f.AddGuide('x2', 0, 'w', '4', '5');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('wd5', 't', 'x2', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, 10, 10);
            f.AddPathCommand(1, '0', '5');
            f.AddPathCommand(2, '2', '0');
            f.AddPathCommand(2, '8', '0');
            f.AddPathCommand(2, '10', '5');
            f.AddPathCommand(2, '8', '10');
            f.AddPathCommand(2, '2', '10');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartProcess':{
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, 1, 1);
            f.AddPathCommand(1, '0', '0');
            f.AddPathCommand(2, '1', '0');
            f.AddPathCommand(2, '1', '1');
            f.AddPathCommand(2, '0', '1');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartPunchedCard':{
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('l', 'hd5', 'r', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, 5, 5);
            f.AddPathCommand(1, '0', '1');
            f.AddPathCommand(2, '1', '0');
            f.AddPathCommand(2, '5', '0');
            f.AddPathCommand(2, '5', '5');
            f.AddPathCommand(2, '0', '5');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartPunchedTape':{
            f.AddGuide('y2', 0, 'h', '9', '10');
            f.AddGuide('ib', 0, 'h', '4', '5');
            f.AddCnx('_3cd4', 'hc', 'hd10');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'y2');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('l', 'hd5', 'r', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, 20, 20);
            f.AddPathCommand(1, '0', '2');
            f.AddPathCommand(3, '5', '2', 'cd2', '-10800000');
            f.AddPathCommand(3, '5', '2', 'cd2', 'cd2');
            f.AddPathCommand(2, '20', '18');
            f.AddPathCommand(3, '5', '2', '0', '-10800000');
            f.AddPathCommand(3, '5', '2', '0', 'cd2');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartSort':{
            f.AddGuide('ir', 0, 'w', '3', '4');
            f.AddGuide('ib', 0, 'h', '3', '4');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('wd4', 'hd4', 'ir', 'ib');
            f.AddPathCommand(0,false, undefined, false, 2, 2);
            f.AddPathCommand(1, '0', '1');
            f.AddPathCommand(2, '1', '0');
            f.AddPathCommand(2, '2', '1');
            f.AddPathCommand(2, '1', '2');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, 2, 2);
            f.AddPathCommand(1, '0', '1');
            f.AddPathCommand(2, '2', '1');
            f.AddPathCommand(0,undefined, 'none', undefined, 2, 2);
            f.AddPathCommand(1, '0', '1');
            f.AddPathCommand(2, '1', '0');
            f.AddPathCommand(2, '2', '1');
            f.AddPathCommand(2, '1', '2');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartSummingJunction':{
            f.AddGuide('idx', 7, 'wd2', '2700000');
            f.AddGuide('idy', 12, 'hd2', '2700000');
            f.AddGuide('il', 1, 'hc', '0', 'idx');
            f.AddGuide('ir', 1, 'hc', 'idx', '0');
            f.AddGuide('it', 1, 'vc', '0', 'idy');
            f.AddGuide('ib', 1, 'vc', 'idy', '0');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('_3cd4', 'il', 'it');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'il', 'ib');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd4', 'ir', 'ib');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('_3cd4', 'ir', 'it');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(3, 'wd2', 'hd2', 'cd2', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', '_3cd4', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', '0', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', 'cd4', 'cd4');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'il', 'it');
            f.AddPathCommand(2, 'ir', 'ib');
            f.AddPathCommand(1, 'ir', 'it');
            f.AddPathCommand(2, 'il', 'ib');
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(3, 'wd2', 'hd2', 'cd2', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', '_3cd4', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', '0', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', 'cd4', 'cd4');
            f.AddPathCommand(6);
            break;
        }
        case 'flowChartTerminator':{
            f.AddGuide('il', 0, 'w', '1018', '21600');
            f.AddGuide('ir', 0, 'w', '20582', '21600');
            f.AddGuide('it', 0, 'h', '3163', '21600');
            f.AddGuide('ib', 0, 'h', '18437', '21600');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, 21600, 21600);
            f.AddPathCommand(1, '3475', '0');
            f.AddPathCommand(2, '18125', '0');
            f.AddPathCommand(3, '3475', '10800', '_3cd4', 'cd2');
            f.AddPathCommand(2, '3475', '21600');
            f.AddPathCommand(3, '3475', '10800', 'cd4', 'cd2');
            f.AddPathCommand(6);
            break;
        }
        case 'foldedCorner':{
            f.AddAdj('adj', 15, '16667');
            f.AddGuide('a', 10, '0', 'adj', '50000');
            f.AddGuide('dy2', 0, 'ss', 'a', '100000');
            f.AddGuide('dy1', 0, 'dy2', '1', '5');
            f.AddGuide('x1', 1, 'r', '0', 'dy2');
            f.AddGuide('x2', 1, 'x1', 'dy1', '0');
            f.AddGuide('y2', 1, 'b', '0', 'dy2');
            f.AddGuide('y1', 1, 'y2', 'dy1', '0');
            f.AddHandleXY('adj','0','50000', undefined, '0', '0', 'x1', 'b');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('l', 't', 'r', 'y2');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'y2');
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darkenLess', false, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'b');
            f.AddPathCommand(2, 'x2', 'y1');
            f.AddPathCommand(2, 'r', 'y2');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'b');
            f.AddPathCommand(2, 'x2', 'y1');
            f.AddPathCommand(2, 'r', 'y2');
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(2, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'y2');
            break;
        }
        case 'frame':{
            f.AddAdj('adj1', 15, '12500');
            f.AddGuide('a1', 10, '0', 'adj1', '50000');
            f.AddGuide('x1', 0, 'ss', 'a1', '100000');
            f.AddGuide('x4', 1, 'r', '0', 'x1');
            f.AddGuide('y4', 1, 'b', '0', 'x1');
            f.AddHandleXY('adj1','0','50000', undefined, '0', '0', 'x1', 't');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('x1', 'x1', 'x4', 'y4');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'x1', 'x1');
            f.AddPathCommand(2, 'x1', 'y4');
            f.AddPathCommand(2, 'x4', 'y4');
            f.AddPathCommand(2, 'x4', 'x1');
            f.AddPathCommand(6);
            break;
        }
        case 'funnel':{
            f.AddGuide('d', 0, 'ss', '1', '20');
            f.AddGuide('rw2', 1, 'wd2', '0', 'd');
            f.AddGuide('rh2', 1, 'hd4', '0', 'd');
            f.AddGuide('t1', 7, 'wd2', '480000');
            f.AddGuide('t2', 12, 'hd4', '480000');
            f.AddGuide('da', 5, 't1', 't2');
            f.AddGuide('2da', 0, 'da', '2', '1');
            f.AddGuide('stAng1', 1, 'cd2', '0', 'da');
            f.AddGuide('swAng1', 1, 'cd2', '2da', '0');
            f.AddGuide('swAng3', 1, 'cd2', '0', '2da');
            f.AddGuide('rw3', 0, 'wd2', '1', '4');
            f.AddGuide('rh3', 0, 'hd4', '1', '4');
            f.AddGuide('ct1', 7, 'hd4', 'stAng1');
            f.AddGuide('st1', 12, 'wd2', 'stAng1');
            f.AddGuide('m1', 9, 'ct1', 'st1', '0');
            f.AddGuide('n1', 0, 'wd2', 'hd4', 'm1');
            f.AddGuide('dx1', 7, 'n1', 'stAng1');
            f.AddGuide('dy1', 12, 'n1', 'stAng1');
            f.AddGuide('x1', 1, 'hc', 'dx1', '0');
            f.AddGuide('y1', 1, 'hd4', 'dy1', '0');
            f.AddGuide('ct3', 7, 'rh3', 'da');
            f.AddGuide('st3', 12, 'rw3', 'da');
            f.AddGuide('m3', 9, 'ct3', 'st3', '0');
            f.AddGuide('n3', 0, 'rw3', 'rh3', 'm3');
            f.AddGuide('dx3', 7, 'n3', 'da');
            f.AddGuide('dy3', 12, 'n3', 'da');
            f.AddGuide('x3', 1, 'hc', 'dx3', '0');
            f.AddGuide('vc3', 1, 'b', '0', 'rh3');
            f.AddGuide('y2', 1, 'vc3', 'dy3', '0');
            f.AddGuide('x2', 1, 'wd2', '0', 'rw2');
            f.AddGuide('cd', 0, 'cd2', '2', '1');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(3, 'wd2', 'hd4', 'stAng1', 'swAng1');
            f.AddPathCommand(2, 'x3', 'y2');
            f.AddPathCommand(3, 'rw3', 'rh3', 'da', 'swAng3');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'x2', 'hd4');
            f.AddPathCommand(3, 'rw2', 'rh2', 'cd2', '-21600000');
            f.AddPathCommand(6);
            break;
        }
        case 'gear6':{
            f.AddAdj('adj1', 15, '15000');
            f.AddAdj('adj2', 15, '3526');
            f.AddGuide('a1', 10, '0', 'adj1', '20000');
            f.AddGuide('a2', 10, '0', 'adj2', '5358');
            f.AddGuide('th', 0, 'ss', 'a1', '100000');
            f.AddGuide('lFD', 0, 'ss', 'a2', '100000');
            f.AddGuide('th2', 0, 'th', '1', '2');
            f.AddGuide('l2', 0, 'lFD', '1', '2');
            f.AddGuide('l3', 1, 'th2', 'l2', '0');
            f.AddGuide('rh', 1, 'hd2', '0', 'th');
            f.AddGuide('rw', 1, 'wd2', '0', 'th');
            f.AddGuide('dr', 1, 'rw', '0', 'rh');
            f.AddGuide('maxr', 3, 'dr', 'rh', 'rw');
            f.AddGuide('ha', 5, 'maxr', 'l3');
            f.AddGuide('aA1', 1, '19800000', '0', 'ha');
            f.AddGuide('aD1', 1, '19800000', 'ha', '0');
            f.AddGuide('ta11', 7, 'rw', 'aA1');
            f.AddGuide('ta12', 12, 'rh', 'aA1');
            f.AddGuide('bA1', 5, 'ta11', 'ta12');
            f.AddGuide('cta1', 7, 'rh', 'bA1');
            f.AddGuide('sta1', 12, 'rw', 'bA1');
            f.AddGuide('ma1', 9, 'cta1', 'sta1', '0');
            f.AddGuide('na1', 0, 'rw', 'rh', 'ma1');
            f.AddGuide('dxa1', 7, 'na1', 'bA1');
            f.AddGuide('dya1', 12, 'na1', 'bA1');
            f.AddGuide('xA1', 1, 'hc', 'dxa1', '0');
            f.AddGuide('yA1', 1, 'vc', 'dya1', '0');
            f.AddGuide('td11', 7, 'rw', 'aD1');
            f.AddGuide('td12', 12, 'rh', 'aD1');
            f.AddGuide('bD1', 5, 'td11', 'td12');
            f.AddGuide('ctd1', 7, 'rh', 'bD1');
            f.AddGuide('std1', 12, 'rw', 'bD1');
            f.AddGuide('md1', 9, 'ctd1', 'std1', '0');
            f.AddGuide('nd1', 0, 'rw', 'rh', 'md1');
            f.AddGuide('dxd1', 7, 'nd1', 'bD1');
            f.AddGuide('dyd1', 12, 'nd1', 'bD1');
            f.AddGuide('xD1', 1, 'hc', 'dxd1', '0');
            f.AddGuide('yD1', 1, 'vc', 'dyd1', '0');
            f.AddGuide('xAD1', 1, 'xA1', '0', 'xD1');
            f.AddGuide('yAD1', 1, 'yA1', '0', 'yD1');
            f.AddGuide('lAD1', 9, 'xAD1', 'yAD1', '0');
            f.AddGuide('a1', 5, 'yAD1', 'xAD1');
            f.AddGuide('dxF1', 12, 'lFD', 'a1');
            f.AddGuide('dyF1', 7, 'lFD', 'a1');
            f.AddGuide('xF1', 1, 'xD1', 'dxF1', '0');
            f.AddGuide('yF1', 1, 'yD1', 'dyF1', '0');
            f.AddGuide('xE1', 1, 'xA1', '0', 'dxF1');
            f.AddGuide('yE1', 1, 'yA1', '0', 'dyF1');
            f.AddGuide('yC1t', 12, 'th', 'a1');
            f.AddGuide('xC1t', 7, 'th', 'a1');
            f.AddGuide('yC1', 1, 'yF1', 'yC1t', '0');
            f.AddGuide('xC1', 1, 'xF1', '0', 'xC1t');
            f.AddGuide('yB1', 1, 'yE1', 'yC1t', '0');
            f.AddGuide('xB1', 1, 'xE1', '0', 'xC1t');
            f.AddGuide('aD6', 1, '_3cd4', 'ha', '0');
            f.AddGuide('td61', 7, 'rw', 'aD6');
            f.AddGuide('td62', 12, 'rh', 'aD6');
            f.AddGuide('bD6', 5, 'td61', 'td62');
            f.AddGuide('ctd6', 7, 'rh', 'bD6');
            f.AddGuide('std6', 12, 'rw', 'bD6');
            f.AddGuide('md6', 9, 'ctd6', 'std6', '0');
            f.AddGuide('nd6', 0, 'rw', 'rh', 'md6');
            f.AddGuide('dxd6', 7, 'nd6', 'bD6');
            f.AddGuide('dyd6', 12, 'nd6', 'bD6');
            f.AddGuide('xD6', 1, 'hc', 'dxd6', '0');
            f.AddGuide('yD6', 1, 'vc', 'dyd6', '0');
            f.AddGuide('xA6', 1, 'hc', '0', 'dxd6');
            f.AddGuide('xF6', 1, 'xD6', '0', 'lFD');
            f.AddGuide('xE6', 1, 'xA6', 'lFD', '0');
            f.AddGuide('yC6', 1, 'yD6', '0', 'th');
            f.AddGuide('swAng1', 1, 'bA1', '0', 'bD6');
            f.AddGuide('aA2', 1, '1800000', '0', 'ha');
            f.AddGuide('aD2', 1, '1800000', 'ha', '0');
            f.AddGuide('ta21', 7, 'rw', 'aA2');
            f.AddGuide('ta22', 12, 'rh', 'aA2');
            f.AddGuide('bA2', 5, 'ta21', 'ta22');
            f.AddGuide('yA2', 1, 'h', '0', 'yD1');
            f.AddGuide('td21', 7, 'rw', 'aD2');
            f.AddGuide('td22', 12, 'rh', 'aD2');
            f.AddGuide('bD2', 5, 'td21', 'td22');
            f.AddGuide('yD2', 1, 'h', '0', 'yA1');
            f.AddGuide('yC2', 1, 'h', '0', 'yB1');
            f.AddGuide('yB2', 1, 'h', '0', 'yC1');
            f.AddGuide('xB2', 15, 'xC1');
            f.AddGuide('swAng2', 1, 'bA2', '0', 'bD1');
            f.AddGuide('aD3', 1, 'cd4', 'ha', '0');
            f.AddGuide('td31', 7, 'rw', 'aD3');
            f.AddGuide('td32', 12, 'rh', 'aD3');
            f.AddGuide('bD3', 5, 'td31', 'td32');
            f.AddGuide('yD3', 1, 'h', '0', 'yD6');
            f.AddGuide('yB3', 1, 'h', '0', 'yC6');
            f.AddGuide('aD4', 1, '9000000', 'ha', '0');
            f.AddGuide('td41', 7, 'rw', 'aD4');
            f.AddGuide('td42', 12, 'rh', 'aD4');
            f.AddGuide('bD4', 5, 'td41', 'td42');
            f.AddGuide('xD4', 1, 'w', '0', 'xD1');
            f.AddGuide('xC4', 1, 'w', '0', 'xC1');
            f.AddGuide('xB4', 1, 'w', '0', 'xB1');
            f.AddGuide('aD5', 1, '12600000', 'ha', '0');
            f.AddGuide('td51', 7, 'rw', 'aD5');
            f.AddGuide('td52', 12, 'rh', 'aD5');
            f.AddGuide('bD5', 5, 'td51', 'td52');
            f.AddGuide('xD5', 1, 'w', '0', 'xA1');
            f.AddGuide('xC5', 1, 'w', '0', 'xB1');
            f.AddGuide('xB5', 1, 'w', '0', 'xC1');
            f.AddGuide('xCxn1', 2, 'xB1', 'xC1', '2');
            f.AddGuide('yCxn1', 2, 'yB1', 'yC1', '2');
            f.AddGuide('yCxn2', 1, 'b', '0', 'yCxn1');
            f.AddGuide('xCxn4', 2, 'r', '0', 'xCxn1');
            f.AddHandleXY(undefined, '0', '0','adj1','0','20000', 'xD6', 'yD6');
            f.AddHandleXY('adj2','0','5358', undefined, '0', '0', 'xA6', 'yD6');
            f.AddCnx('19800000', 'xCxn1', 'yCxn1');
            f.AddCnx('1800000', 'xCxn1', 'yCxn2');
            f.AddCnx('cd4', 'hc', 'yB3');
            f.AddCnx('9000000', 'xCxn4', 'yCxn2');
            f.AddCnx('12600000', 'xCxn4', 'yCxn1');
            f.AddCnx('_3cd4', 'hc', 'yC6');
            f.AddRect('xD5', 'yA1', 'xA1', 'yD2');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'xA1', 'yA1');
            f.AddPathCommand(2, 'xB1', 'yB1');
            f.AddPathCommand(2, 'xC1', 'yC1');
            f.AddPathCommand(2, 'xD1', 'yD1');
            f.AddPathCommand(3, 'rh', 'rw', 'bD1', 'swAng2');
            f.AddPathCommand(2, 'xC1', 'yB2');
            f.AddPathCommand(2, 'xB1', 'yC2');
            f.AddPathCommand(2, 'xA1', 'yD2');
            f.AddPathCommand(3, 'rh', 'rw', 'bD2', 'swAng1');
            f.AddPathCommand(2, 'xF6', 'yB3');
            f.AddPathCommand(2, 'xE6', 'yB3');
            f.AddPathCommand(2, 'xA6', 'yD3');
            f.AddPathCommand(3, 'rh', 'rw', 'bD3', 'swAng1');
            f.AddPathCommand(2, 'xB4', 'yC2');
            f.AddPathCommand(2, 'xC4', 'yB2');
            f.AddPathCommand(2, 'xD4', 'yA2');
            f.AddPathCommand(3, 'rh', 'rw', 'bD4', 'swAng2');
            f.AddPathCommand(2, 'xB5', 'yC1');
            f.AddPathCommand(2, 'xC5', 'yB1');
            f.AddPathCommand(2, 'xD5', 'yA1');
            f.AddPathCommand(3, 'rh', 'rw', 'bD5', 'swAng1');
            f.AddPathCommand(2, 'xE6', 'yC6');
            f.AddPathCommand(2, 'xF6', 'yC6');
            f.AddPathCommand(2, 'xD6', 'yD6');
            f.AddPathCommand(3, 'rh', 'rw', 'bD6', 'swAng1');
            f.AddPathCommand(6);
            break;
        }
        case 'gear9':{
            f.AddAdj('adj1', 15, '10000');
            f.AddAdj('adj2', 15, '1763');
            f.AddGuide('a1', 10, '0', 'adj1', '20000');
            f.AddGuide('a2', 10, '0', 'adj2', '2679');
            f.AddGuide('th', 0, 'ss', 'a1', '100000');
            f.AddGuide('lFD', 0, 'ss', 'a2', '100000');
            f.AddGuide('th2', 0, 'th', '1', '2');
            f.AddGuide('l2', 0, 'lFD', '1', '2');
            f.AddGuide('l3', 1, 'th2', 'l2', '0');
            f.AddGuide('rh', 1, 'hd2', '0', 'th');
            f.AddGuide('rw', 1, 'wd2', '0', 'th');
            f.AddGuide('dr', 1, 'rw', '0', 'rh');
            f.AddGuide('maxr', 3, 'dr', 'rh', 'rw');
            f.AddGuide('ha', 5, 'maxr', 'l3');
            f.AddGuide('aA1', 1, '18600000', '0', 'ha');
            f.AddGuide('aD1', 1, '18600000', 'ha', '0');
            f.AddGuide('ta11', 7, 'rw', 'aA1');
            f.AddGuide('ta12', 12, 'rh', 'aA1');
            f.AddGuide('bA1', 5, 'ta11', 'ta12');
            f.AddGuide('cta1', 7, 'rh', 'bA1');
            f.AddGuide('sta1', 12, 'rw', 'bA1');
            f.AddGuide('ma1', 9, 'cta1', 'sta1', '0');
            f.AddGuide('na1', 0, 'rw', 'rh', 'ma1');
            f.AddGuide('dxa1', 7, 'na1', 'bA1');
            f.AddGuide('dya1', 12, 'na1', 'bA1');
            f.AddGuide('xA1', 1, 'hc', 'dxa1', '0');
            f.AddGuide('yA1', 1, 'vc', 'dya1', '0');
            f.AddGuide('td11', 7, 'rw', 'aD1');
            f.AddGuide('td12', 12, 'rh', 'aD1');
            f.AddGuide('bD1', 5, 'td11', 'td12');
            f.AddGuide('ctd1', 7, 'rh', 'bD1');
            f.AddGuide('std1', 12, 'rw', 'bD1');
            f.AddGuide('md1', 9, 'ctd1', 'std1', '0');
            f.AddGuide('nd1', 0, 'rw', 'rh', 'md1');
            f.AddGuide('dxd1', 7, 'nd1', 'bD1');
            f.AddGuide('dyd1', 12, 'nd1', 'bD1');
            f.AddGuide('xD1', 1, 'hc', 'dxd1', '0');
            f.AddGuide('yD1', 1, 'vc', 'dyd1', '0');
            f.AddGuide('xAD1', 1, 'xA1', '0', 'xD1');
            f.AddGuide('yAD1', 1, 'yA1', '0', 'yD1');
            f.AddGuide('lAD1', 9, 'xAD1', 'yAD1', '0');
            f.AddGuide('a1', 5, 'yAD1', 'xAD1');
            f.AddGuide('dxF1', 12, 'lFD', 'a1');
            f.AddGuide('dyF1', 7, 'lFD', 'a1');
            f.AddGuide('xF1', 1, 'xD1', 'dxF1', '0');
            f.AddGuide('yF1', 1, 'yD1', 'dyF1', '0');
            f.AddGuide('xE1', 1, 'xA1', '0', 'dxF1');
            f.AddGuide('yE1', 1, 'yA1', '0', 'dyF1');
            f.AddGuide('yC1t', 12, 'th', 'a1');
            f.AddGuide('xC1t', 7, 'th', 'a1');
            f.AddGuide('yC1', 1, 'yF1', 'yC1t', '0');
            f.AddGuide('xC1', 1, 'xF1', '0', 'xC1t');
            f.AddGuide('yB1', 1, 'yE1', 'yC1t', '0');
            f.AddGuide('xB1', 1, 'xE1', '0', 'xC1t');
            f.AddGuide('aA2', 1, '21000000', '0', 'ha');
            f.AddGuide('aD2', 1, '21000000', 'ha', '0');
            f.AddGuide('ta21', 7, 'rw', 'aA2');
            f.AddGuide('ta22', 12, 'rh', 'aA2');
            f.AddGuide('bA2', 5, 'ta21', 'ta22');
            f.AddGuide('cta2', 7, 'rh', 'bA2');
            f.AddGuide('sta2', 12, 'rw', 'bA2');
            f.AddGuide('ma2', 9, 'cta2', 'sta2', '0');
            f.AddGuide('na2', 0, 'rw', 'rh', 'ma2');
            f.AddGuide('dxa2', 7, 'na2', 'bA2');
            f.AddGuide('dya2', 12, 'na2', 'bA2');
            f.AddGuide('xA2', 1, 'hc', 'dxa2', '0');
            f.AddGuide('yA2', 1, 'vc', 'dya2', '0');
            f.AddGuide('td21', 7, 'rw', 'aD2');
            f.AddGuide('td22', 12, 'rh', 'aD2');
            f.AddGuide('bD2', 5, 'td21', 'td22');
            f.AddGuide('ctd2', 7, 'rh', 'bD2');
            f.AddGuide('std2', 12, 'rw', 'bD2');
            f.AddGuide('md2', 9, 'ctd2', 'std2', '0');
            f.AddGuide('nd2', 0, 'rw', 'rh', 'md2');
            f.AddGuide('dxd2', 7, 'nd2', 'bD2');
            f.AddGuide('dyd2', 12, 'nd2', 'bD2');
            f.AddGuide('xD2', 1, 'hc', 'dxd2', '0');
            f.AddGuide('yD2', 1, 'vc', 'dyd2', '0');
            f.AddGuide('xAD2', 1, 'xA2', '0', 'xD2');
            f.AddGuide('yAD2', 1, 'yA2', '0', 'yD2');
            f.AddGuide('lAD2', 9, 'xAD2', 'yAD2', '0');
            f.AddGuide('a2', 5, 'yAD2', 'xAD2');
            f.AddGuide('dxF2', 12, 'lFD', 'a2');
            f.AddGuide('dyF2', 7, 'lFD', 'a2');
            f.AddGuide('xF2', 1, 'xD2', 'dxF2', '0');
            f.AddGuide('yF2', 1, 'yD2', 'dyF2', '0');
            f.AddGuide('xE2', 1, 'xA2', '0', 'dxF2');
            f.AddGuide('yE2', 1, 'yA2', '0', 'dyF2');
            f.AddGuide('yC2t', 12, 'th', 'a2');
            f.AddGuide('xC2t', 7, 'th', 'a2');
            f.AddGuide('yC2', 1, 'yF2', 'yC2t', '0');
            f.AddGuide('xC2', 1, 'xF2', '0', 'xC2t');
            f.AddGuide('yB2', 1, 'yE2', 'yC2t', '0');
            f.AddGuide('xB2', 1, 'xE2', '0', 'xC2t');
            f.AddGuide('swAng1', 1, 'bA2', '0', 'bD1');
            f.AddGuide('aA3', 1, '1800000', '0', 'ha');
            f.AddGuide('aD3', 1, '1800000', 'ha', '0');
            f.AddGuide('ta31', 7, 'rw', 'aA3');
            f.AddGuide('ta32', 12, 'rh', 'aA3');
            f.AddGuide('bA3', 5, 'ta31', 'ta32');
            f.AddGuide('cta3', 7, 'rh', 'bA3');
            f.AddGuide('sta3', 12, 'rw', 'bA3');
            f.AddGuide('ma3', 9, 'cta3', 'sta3', '0');
            f.AddGuide('na3', 0, 'rw', 'rh', 'ma3');
            f.AddGuide('dxa3', 7, 'na3', 'bA3');
            f.AddGuide('dya3', 12, 'na3', 'bA3');
            f.AddGuide('xA3', 1, 'hc', 'dxa3', '0');
            f.AddGuide('yA3', 1, 'vc', 'dya3', '0');
            f.AddGuide('td31', 7, 'rw', 'aD3');
            f.AddGuide('td32', 12, 'rh', 'aD3');
            f.AddGuide('bD3', 5, 'td31', 'td32');
            f.AddGuide('ctd3', 7, 'rh', 'bD3');
            f.AddGuide('std3', 12, 'rw', 'bD3');
            f.AddGuide('md3', 9, 'ctd3', 'std3', '0');
            f.AddGuide('nd3', 0, 'rw', 'rh', 'md3');
            f.AddGuide('dxd3', 7, 'nd3', 'bD3');
            f.AddGuide('dyd3', 12, 'nd3', 'bD3');
            f.AddGuide('xD3', 1, 'hc', 'dxd3', '0');
            f.AddGuide('yD3', 1, 'vc', 'dyd3', '0');
            f.AddGuide('xAD3', 1, 'xA3', '0', 'xD3');
            f.AddGuide('yAD3', 1, 'yA3', '0', 'yD3');
            f.AddGuide('lAD3', 9, 'xAD3', 'yAD3', '0');
            f.AddGuide('a3', 5, 'yAD3', 'xAD3');
            f.AddGuide('dxF3', 12, 'lFD', 'a3');
            f.AddGuide('dyF3', 7, 'lFD', 'a3');
            f.AddGuide('xF3', 1, 'xD3', 'dxF3', '0');
            f.AddGuide('yF3', 1, 'yD3', 'dyF3', '0');
            f.AddGuide('xE3', 1, 'xA3', '0', 'dxF3');
            f.AddGuide('yE3', 1, 'yA3', '0', 'dyF3');
            f.AddGuide('yC3t', 12, 'th', 'a3');
            f.AddGuide('xC3t', 7, 'th', 'a3');
            f.AddGuide('yC3', 1, 'yF3', 'yC3t', '0');
            f.AddGuide('xC3', 1, 'xF3', '0', 'xC3t');
            f.AddGuide('yB3', 1, 'yE3', 'yC3t', '0');
            f.AddGuide('xB3', 1, 'xE3', '0', 'xC3t');
            f.AddGuide('swAng2', 1, 'bA3', '0', 'bD2');
            f.AddGuide('aA4', 1, '4200000', '0', 'ha');
            f.AddGuide('aD4', 1, '4200000', 'ha', '0');
            f.AddGuide('ta41', 7, 'rw', 'aA4');
            f.AddGuide('ta42', 12, 'rh', 'aA4');
            f.AddGuide('bA4', 5, 'ta41', 'ta42');
            f.AddGuide('cta4', 7, 'rh', 'bA4');
            f.AddGuide('sta4', 12, 'rw', 'bA4');
            f.AddGuide('ma4', 9, 'cta4', 'sta4', '0');
            f.AddGuide('na4', 0, 'rw', 'rh', 'ma4');
            f.AddGuide('dxa4', 7, 'na4', 'bA4');
            f.AddGuide('dya4', 12, 'na4', 'bA4');
            f.AddGuide('xA4', 1, 'hc', 'dxa4', '0');
            f.AddGuide('yA4', 1, 'vc', 'dya4', '0');
            f.AddGuide('td41', 7, 'rw', 'aD4');
            f.AddGuide('td42', 12, 'rh', 'aD4');
            f.AddGuide('bD4', 5, 'td41', 'td42');
            f.AddGuide('ctd4', 7, 'rh', 'bD4');
            f.AddGuide('std4', 12, 'rw', 'bD4');
            f.AddGuide('md4', 9, 'ctd4', 'std4', '0');
            f.AddGuide('nd4', 0, 'rw', 'rh', 'md4');
            f.AddGuide('dxd4', 7, 'nd4', 'bD4');
            f.AddGuide('dyd4', 12, 'nd4', 'bD4');
            f.AddGuide('xD4', 1, 'hc', 'dxd4', '0');
            f.AddGuide('yD4', 1, 'vc', 'dyd4', '0');
            f.AddGuide('xAD4', 1, 'xA4', '0', 'xD4');
            f.AddGuide('yAD4', 1, 'yA4', '0', 'yD4');
            f.AddGuide('lAD4', 9, 'xAD4', 'yAD4', '0');
            f.AddGuide('a4', 5, 'yAD4', 'xAD4');
            f.AddGuide('dxF4', 12, 'lFD', 'a4');
            f.AddGuide('dyF4', 7, 'lFD', 'a4');
            f.AddGuide('xF4', 1, 'xD4', 'dxF4', '0');
            f.AddGuide('yF4', 1, 'yD4', 'dyF4', '0');
            f.AddGuide('xE4', 1, 'xA4', '0', 'dxF4');
            f.AddGuide('yE4', 1, 'yA4', '0', 'dyF4');
            f.AddGuide('yC4t', 12, 'th', 'a4');
            f.AddGuide('xC4t', 7, 'th', 'a4');
            f.AddGuide('yC4', 1, 'yF4', 'yC4t', '0');
            f.AddGuide('xC4', 1, 'xF4', '0', 'xC4t');
            f.AddGuide('yB4', 1, 'yE4', 'yC4t', '0');
            f.AddGuide('xB4', 1, 'xE4', '0', 'xC4t');
            f.AddGuide('swAng3', 1, 'bA4', '0', 'bD3');
            f.AddGuide('aA5', 1, '6600000', '0', 'ha');
            f.AddGuide('aD5', 1, '6600000', 'ha', '0');
            f.AddGuide('ta51', 7, 'rw', 'aA5');
            f.AddGuide('ta52', 12, 'rh', 'aA5');
            f.AddGuide('bA5', 5, 'ta51', 'ta52');
            f.AddGuide('td51', 7, 'rw', 'aD5');
            f.AddGuide('td52', 12, 'rh', 'aD5');
            f.AddGuide('bD5', 5, 'td51', 'td52');
            f.AddGuide('xD5', 1, 'w', '0', 'xA4');
            f.AddGuide('xC5', 1, 'w', '0', 'xB4');
            f.AddGuide('xB5', 1, 'w', '0', 'xC4');
            f.AddGuide('swAng4', 1, 'bA5', '0', 'bD4');
            f.AddGuide('aD6', 1, '9000000', 'ha', '0');
            f.AddGuide('td61', 7, 'rw', 'aD6');
            f.AddGuide('td62', 12, 'rh', 'aD6');
            f.AddGuide('bD6', 5, 'td61', 'td62');
            f.AddGuide('xD6', 1, 'w', '0', 'xA3');
            f.AddGuide('xC6', 1, 'w', '0', 'xB3');
            f.AddGuide('xB6', 1, 'w', '0', 'xC3');
            f.AddGuide('aD7', 1, '11400000', 'ha', '0');
            f.AddGuide('td71', 7, 'rw', 'aD7');
            f.AddGuide('td72', 12, 'rh', 'aD7');
            f.AddGuide('bD7', 5, 'td71', 'td72');
            f.AddGuide('xD7', 1, 'w', '0', 'xA2');
            f.AddGuide('xC7', 1, 'w', '0', 'xB2');
            f.AddGuide('xB7', 1, 'w', '0', 'xC2');
            f.AddGuide('aD8', 1, '13800000', 'ha', '0');
            f.AddGuide('td81', 7, 'rw', 'aD8');
            f.AddGuide('td82', 12, 'rh', 'aD8');
            f.AddGuide('bD8', 5, 'td81', 'td82');
            f.AddGuide('xA8', 1, 'w', '0', 'xD1');
            f.AddGuide('xD8', 1, 'w', '0', 'xA1');
            f.AddGuide('xC8', 1, 'w', '0', 'xB1');
            f.AddGuide('xB8', 1, 'w', '0', 'xC1');
            f.AddGuide('aA9', 1, '_3cd4', '0', 'ha');
            f.AddGuide('aD9', 1, '_3cd4', 'ha', '0');
            f.AddGuide('td91', 7, 'rw', 'aD9');
            f.AddGuide('td92', 12, 'rh', 'aD9');
            f.AddGuide('bD9', 5, 'td91', 'td92');
            f.AddGuide('ctd9', 7, 'rh', 'bD9');
            f.AddGuide('std9', 12, 'rw', 'bD9');
            f.AddGuide('md9', 9, 'ctd9', 'std9', '0');
            f.AddGuide('nd9', 0, 'rw', 'rh', 'md9');
            f.AddGuide('dxd9', 7, 'nd9', 'bD9');
            f.AddGuide('dyd9', 12, 'nd9', 'bD9');
            f.AddGuide('xD9', 1, 'hc', 'dxd9', '0');
            f.AddGuide('yD9', 1, 'vc', 'dyd9', '0');
            f.AddGuide('ta91', 7, 'rw', 'aA9');
            f.AddGuide('ta92', 12, 'rh', 'aA9');
            f.AddGuide('bA9', 5, 'ta91', 'ta92');
            f.AddGuide('xA9', 1, 'hc', '0', 'dxd9');
            f.AddGuide('xF9', 1, 'xD9', '0', 'lFD');
            f.AddGuide('xE9', 1, 'xA9', 'lFD', '0');
            f.AddGuide('yC9', 1, 'yD9', '0', 'th');
            f.AddGuide('swAng5', 1, 'bA9', '0', 'bD8');
            f.AddGuide('xCxn1', 2, 'xB1', 'xC1', '2');
            f.AddGuide('yCxn1', 2, 'yB1', 'yC1', '2');
            f.AddGuide('xCxn2', 2, 'xB2', 'xC2', '2');
            f.AddGuide('yCxn2', 2, 'yB2', 'yC2', '2');
            f.AddGuide('xCxn3', 2, 'xB3', 'xC3', '2');
            f.AddGuide('yCxn3', 2, 'yB3', 'yC3', '2');
            f.AddGuide('xCxn4', 2, 'xB4', 'xC4', '2');
            f.AddGuide('yCxn4', 2, 'yB4', 'yC4', '2');
            f.AddGuide('xCxn5', 2, 'r', '0', 'xCxn4');
            f.AddGuide('xCxn6', 2, 'r', '0', 'xCxn3');
            f.AddGuide('xCxn7', 2, 'r', '0', 'xCxn2');
            f.AddGuide('xCxn8', 2, 'r', '0', 'xCxn1');
            f.AddHandleXY(undefined, '0', '0','adj1','0','20000', 'xD9', 'yD9');
            f.AddHandleXY('adj2','0','2679', undefined, '0', '0', 'xA9', 'yD9');
            f.AddCnx('18600000', 'xCxn1', 'yCxn1');
            f.AddCnx('21000000', 'xCxn2', 'yCxn2');
            f.AddCnx('1800000', 'xCxn3', 'yCxn3');
            f.AddCnx('4200000', 'xCxn4', 'yCxn4');
            f.AddCnx('6600000', 'xCxn5', 'yCxn4');
            f.AddCnx('9000000', 'xCxn6', 'yCxn3');
            f.AddCnx('11400000', 'xCxn7', 'yCxn2');
            f.AddCnx('13800000', 'xCxn8', 'yCxn1');
            f.AddCnx('_3cd4', 'hc', 'yC9');
            f.AddRect('xA8', 'yD1', 'xD1', 'yD3');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'xA1', 'yA1');
            f.AddPathCommand(2, 'xB1', 'yB1');
            f.AddPathCommand(2, 'xC1', 'yC1');
            f.AddPathCommand(2, 'xD1', 'yD1');
            f.AddPathCommand(3, 'rh', 'rw', 'bD1', 'swAng1');
            f.AddPathCommand(2, 'xB2', 'yB2');
            f.AddPathCommand(2, 'xC2', 'yC2');
            f.AddPathCommand(2, 'xD2', 'yD2');
            f.AddPathCommand(3, 'rh', 'rw', 'bD2', 'swAng2');
            f.AddPathCommand(2, 'xB3', 'yB3');
            f.AddPathCommand(2, 'xC3', 'yC3');
            f.AddPathCommand(2, 'xD3', 'yD3');
            f.AddPathCommand(3, 'rh', 'rw', 'bD3', 'swAng3');
            f.AddPathCommand(2, 'xB4', 'yB4');
            f.AddPathCommand(2, 'xC4', 'yC4');
            f.AddPathCommand(2, 'xD4', 'yD4');
            f.AddPathCommand(3, 'rh', 'rw', 'bD4', 'swAng4');
            f.AddPathCommand(2, 'xB5', 'yC4');
            f.AddPathCommand(2, 'xC5', 'yB4');
            f.AddPathCommand(2, 'xD5', 'yA4');
            f.AddPathCommand(3, 'rh', 'rw', 'bD5', 'swAng3');
            f.AddPathCommand(2, 'xB6', 'yC3');
            f.AddPathCommand(2, 'xC6', 'yB3');
            f.AddPathCommand(2, 'xD6', 'yA3');
            f.AddPathCommand(3, 'rh', 'rw', 'bD6', 'swAng2');
            f.AddPathCommand(2, 'xB7', 'yC2');
            f.AddPathCommand(2, 'xC7', 'yB2');
            f.AddPathCommand(2, 'xD7', 'yA2');
            f.AddPathCommand(3, 'rh', 'rw', 'bD7', 'swAng1');
            f.AddPathCommand(2, 'xB8', 'yC1');
            f.AddPathCommand(2, 'xC8', 'yB1');
            f.AddPathCommand(2, 'xD8', 'yA1');
            f.AddPathCommand(3, 'rh', 'rw', 'bD8', 'swAng5');
            f.AddPathCommand(2, 'xE9', 'yC9');
            f.AddPathCommand(2, 'xF9', 'yC9');
            f.AddPathCommand(2, 'xD9', 'yD9');
            f.AddPathCommand(3, 'rh', 'rw', 'bD9', 'swAng5');
            f.AddPathCommand(6);
            break;
        }
        case 'halfFrame':{
            f.AddAdj('adj1', 15, '33333');
            f.AddAdj('adj2', 15, '33333');
            f.AddGuide('maxAdj2', 0, '100000', 'w', 'ss');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('x1', 0, 'ss', 'a2', '100000');
            f.AddGuide('g1', 0, 'h', 'x1', 'w');
            f.AddGuide('g2', 1, 'h', '0', 'g1');
            f.AddGuide('maxAdj1', 0, '100000', 'g2', 'ss');
            f.AddGuide('a1', 10, '0', 'adj1', 'maxAdj1');
            f.AddGuide('y1', 0, 'ss', 'a1', '100000');
            f.AddGuide('dx2', 0, 'y1', 'w', 'h');
            f.AddGuide('x2', 1, 'r', '0', 'dx2');
            f.AddGuide('dy2', 0, 'x1', 'h', 'w');
            f.AddGuide('y2', 1, 'b', '0', 'dy2');
            f.AddGuide('cx1', 0, 'x1', '1', '2');
            f.AddGuide('cy1', 2, 'y2', 'b', '2');
            f.AddGuide('cx2', 2, 'x2', 'r', '2');
            f.AddGuide('cy2', 0, 'y1', '1', '2');
            f.AddHandleXY(undefined, '0', '0','adj1','0','maxAdj1', 'l', 'y1');
            f.AddHandleXY('adj2','0','maxAdj2', undefined, '0', '0', 'x1', 't');
            f.AddCnx('0', 'cx2', 'cy2');
            f.AddCnx('cd4', 'cx1', 'cy1');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'x2', 'y1');
            f.AddPathCommand(2, 'x1', 'y1');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'heart':{
            f.AddGuide('dx1', 0, 'w', '49', '48');
            f.AddGuide('dx2', 0, 'w', '10', '48');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x2', 1, 'hc', '0', 'dx2');
            f.AddGuide('x3', 1, 'hc', 'dx2', '0');
            f.AddGuide('x4', 1, 'hc', 'dx1', '0');
            f.AddGuide('y1', 1, 't', '0', 'hd3');
            f.AddGuide('il', 0, 'w', '1', '6');
            f.AddGuide('ir', 0, 'w', '5', '6');
            f.AddGuide('ib', 0, 'h', '2', '3');
            f.AddCnx('_3cd4', 'hc', 'hd4');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddRect('il', 'hd4', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'hc', 'hd4');
            f.AddPathCommand(5, 'x3', 'y1', 'x4', 'hd4', 'hc', 'b');
            f.AddPathCommand(5, 'x1', 'hd4', 'x2', 'y1', 'hc', 'hd4');
            f.AddPathCommand(6);
            break;
        }
        case 'heptagon':{
            f.AddAdj('hf', 15, '102572');
            f.AddAdj('vf', 15, '105210');
            f.AddGuide('swd2', 0, 'wd2', 'hf', '100000');
            f.AddGuide('shd2', 0, 'hd2', 'vf', '100000');
            f.AddGuide('svc', 0, 'vc', 'vf', '100000');
            f.AddGuide('dx1', 0, 'swd2', '97493', '100000');
            f.AddGuide('dx2', 0, 'swd2', '78183', '100000');
            f.AddGuide('dx3', 0, 'swd2', '43388', '100000');
            f.AddGuide('dy1', 0, 'shd2', '62349', '100000');
            f.AddGuide('dy2', 0, 'shd2', '22252', '100000');
            f.AddGuide('dy3', 0, 'shd2', '90097', '100000');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x2', 1, 'hc', '0', 'dx2');
            f.AddGuide('x3', 1, 'hc', '0', 'dx3');
            f.AddGuide('x4', 1, 'hc', 'dx3', '0');
            f.AddGuide('x5', 1, 'hc', 'dx2', '0');
            f.AddGuide('x6', 1, 'hc', 'dx1', '0');
            f.AddGuide('y1', 1, 'svc', '0', 'dy1');
            f.AddGuide('y2', 1, 'svc', 'dy2', '0');
            f.AddGuide('y3', 1, 'svc', 'dy3', '0');
            f.AddGuide('ib', 1, 'b', '0', 'y1');
            f.AddCnx('0', 'x5', 'y1');
            f.AddCnx('0', 'x6', 'y2');
            f.AddCnx('cd4', 'x4', 'y3');
            f.AddCnx('cd4', 'x3', 'y3');
            f.AddCnx('cd2', 'x1', 'y2');
            f.AddCnx('cd2', 'x2', 'y1');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('x2', 'y1', 'x5', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y2');
            f.AddPathCommand(2, 'x2', 'y1');
            f.AddPathCommand(2, 'hc', 't');
            f.AddPathCommand(2, 'x5', 'y1');
            f.AddPathCommand(2, 'x6', 'y2');
            f.AddPathCommand(2, 'x4', 'y3');
            f.AddPathCommand(2, 'x3', 'y3');
            f.AddPathCommand(6);
            break;
        }
        case 'hexagon':{
            f.AddAdj('adj', 15, '25000');
            f.AddAdj('vf', 15, '115470');
            f.AddGuide('maxAdj', 0, '50000', 'w', 'ss');
            f.AddGuide('a', 10, '0', 'adj', 'maxAdj');
            f.AddGuide('shd2', 0, 'hd2', 'vf', '100000');
            f.AddGuide('x1', 0, 'ss', 'a', '100000');
            f.AddGuide('x2', 1, 'r', '0', 'x1');
            f.AddGuide('dy1', 12, 'shd2', '3600000');
            f.AddGuide('y1', 1, 'vc', '0', 'dy1');
            f.AddGuide('y2', 1, 'vc', 'dy1', '0');
            f.AddGuide('q1', 0, 'maxAdj', '-1', '2');
            f.AddGuide('q2', 1, 'a', 'q1', '0');
            f.AddGuide('q3', 3, 'q2', '4', '2');
            f.AddGuide('q4', 3, 'q2', '3', '2');
            f.AddGuide('q5', 3, 'q2', 'q1', '0');
            f.AddGuide('q6', 2, 'a', 'q5', 'q1');
            f.AddGuide('q7', 0, 'q6', 'q4', '-1');
            f.AddGuide('q8', 1, 'q3', 'q7', '0');
            f.AddGuide('il', 0, 'w', 'q8', '24');
            f.AddGuide('it', 0, 'h', 'q8', '24');
            f.AddGuide('ir', 1, 'r', '0', 'il');
            f.AddGuide('ib', 1, 'b', '0', 'it');
            f.AddHandleXY('adj','0','maxAdj', undefined, '0', '0', 'x1', 't');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'x2', 'y2');
            f.AddCnx('cd4', 'x1', 'y2');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'x1', 'y1');
            f.AddCnx('_3cd4', 'x2', 'y1');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(2, 'x1', 'y1');
            f.AddPathCommand(2, 'x2', 'y1');
            f.AddPathCommand(2, 'r', 'vc');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(6);
            break;
        }
        case 'homePlate':{
            f.AddAdj('adj', 15, '50000');
            f.AddGuide('maxAdj', 0, '100000', 'w', 'ss');
            f.AddGuide('a', 10, '0', 'adj', 'maxAdj');
            f.AddGuide('dx1', 0, 'ss', 'a', '100000');
            f.AddGuide('x1', 1, 'r', '0', 'dx1');
            f.AddGuide('ir', 2, 'x1', 'r', '2');
            f.AddGuide('x2', 0, 'x1', '1', '2');
            f.AddHandleXY('adj','0','maxAdj', undefined, '0', '0', 'x1', 't');
            f.AddCnx('_3cd4', 'x2', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'x1', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('l', 't', 'ir', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'x1', 't');
            f.AddPathCommand(2, 'r', 'vc');
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'horizontalScroll':{
            f.AddAdj('adj', 15, '12500');
            f.AddGuide('a', 10, '0', 'adj', '25000');
            f.AddGuide('ch', 0, 'ss', 'a', '100000');
            f.AddGuide('ch2', 0, 'ch', '1', '2');
            f.AddGuide('ch4', 0, 'ch', '1', '4');
            f.AddGuide('y3', 1, 'ch', 'ch2', '0');
            f.AddGuide('y4', 1, 'ch', 'ch', '0');
            f.AddGuide('y6', 1, 'b', '0', 'ch');
            f.AddGuide('y7', 1, 'b', '0', 'ch2');
            f.AddGuide('y5', 1, 'y6', '0', 'ch2');
            f.AddGuide('x3', 1, 'r', '0', 'ch');
            f.AddGuide('x4', 1, 'r', '0', 'ch2');
            f.AddHandleXY('adj','0','25000', undefined, '0', '0', 'ch', 't');
            f.AddCnx('cd4', 'hc', 'ch');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 'y6');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('ch', 'ch', 'x4', 'y6');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'r', 'ch2');
            f.AddPathCommand(3, 'ch2', 'ch2', '0', 'cd4');
            f.AddPathCommand(2, 'x4', 'ch2');
            f.AddPathCommand(3, 'ch4', 'ch4', '0', 'cd2');
            f.AddPathCommand(2, 'x3', 'ch');
            f.AddPathCommand(2, 'ch2', 'ch');
            f.AddPathCommand(3, 'ch2', 'ch2', '_3cd4', '-5400000');
            f.AddPathCommand(2, 'l', 'y7');
            f.AddPathCommand(3, 'ch2', 'ch2', 'cd2', '-10800000');
            f.AddPathCommand(2, 'ch', 'y6');
            f.AddPathCommand(2, 'x4', 'y6');
            f.AddPathCommand(3, 'ch2', 'ch2', 'cd4', '-5400000');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'ch2', 'y4');
            f.AddPathCommand(3, 'ch2', 'ch2', 'cd4', '-5400000');
            f.AddPathCommand(3, 'ch4', 'ch4', '0', '-10800000');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darkenLess', false, undefined, undefined);
            f.AddPathCommand(1, 'ch2', 'y4');
            f.AddPathCommand(3, 'ch2', 'ch2', 'cd4', '-5400000');
            f.AddPathCommand(3, 'ch4', 'ch4', '0', '-10800000');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'x4', 'ch');
            f.AddPathCommand(3, 'ch2', 'ch2', 'cd4', '-16200000');
            f.AddPathCommand(3, 'ch4', 'ch4', 'cd2', '-10800000');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y3');
            f.AddPathCommand(3, 'ch2', 'ch2', 'cd2', 'cd4');
            f.AddPathCommand(2, 'x3', 'ch');
            f.AddPathCommand(2, 'x3', 'ch2');
            f.AddPathCommand(3, 'ch2', 'ch2', 'cd2', 'cd2');
            f.AddPathCommand(2, 'r', 'y5');
            f.AddPathCommand(3, 'ch2', 'ch2', '0', 'cd4');
            f.AddPathCommand(2, 'ch', 'y6');
            f.AddPathCommand(2, 'ch', 'y7');
            f.AddPathCommand(3, 'ch2', 'ch2', '0', 'cd2');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'x3', 'ch');
            f.AddPathCommand(2, 'x4', 'ch');
            f.AddPathCommand(3, 'ch2', 'ch2', 'cd4', '-5400000');
            f.AddPathCommand(1, 'x4', 'ch');
            f.AddPathCommand(2, 'x4', 'ch2');
            f.AddPathCommand(3, 'ch4', 'ch4', '0', 'cd2');
            f.AddPathCommand(1, 'ch2', 'y4');
            f.AddPathCommand(2, 'ch2', 'y3');
            f.AddPathCommand(3, 'ch4', 'ch4', 'cd2', 'cd2');
            f.AddPathCommand(3, 'ch2', 'ch2', '0', 'cd2');
            f.AddPathCommand(1, 'ch', 'y3');
            f.AddPathCommand(2, 'ch', 'y6');
            break;
        }
        case 'irregularSeal1':{
            f.AddGuide('x5', 0, 'w', '4627', '21600');
            f.AddGuide('x12', 0, 'w', '8485', '21600');
            f.AddGuide('x21', 0, 'w', '16702', '21600');
            f.AddGuide('x24', 0, 'w', '14522', '21600');
            f.AddGuide('y3', 0, 'h', '6320', '21600');
            f.AddGuide('y6', 0, 'h', '8615', '21600');
            f.AddGuide('y9', 0, 'h', '13937', '21600');
            f.AddGuide('y18', 0, 'h', '13290', '21600');
            f.AddCnx('_3cd4', 'x24', 't');
            f.AddCnx('cd2', 'l', 'y6');
            f.AddCnx('cd4', 'x12', 'b');
            f.AddCnx('0', 'r', 'y18');
            f.AddRect('x5', 'y3', 'x21', 'y9');
            f.AddPathCommand(0,undefined, undefined, undefined, 21600, 21600);
            f.AddPathCommand(1, '10800', '5800');
            f.AddPathCommand(2, '14522', '0');
            f.AddPathCommand(2, '14155', '5325');
            f.AddPathCommand(2, '18380', '4457');
            f.AddPathCommand(2, '16702', '7315');
            f.AddPathCommand(2, '21097', '8137');
            f.AddPathCommand(2, '17607', '10475');
            f.AddPathCommand(2, '21600', '13290');
            f.AddPathCommand(2, '16837', '12942');
            f.AddPathCommand(2, '18145', '18095');
            f.AddPathCommand(2, '14020', '14457');
            f.AddPathCommand(2, '13247', '19737');
            f.AddPathCommand(2, '10532', '14935');
            f.AddPathCommand(2, '8485', '21600');
            f.AddPathCommand(2, '7715', '15627');
            f.AddPathCommand(2, '4762', '17617');
            f.AddPathCommand(2, '5667', '13937');
            f.AddPathCommand(2, '135', '14587');
            f.AddPathCommand(2, '3722', '11775');
            f.AddPathCommand(2, '0', '8615');
            f.AddPathCommand(2, '4627', '7617');
            f.AddPathCommand(2, '370', '2295');
            f.AddPathCommand(2, '7312', '6320');
            f.AddPathCommand(2, '8352', '2295');
            f.AddPathCommand(6);
            break;
        }
        case 'irregularSeal2':{
            f.AddGuide('x2', 0, 'w', '9722', '21600');
            f.AddGuide('x5', 0, 'w', '5372', '21600');
            f.AddGuide('x16', 0, 'w', '11612', '21600');
            f.AddGuide('x19', 0, 'w', '14640', '21600');
            f.AddGuide('y2', 0, 'h', '1887', '21600');
            f.AddGuide('y3', 0, 'h', '6382', '21600');
            f.AddGuide('y8', 0, 'h', '12877', '21600');
            f.AddGuide('y14', 0, 'h', '19712', '21600');
            f.AddGuide('y16', 0, 'h', '18842', '21600');
            f.AddGuide('y17', 0, 'h', '15935', '21600');
            f.AddGuide('y24', 0, 'h', '6645', '21600');
            f.AddCnx('_3cd4', 'x2', 'y2');
            f.AddCnx('cd2', 'l', 'y8');
            f.AddCnx('cd4', 'x16', 'y16');
            f.AddCnx('0', 'r', 'y24');
            f.AddRect('x5', 'y3', 'x19', 'y17');
            f.AddPathCommand(0,undefined, undefined, undefined, 21600, 21600);
            f.AddPathCommand(1, '11462', '4342');
            f.AddPathCommand(2, '14790', '0');
            f.AddPathCommand(2, '14525', '5777');
            f.AddPathCommand(2, '18007', '3172');
            f.AddPathCommand(2, '16380', '6532');
            f.AddPathCommand(2, '21600', '6645');
            f.AddPathCommand(2, '16985', '9402');
            f.AddPathCommand(2, '18270', '11290');
            f.AddPathCommand(2, '16380', '12310');
            f.AddPathCommand(2, '18877', '15632');
            f.AddPathCommand(2, '14640', '14350');
            f.AddPathCommand(2, '14942', '17370');
            f.AddPathCommand(2, '12180', '15935');
            f.AddPathCommand(2, '11612', '18842');
            f.AddPathCommand(2, '9872', '17370');
            f.AddPathCommand(2, '8700', '19712');
            f.AddPathCommand(2, '7527', '18125');
            f.AddPathCommand(2, '4917', '21600');
            f.AddPathCommand(2, '4805', '18240');
            f.AddPathCommand(2, '1285', '17825');
            f.AddPathCommand(2, '3330', '15370');
            f.AddPathCommand(2, '0', '12877');
            f.AddPathCommand(2, '3935', '11592');
            f.AddPathCommand(2, '1172', '8270');
            f.AddPathCommand(2, '5372', '7817');
            f.AddPathCommand(2, '4502', '3625');
            f.AddPathCommand(2, '8550', '6382');
            f.AddPathCommand(2, '9722', '1887');
            f.AddPathCommand(6);
            break;
        }
        case 'leftArrow':{
            f.AddAdj('adj1', 15, '50000');
            f.AddAdj('adj2', 15, '50000');
            f.AddGuide('maxAdj2', 0, '100000', 'w', 'ss');
            f.AddGuide('a1', 10, '0', 'adj1', '100000');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('dx2', 0, 'ss', 'a2', '100000');
            f.AddGuide('x2', 1, 'l', 'dx2', '0');
            f.AddGuide('dy1', 0, 'h', 'a1', '200000');
            f.AddGuide('y1', 1, 'vc', '0', 'dy1');
            f.AddGuide('y2', 1, 'vc', 'dy1', '0');
            f.AddGuide('dx1', 0, 'y1', 'dx2', 'hd2');
            f.AddGuide('x1', 1, 'x2', '0', 'dx1');
            f.AddHandleXY(undefined, '0', '0','adj1','0','100000', 'r', 'y1');
            f.AddHandleXY('adj2','0','maxAdj2', undefined, '0', '0', 'x2', 't');
            f.AddCnx('_3cd4', 'x2', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'x2', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('x1', 'y1', 'r', 'y2');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(2, 'x2', 'y1');
            f.AddPathCommand(2, 'r', 'y1');
            f.AddPathCommand(2, 'r', 'y2');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x2', 'b');
            f.AddPathCommand(6);
            break;
        }



        case 'upArrow':{
            f.AddAdj('adj1', 15, '50000');
            f.AddAdj('adj2', 15, '50000');
            f.AddGuide('maxAdj2', 0, '100000', 'h', 'ss');
            f.AddGuide('a1', 10, '0', 'adj1', '100000');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('dy2', 0, 'ss', 'a2', '100000');
            f.AddGuide('y2', 1, 't', 'dy2', '0');
            f.AddGuide('dx1', 0, 'w', 'a1', '200000');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x2', 1, 'hc', 'dx1', '0');
            f.AddGuide('dy1', 0, 'x1', 'dy2', 'wd2');
            f.AddGuide('y1', 1, 'y2', '0', 'dy1');
            f.AddHandleXY('adj1','0','100000', undefined, '0', '0', 'x1', 'b');
            f.AddHandleXY( undefined, '0', '0','adj2','0','maxAdj2', 'l', 'y2');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'y2');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'y2');
            f.AddRect('x1', 'y1', 'x2', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y2');
            f.AddPathCommand(2, 'hc', 't');
            f.AddPathCommand(2, 'r', 'y2');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x2', 'b');
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(6);
            break;
        }


        case 'leftArrowCallout':{
            f.AddAdj('adj1', 15, '25000');
            f.AddAdj('adj2', 15, '25000');
            f.AddAdj('adj3', 15, '25000');
            f.AddAdj('adj4', 15, '64977');
            f.AddGuide('maxAdj2', 0, '50000', 'h', 'ss');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('maxAdj1', 0, 'a2', '2', '1');
            f.AddGuide('a1', 10, '0', 'adj1', 'maxAdj1');
            f.AddGuide('maxAdj3', 0, '100000', 'w', 'ss');
            f.AddGuide('a3', 10, '0', 'adj3', 'maxAdj3');
            f.AddGuide('q2', 0, 'a3', 'ss', 'w');
            f.AddGuide('maxAdj4', 1, '100000', '0', 'q2');
            f.AddGuide('a4', 10, '0', 'adj4', 'maxAdj4');
            f.AddGuide('dy1', 0, 'ss', 'a2', '100000');
            f.AddGuide('dy2', 0, 'ss', 'a1', '200000');
            f.AddGuide('y1', 1, 'vc', '0', 'dy1');
            f.AddGuide('y2', 1, 'vc', '0', 'dy2');
            f.AddGuide('y3', 1, 'vc', 'dy2', '0');
            f.AddGuide('y4', 1, 'vc', 'dy1', '0');
            f.AddGuide('x1', 0, 'ss', 'a3', '100000');
            f.AddGuide('dx2', 0, 'w', 'a4', '100000');
            f.AddGuide('x2', 1, 'r', '0', 'dx2');
            f.AddGuide('x3', 2, 'x2', 'r', '2');
            f.AddHandleXY(undefined, '0', '0','adj1','0','maxAdj1', 'x1', 'y2');
            f.AddHandleXY(undefined, '0', '0','adj2','0','maxAdj2', 'l', 'y1');
            f.AddHandleXY('adj3','0','maxAdj3', undefined, '0', '0', 'x1', 't');
            f.AddHandleXY('adj4','0','maxAdj4', undefined, '0', '0', 'x2', 'b');
            f.AddCnx('_3cd4', 'x3', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'x3', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('x2', 't', 'r', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(2, 'x1', 'y1');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'x2', 'b');
            f.AddPathCommand(2, 'x2', 'y3');
            f.AddPathCommand(2, 'x1', 'y3');
            f.AddPathCommand(2, 'x1', 'y4');
            f.AddPathCommand(6);
            break;
        }
        case 'leftBrace':{
            f.AddAdj('adj1', 15, '8333');
            f.AddAdj('adj2', 15, '50000');
            f.AddGuide('a2', 10, '0', 'adj2', '100000');
            f.AddGuide('q1', 1, '100000', '0', 'a2');
            f.AddGuide('q2', 16, 'q1', 'a2');
            f.AddGuide('q3', 0, 'q2', '1', '2');
            f.AddGuide('maxAdj1', 0, 'q3', 'h', 'ss');
            f.AddGuide('a1', 10, '0', 'adj1', 'maxAdj1');
            f.AddGuide('y1', 0, 'ss', 'a1', '100000');
            f.AddGuide('y3', 0, 'h', 'a2', '100000');
            f.AddGuide('y4', 1, 'y3', 'y1', '0');
            f.AddGuide('dx1', 7, 'wd2', '2700000');
            f.AddGuide('dy1', 12, 'y1', '2700000');
            f.AddGuide('il', 1, 'r', '0', 'dx1');
            f.AddGuide('it', 1, 'y1', '0', 'dy1');
            f.AddGuide('ib', 1, 'b', 'dy1', 'y1');
            f.AddHandleXY(undefined, '0', '0','adj1','0','maxAdj1', 'hc', 'y1');
            f.AddHandleXY(undefined, '0', '0','adj2','0','100000', 'l', 'y3');
            f.AddCnx('cd4', 'r', 't');
            f.AddCnx('cd2', 'l', 'y3');
            f.AddCnx('_3cd4', 'r', 'b');
            f.AddRect('il', 'it', 'r', 'ib');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'r', 'b');
            f.AddPathCommand(3, 'wd2', 'y1', 'cd4', 'cd4');
            f.AddPathCommand(2, 'hc', 'y4');
            f.AddPathCommand(3, 'wd2', 'y1', '0', '-5400000');
            f.AddPathCommand(3, 'wd2', 'y1', 'cd4', '-5400000');
            f.AddPathCommand(2, 'hc', 'y1');
            f.AddPathCommand(3, 'wd2', 'y1', 'cd2', 'cd4');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'r', 'b');
            f.AddPathCommand(3, 'wd2', 'y1', 'cd4', 'cd4');
            f.AddPathCommand(2, 'hc', 'y4');
            f.AddPathCommand(3, 'wd2', 'y1', '0', '-5400000');
            f.AddPathCommand(3, 'wd2', 'y1', 'cd4', '-5400000');
            f.AddPathCommand(2, 'hc', 'y1');
            f.AddPathCommand(3, 'wd2', 'y1', 'cd2', 'cd4');
            break;
        }
        case 'leftBracket':{
            f.AddAdj('adj', 15, '8333');
            f.AddGuide('maxAdj', 0, '50000', 'h', 'ss');
            f.AddGuide('a', 10, '0', 'adj', 'maxAdj');
            f.AddGuide('y1', 0, 'ss', 'a', '100000');
            f.AddGuide('y2', 1, 'b', '0', 'y1');
            f.AddGuide('dx1', 7, 'w', '2700000');
            f.AddGuide('dy1', 12, 'y1', '2700000');
            f.AddGuide('il', 1, 'r', '0', 'dx1');
            f.AddGuide('it', 1, 'y1', '0', 'dy1');
            f.AddGuide('ib', 1, 'b', 'dy1', 'y1');
            f.AddHandleXY(undefined, '0', '0','adj','0','maxAdj', 'l', 'y1');
            f.AddCnx('cd4', 'r', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'r', 'b');
            f.AddRect('il', 'it', 'r', 'ib');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'r', 'b');
            f.AddPathCommand(3, 'w', 'y1', 'cd4', 'cd4');
            f.AddPathCommand(2, 'l', 'y1');
            f.AddPathCommand(3, 'w', 'y1', 'cd2', 'cd4');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'r', 'b');
            f.AddPathCommand(3, 'w', 'y1', 'cd4', 'cd4');
            f.AddPathCommand(2, 'l', 'y1');
            f.AddPathCommand(3, 'w', 'y1', 'cd2', 'cd4');
            break;
        }
        case 'leftCircularArrow':{
            f.AddAdj('adj1', 15, '12500');
            f.AddAdj('adj2', 15, '-1142319');
            f.AddAdj('adj3', 15, '1142319');
            f.AddAdj('adj4', 15, '10800000');
            f.AddAdj('adj5', 15, '12500');
            f.AddGuide('a5', 10, '0', 'adj5', '25000');
            f.AddGuide('maxAdj1', 0, 'a5', '2', '1');
            f.AddGuide('a1', 10, '0', 'adj1', 'maxAdj1');
            f.AddGuide('enAng', 10, '1', 'adj3', '21599999');
            f.AddGuide('stAng', 10, '0', 'adj4', '21599999');
            f.AddGuide('th', 0, 'ss', 'a1', '100000');
            f.AddGuide('thh', 0, 'ss', 'a5', '100000');
            f.AddGuide('th2', 0, 'th', '1', '2');
            f.AddGuide('rw1', 1, 'wd2', 'th2', 'thh');
            f.AddGuide('rh1', 1, 'hd2', 'th2', 'thh');
            f.AddGuide('rw2', 1, 'rw1', '0', 'th');
            f.AddGuide('rh2', 1, 'rh1', '0', 'th');
            f.AddGuide('rw3', 1, 'rw2', 'th2', '0');
            f.AddGuide('rh3', 1, 'rh2', 'th2', '0');
            f.AddGuide('wtH', 12, 'rw3', 'enAng');
            f.AddGuide('htH', 7, 'rh3', 'enAng');
            f.AddGuide('dxH', 6, 'rw3', 'htH', 'wtH');
            f.AddGuide('dyH', 11, 'rh3', 'htH', 'wtH');
            f.AddGuide('xH', 1, 'hc', 'dxH', '0');
            f.AddGuide('yH', 1, 'vc', 'dyH', '0');
            f.AddGuide('rI', 16, 'rw2', 'rh2');
            f.AddGuide('u1', 0, 'dxH', 'dxH', '1');
            f.AddGuide('u2', 0, 'dyH', 'dyH', '1');
            f.AddGuide('u3', 0, 'rI', 'rI', '1');
            f.AddGuide('u4', 1, 'u1', '0', 'u3');
            f.AddGuide('u5', 1, 'u2', '0', 'u3');
            f.AddGuide('u6', 0, 'u4', 'u5', 'u1');
            f.AddGuide('u7', 0, 'u6', '1', 'u2');
            f.AddGuide('u8', 1, '1', '0', 'u7');
            f.AddGuide('u9', 13, 'u8');
            f.AddGuide('u10', 0, 'u4', '1', 'dxH');
            f.AddGuide('u11', 0, 'u10', '1', 'dyH');
            f.AddGuide('u12', 2, '1', 'u9', 'u11');
            f.AddGuide('u13', 5, '1', 'u12');
            f.AddGuide('u14', 1, 'u13', '21600000', '0');
            f.AddGuide('u15', 3, 'u13', 'u13', 'u14');
            f.AddGuide('u16', 1, 'u15', '0', 'enAng');
            f.AddGuide('u17', 1, 'u16', '21600000', '0');
            f.AddGuide('u18', 3, 'u16', 'u16', 'u17');
            f.AddGuide('u19', 1, 'u18', '0', 'cd2');
            f.AddGuide('u20', 1, 'u18', '0', '21600000');
            f.AddGuide('u21', 3, 'u19', 'u20', 'u18');
            f.AddGuide('u22', 4, 'u21');
            f.AddGuide('minAng', 0, 'u22', '-1', '1');
            f.AddGuide('u23', 4, 'adj2');
            f.AddGuide('a2', 0, 'u23', '-1', '1');
            f.AddGuide('aAng', 10, 'minAng', 'a2', '0');
            f.AddGuide('ptAng', 1, 'enAng', 'aAng', '0');
            f.AddGuide('wtA', 12, 'rw3', 'ptAng');
            f.AddGuide('htA', 7, 'rh3', 'ptAng');
            f.AddGuide('dxA', 6, 'rw3', 'htA', 'wtA');
            f.AddGuide('dyA', 11, 'rh3', 'htA', 'wtA');
            f.AddGuide('xA', 1, 'hc', 'dxA', '0');
            f.AddGuide('yA', 1, 'vc', 'dyA', '0');
            f.AddGuide('wtE', 12, 'rw1', 'stAng');
            f.AddGuide('htE', 7, 'rh1', 'stAng');
            f.AddGuide('dxE', 6, 'rw1', 'htE', 'wtE');
            f.AddGuide('dyE', 11, 'rh1', 'htE', 'wtE');
            f.AddGuide('xE', 1, 'hc', 'dxE', '0');
            f.AddGuide('yE', 1, 'vc', 'dyE', '0');
            f.AddGuide('wtD', 12, 'rw2', 'stAng');
            f.AddGuide('htD', 7, 'rh2', 'stAng');
            f.AddGuide('dxD', 6, 'rw2', 'htD', 'wtD');
            f.AddGuide('dyD', 11, 'rh2', 'htD', 'wtD');
            f.AddGuide('xD', 1, 'hc', 'dxD', '0');
            f.AddGuide('yD', 1, 'vc', 'dyD', '0');
            f.AddGuide('dxG', 7, 'thh', 'ptAng');
            f.AddGuide('dyG', 12, 'thh', 'ptAng');
            f.AddGuide('xG', 1, 'xH', 'dxG', '0');
            f.AddGuide('yG', 1, 'yH', 'dyG', '0');
            f.AddGuide('dxB', 7, 'thh', 'ptAng');
            f.AddGuide('dyB', 12, 'thh', 'ptAng');
            f.AddGuide('xB', 1, 'xH', '0', 'dxB', '0');
            f.AddGuide('yB', 1, 'yH', '0', 'dyB', '0');
            f.AddGuide('sx1', 1, 'xB', '0', 'hc');
            f.AddGuide('sy1', 1, 'yB', '0', 'vc');
            f.AddGuide('sx2', 1, 'xG', '0', 'hc');
            f.AddGuide('sy2', 1, 'yG', '0', 'vc');
            f.AddGuide('rO', 16, 'rw1', 'rh1');
            f.AddGuide('x1O', 0, 'sx1', 'rO', 'rw1');
            f.AddGuide('y1O', 0, 'sy1', 'rO', 'rh1');
            f.AddGuide('x2O', 0, 'sx2', 'rO', 'rw1');
            f.AddGuide('y2O', 0, 'sy2', 'rO', 'rh1');
            f.AddGuide('dxO', 1, 'x2O', '0', 'x1O');
            f.AddGuide('dyO', 1, 'y2O', '0', 'y1O');
            f.AddGuide('dO', 9, 'dxO', 'dyO', '0');
            f.AddGuide('q1', 0, 'x1O', 'y2O', '1');
            f.AddGuide('q2', 0, 'x2O', 'y1O', '1');
            f.AddGuide('DO', 1, 'q1', '0', 'q2');
            f.AddGuide('q3', 0, 'rO', 'rO', '1');
            f.AddGuide('q4', 0, 'dO', 'dO', '1');
            f.AddGuide('q5', 0, 'q3', 'q4', '1');
            f.AddGuide('q6', 0, 'DO', 'DO', '1');
            f.AddGuide('q7', 1, 'q5', '0', 'q6');
            f.AddGuide('q8', 8, 'q7', '0');
            f.AddGuide('sdelO', 13, 'q8');
            f.AddGuide('ndyO', 0, 'dyO', '-1', '1');
            f.AddGuide('sdyO', 3, 'ndyO', '-1', '1');
            f.AddGuide('q9', 0, 'sdyO', 'dxO', '1');
            f.AddGuide('q10', 0, 'q9', 'sdelO', '1');
            f.AddGuide('q11', 0, 'DO', 'dyO', '1');
            f.AddGuide('dxF1', 2, 'q11', 'q10', 'q4');
            f.AddGuide('q12', 1, 'q11', '0', 'q10');
            f.AddGuide('dxF2', 0, 'q12', '1', 'q4');
            f.AddGuide('adyO', 4, 'dyO');
            f.AddGuide('q13', 0, 'adyO', 'sdelO', '1');
            f.AddGuide('q14', 0, 'DO', 'dxO', '-1');
            f.AddGuide('dyF1', 2, 'q14', 'q13', 'q4');
            f.AddGuide('q15', 1, 'q14', '0', 'q13');
            f.AddGuide('dyF2', 0, 'q15', '1', 'q4');
            f.AddGuide('q16', 1, 'x2O', '0', 'dxF1');
            f.AddGuide('q17', 1, 'x2O', '0', 'dxF2');
            f.AddGuide('q18', 1, 'y2O', '0', 'dyF1');
            f.AddGuide('q19', 1, 'y2O', '0', 'dyF2');
            f.AddGuide('q20', 9, 'q16', 'q18', '0');
            f.AddGuide('q21', 9, 'q17', 'q19', '0');
            f.AddGuide('q22', 1, 'q21', '0', 'q20');
            f.AddGuide('dxF', 3, 'q22', 'dxF1', 'dxF2');
            f.AddGuide('dyF', 3, 'q22', 'dyF1', 'dyF2');
            f.AddGuide('sdxF', 0, 'dxF', 'rw1', 'rO');
            f.AddGuide('sdyF', 0, 'dyF', 'rh1', 'rO');
            f.AddGuide('xF', 1, 'hc', 'sdxF', '0');
            f.AddGuide('yF', 1, 'vc', 'sdyF', '0');
            f.AddGuide('x1I', 0, 'sx1', 'rI', 'rw2');
            f.AddGuide('y1I', 0, 'sy1', 'rI', 'rh2');
            f.AddGuide('x2I', 0, 'sx2', 'rI', 'rw2');
            f.AddGuide('y2I', 0, 'sy2', 'rI', 'rh2');
            f.AddGuide('dxI', 1, 'x2I', '0', 'x1I');
            f.AddGuide('dyI', 1, 'y2I', '0', 'y1I');
            f.AddGuide('dI', 9, 'dxI', 'dyI', '0');
            f.AddGuide('v1', 0, 'x1I', 'y2I', '1');
            f.AddGuide('v2', 0, 'x2I', 'y1I', '1');
            f.AddGuide('DI', 1, 'v1', '0', 'v2');
            f.AddGuide('v3', 0, 'rI', 'rI', '1');
            f.AddGuide('v4', 0, 'dI', 'dI', '1');
            f.AddGuide('v5', 0, 'v3', 'v4', '1');
            f.AddGuide('v6', 0, 'DI', 'DI', '1');
            f.AddGuide('v7', 1, 'v5', '0', 'v6');
            f.AddGuide('v8', 8, 'v7', '0');
            f.AddGuide('sdelI', 13, 'v8');
            f.AddGuide('v9', 0, 'sdyO', 'dxI', '1');
            f.AddGuide('v10', 0, 'v9', 'sdelI', '1');
            f.AddGuide('v11', 0, 'DI', 'dyI', '1');
            f.AddGuide('dxC1', 2, 'v11', 'v10', 'v4');
            f.AddGuide('v12', 1, 'v11', '0', 'v10');
            f.AddGuide('dxC2', 0, 'v12', '1', 'v4');
            f.AddGuide('adyI', 4, 'dyI');
            f.AddGuide('v13', 0, 'adyI', 'sdelI', '1');
            f.AddGuide('v14', 0, 'DI', 'dxI', '-1');
            f.AddGuide('dyC1', 2, 'v14', 'v13', 'v4');
            f.AddGuide('v15', 1, 'v14', '0', 'v13');
            f.AddGuide('dyC2', 0, 'v15', '1', 'v4');
            f.AddGuide('v16', 1, 'x1I', '0', 'dxC1');
            f.AddGuide('v17', 1, 'x1I', '0', 'dxC2');
            f.AddGuide('v18', 1, 'y1I', '0', 'dyC1');
            f.AddGuide('v19', 1, 'y1I', '0', 'dyC2');
            f.AddGuide('v20', 9, 'v16', 'v18', '0');
            f.AddGuide('v21', 9, 'v17', 'v19', '0');
            f.AddGuide('v22', 1, 'v21', '0', 'v20');
            f.AddGuide('dxC', 3, 'v22', 'dxC1', 'dxC2');
            f.AddGuide('dyC', 3, 'v22', 'dyC1', 'dyC2');
            f.AddGuide('sdxC', 0, 'dxC', 'rw2', 'rI');
            f.AddGuide('sdyC', 0, 'dyC', 'rh2', 'rI');
            f.AddGuide('xC', 1, 'hc', 'sdxC', '0');
            f.AddGuide('yC', 1, 'vc', 'sdyC', '0');
            f.AddGuide('ist0', 5, 'sdxC', 'sdyC');
            f.AddGuide('ist1', 1, 'ist0', '21600000', '0');
            f.AddGuide('istAng0', 3, 'ist0', 'ist0', 'ist1');
            f.AddGuide('isw1', 1, 'stAng', '0', 'istAng0');
            f.AddGuide('isw2', 1, 'isw1', '21600000', '0');
            f.AddGuide('iswAng0', 3, 'isw1', 'isw1', 'isw2');
            f.AddGuide('istAng', 1, 'istAng0', 'iswAng0', '0');
            f.AddGuide('iswAng', 1, '0', '0', 'iswAng0');
            f.AddGuide('p1', 1, 'xF', '0', 'xC');
            f.AddGuide('p2', 1, 'yF', '0', 'yC');
            f.AddGuide('p3', 9, 'p1', 'p2', '0');
            f.AddGuide('p4', 0, 'p3', '1', '2');
            f.AddGuide('p5', 1, 'p4', '0', 'thh');
            f.AddGuide('xGp', 3, 'p5', 'xF', 'xG');
            f.AddGuide('yGp', 3, 'p5', 'yF', 'yG');
            f.AddGuide('xBp', 3, 'p5', 'xC', 'xB');
            f.AddGuide('yBp', 3, 'p5', 'yC', 'yB');
            f.AddGuide('en0', 5, 'sdxF', 'sdyF');
            f.AddGuide('en1', 1, 'en0', '21600000', '0');
            f.AddGuide('en2', 3, 'en0', 'en0', 'en1');
            f.AddGuide('sw0', 1, 'en2', '0', 'stAng');
            f.AddGuide('sw1', 1, 'sw0', '0', '21600000');
            f.AddGuide('swAng', 3, 'sw0', 'sw1', 'sw0');
            f.AddGuide('stAng0', 1, 'stAng', 'swAng', '0');
            f.AddGuide('swAng0', 1, '0', '0', 'swAng');
            f.AddGuide('wtI', 12, 'rw3', 'stAng');
            f.AddGuide('htI', 7, 'rh3', 'stAng');
            f.AddGuide('dxI', 6, 'rw3', 'htI', 'wtI');
            f.AddGuide('dyI', 11, 'rh3', 'htI', 'wtI');
            f.AddGuide('xI', 1, 'hc', 'dxI', '0');
            f.AddGuide('yI', 1, 'vc', 'dyI', '0');
            f.AddGuide('aI', 1, 'stAng', 'cd4', '0');
            f.AddGuide('aA', 1, 'ptAng', '0', 'cd4');
            f.AddGuide('aB', 1, 'ptAng', 'cd2', '0');
            f.AddGuide('idx', 7, 'rw1', '2700000');
            f.AddGuide('idy', 12, 'rh1', '2700000');
            f.AddGuide('il', 1, 'hc', '0', 'idx');
            f.AddGuide('ir', 1, 'hc', 'idx', '0');
            f.AddGuide('it', 1, 'vc', '0', 'idy');
            f.AddGuide('ib', 1, 'vc', 'idy', '0');
            f.AddHandlePolar('adj2','minAng','0', undefined, '0', '0', 'xA', 'yA');
            f.AddHandlePolar('adj4','0','21599999', undefined, '0', '0', 'xE', 'yE');
            f.AddHandlePolar(undefined, '0', '0','adj1','0','maxAdj1', 'xF', 'yF');
            f.AddHandlePolar(undefined, '0', '0','adj5','0','25000', 'xB', 'yB');
            f.AddCnx('aI', 'xI', 'yI');
            f.AddCnx('ptAng', 'xGp', 'yGp');
            f.AddCnx('aA', 'xA', 'yA');
            f.AddCnx('aB', 'xBp', 'yBp');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'xE', 'yE');
            f.AddPathCommand(2, 'xD', 'yD');
            f.AddPathCommand(3, 'rw2', 'rh2', 'istAng', 'iswAng');
            f.AddPathCommand(2, 'xBp', 'yBp');
            f.AddPathCommand(2, 'xA', 'yA');
            f.AddPathCommand(2, 'xGp', 'yGp');
            f.AddPathCommand(2, 'xF', 'yF');
            f.AddPathCommand(3, 'rw1', 'rh1', 'stAng0', 'swAng0');
            f.AddPathCommand(6);
            break;
        }
        case 'leftRightArrow':{
            f.AddAdj('adj1', 15, '50000');
            f.AddAdj('adj2', 15, '50000');
            f.AddGuide('maxAdj2', 0, '50000', 'w', 'ss');
            f.AddGuide('a1', 10, '0', 'adj1', '100000');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('x2', 0, 'ss', 'a2', '100000');
            f.AddGuide('x3', 1, 'r', '0', 'x2');
            f.AddGuide('dy', 0, 'h', 'a1', '200000');
            f.AddGuide('y1', 1, 'vc', '0', 'dy');
            f.AddGuide('y2', 1, 'vc', 'dy', '0');
            f.AddGuide('dx1', 0, 'y1', 'x2', 'hd2');
            f.AddGuide('x1', 1, 'x2', '0', 'dx1');
            f.AddGuide('x4', 1, 'x3', 'dx1', '0');
            f.AddHandleXY(undefined, '0', '0','adj1','0','100000', 'x3', 'y1');
            f.AddHandleXY('adj2','0','maxAdj2', undefined, '0', '0', 'x2', 't');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'x3', 'b');
            f.AddCnx('cd4', 'x2', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'x2', 't');
            f.AddCnx('_3cd4', 'x3', 't');
            f.AddRect('x1', 'y1', 'x4', 'y2');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(2, 'x2', 'y1');
            f.AddPathCommand(2, 'x3', 'y1');
            f.AddPathCommand(2, 'x3', 't');
            f.AddPathCommand(2, 'r', 'vc');
            f.AddPathCommand(2, 'x3', 'b');
            f.AddPathCommand(2, 'x3', 'y2');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x2', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'leftRightArrowCallout':{
            f.AddAdj('adj1', 15, '25000');
            f.AddAdj('adj2', 15, '25000');
            f.AddAdj('adj3', 15, '25000');
            f.AddAdj('adj4', 15, '48123');
            f.AddGuide('maxAdj2', 0, '50000', 'h', 'ss');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('maxAdj1', 0, 'a2', '2', '1');
            f.AddGuide('a1', 10, '0', 'adj1', 'maxAdj1');
            f.AddGuide('maxAdj3', 0, '50000', 'w', 'ss');
            f.AddGuide('a3', 10, '0', 'adj3', 'maxAdj3');
            f.AddGuide('q2', 0, 'a3', 'ss', 'wd2');
            f.AddGuide('maxAdj4', 1, '100000', '0', 'q2');
            f.AddGuide('a4', 10, '0', 'adj4', 'maxAdj4');
            f.AddGuide('dy1', 0, 'ss', 'a2', '100000');
            f.AddGuide('dy2', 0, 'ss', 'a1', '200000');
            f.AddGuide('y1', 1, 'vc', '0', 'dy1');
            f.AddGuide('y2', 1, 'vc', '0', 'dy2');
            f.AddGuide('y3', 1, 'vc', 'dy2', '0');
            f.AddGuide('y4', 1, 'vc', 'dy1', '0');
            f.AddGuide('x1', 0, 'ss', 'a3', '100000');
            f.AddGuide('x4', 1, 'r', '0', 'x1');
            f.AddGuide('dx2', 0, 'w', 'a4', '200000');
            f.AddGuide('x2', 1, 'hc', '0', 'dx2');
            f.AddGuide('x3', 1, 'hc', 'dx2', '0');
            f.AddHandleXY(undefined, '0', '0','adj1','0','maxAdj1', 'x1', 'y2');
            f.AddHandleXY(undefined, '0', '0','adj2','0','maxAdj2', 'l', 'y1');
            f.AddHandleXY('adj3','0','maxAdj3', undefined, '0', '0', 'x1', 't');
            f.AddHandleXY('adj4','0','maxAdj4', undefined, '0', '0', 'x2', 'b');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('x2', 't', 'x3', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(2, 'x1', 'y1');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(2, 'x3', 't');
            f.AddPathCommand(2, 'x3', 'y2');
            f.AddPathCommand(2, 'x4', 'y2');
            f.AddPathCommand(2, 'x4', 'y1');
            f.AddPathCommand(2, 'r', 'vc');
            f.AddPathCommand(2, 'x4', 'y4');
            f.AddPathCommand(2, 'x4', 'y3');
            f.AddPathCommand(2, 'x3', 'y3');
            f.AddPathCommand(2, 'x3', 'b');
            f.AddPathCommand(2, 'x2', 'b');
            f.AddPathCommand(2, 'x2', 'y3');
            f.AddPathCommand(2, 'x1', 'y3');
            f.AddPathCommand(2, 'x1', 'y4');
            f.AddPathCommand(6);
            break;
        }
        case 'leftRightCircularArrow':{
            f.AddAdj('adj1', 15, '12500');
            f.AddAdj('adj2', 15, '1142319');
            f.AddAdj('adj3', 15, '20457681');
            f.AddAdj('adj4', 15, '11942319');
            f.AddAdj('adj5', 15, '12500');
            f.AddGuide('a5', 10, '0', 'adj5', '25000');
            f.AddGuide('maxAdj1', 0, 'a5', '2', '1');
            f.AddGuide('a1', 10, '0', 'adj1', 'maxAdj1');
            f.AddGuide('enAng', 10, '1', 'adj3', '21599999');
            f.AddGuide('stAng', 10, '0', 'adj4', '21599999');
            f.AddGuide('th', 0, 'ss', 'a1', '100000');
            f.AddGuide('thh', 0, 'ss', 'a5', '100000');
            f.AddGuide('th2', 0, 'th', '1', '2');
            f.AddGuide('rw1', 1, 'wd2', 'th2', 'thh');
            f.AddGuide('rh1', 1, 'hd2', 'th2', 'thh');
            f.AddGuide('rw2', 1, 'rw1', '0', 'th');
            f.AddGuide('rh2', 1, 'rh1', '0', 'th');
            f.AddGuide('rw3', 1, 'rw2', 'th2', '0');
            f.AddGuide('rh3', 1, 'rh2', 'th2', '0');
            f.AddGuide('wtH', 12, 'rw3', 'enAng');
            f.AddGuide('htH', 7, 'rh3', 'enAng');
            f.AddGuide('dxH', 6, 'rw3', 'htH', 'wtH');
            f.AddGuide('dyH', 11, 'rh3', 'htH', 'wtH');
            f.AddGuide('xH', 1, 'hc', 'dxH', '0');
            f.AddGuide('yH', 1, 'vc', 'dyH', '0');
            f.AddGuide('rI', 16, 'rw2', 'rh2');
            f.AddGuide('u1', 0, 'dxH', 'dxH', '1');
            f.AddGuide('u2', 0, 'dyH', 'dyH', '1');
            f.AddGuide('u3', 0, 'rI', 'rI', '1');
            f.AddGuide('u4', 1, 'u1', '0', 'u3');
            f.AddGuide('u5', 1, 'u2', '0', 'u3');
            f.AddGuide('u6', 0, 'u4', 'u5', 'u1');
            f.AddGuide('u7', 0, 'u6', '1', 'u2');
            f.AddGuide('u8', 1, '1', '0', 'u7');
            f.AddGuide('u9', 13, 'u8');
            f.AddGuide('u10', 0, 'u4', '1', 'dxH');
            f.AddGuide('u11', 0, 'u10', '1', 'dyH');
            f.AddGuide('u12', 2, '1', 'u9', 'u11');
            f.AddGuide('u13', 5, '1', 'u12');
            f.AddGuide('u14', 1, 'u13', '21600000', '0');
            f.AddGuide('u15', 3, 'u13', 'u13', 'u14');
            f.AddGuide('u16', 1, 'u15', '0', 'enAng');
            f.AddGuide('u17', 1, 'u16', '21600000', '0');
            f.AddGuide('u18', 3, 'u16', 'u16', 'u17');
            f.AddGuide('u19', 1, 'u18', '0', 'cd2');
            f.AddGuide('u20', 1, 'u18', '0', '21600000');
            f.AddGuide('u21', 3, 'u19', 'u20', 'u18');
            f.AddGuide('maxAng', 4, 'u21');
            f.AddGuide('aAng', 10, '0', 'adj2', 'maxAng');
            f.AddGuide('ptAng', 1, 'enAng', 'aAng', '0');
            f.AddGuide('wtA', 12, 'rw3', 'ptAng');
            f.AddGuide('htA', 7, 'rh3', 'ptAng');
            f.AddGuide('dxA', 6, 'rw3', 'htA', 'wtA');
            f.AddGuide('dyA', 11, 'rh3', 'htA', 'wtA');
            f.AddGuide('xA', 1, 'hc', 'dxA', '0');
            f.AddGuide('yA', 1, 'vc', 'dyA', '0');
            f.AddGuide('dxG', 7, 'thh', 'ptAng');
            f.AddGuide('dyG', 12, 'thh', 'ptAng');
            f.AddGuide('xG', 1, 'xH', 'dxG', '0');
            f.AddGuide('yG', 1, 'yH', 'dyG', '0');
            f.AddGuide('dxB', 7, 'thh', 'ptAng');
            f.AddGuide('dyB', 12, 'thh', 'ptAng');
            f.AddGuide('xB', 1, 'xH', '0', 'dxB', '0');
            f.AddGuide('yB', 1, 'yH', '0', 'dyB', '0');
            f.AddGuide('sx1', 1, 'xB', '0', 'hc');
            f.AddGuide('sy1', 1, 'yB', '0', 'vc');
            f.AddGuide('sx2', 1, 'xG', '0', 'hc');
            f.AddGuide('sy2', 1, 'yG', '0', 'vc');
            f.AddGuide('rO', 16, 'rw1', 'rh1');
            f.AddGuide('x1O', 0, 'sx1', 'rO', 'rw1');
            f.AddGuide('y1O', 0, 'sy1', 'rO', 'rh1');
            f.AddGuide('x2O', 0, 'sx2', 'rO', 'rw1');
            f.AddGuide('y2O', 0, 'sy2', 'rO', 'rh1');
            f.AddGuide('dxO', 1, 'x2O', '0', 'x1O');
            f.AddGuide('dyO', 1, 'y2O', '0', 'y1O');
            f.AddGuide('dO', 9, 'dxO', 'dyO', '0');
            f.AddGuide('q1', 0, 'x1O', 'y2O', '1');
            f.AddGuide('q2', 0, 'x2O', 'y1O', '1');
            f.AddGuide('DO', 1, 'q1', '0', 'q2');
            f.AddGuide('q3', 0, 'rO', 'rO', '1');
            f.AddGuide('q4', 0, 'dO', 'dO', '1');
            f.AddGuide('q5', 0, 'q3', 'q4', '1');
            f.AddGuide('q6', 0, 'DO', 'DO', '1');
            f.AddGuide('q7', 1, 'q5', '0', 'q6');
            f.AddGuide('q8', 8, 'q7', '0');
            f.AddGuide('sdelO', 13, 'q8');
            f.AddGuide('ndyO', 0, 'dyO', '-1', '1');
            f.AddGuide('sdyO', 3, 'ndyO', '-1', '1');
            f.AddGuide('q9', 0, 'sdyO', 'dxO', '1');
            f.AddGuide('q10', 0, 'q9', 'sdelO', '1');
            f.AddGuide('q11', 0, 'DO', 'dyO', '1');
            f.AddGuide('dxF1', 2, 'q11', 'q10', 'q4');
            f.AddGuide('q12', 1, 'q11', '0', 'q10');
            f.AddGuide('dxF2', 0, 'q12', '1', 'q4');
            f.AddGuide('adyO', 4, 'dyO');
            f.AddGuide('q13', 0, 'adyO', 'sdelO', '1');
            f.AddGuide('q14', 0, 'DO', 'dxO', '-1');
            f.AddGuide('dyF1', 2, 'q14', 'q13', 'q4');
            f.AddGuide('q15', 1, 'q14', '0', 'q13');
            f.AddGuide('dyF2', 0, 'q15', '1', 'q4');
            f.AddGuide('q16', 1, 'x2O', '0', 'dxF1');
            f.AddGuide('q17', 1, 'x2O', '0', 'dxF2');
            f.AddGuide('q18', 1, 'y2O', '0', 'dyF1');
            f.AddGuide('q19', 1, 'y2O', '0', 'dyF2');
            f.AddGuide('q20', 9, 'q16', 'q18', '0');
            f.AddGuide('q21', 9, 'q17', 'q19', '0');
            f.AddGuide('q22', 1, 'q21', '0', 'q20');
            f.AddGuide('dxF', 3, 'q22', 'dxF1', 'dxF2');
            f.AddGuide('dyF', 3, 'q22', 'dyF1', 'dyF2');
            f.AddGuide('sdxF', 0, 'dxF', 'rw1', 'rO');
            f.AddGuide('sdyF', 0, 'dyF', 'rh1', 'rO');
            f.AddGuide('xF', 1, 'hc', 'sdxF', '0');
            f.AddGuide('yF', 1, 'vc', 'sdyF', '0');
            f.AddGuide('x1I', 0, 'sx1', 'rI', 'rw2');
            f.AddGuide('y1I', 0, 'sy1', 'rI', 'rh2');
            f.AddGuide('x2I', 0, 'sx2', 'rI', 'rw2');
            f.AddGuide('y2I', 0, 'sy2', 'rI', 'rh2');
            f.AddGuide('dxI', 1, 'x2I', '0', 'x1I');
            f.AddGuide('dyI', 1, 'y2I', '0', 'y1I');
            f.AddGuide('dI', 9, 'dxI', 'dyI', '0');
            f.AddGuide('v1', 0, 'x1I', 'y2I', '1');
            f.AddGuide('v2', 0, 'x2I', 'y1I', '1');
            f.AddGuide('DI', 1, 'v1', '0', 'v2');
            f.AddGuide('v3', 0, 'rI', 'rI', '1');
            f.AddGuide('v4', 0, 'dI', 'dI', '1');
            f.AddGuide('v5', 0, 'v3', 'v4', '1');
            f.AddGuide('v6', 0, 'DI', 'DI', '1');
            f.AddGuide('v7', 1, 'v5', '0', 'v6');
            f.AddGuide('v8', 8, 'v7', '0');
            f.AddGuide('sdelI', 13, 'v8');
            f.AddGuide('v9', 0, 'sdyO', 'dxI', '1');
            f.AddGuide('v10', 0, 'v9', 'sdelI', '1');
            f.AddGuide('v11', 0, 'DI', 'dyI', '1');
            f.AddGuide('dxC1', 2, 'v11', 'v10', 'v4');
            f.AddGuide('v12', 1, 'v11', '0', 'v10');
            f.AddGuide('dxC2', 0, 'v12', '1', 'v4');
            f.AddGuide('adyI', 4, 'dyI');
            f.AddGuide('v13', 0, 'adyI', 'sdelI', '1');
            f.AddGuide('v14', 0, 'DI', 'dxI', '-1');
            f.AddGuide('dyC1', 2, 'v14', 'v13', 'v4');
            f.AddGuide('v15', 1, 'v14', '0', 'v13');
            f.AddGuide('dyC2', 0, 'v15', '1', 'v4');
            f.AddGuide('v16', 1, 'x1I', '0', 'dxC1');
            f.AddGuide('v17', 1, 'x1I', '0', 'dxC2');
            f.AddGuide('v18', 1, 'y1I', '0', 'dyC1');
            f.AddGuide('v19', 1, 'y1I', '0', 'dyC2');
            f.AddGuide('v20', 9, 'v16', 'v18', '0');
            f.AddGuide('v21', 9, 'v17', 'v19', '0');
            f.AddGuide('v22', 1, 'v21', '0', 'v20');
            f.AddGuide('dxC', 3, 'v22', 'dxC1', 'dxC2');
            f.AddGuide('dyC', 3, 'v22', 'dyC1', 'dyC2');
            f.AddGuide('sdxC', 0, 'dxC', 'rw2', 'rI');
            f.AddGuide('sdyC', 0, 'dyC', 'rh2', 'rI');
            f.AddGuide('xC', 1, 'hc', 'sdxC', '0');
            f.AddGuide('yC', 1, 'vc', 'sdyC', '0');
            f.AddGuide('wtI', 12, 'rw3', 'stAng');
            f.AddGuide('htI', 7, 'rh3', 'stAng');
            f.AddGuide('dxI', 6, 'rw3', 'htI', 'wtI');
            f.AddGuide('dyI', 11, 'rh3', 'htI', 'wtI');
            f.AddGuide('xI', 1, 'hc', 'dxI', '0');
            f.AddGuide('yI', 1, 'vc', 'dyI', '0');
            f.AddGuide('lptAng', 1, 'stAng', '0', 'aAng');
            f.AddGuide('wtL', 12, 'rw3', 'lptAng');
            f.AddGuide('htL', 7, 'rh3', 'lptAng');
            f.AddGuide('dxL', 6, 'rw3', 'htL', 'wtL');
            f.AddGuide('dyL', 11, 'rh3', 'htL', 'wtL');
            f.AddGuide('xL', 1, 'hc', 'dxL', '0');
            f.AddGuide('yL', 1, 'vc', 'dyL', '0');
            f.AddGuide('dxK', 7, 'thh', 'lptAng');
            f.AddGuide('dyK', 12, 'thh', 'lptAng');
            f.AddGuide('xK', 1, 'xI', 'dxK', '0');
            f.AddGuide('yK', 1, 'yI', 'dyK', '0');
            f.AddGuide('dxJ', 7, 'thh', 'lptAng');
            f.AddGuide('dyJ', 12, 'thh', 'lptAng');
            f.AddGuide('xJ', 1, 'xI', '0', 'dxJ', '0');
            f.AddGuide('yJ', 1, 'yI', '0', 'dyJ', '0');
            f.AddGuide('p1', 1, 'xF', '0', 'xC');
            f.AddGuide('p2', 1, 'yF', '0', 'yC');
            f.AddGuide('p3', 9, 'p1', 'p2', '0');
            f.AddGuide('p4', 0, 'p3', '1', '2');
            f.AddGuide('p5', 1, 'p4', '0', 'thh');
            f.AddGuide('xGp', 3, 'p5', 'xF', 'xG');
            f.AddGuide('yGp', 3, 'p5', 'yF', 'yG');
            f.AddGuide('xBp', 3, 'p5', 'xC', 'xB');
            f.AddGuide('yBp', 3, 'p5', 'yC', 'yB');
            f.AddGuide('en0', 5, 'sdxF', 'sdyF');
            f.AddGuide('en1', 1, 'en0', '21600000', '0');
            f.AddGuide('en2', 3, 'en0', 'en0', 'en1');
            f.AddGuide('od0', 1, 'en2', '0', 'enAng');
            f.AddGuide('od1', 1, 'od0', '21600000', '0');
            f.AddGuide('od2', 3, 'od0', 'od0', 'od1');
            f.AddGuide('st0', 1, 'stAng', '0', 'od2');
            f.AddGuide('st1', 1, 'st0', '21600000', '0');
            f.AddGuide('st2', 3, 'st0', 'st0', 'st1');
            f.AddGuide('sw0', 1, 'en2', '0', 'st2');
            f.AddGuide('sw1', 1, 'sw0', '21600000', '0');
            f.AddGuide('swAng', 3, 'sw0', 'sw0', 'sw1');
            f.AddGuide('ist0', 5, 'sdxC', 'sdyC');
            f.AddGuide('ist1', 1, 'ist0', '21600000', '0');
            f.AddGuide('istAng', 3, 'ist0', 'ist0', 'ist1');
            f.AddGuide('id0', 1, 'istAng', '0', 'enAng');
            f.AddGuide('id1', 1, 'id0', '0', '21600000');
            f.AddGuide('id2', 3, 'id0', 'id1', 'id0');
            f.AddGuide('ien0', 1, 'stAng', '0', 'id2');
            f.AddGuide('ien1', 1, 'ien0', '0', '21600000');
            f.AddGuide('ien2', 3, 'ien1', 'ien1', 'ien0');
            f.AddGuide('isw1', 1, 'ien2', '0', 'istAng');
            f.AddGuide('isw2', 1, 'isw1', '0', '21600000');
            f.AddGuide('iswAng', 3, 'isw1', 'isw2', 'isw1');
            f.AddGuide('wtE', 12, 'rw1', 'st2');
            f.AddGuide('htE', 7, 'rh1', 'st2');
            f.AddGuide('dxE', 6, 'rw1', 'htE', 'wtE');
            f.AddGuide('dyE', 11, 'rh1', 'htE', 'wtE');
            f.AddGuide('xE', 1, 'hc', 'dxE', '0');
            f.AddGuide('yE', 1, 'vc', 'dyE', '0');
            f.AddGuide('wtD', 12, 'rw2', 'ien2');
            f.AddGuide('htD', 7, 'rh2', 'ien2');
            f.AddGuide('dxD', 6, 'rw2', 'htD', 'wtD');
            f.AddGuide('dyD', 11, 'rh2', 'htD', 'wtD');
            f.AddGuide('xD', 1, 'hc', 'dxD', '0');
            f.AddGuide('yD', 1, 'vc', 'dyD', '0');
            f.AddGuide('xKp', 3, 'p5', 'xE', 'xK');
            f.AddGuide('yKp', 3, 'p5', 'yE', 'yK');
            f.AddGuide('xJp', 3, 'p5', 'xD', 'xJ');
            f.AddGuide('yJp', 3, 'p5', 'yD', 'yJ');
            f.AddGuide('aL', 1, 'lptAng', '0', 'cd4');
            f.AddGuide('aA', 1, 'ptAng', 'cd4', '0');
            f.AddGuide('aB', 1, 'ptAng', 'cd2', '0');
            f.AddGuide('aJ', 1, 'lptAng', 'cd2', '0');
            f.AddGuide('idx', 7, 'rw1', '2700000');
            f.AddGuide('idy', 12, 'rh1', '2700000');
            f.AddGuide('il', 1, 'hc', '0', 'idx');
            f.AddGuide('ir', 1, 'hc', 'idx', '0');
            f.AddGuide('it', 1, 'vc', '0', 'idy');
            f.AddGuide('ib', 1, 'vc', 'idy', '0');
            f.AddHandlePolar('adj2','0','maxAng', undefined, '0', '0', 'xA', 'yA');
            f.AddHandlePolar('adj4','0','21599999', undefined, '0', '0', 'xE', 'yE');
            f.AddHandlePolar(undefined, '0', '0','adj1','0','maxAdj1', 'xF', 'yF');
            f.AddHandlePolar(undefined, '0', '0','adj5','0','25000', 'xB', 'yB');
            f.AddCnx('aL', 'xL', 'yL');
            f.AddCnx('lptAng', 'xKp', 'yKp');
            f.AddCnx('ptAng', 'xGp', 'yGp');
            f.AddCnx('aA', 'xA', 'yA');
            f.AddCnx('aB', 'xBp', 'yBp');
            f.AddCnx('aJ', 'xJp', 'yJp');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'xL', 'yL');
            f.AddPathCommand(2, 'xKp', 'yKp');
            f.AddPathCommand(2, 'xE', 'yE');
            f.AddPathCommand(3, 'rw1', 'rh1', 'st2', 'swAng');
            f.AddPathCommand(2, 'xGp', 'yGp');
            f.AddPathCommand(2, 'xA', 'yA');
            f.AddPathCommand(2, 'xBp', 'yBp');
            f.AddPathCommand(2, 'xC', 'yC');
            f.AddPathCommand(3, 'rw2', 'rh2', 'istAng', 'iswAng');
            f.AddPathCommand(2, 'xJp', 'yJp');
            f.AddPathCommand(6);
            break;
        }
        case 'leftRightRibbon':{
            f.AddAdj('adj1', 15, '50000');
            f.AddAdj('adj2', 15, '50000');
            f.AddAdj('adj3', 15, '16667');
            f.AddGuide('a3', 10, '0', 'adj3', '33333');
            f.AddGuide('maxAdj1', 1, '100000', '0', 'a3');
            f.AddGuide('a1', 10, '0', 'adj1', 'maxAdj1');
            f.AddGuide('w1', 1, 'wd2', '0', 'wd32');
            f.AddGuide('maxAdj2', 0, '100000', 'w1', 'ss');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('x1', 0, 'ss', 'a2', '100000');
            f.AddGuide('x4', 1, 'r', '0', 'x1');
            f.AddGuide('dy1', 0, 'h', 'a1', '200000');
            f.AddGuide('dy2', 0, 'h', 'a3', '-200000');
            f.AddGuide('ly1', 1, 'vc', 'dy2', 'dy1');
            f.AddGuide('ry4', 1, 'vc', 'dy1', 'dy2');
            f.AddGuide('ly2', 1, 'ly1', 'dy1', '0');
            f.AddGuide('ry3', 1, 'b', '0', 'ly2');
            f.AddGuide('ly4', 0, 'ly2', '2', '1');
            f.AddGuide('ry1', 1, 'b', '0', 'ly4');
            f.AddGuide('ly3', 1, 'ly4', '0', 'ly1');
            f.AddGuide('ry2', 1, 'b', '0', 'ly3');
            f.AddGuide('hR', 0, 'a3', 'ss', '400000');
            f.AddGuide('x2', 1, 'hc', '0', 'wd32');
            f.AddGuide('x3', 1, 'hc', 'wd32', '0');
            f.AddGuide('y1', 1, 'ly1', 'hR', '0');
            f.AddGuide('y2', 1, 'ry2', '0', 'hR');
            f.AddHandleXY(undefined, '0', '0','adj1','0','maxAdj1', 'x4', 'ry2');
            f.AddHandleXY('adj2','0','maxAdj2', undefined, '0', '0', 'x1', 't');
            f.AddHandleXY(undefined, '0', '0','adj3','0','33333', 'x3', 'ry2');
            f.AddCnx('0', 'r', 'ry3');
            f.AddCnx('cd4', 'x4', 'b');
            f.AddCnx('cd4', 'x1', 'ly4');
            f.AddCnx('cd2', 'l', 'ly2');
            f.AddCnx('_3cd4', 'x1', 't');
            f.AddCnx('_3cd4', 'x4', 'ry1');
            f.AddRect('x1', 'ly1', 'x4', 'ry4');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 'ly2');
            f.AddPathCommand(2, 'x1', 't');
            f.AddPathCommand(2, 'x1', 'ly1');
            f.AddPathCommand(2, 'hc', 'ly1');
            f.AddPathCommand(3, 'wd32', 'hR', '_3cd4', 'cd2');
            f.AddPathCommand(3, 'wd32', 'hR', '_3cd4', '-10800000');
            f.AddPathCommand(2, 'x4', 'ry2');
            f.AddPathCommand(2, 'x4', 'ry1');
            f.AddPathCommand(2, 'r', 'ry3');
            f.AddPathCommand(2, 'x4', 'b');
            f.AddPathCommand(2, 'x4', 'ry4');
            f.AddPathCommand(2, 'hc', 'ry4');
            f.AddPathCommand(3, 'wd32', 'hR', 'cd4', 'cd4');
            f.AddPathCommand(2, 'x2', 'ly3');
            f.AddPathCommand(2, 'x1', 'ly3');
            f.AddPathCommand(2, 'x1', 'ly4');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darkenLess', false, undefined, undefined);
            f.AddPathCommand(1, 'x3', 'y1');
            f.AddPathCommand(3, 'wd32', 'hR', '0', 'cd4');
            f.AddPathCommand(3, 'wd32', 'hR', '_3cd4', '-10800000');
            f.AddPathCommand(2, 'x3', 'ry2');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'ly2');
            f.AddPathCommand(2, 'x1', 't');
            f.AddPathCommand(2, 'x1', 'ly1');
            f.AddPathCommand(2, 'hc', 'ly1');
            f.AddPathCommand(3, 'wd32', 'hR', '_3cd4', 'cd2');
            f.AddPathCommand(3, 'wd32', 'hR', '_3cd4', '-10800000');
            f.AddPathCommand(2, 'x4', 'ry2');
            f.AddPathCommand(2, 'x4', 'ry1');
            f.AddPathCommand(2, 'r', 'ry3');
            f.AddPathCommand(2, 'x4', 'b');
            f.AddPathCommand(2, 'x4', 'ry4');
            f.AddPathCommand(2, 'hc', 'ry4');
            f.AddPathCommand(3, 'wd32', 'hR', 'cd4', 'cd4');
            f.AddPathCommand(2, 'x2', 'ly3');
            f.AddPathCommand(2, 'x1', 'ly3');
            f.AddPathCommand(2, 'x1', 'ly4');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'x3', 'y1');
            f.AddPathCommand(2, 'x3', 'ry2');
            f.AddPathCommand(1, 'x2', 'y2');
            f.AddPathCommand(2, 'x2', 'ly3');
            break;
        }
        case 'leftRightUpArrow':{
            f.AddAdj('adj1', 15, '25000');
            f.AddAdj('adj2', 15, '25000');
            f.AddAdj('adj3', 15, '25000');
            f.AddGuide('a2', 10, '0', 'adj2', '50000');
            f.AddGuide('maxAdj1', 0, 'a2', '2', '1');
            f.AddGuide('a1', 10, '0', 'adj1', 'maxAdj1');
            f.AddGuide('q1', 1, '100000', '0', 'maxAdj1');
            f.AddGuide('maxAdj3', 0, 'q1', '1', '2');
            f.AddGuide('a3', 10, '0', 'adj3', 'maxAdj3');
            f.AddGuide('x1', 0, 'ss', 'a3', '100000');
            f.AddGuide('dx2', 0, 'ss', 'a2', '100000');
            f.AddGuide('x2', 1, 'hc', '0', 'dx2');
            f.AddGuide('x5', 1, 'hc', 'dx2', '0');
            f.AddGuide('dx3', 0, 'ss', 'a1', '200000');
            f.AddGuide('x3', 1, 'hc', '0', 'dx3');
            f.AddGuide('x4', 1, 'hc', 'dx3', '0');
            f.AddGuide('x6', 1, 'r', '0', 'x1');
            f.AddGuide('dy2', 0, 'ss', 'a2', '50000');
            f.AddGuide('y2', 1, 'b', '0', 'dy2');
            f.AddGuide('y4', 1, 'b', '0', 'dx2');
            f.AddGuide('y3', 1, 'y4', '0', 'dx3');
            f.AddGuide('y5', 1, 'y4', 'dx3', '0');
            f.AddGuide('il', 0, 'dx3', 'x1', 'dx2');
            f.AddGuide('ir', 1, 'r', '0', 'il');
            f.AddHandleXY('adj1','0','maxAdj1', undefined, '0', '0', 'x3', 'x1');
            f.AddHandleXY('adj2','0','50000', undefined, '0', '0', 'x2', 't');
            f.AddHandleXY(undefined, '0', '0','adj3','0','maxAdj3', 'r', 'x1');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'y4');
            f.AddCnx('cd4', 'hc', 'y5');
            f.AddCnx('0', 'r', 'y4');
            f.AddRect('il', 'y3', 'ir', 'y5');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y4');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(2, 'x1', 'y3');
            f.AddPathCommand(2, 'x3', 'y3');
            f.AddPathCommand(2, 'x3', 'x1');
            f.AddPathCommand(2, 'x2', 'x1');
            f.AddPathCommand(2, 'hc', 't');
            f.AddPathCommand(2, 'x5', 'x1');
            f.AddPathCommand(2, 'x4', 'x1');
            f.AddPathCommand(2, 'x4', 'y3');
            f.AddPathCommand(2, 'x6', 'y3');
            f.AddPathCommand(2, 'x6', 'y2');
            f.AddPathCommand(2, 'r', 'y4');
            f.AddPathCommand(2, 'x6', 'b');
            f.AddPathCommand(2, 'x6', 'y5');
            f.AddPathCommand(2, 'x1', 'y5');
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'leftUpArrow':{
            f.AddAdj('adj1', 15, '25000');
            f.AddAdj('adj2', 15, '25000');
            f.AddAdj('adj3', 15, '25000');
            f.AddGuide('a2', 10, '0', 'adj2', '50000');
            f.AddGuide('maxAdj1', 0, 'a2', '2', '1');
            f.AddGuide('a1', 10, '0', 'adj1', 'maxAdj1');
            f.AddGuide('maxAdj3', 1, '100000', '0', 'maxAdj1');
            f.AddGuide('a3', 10, '0', 'adj3', 'maxAdj3');
            f.AddGuide('x1', 0, 'ss', 'a3', '100000');
            f.AddGuide('dx2', 0, 'ss', 'a2', '50000');
            f.AddGuide('x2', 1, 'r', '0', 'dx2');
            f.AddGuide('y2', 1, 'b', '0', 'dx2');
            f.AddGuide('dx4', 0, 'ss', 'a2', '100000');
            f.AddGuide('x4', 1, 'r', '0', 'dx4');
            f.AddGuide('y4', 1, 'b', '0', 'dx4');
            f.AddGuide('dx3', 0, 'ss', 'a1', '200000');
            f.AddGuide('x3', 1, 'x4', '0', 'dx3');
            f.AddGuide('x5', 1, 'x4', 'dx3', '0');
            f.AddGuide('y3', 1, 'y4', '0', 'dx3');
            f.AddGuide('y5', 1, 'y4', 'dx3', '0');
            f.AddGuide('il', 0, 'dx3', 'x1', 'dx4');
            f.AddGuide('cx1', 2, 'x1', 'x5', '2');
            f.AddGuide('cy1', 2, 'x1', 'y5', '2');
            f.AddHandleXY(undefined, '0', '0','adj1','0','maxAdj1', 'x3', 'y3');
            f.AddHandleXY('adj2','0','50000', undefined, '0', '0', 'x2', 't');
            f.AddHandleXY(undefined, '0', '0','adj3','0','maxAdj3', 'x3', 'x1');
            f.AddCnx('_3cd4', 'x4', 't');
            f.AddCnx('cd2', 'x2', 'x1');
            f.AddCnx('_3cd4', 'x1', 'y2');
            f.AddCnx('cd2', 'l', 'y4');
            f.AddCnx('cd4', 'x1', 'b');
            f.AddCnx('cd4', 'cx1', 'y5');
            f.AddCnx('0', 'x5', 'cy1');
            f.AddCnx('0', 'r', 'x1');
            f.AddRect('il', 'y3', 'x4', 'y5');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y4');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(2, 'x1', 'y3');
            f.AddPathCommand(2, 'x3', 'y3');
            f.AddPathCommand(2, 'x3', 'x1');
            f.AddPathCommand(2, 'x2', 'x1');
            f.AddPathCommand(2, 'x4', 't');
            f.AddPathCommand(2, 'r', 'x1');
            f.AddPathCommand(2, 'x5', 'x1');
            f.AddPathCommand(2, 'x5', 'y5');
            f.AddPathCommand(2, 'x1', 'y5');
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'lightningBolt':{
            f.AddGuide('x1', 0, 'w', '5022', '21600');
            f.AddGuide('x3', 0, 'w', '8472', '21600');
            f.AddGuide('x4', 0, 'w', '8757', '21600');
            f.AddGuide('x5', 0, 'w', '10012', '21600');
            f.AddGuide('x8', 0, 'w', '12860', '21600');
            f.AddGuide('x9', 0, 'w', '13917', '21600');
            f.AddGuide('x11', 0, 'w', '16577', '21600');
            f.AddGuide('y1', 0, 'h', '3890', '21600');
            f.AddGuide('y2', 0, 'h', '6080', '21600');
            f.AddGuide('y4', 0, 'h', '7437', '21600');
            f.AddGuide('y6', 0, 'h', '9705', '21600');
            f.AddGuide('y7', 0, 'h', '12007', '21600');
            f.AddGuide('y10', 0, 'h', '14277', '21600');
            f.AddGuide('y11', 0, 'h', '14915', '21600');
            f.AddCnx('_3cd4', 'x3', 't');
            f.AddCnx('_3cd4', 'l', 'y1');
            f.AddCnx('cd2', 'x1', 'y6');
            f.AddCnx('cd2', 'x5', 'y11');
            f.AddCnx('cd4', 'r', 'b');
            f.AddCnx('0', 'x11', 'y7');
            f.AddCnx('0', 'x8', 'y2');
            f.AddRect('x4', 'y4', 'x9', 'y10');
            f.AddPathCommand(0,undefined, undefined, undefined, 21600, 21600);
            f.AddPathCommand(1, '8472', '0');
            f.AddPathCommand(2, '12860', '6080');
            f.AddPathCommand(2, '11050', '6797');
            f.AddPathCommand(2, '16577', '12007');
            f.AddPathCommand(2, '14767', '12877');
            f.AddPathCommand(2, '21600', '21600');
            f.AddPathCommand(2, '10012', '14915');
            f.AddPathCommand(2, '12222', '13987');
            f.AddPathCommand(2, '5022', '9705');
            f.AddPathCommand(2, '7602', '8382');
            f.AddPathCommand(2, '0', '3890');
            f.AddPathCommand(6);
            break;
        }
        case 'line':{
            f.AddCnx('cd4', 'l', 't');
            f.AddCnx('_3cd4', 'r', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 'b');
            break;
        }
        case 'lineInv':{
            f.AddCnx('cd4', 'l', 'b');
            f.AddCnx('_3cd4', 'r', 't');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'b');
            f.AddPathCommand(2, 'r', 't');
            break;
        }
        case 'mathDivide':{
            f.AddAdj('adj1', 15, '23520');
            f.AddAdj('adj2', 15, '5880');
            f.AddAdj('adj3', 15, '11760');
            f.AddGuide('a1', 10, '1000', 'adj1', '36745');
            f.AddGuide('ma1', 1, '0', '0', 'a1');
            f.AddGuide('ma3h', 2, '73490', 'ma1', '4');
            f.AddGuide('ma3w', 0, '36745', 'w', 'h');
            f.AddGuide('maxAdj3', 16, 'ma3h', 'ma3w');
            f.AddGuide('a3', 10, '1000', 'adj3', 'maxAdj3');
            f.AddGuide('m4a3', 0, '-4', 'a3', '1');
            f.AddGuide('maxAdj2', 1, '73490', 'm4a3', 'a1');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('dy1', 0, 'h', 'a1', '200000');
            f.AddGuide('yg', 0, 'h', 'a2', '100000');
            f.AddGuide('rad', 0, 'h', 'a3', '100000');
            f.AddGuide('dx1', 0, 'w', '73490', '200000');
            f.AddGuide('y3', 1, 'vc', '0', 'dy1');
            f.AddGuide('y4', 1, 'vc', 'dy1', '0');
            f.AddGuide('a', 1, 'yg', 'rad', '0');
            f.AddGuide('y2', 1, 'y3', '0', 'a');
            f.AddGuide('y1', 1, 'y2', '0', 'rad');
            f.AddGuide('y5', 1, 'b', '0', 'y1');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x3', 1, 'hc', 'dx1', '0');
            f.AddGuide('x2', 1, 'hc', '0', 'rad');
            f.AddHandleXY(undefined, '0', '0','adj1','1000','36745', 'l', 'y3');
            f.AddHandleXY(undefined, '0', '0','adj2','0','maxAdj2', 'r', 'y2');
            f.AddHandleXY('adj3','1000','maxAdj3', undefined, '0', '0', 'x2', 't');
            f.AddCnx('0', 'x3', 'vc');
            f.AddCnx('cd4', 'hc', 'y5');
            f.AddCnx('cd2', 'x1', 'vc');
            f.AddCnx('_3cd4', 'hc', 'y1');
            f.AddRect('x1', 'y3', 'x3', 'y4');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'hc', 'y1');
            f.AddPathCommand(3, 'rad', 'rad', '_3cd4', '21600000');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'hc', 'y5');
            f.AddPathCommand(3, 'rad', 'rad', 'cd4', '21600000');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'x1', 'y3');
            f.AddPathCommand(2, 'x3', 'y3');
            f.AddPathCommand(2, 'x3', 'y4');
            f.AddPathCommand(2, 'x1', 'y4');
            f.AddPathCommand(6);
            break;
        }
        case 'mathEqual':
        {
            f.AddAdj('adj1', 15, '23520');
            f.AddAdj('adj2', 15, '11760');
            f.AddGuide('a1', 10, '0', 'adj1', '36745');
            f.AddGuide('2a1', 0, 'a1', '2', '1');
            f.AddGuide('mAdj2', 1, '100000', '0', '2a1');
            f.AddGuide('a2', 10, '0', 'adj2', 'mAdj2');
            f.AddGuide('dy1', 0, 'h', 'a1', '100000');
            f.AddGuide('dy2', 0, 'h', 'a2', '200000');
            f.AddGuide('dx1', 0, 'w', '73490', '200000');
            f.AddGuide('y2', 1, 'vc', '0', 'dy2');
            f.AddGuide('y3', 1, 'vc', 'dy2', '0');
            f.AddGuide('y1', 1, 'y2', '0', 'dy1');
            f.AddGuide('y4', 1, 'y3', 'dy1', '0');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x2', 1, 'hc', 'dx1', '0');
            f.AddGuide('yC1', 2, 'y1', 'y2', '2');
            f.AddGuide('yC2', 2, 'y3', 'y4', '2');
            f.AddHandleXY(undefined, '0', '0','adj1','0','36745', 'l', 'y1');
            f.AddHandleXY(undefined, '0', '0','adj2','0','mAdj2', 'r', 'y2');
            f.AddCnx('0', 'x2', 'yC1');
            f.AddCnx('0', 'x2', 'yC2');
            f.AddCnx('cd4', 'hc', 'y4');
            f.AddCnx('cd2', 'x1', 'yC1');
            f.AddCnx('cd2', 'x1', 'yC2');
            f.AddCnx('_3cd4', 'hc', 'y1');
            f.AddRect('x1', 'y1', 'x2', 'y4');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(2, 'x2', 'y1');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'x1', 'y3');
            f.AddPathCommand(2, 'x2', 'y3');
            f.AddPathCommand(2, 'x2', 'y4');
            f.AddPathCommand(2, 'x1', 'y4');
            f.AddPathCommand(6);
            break;
        }
        case 'mathMinus':{
            f.AddAdj('adj1', 15, '23520');
            f.AddGuide('a1', 10, '0', 'adj1', '100000');
            f.AddGuide('dy1', 0, 'h', 'a1', '200000');
            f.AddGuide('dx1', 0, 'w', '73490', '200000');
            f.AddGuide('y1', 1, 'vc', '0', 'dy1');
            f.AddGuide('y2', 1, 'vc', 'dy1', '0');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x2', 1, 'hc', 'dx1', '0');
            f.AddHandleXY(undefined, '0', '0','adj1','0','100000', 'l', 'y1');
            f.AddCnx('0', 'x2', 'vc');
            f.AddCnx('cd4', 'hc', 'y2');
            f.AddCnx('cd2', 'x1', 'vc');
            f.AddCnx('_3cd4', 'hc', 'y1');
            f.AddRect('x1', 'y1', 'x2', 'y2');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(2, 'x2', 'y1');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(6);
            break;
        }
        case 'mathMultiply':{
            f.AddAdj('adj1', 15, '23520');
            f.AddGuide('a1', 10, '0', 'adj1', '51965');
            f.AddGuide('th', 0, 'ss', 'a1', '100000');
            f.AddGuide('a', 5, 'w', 'h');
            f.AddGuide('sa', 12, '1', 'a');
            f.AddGuide('ca', 7, '1', 'a');
            f.AddGuide('ta', 14, '1', 'a');
            f.AddGuide('dl', 9, 'w', 'h', '0');
            f.AddGuide('rw', 0, 'dl', '51965', '100000');
            f.AddGuide('lM', 1, 'dl', '0', 'rw');
            f.AddGuide('xM', 0, 'ca', 'lM', '2');
            f.AddGuide('yM', 0, 'sa', 'lM', '2');
            f.AddGuide('dxAM', 0, 'sa', 'th', '2');
            f.AddGuide('dyAM', 0, 'ca', 'th', '2');
            f.AddGuide('xA', 1, 'xM', '0', 'dxAM');
            f.AddGuide('yA', 1, 'yM', 'dyAM', '0');
            f.AddGuide('xB', 1, 'xM', 'dxAM', '0');
            f.AddGuide('yB', 1, 'yM', '0', 'dyAM');
            f.AddGuide('xBC', 1, 'hc', '0', 'xB');
            f.AddGuide('yBC', 0, 'xBC', 'ta', '1');
            f.AddGuide('yC', 1, 'yBC', 'yB', '0');
            f.AddGuide('xD', 1, 'r', '0', 'xB');
            f.AddGuide('xE', 1, 'r', '0', 'xA');
            f.AddGuide('yFE', 1, 'vc', '0', 'yA');
            f.AddGuide('xFE', 0, 'yFE', '1', 'ta');
            f.AddGuide('xF', 1, 'xE', '0', 'xFE');
            f.AddGuide('xL', 1, 'xA', 'xFE', '0');
            f.AddGuide('yG', 1, 'b', '0', 'yA');
            f.AddGuide('yH', 1, 'b', '0', 'yB');
            f.AddGuide('yI', 1, 'b', '0', 'yC');
            f.AddGuide('xC2', 1, 'r', '0', 'xM');
            f.AddGuide('yC3', 1, 'b', '0', 'yM');
            f.AddHandleXY(undefined, '0', '0','adj1','0','51965', 'l', 'th');
            f.AddCnx('cd2', 'xM', 'yM');
            f.AddCnx('_3cd4', 'xC2', 'yM');
            f.AddCnx('0', 'xC2', 'yC3');
            f.AddCnx('cd4', 'xM', 'yC3');
            f.AddRect('xA', 'yB', 'xE', 'yH');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'xA', 'yA');
            f.AddPathCommand(2, 'xB', 'yB');
            f.AddPathCommand(2, 'hc', 'yC');
            f.AddPathCommand(2, 'xD', 'yB');
            f.AddPathCommand(2, 'xE', 'yA');
            f.AddPathCommand(2, 'xF', 'vc');
            f.AddPathCommand(2, 'xE', 'yG');
            f.AddPathCommand(2, 'xD', 'yH');
            f.AddPathCommand(2, 'hc', 'yI');
            f.AddPathCommand(2, 'xB', 'yH');
            f.AddPathCommand(2, 'xA', 'yG');
            f.AddPathCommand(2, 'xL', 'vc');
            f.AddPathCommand(6);
            break;
        }
        case 'mathNotEqual':{
            f.AddAdj('adj1', 15, '23520');
            f.AddAdj('adj2', 15, '6600000');
            f.AddAdj('adj3', 15, '11760');
            f.AddGuide('a1', 10, '0', 'adj1', '50000');
            f.AddGuide('crAng', 10, '4200000', 'adj2', '6600000');
            f.AddGuide('2a1', 0, 'a1', '2', '1');
            f.AddGuide('maxAdj3', 1, '100000', '0', '2a1');
            f.AddGuide('a3', 10, '0', 'adj3', 'maxAdj3');
            f.AddGuide('dy1', 0, 'h', 'a1', '100000');
            f.AddGuide('dy2', 0, 'h', 'a3', '200000');
            f.AddGuide('dx1', 0, 'w', '73490', '200000');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x8', 1, 'hc', 'dx1', '0');
            f.AddGuide('y2', 1, 'vc', '0', 'dy2');
            f.AddGuide('y3', 1, 'vc', 'dy2', '0');
            f.AddGuide('y1', 1, 'y2', '0', 'dy1');
            f.AddGuide('y4', 1, 'y3', 'dy1', '0');
            f.AddGuide('cadj2', 1, 'crAng', '0', 'cd4');
            f.AddGuide('xadj2', 14, 'hd2', 'cadj2');
            f.AddGuide('len', 9, 'xadj2', 'hd2', '0');
            f.AddGuide('bhw', 0, 'len', 'dy1', 'hd2');
            f.AddGuide('bhw2', 0, 'bhw', '1', '2');
            f.AddGuide('x7', 1, 'hc', 'xadj2', 'bhw2');
            f.AddGuide('dx67', 0, 'xadj2', 'y1', 'hd2');
            f.AddGuide('x6', 1, 'x7', '0', 'dx67');
            f.AddGuide('dx57', 0, 'xadj2', 'y2', 'hd2');
            f.AddGuide('x5', 1, 'x7', '0', 'dx57');
            f.AddGuide('dx47', 0, 'xadj2', 'y3', 'hd2');
            f.AddGuide('x4', 1, 'x7', '0', 'dx47');
            f.AddGuide('dx37', 0, 'xadj2', 'y4', 'hd2');
            f.AddGuide('x3', 1, 'x7', '0', 'dx37');
            f.AddGuide('dx27', 0, 'xadj2', '2', '1');
            f.AddGuide('x2', 1, 'x7', '0', 'dx27');
            f.AddGuide('rx7', 1, 'x7', 'bhw', '0');
            f.AddGuide('rx6', 1, 'x6', 'bhw', '0');
            f.AddGuide('rx5', 1, 'x5', 'bhw', '0');
            f.AddGuide('rx4', 1, 'x4', 'bhw', '0');
            f.AddGuide('rx3', 1, 'x3', 'bhw', '0');
            f.AddGuide('rx2', 1, 'x2', 'bhw', '0');
            f.AddGuide('dx7', 0, 'dy1', 'hd2', 'len');
            f.AddGuide('rxt', 1, 'x7', 'dx7', '0');
            f.AddGuide('lxt', 1, 'rx7', '0', 'dx7');
            f.AddGuide('rx', 3, 'cadj2', 'rxt', 'rx7');
            f.AddGuide('lx', 3, 'cadj2', 'x7', 'lxt');
            f.AddGuide('dy3', 0, 'dy1', 'xadj2', 'len');
            f.AddGuide('dy4', 1, '0', '0', 'dy3');
            f.AddGuide('ry', 3, 'cadj2', 'dy3', 't');
            f.AddGuide('ly', 3, 'cadj2', 't', 'dy4');
            f.AddGuide('dlx', 1, 'w', '0', 'rx');
            f.AddGuide('drx', 1, 'w', '0', 'lx');
            f.AddGuide('dly', 1, 'h', '0', 'ry');
            f.AddGuide('dry', 1, 'h', '0', 'ly');
            f.AddGuide('xC1', 2, 'rx', 'lx', '2');
            f.AddGuide('xC2', 2, 'drx', 'dlx', '2');
            f.AddGuide('yC1', 2, 'ry', 'ly', '2');
            f.AddGuide('yC2', 2, 'y1', 'y2', '2');
            f.AddGuide('yC3', 2, 'y3', 'y4', '2');
            f.AddGuide('yC4', 2, 'dry', 'dly', '2');
            f.AddHandleXY(undefined, '0', '0','adj1','0','50000', 'l', 'y1');
            f.AddHandlePolar('adj2','4200000','6600000', undefined, '0', '0', 'lx', 't');
            f.AddHandleXY(undefined, '0', '0','adj3','0','maxAdj3', 'r', 'y2');
            f.AddCnx('0', 'x8', 'yC2');
            f.AddCnx('0', 'x8', 'yC3');
            f.AddCnx('cd4', 'xC2', 'yC4');
            f.AddCnx('cd2', 'x1', 'yC2');
            f.AddCnx('cd2', 'x1', 'yC3');
            f.AddCnx('_3cd4', 'xC1', 'yC1');
            f.AddRect('x1', 'y1', 'x8', 'y4');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(2, 'x6', 'y1');
            f.AddPathCommand(2, 'lx', 'ly');
            f.AddPathCommand(2, 'rx', 'ry');
            f.AddPathCommand(2, 'rx6', 'y1');
            f.AddPathCommand(2, 'x8', 'y1');
            f.AddPathCommand(2, 'x8', 'y2');
            f.AddPathCommand(2, 'rx5', 'y2');
            f.AddPathCommand(2, 'rx4', 'y3');
            f.AddPathCommand(2, 'x8', 'y3');
            f.AddPathCommand(2, 'x8', 'y4');
            f.AddPathCommand(2, 'rx3', 'y4');
            f.AddPathCommand(2, 'drx', 'dry');
            f.AddPathCommand(2, 'dlx', 'dly');
            f.AddPathCommand(2, 'x3', 'y4');
            f.AddPathCommand(2, 'x1', 'y4');
            f.AddPathCommand(2, 'x1', 'y3');
            f.AddPathCommand(2, 'x4', 'y3');
            f.AddPathCommand(2, 'x5', 'y2');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(6);
            break;
        }
        case 'mathPlus':{
            f.AddAdj('adj1', 15, '23520');
            f.AddGuide('a1', 10, '0', 'adj1', '73490');
            f.AddGuide('dx1', 0, 'w', '73490', '200000');
            f.AddGuide('dy1', 0, 'h', '73490', '200000');
            f.AddGuide('dx2', 0, 'ss', 'a1', '200000');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x2', 1, 'hc', '0', 'dx2');
            f.AddGuide('x3', 1, 'hc', 'dx2', '0');
            f.AddGuide('x4', 1, 'hc', 'dx1', '0');
            f.AddGuide('y1', 1, 'vc', '0', 'dy1');
            f.AddGuide('y2', 1, 'vc', '0', 'dx2');
            f.AddGuide('y3', 1, 'vc', 'dx2', '0');
            f.AddGuide('y4', 1, 'vc', 'dy1', '0');
            f.AddHandleXY(undefined, '0', '0','adj1','0','73490', 'l', 'y2');
            f.AddCnx('0', 'x4', 'vc');
            f.AddCnx('cd4', 'hc', 'y4');
            f.AddCnx('cd2', 'x1', 'vc');
            f.AddCnx('_3cd4', 'hc', 'y1');
            f.AddRect('x1', 'y2', 'x4', 'y3');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y2');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x2', 'y1');
            f.AddPathCommand(2, 'x3', 'y1');
            f.AddPathCommand(2, 'x3', 'y2');
            f.AddPathCommand(2, 'x4', 'y2');
            f.AddPathCommand(2, 'x4', 'y3');
            f.AddPathCommand(2, 'x3', 'y3');
            f.AddPathCommand(2, 'x3', 'y4');
            f.AddPathCommand(2, 'x2', 'y4');
            f.AddPathCommand(2, 'x2', 'y3');
            f.AddPathCommand(2, 'x1', 'y3');
            f.AddPathCommand(6);
            break;
        }
        case 'moon':{
            f.AddAdj('adj', 15, '50000');
            f.AddGuide('a', 10, '0', 'adj', '875000');
            f.AddGuide('g0', 0, 'ss', 'a', '100000');
            f.AddGuide('g0w', 0, 'g0', 'w', 'ss');
            f.AddGuide('g1', 1, 'ss', '0', 'g0');
            f.AddGuide('g2', 0, 'g0', 'g0', 'g1');
            f.AddGuide('g3', 0, 'ss', 'ss', 'g1');
            f.AddGuide('g4', 0, 'g3', '2', '1');
            f.AddGuide('g5', 1, 'g4', '0', 'g2');
            f.AddGuide('g6', 1, 'g5', '0', 'g0');
            f.AddGuide('g6w', 0, 'g6', 'w', 'ss');
            f.AddGuide('g7', 0, 'g5', '1', '2');
            f.AddGuide('g8', 1, 'g7', '0', 'g0');
            f.AddGuide('dy1', 0, 'g8', 'hd2', 'ss');
            f.AddGuide('g10h', 1, 'vc', '0', 'dy1');
            f.AddGuide('g11h', 1, 'vc', 'dy1', '0');
            f.AddGuide('g12', 0, 'g0', '9598', '32768');
            f.AddGuide('g12w', 0, 'g12', 'w', 'ss');
            f.AddGuide('g13', 1, 'ss', '0', 'g12');
            f.AddGuide('q1', 0, 'ss', 'ss', '1');
            f.AddGuide('q2', 0, 'g13', 'g13', '1');
            f.AddGuide('q3', 1, 'q1', '0', 'q2');
            f.AddGuide('q4', 13, 'q3');
            f.AddGuide('dy4', 0, 'q4', 'hd2', 'ss');
            f.AddGuide('g15h', 1, 'vc', '0', 'dy4');
            f.AddGuide('g16h', 1, 'vc', 'dy4', '0');
            f.AddGuide('g17w', 1, 'g6w', '0', 'g0w');
            f.AddGuide('g18w', 0, 'g17w', '1', '2');
            f.AddGuide('dx2p', 1, 'g0w', 'g18w', 'w');
            f.AddGuide('dx2', 0, 'dx2p', '-1', '1');
            f.AddGuide('dy2', 0, 'hd2', '-1', '1');
            f.AddGuide('stAng1', 5, 'dx2', 'dy2');
            f.AddGuide('enAngp1', 5, 'dx2', 'hd2');
            f.AddGuide('enAng1', 1, 'enAngp1', '0', '21600000');
            f.AddGuide('swAng1', 1, 'enAng1', '0', 'stAng1');
            f.AddHandleXY('adj','0','87500', undefined, '0', '0', 'g0w', 'vc');
            f.AddCnx('_3cd4', 'r', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'r', 'b');
            f.AddCnx('0', 'g0w', 'vc');
            f.AddRect('g12w', 'g15h', 'g0w', 'g16h');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'r', 'b');
            f.AddPathCommand(3, 'w', 'hd2', 'cd4', 'cd2');
            f.AddPathCommand(3, 'g18w', 'dy1', 'stAng1', 'swAng1');
            f.AddPathCommand(6);
            break;
        }
        case 'nonIsoscelesTrapezoid':{
            f.AddAdj('adj1', 15, '25000');
            f.AddAdj('adj2', 15, '25000');
            f.AddGuide('maxAdj', 0, '50000', 'w', 'ss');
            f.AddGuide('a1', 10, '0', 'adj1', 'maxAdj');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj');
            f.AddGuide('x1', 0, 'ss', 'a1', '200000');
            f.AddGuide('x2', 0, 'ss', 'a1', '100000');
            f.AddGuide('dx3', 0, 'ss', 'a2', '100000');
            f.AddGuide('x3', 1, 'r', '0', 'dx3');
            f.AddGuide('x4', 2, 'r', 'x3', '2');
            f.AddGuide('il', 0, 'wd3', 'a1', 'maxAdj');
            f.AddGuide('adjm', 8, 'a1', 'a2');
            f.AddGuide('it', 0, 'hd3', 'adjm', 'maxAdj');
            f.AddGuide('irt', 0, 'wd3', 'a2', 'maxAdj');
            f.AddGuide('ir', 1, 'r', '0', 'irt');
            f.AddHandleXY('adj1','0','maxAdj', undefined, '0', '0', 'x2', 't');
            f.AddHandleXY('adj2','0','maxAdj', undefined, '0', '0', 'x3', 't');
            f.AddCnx('0', 'x4', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'x1', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('il', 'it', 'ir', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'b');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(2, 'x3', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'noSmoking':{
            f.AddAdj('adj', 15, '18750');
            f.AddGuide('a', 10, '0', 'adj', '50000');
            f.AddGuide('dr', 0, 'ss', 'a', '100000');
            f.AddGuide('iwd2', 1, 'wd2', '0', 'dr');
            f.AddGuide('ihd2', 1, 'hd2', '0', 'dr');
            f.AddGuide('ang', 5, 'w', 'h');
            f.AddGuide('ct', 7, 'ihd2', 'ang');
            f.AddGuide('st', 12, 'iwd2', 'ang');
            f.AddGuide('m', 9, 'ct', 'st', '0');
            f.AddGuide('n', 0, 'iwd2', 'ihd2', 'm');
            f.AddGuide('drd2', 0, 'dr', '1', '2');
            f.AddGuide('dang', 5, 'n', 'drd2');
            f.AddGuide('2dang', 0, 'dang', '2', '1');
            f.AddGuide('swAng', 1, '-10800000', '2dang', '0');
            f.AddGuide('t3', 5, 'w', 'h');
            f.AddGuide('stAng1', 1, 't3', '0', 'dang');
            f.AddGuide('stAng2', 1, 'stAng1', '0', 'cd2');
            f.AddGuide('ct1', 7, 'ihd2', 'stAng1');
            f.AddGuide('st1', 12, 'iwd2', 'stAng1');
            f.AddGuide('m1', 9, 'ct1', 'st1', '0');
            f.AddGuide('n1', 0, 'iwd2', 'ihd2', 'm1');
            f.AddGuide('dx1', 7, 'n1', 'stAng1');
            f.AddGuide('dy1', 12, 'n1', 'stAng1');
            f.AddGuide('x1', 1, 'hc', 'dx1', '0');
            f.AddGuide('y1', 1, 'vc', 'dy1', '0');
            f.AddGuide('x2', 1, 'hc', '0', 'dx1');
            f.AddGuide('y2', 1, 'vc', '0', 'dy1');
            f.AddGuide('idx', 7, 'wd2', '2700000');
            f.AddGuide('idy', 12, 'hd2', '2700000');
            f.AddGuide('il', 1, 'hc', '0', 'idx');
            f.AddGuide('ir', 1, 'hc', 'idx', '0');
            f.AddGuide('it', 1, 'vc', '0', 'idy');
            f.AddGuide('ib', 1, 'vc', 'idy', '0');
            f.AddHandlePolar(undefined, '0', '0','adj','0','50000', 'dr', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('_3cd4', 'il', 'it');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'il', 'ib');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd4', 'ir', 'ib');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('_3cd4', 'ir', 'it');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
           f.AddPathCommand(3, 'iwd2', 'ihd2', 'stAng1', 'swAng');
             f.AddPathCommand(2, 'x1', 'y1');
          // f.AddPathCommand(6);
            f.AddPathCommand(1, 'x2', 'y2');
            f.AddPathCommand(3, 'iwd2', 'ihd2', 'stAng2', 'swAng');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(3, 'wd2', 'hd2', 'cd2', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', '_3cd4', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', '0', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', 'cd4', 'cd4');
            f.AddPathCommand(6);

            break;
        }
        case 'notchedRightArrow':{
            f.AddAdj('adj1', 15, '50000');
            f.AddAdj('adj2', 15, '50000');
            f.AddGuide('maxAdj2', 0, '100000', 'w', 'ss');
            f.AddGuide('a1', 10, '0', 'adj1', '100000');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('dx2', 0, 'ss', 'a2', '100000');
            f.AddGuide('x2', 1, 'r', '0', 'dx2');
            f.AddGuide('dy1', 0, 'h', 'a1', '200000');
            f.AddGuide('y1', 1, 'vc', '0', 'dy1');
            f.AddGuide('y2', 1, 'vc', 'dy1', '0');
            f.AddGuide('x1', 0, 'dy1', 'dx2', 'hd2');
            f.AddGuide('x3', 1, 'r', '0', 'x1');
            f.AddHandleXY(undefined, '0', '0','adj1','0','100000', 'r', 'y1');
            f.AddHandleXY('adj2','0','maxAdj2', undefined, '0', '0', 'x2', 't');
            f.AddCnx('_3cd4', 'x2', 't');
            f.AddCnx('cd2', 'x1', 'vc');
            f.AddCnx('cd4', 'x2', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('x1', 'y1', 'x3', 'y2');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y1');
            f.AddPathCommand(2, 'x2', 'y1');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(2, 'r', 'vc');
            f.AddPathCommand(2, 'x2', 'b');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'l', 'y2');
            f.AddPathCommand(2, 'x1', 'vc');
            f.AddPathCommand(6);
            break;
        }
        case 'octagon':{
            f.AddAdj('adj', 15, '29289');
            f.AddGuide('a', 10, '0', 'adj', '50000');
            f.AddGuide('x1', 0, 'ss', 'a', '100000');
            f.AddGuide('x2', 1, 'r', '0', 'x1');
            f.AddGuide('y2', 1, 'b', '0', 'x1');
            f.AddGuide('il', 0, 'x1', '1', '2');
            f.AddGuide('ir', 1, 'r', '0', 'il');
            f.AddGuide('ib', 1, 'b', '0', 'il');
            f.AddHandleXY('adj','0','50000', undefined, '0', '0', 'x1', 't');
            f.AddCnx('0', 'r', 'x1');
            f.AddCnx('0', 'r', 'y2');
            f.AddCnx('cd4', 'x2', 'b');
            f.AddCnx('cd4', 'x1', 'b');
            f.AddCnx('cd2', 'l', 'y2');
            f.AddCnx('cd2', 'l', 'x1');
            f.AddCnx('_3cd4', 'x1', 't');
            f.AddCnx('_3cd4', 'x2', 't');
            f.AddRect('il', 'il', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'x1');
            f.AddPathCommand(2, 'x1', 't');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(2, 'r', 'x1');
            f.AddPathCommand(2, 'r', 'y2');
            f.AddPathCommand(2, 'x2', 'b');
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(2, 'l', 'y2');
            f.AddPathCommand(6);
            break;
        }
        case 'parallelogram':{
            f.AddAdj('adj', 15, '25000');
            f.AddGuide('maxAdj', 0, '100000', 'w', 'ss');
            f.AddGuide('a', 10, '0', 'adj', 'maxAdj');
            f.AddGuide('x1', 0, 'ss', 'a', '200000');
            f.AddGuide('x2', 0, 'ss', 'a', '100000');
            f.AddGuide('x6', 1, 'r', '0', 'x1');
            f.AddGuide('x5', 1, 'r', '0', 'x2');
            f.AddGuide('x3', 0, 'x5', '1', '2');
            f.AddGuide('x4', 1, 'r', '0', 'x3');
            f.AddGuide('il', 0, 'wd2', 'a', 'maxAdj');
            f.AddGuide('q1', 0, '5', 'a', 'maxAdj');
            f.AddGuide('q2', 2, '1', 'q1', '12');
            f.AddGuide('il', 0, 'q2', 'w', '1');
            f.AddGuide('it', 0, 'q2', 'h', '1');
            f.AddGuide('ir', 1, 'r', '0', 'il');
            f.AddGuide('ib', 1, 'b', '0', 'it');
            f.AddGuide('q3', 0, 'h', 'hc', 'x2');
            f.AddGuide('y1', 10, '0', 'q3', 'h');
            f.AddGuide('y2', 1, 'b', '0', 'y1');
            f.AddHandleXY('adj','0','maxAdj', undefined, '0', '0', 'x2', 't');
            f.AddCnx('_3cd4', 'hc', 'y2');
            f.AddCnx('_3cd4', 'x4', 't');
            f.AddCnx('0', 'x6', 'vc');
            f.AddCnx('cd4', 'x3', 'b');
            f.AddCnx('cd4', 'hc', 'y1');
            f.AddCnx('cd2', 'x1', 'vc');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'b');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'x5', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'pentagon':{
            f.AddAdj('hf', 15, '105146');
            f.AddAdj('vf', 15, '110557');
            f.AddGuide('swd2', 0, 'wd2', 'hf', '100000');
            f.AddGuide('shd2', 0, 'hd2', 'vf', '100000');
            f.AddGuide('svc', 0, 'vc', 'vf', '100000');
            f.AddGuide('dx1', 7, 'swd2', '1080000');
            f.AddGuide('dx2', 7, 'swd2', '18360000');
            f.AddGuide('dy1', 12, 'shd2', '1080000');
            f.AddGuide('dy2', 12, 'shd2', '18360000');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x2', 1, 'hc', '0', 'dx2');
            f.AddGuide('x3', 1, 'hc', 'dx2', '0');
            f.AddGuide('x4', 1, 'hc', 'dx1', '0');
            f.AddGuide('y1', 1, 'svc', '0', 'dy1');
            f.AddGuide('y2', 1, 'svc', '0', 'dy2');
            f.AddGuide('it', 0, 'y1', 'dx2', 'dx1');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'x1', 'y1');
            f.AddCnx('cd4', 'x2', 'y2');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd4', 'x3', 'y2');
            f.AddCnx('0', 'x4', 'y1');
            f.AddRect('x2', 'it', 'x3', 'y2');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(2, 'hc', 't');
            f.AddPathCommand(2, 'x4', 'y1');
            f.AddPathCommand(2, 'x3', 'y2');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(6);
            break;
        }
        case 'pie':{
            f.AddAdj('adj1', 15, '0');
            f.AddAdj('adj2', 15, '16200000');
            f.AddGuide('stAng', 10, '0', 'adj1', '21599999');
            f.AddGuide('enAng', 10, '0', 'adj2', '21599999');
            f.AddGuide('sw1', 1, 'enAng', '0', 'stAng');
            f.AddGuide('sw2', 1, 'sw1', '21600000', '0');
            f.AddGuide('swAng', 3, 'sw1', 'sw1', 'sw2');
            f.AddGuide('wt1', 12, 'wd2', 'stAng');
            f.AddGuide('ht1', 7, 'hd2', 'stAng');
            f.AddGuide('dx1', 6, 'wd2', 'ht1', 'wt1');
            f.AddGuide('dy1', 11, 'hd2', 'ht1', 'wt1');
            f.AddGuide('x1', 1, 'hc', 'dx1', '0');
            f.AddGuide('y1', 1, 'vc', 'dy1', '0');
            f.AddGuide('wt2', 12, 'wd2', 'enAng');
            f.AddGuide('ht2', 7, 'hd2', 'enAng');
            f.AddGuide('dx2', 6, 'wd2', 'ht2', 'wt2');
            f.AddGuide('dy2', 11, 'hd2', 'ht2', 'wt2');
            f.AddGuide('x2', 1, 'hc', 'dx2', '0');
            f.AddGuide('y2', 1, 'vc', 'dy2', '0');
            f.AddGuide('idx', 7, 'hc', '2700000');
            f.AddGuide('idy', 12, 'vc', '2700000');
            f.AddGuide('il', 1, 'hc', '0', 'idx');
            f.AddGuide('ir', 1, 'hc', 'idx', '0');
            f.AddGuide('it', 1, 'vc', '0', 'idy');
            f.AddGuide('ib', 1, 'vc', 'idy', '0');
            f.AddHandlePolar('adj1','0','21599999', undefined, '0', '0', 'x1', 'y1');
            f.AddHandlePolar('adj2','0','21599999', undefined, '0', '0', 'x2', 'y2');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(3, 'wd2', 'hd2', 'stAng', 'swAng');
            f.AddPathCommand(2, 'hc', 'vc');
            f.AddPathCommand(6);
            break;
        }
        case 'pieWedge':{
            f.AddGuide('g1', 7, 'w', '13500000');
            f.AddGuide('g2', 12, 'h', '13500000');
            f.AddGuide('x1', 1, 'r', 'g1', '0');
            f.AddGuide('y1', 1, 'b', 'g2', '0');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddRect('x1', 'y1', 'r', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'b');
            f.AddPathCommand(3, 'w', 'h', 'cd2', 'cd4');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'plaque':{
            f.AddAdj('adj', 15, '16667');
            f.AddGuide('a', 10, '0', 'adj', '50000');
            f.AddGuide('x1', 0, 'ss', 'a', '100000');
            f.AddGuide('x2', 1, 'r', '0', 'x1');
            f.AddGuide('y2', 1, 'b', '0', 'x1');
            f.AddGuide('il', 0, 'x1', '70711', '100000');
            f.AddGuide('ir', 1, 'r', '0', 'il');
            f.AddGuide('ib', 1, 'b', '0', 'il');
            f.AddHandleXY('adj','0','50000', undefined, '0', '0', 'x1', 't');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('il', 'il', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'x1');
            f.AddPathCommand(3, 'x1', 'x1', 'cd4', '-5400000');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(3, 'x1', 'x1', 'cd2', '-5400000');
            f.AddPathCommand(2, 'r', 'y2');
            f.AddPathCommand(3, 'x1', 'x1', '_3cd4', '-5400000');
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(3, 'x1', 'x1', '0', '-5400000');
            f.AddPathCommand(6);
            break;
        }
        case 'plaqueTabs':{
            f.AddGuide('md', 9, 'w', 'h', '0');
            f.AddGuide('dx', 0, '1', 'md', '20');
            f.AddGuide('y1', 1, '0', 'b', 'dx');
            f.AddGuide('x1', 1, '0', 'r', 'dx');
            f.AddCnx('cd2', 'l', 't');
            f.AddCnx('cd2', 'l', 'dx');
            f.AddCnx('cd2', 'l', 'y1');
            f.AddCnx('cd2', 'l', 'b');
            f.AddCnx('_3cd4', 'dx', 't');
            f.AddCnx('_3cd4', 'x1', 't');
            f.AddCnx('cd4', 'dx', 'b');
            f.AddCnx('cd4', 'x1', 'b');
            f.AddCnx('0', 'r', 't');
            f.AddCnx('0', 'r', 'dx');
            f.AddCnx('0', 'r', 'y1');
            f.AddCnx('0', 'r', 'b');
            f.AddRect('dx', 'dx', 'x1', 'y1');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'dx', 't');
            f.AddPathCommand(3, 'dx', 'dx', '0', 'cd4');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y1');
            f.AddPathCommand(3, 'dx', 'dx', '_3cd4', 'cd4');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'r', 't');
            f.AddPathCommand(2, 'r', 'dx');
            f.AddPathCommand(3, 'dx', 'dx', 'cd4', 'cd4');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'b');
            f.AddPathCommand(3, 'dx', 'dx', 'cd2', 'cd4');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'plus':{
            f.AddAdj('adj', 15, '25000');
            f.AddGuide('a', 10, '0', 'adj', '50000');
            f.AddGuide('x1', 0, 'ss', 'a', '100000');
            f.AddGuide('x2', 1, 'r', '0', 'x1');
            f.AddGuide('y2', 1, 'b', '0', 'x1');
            f.AddGuide('d', 1, 'w', '0', 'h');
            f.AddGuide('il', 3, 'd', 'l', 'x1');
            f.AddGuide('ir', 3, 'd', 'r', 'x2');
            f.AddGuide('it', 3, 'd', 'x1', 't');
            f.AddGuide('ib', 3, 'd', 'y2', 'b');
            f.AddHandleXY('adj','0','50000', undefined, '0', '0', 'x1', 't');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'x1');
            f.AddPathCommand(2, 'x1', 'x1');
            f.AddPathCommand(2, 'x1', 't');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(2, 'x2', 'x1');
            f.AddPathCommand(2, 'r', 'x1');
            f.AddPathCommand(2, 'r', 'y2');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x2', 'b');
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(2, 'l', 'y2');
            f.AddPathCommand(6);
            break;
        }
        case 'quadArrow':{
            f.AddAdj('adj1', 15, '22500');
            f.AddAdj('adj2', 15, '22500');
            f.AddAdj('adj3', 15, '22500');
            f.AddGuide('a2', 10, '0', 'adj2', '50000');
            f.AddGuide('maxAdj1', 0, 'a2', '2', '1');
            f.AddGuide('a1', 10, '0', 'adj1', 'maxAdj1');
            f.AddGuide('q1', 1, '100000', '0', 'maxAdj1');
            f.AddGuide('maxAdj3', 0, 'q1', '1', '2');
            f.AddGuide('a3', 10, '0', 'adj3', 'maxAdj3');
            f.AddGuide('x1', 0, 'ss', 'a3', '100000');
            f.AddGuide('dx2', 0, 'ss', 'a2', '100000');
            f.AddGuide('x2', 1, 'hc', '0', 'dx2');
            f.AddGuide('x5', 1, 'hc', 'dx2', '0');
            f.AddGuide('dx3', 0, 'ss', 'a1', '200000');
            f.AddGuide('x3', 1, 'hc', '0', 'dx3');
            f.AddGuide('x4', 1, 'hc', 'dx3', '0');
            f.AddGuide('x6', 1, 'r', '0', 'x1');
            f.AddGuide('y2', 1, 'vc', '0', 'dx2');
            f.AddGuide('y5', 1, 'vc', 'dx2', '0');
            f.AddGuide('y3', 1, 'vc', '0', 'dx3');
            f.AddGuide('y4', 1, 'vc', 'dx3', '0');
            f.AddGuide('y6', 1, 'b', '0', 'x1');
            f.AddGuide('il', 0, 'dx3', 'x1', 'dx2');
            f.AddGuide('ir', 1, 'r', '0', 'il');
            f.AddHandleXY('adj1','0','maxAdj1', undefined, '0', '0', 'x3', 'x1');
            f.AddHandleXY('adj2','0','50000', undefined, '0', '0', 'x2', 't');
            f.AddHandleXY(undefined, '0', '0','adj3','0','maxAdj3', 'r', 'x1');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('il', 'y3', 'ir', 'y4');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(2, 'x1', 'y3');
            f.AddPathCommand(2, 'x3', 'y3');
            f.AddPathCommand(2, 'x3', 'x1');
            f.AddPathCommand(2, 'x2', 'x1');
            f.AddPathCommand(2, 'hc', 't');
            f.AddPathCommand(2, 'x5', 'x1');
            f.AddPathCommand(2, 'x4', 'x1');
            f.AddPathCommand(2, 'x4', 'y3');
            f.AddPathCommand(2, 'x6', 'y3');
            f.AddPathCommand(2, 'x6', 'y2');
            f.AddPathCommand(2, 'r', 'vc');
            f.AddPathCommand(2, 'x6', 'y5');
            f.AddPathCommand(2, 'x6', 'y4');
            f.AddPathCommand(2, 'x4', 'y4');
            f.AddPathCommand(2, 'x4', 'y6');
            f.AddPathCommand(2, 'x5', 'y6');
            f.AddPathCommand(2, 'hc', 'b');
            f.AddPathCommand(2, 'x2', 'y6');
            f.AddPathCommand(2, 'x3', 'y6');
            f.AddPathCommand(2, 'x3', 'y4');
            f.AddPathCommand(2, 'x1', 'y4');
            f.AddPathCommand(2, 'x1', 'y5');
            f.AddPathCommand(6);
            break;
        }
        case 'quadArrowCallout':{
            f.AddAdj('adj1', 15, '18515');
            f.AddAdj('adj2', 15, '18515');
            f.AddAdj('adj3', 15, '18515');
            f.AddAdj('adj4', 15, '48123');
            f.AddGuide('a2', 10, '0', 'adj2', '50000');
            f.AddGuide('maxAdj1', 0, 'a2', '2', '1');
            f.AddGuide('a1', 10, '0', 'adj1', 'maxAdj1');
            f.AddGuide('maxAdj3', 1, '50000', '0', 'a2');
            f.AddGuide('a3', 10, '0', 'adj3', 'maxAdj3');
            f.AddGuide('q2', 0, 'a3', '2', '1');
            f.AddGuide('maxAdj4', 1, '100000', '0', 'q2');
            f.AddGuide('a4', 10, 'a1', 'adj4', 'maxAdj4');
            f.AddGuide('dx2', 0, 'ss', 'a2', '100000');
            f.AddGuide('dx3', 0, 'ss', 'a1', '200000');
            f.AddGuide('ah', 0, 'ss', 'a3', '100000');
            f.AddGuide('dx1', 0, 'w', 'a4', '200000');
            f.AddGuide('dy1', 0, 'h', 'a4', '200000');
            f.AddGuide('x8', 1, 'r', '0', 'ah');
            f.AddGuide('x2', 1, 'hc', '0', 'dx1');
            f.AddGuide('x7', 1, 'hc', 'dx1', '0');
            f.AddGuide('x3', 1, 'hc', '0', 'dx2');
            f.AddGuide('x6', 1, 'hc', 'dx2', '0');
            f.AddGuide('x4', 1, 'hc', '0', 'dx3');
            f.AddGuide('x5', 1, 'hc', 'dx3', '0');
            f.AddGuide('y8', 1, 'b', '0', 'ah');
            f.AddGuide('y2', 1, 'vc', '0', 'dy1');
            f.AddGuide('y7', 1, 'vc', 'dy1', '0');
            f.AddGuide('y3', 1, 'vc', '0', 'dx2');
            f.AddGuide('y6', 1, 'vc', 'dx2', '0');
            f.AddGuide('y4', 1, 'vc', '0', 'dx3');
            f.AddGuide('y5', 1, 'vc', 'dx3', '0');
            f.AddHandleXY('adj1','0','maxAdj1', undefined, '0', '0', 'x4', 'ah');
            f.AddHandleXY('adj2','0','50000', undefined, '0', '0', 'x3', 't');
            f.AddHandleXY(undefined, '0', '0','adj3','0','maxAdj3', 'r', 'ah');
            f.AddHandleXY(undefined, '0', '0','adj4','a1','maxAdj4', 'l', 'y2');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('x2', 'y2', 'x7', 'y7');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(2, 'ah', 'y3');
            f.AddPathCommand(2, 'ah', 'y4');
            f.AddPathCommand(2, 'x2', 'y4');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x4', 'y2');
            f.AddPathCommand(2, 'x4', 'ah');
            f.AddPathCommand(2, 'x3', 'ah');
            f.AddPathCommand(2, 'hc', 't');
            f.AddPathCommand(2, 'x6', 'ah');
            f.AddPathCommand(2, 'x5', 'ah');
            f.AddPathCommand(2, 'x5', 'y2');
            f.AddPathCommand(2, 'x7', 'y2');
            f.AddPathCommand(2, 'x7', 'y4');
            f.AddPathCommand(2, 'x8', 'y4');
            f.AddPathCommand(2, 'x8', 'y3');
            f.AddPathCommand(2, 'r', 'vc');
            f.AddPathCommand(2, 'x8', 'y6');
            f.AddPathCommand(2, 'x8', 'y5');
            f.AddPathCommand(2, 'x7', 'y5');
            f.AddPathCommand(2, 'x7', 'y7');
            f.AddPathCommand(2, 'x5', 'y7');
            f.AddPathCommand(2, 'x5', 'y8');
            f.AddPathCommand(2, 'x6', 'y8');
            f.AddPathCommand(2, 'hc', 'b');
            f.AddPathCommand(2, 'x3', 'y8');
            f.AddPathCommand(2, 'x4', 'y8');
            f.AddPathCommand(2, 'x4', 'y7');
            f.AddPathCommand(2, 'x2', 'y7');
            f.AddPathCommand(2, 'x2', 'y5');
            f.AddPathCommand(2, 'ah', 'y5');
            f.AddPathCommand(2, 'ah', 'y6');
            f.AddPathCommand(6);
            break;
        }
        case 'rect':{
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'ribbon':{
            f.AddAdj('adj1', 15, '16667');
            f.AddAdj('adj2', 15, '50000');
            f.AddGuide('a1', 10, '0', 'adj1', '33333');
            f.AddGuide('a2', 10, '25000', 'adj2', '75000');
            f.AddGuide('x10', 1, 'r', '0', 'wd8');
            f.AddGuide('dx2', 0, 'w', 'a2', '200000');
            f.AddGuide('x2', 1, 'hc', '0', 'dx2');
            f.AddGuide('x9', 1, 'hc', 'dx2', '0');
            f.AddGuide('x3', 1, 'x2', 'wd32', '0');
            f.AddGuide('x8', 1, 'x9', '0', 'wd32');
            f.AddGuide('x5', 1, 'x2', 'wd8', '0');
            f.AddGuide('x6', 1, 'x9', '0', 'wd8');
            f.AddGuide('x4', 1, 'x5', '0', 'wd32');
            f.AddGuide('x7', 1, 'x6', 'wd32', '0');
            f.AddGuide('y1', 0, 'h', 'a1', '200000');
            f.AddGuide('y2', 0, 'h', 'a1', '100000');
            f.AddGuide('y4', 1, 'b', '0', 'y2');
            f.AddGuide('y3', 0, 'y4', '1', '2');
            f.AddGuide('hR', 0, 'h', 'a1', '400000');
            f.AddGuide('y5', 1, 'b', '0', 'hR');
            f.AddGuide('y6', 1, 'y2', '0', 'hR');
            f.AddHandleXY(undefined, '0', '0','adj1','0','33333', 'hc', 'y2');
            f.AddHandleXY('adj2','25000','75000', undefined, '0', '0', 'x2', 't');
            f.AddCnx('_3cd4', 'hc', 'y2');
            f.AddCnx('cd2', 'wd8', 'y3');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'x10', 'y3');
            f.AddRect('x2', 'y2', 'x9', 'b');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'x4', 't');
            f.AddPathCommand(3, 'wd32', 'hR', '_3cd4', 'cd2');
            f.AddPathCommand(2, 'x3', 'y1');
            f.AddPathCommand(3, 'wd32', 'hR', '_3cd4', '-10800000');
            f.AddPathCommand(2, 'x8', 'y2');
            f.AddPathCommand(3, 'wd32', 'hR', 'cd4', '-10800000');
            f.AddPathCommand(2, 'x7', 'y1');
            f.AddPathCommand(3, 'wd32', 'hR', 'cd4', 'cd2');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'x10', 'y3');
            f.AddPathCommand(2, 'r', 'y4');
            f.AddPathCommand(2, 'x9', 'y4');
            f.AddPathCommand(2, 'x9', 'y5');
            f.AddPathCommand(3, 'wd32', 'hR', '0', 'cd4');
            f.AddPathCommand(2, 'x3', 'b');
            f.AddPathCommand(3, 'wd32', 'hR', 'cd4', 'cd4');
            f.AddPathCommand(2, 'x2', 'y4');
            f.AddPathCommand(2, 'l', 'y4');
            f.AddPathCommand(2, 'wd8', 'y3');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darkenLess', false, undefined, undefined);
            f.AddPathCommand(1, 'x5', 'hR');
            f.AddPathCommand(3, 'wd32', 'hR', '0', 'cd4');
            f.AddPathCommand(2, 'x3', 'y1');
            f.AddPathCommand(3, 'wd32', 'hR', '_3cd4', '-10800000');
            f.AddPathCommand(2, 'x5', 'y2');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'x6', 'hR');
            f.AddPathCommand(3, 'wd32', 'hR', 'cd2', '-5400000');
            f.AddPathCommand(2, 'x8', 'y1');
            f.AddPathCommand(3, 'wd32', 'hR', '_3cd4', 'cd2');
            f.AddPathCommand(2, 'x6', 'y2');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'x4', 't');
            f.AddPathCommand(3, 'wd32', 'hR', '_3cd4', 'cd2');
            f.AddPathCommand(2, 'x3', 'y1');
            f.AddPathCommand(3, 'wd32', 'hR', '_3cd4', '-10800000');
            f.AddPathCommand(2, 'x8', 'y2');
            f.AddPathCommand(3, 'wd32', 'hR', 'cd4', '-10800000');
            f.AddPathCommand(2, 'x7', 'y1');
            f.AddPathCommand(3, 'wd32', 'hR', 'cd4', 'cd2');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'x10', 'y3');
            f.AddPathCommand(2, 'r', 'y4');
            f.AddPathCommand(2, 'x9', 'y4');
            f.AddPathCommand(2, 'x9', 'y5');
            f.AddPathCommand(3, 'wd32', 'hR', '0', 'cd4');
            f.AddPathCommand(2, 'x3', 'b');
            f.AddPathCommand(3, 'wd32', 'hR', 'cd4', 'cd4');
            f.AddPathCommand(2, 'x2', 'y4');
            f.AddPathCommand(2, 'l', 'y4');
            f.AddPathCommand(2, 'wd8', 'y3');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'x5', 'hR');
            f.AddPathCommand(2, 'x5', 'y2');
            f.AddPathCommand(1, 'x6', 'y2');
            f.AddPathCommand(2, 'x6', 'hR');
            f.AddPathCommand(1, 'x2', 'y4');
            f.AddPathCommand(2, 'x2', 'y6');
            f.AddPathCommand(1, 'x9', 'y6');
            f.AddPathCommand(2, 'x9', 'y4');
            break;
        }
        case 'ribbon2':{
            f.AddAdj('adj1', 15, '16667');
            f.AddAdj('adj2', 15, '50000');
            f.AddGuide('a1', 10, '0', 'adj1', '33333');
            f.AddGuide('a2', 10, '25000', 'adj2', '75000');
            f.AddGuide('x10', 1, 'r', '0', 'wd8');
            f.AddGuide('dx2', 0, 'w', 'a2', '200000');
            f.AddGuide('x2', 1, 'hc', '0', 'dx2');
            f.AddGuide('x9', 1, 'hc', 'dx2', '0');
            f.AddGuide('x3', 1, 'x2', 'wd32', '0');
            f.AddGuide('x8', 1, 'x9', '0', 'wd32');
            f.AddGuide('x5', 1, 'x2', 'wd8', '0');
            f.AddGuide('x6', 1, 'x9', '0', 'wd8');
            f.AddGuide('x4', 1, 'x5', '0', 'wd32');
            f.AddGuide('x7', 1, 'x6', 'wd32', '0');
            f.AddGuide('dy1', 0, 'h', 'a1', '200000');
            f.AddGuide('y1', 1, 'b', '0', 'dy1');
            f.AddGuide('dy2', 0, 'h', 'a1', '100000');
            f.AddGuide('y2', 1, 'b', '0', 'dy2');
            f.AddGuide('y4', 1, 't', 'dy2', '0');
            f.AddGuide('y3', 2, 'y4', 'b', '2');
            f.AddGuide('hR', 0, 'h', 'a1', '400000');
            f.AddGuide('y6', 1, 'b', '0', 'hR');
            f.AddGuide('y7', 1, 'y1', '0', 'hR');
            f.AddHandleXY(undefined, '0', '0','adj1','0','33333', 'hc', 'y2');
            f.AddHandleXY('adj2','25000','75000', undefined, '0', '0', 'x2', 'b');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'wd8', 'y3');
            f.AddCnx('cd4', 'hc', 'y2');
            f.AddCnx('0', 'x10', 'y3');
            f.AddRect('x2', 't', 'x9', 'y2');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 'b');
            f.AddPathCommand(2, 'x4', 'b');
            f.AddPathCommand(3, 'wd32', 'hR', 'cd4', '-10800000');
            f.AddPathCommand(2, 'x3', 'y1');
            f.AddPathCommand(3, 'wd32', 'hR', 'cd4', 'cd2');
            f.AddPathCommand(2, 'x8', 'y2');
            f.AddPathCommand(3, 'wd32', 'hR', '_3cd4', 'cd2');
            f.AddPathCommand(2, 'x7', 'y1');
            f.AddPathCommand(3, 'wd32', 'hR', '_3cd4', '-10800000');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'x10', 'y3');
            f.AddPathCommand(2, 'r', 'y4');
            f.AddPathCommand(2, 'x9', 'y4');
            f.AddPathCommand(2, 'x9', 'hR');
            f.AddPathCommand(3, 'wd32', 'hR', '0', '-5400000');
            f.AddPathCommand(2, 'x3', 't');
            f.AddPathCommand(3, 'wd32', 'hR', '_3cd4', '-5400000');
            f.AddPathCommand(2, 'x2', 'y4');
            f.AddPathCommand(2, 'l', 'y4');
            f.AddPathCommand(2, 'wd8', 'y3');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darkenLess', false, undefined, undefined);
            f.AddPathCommand(1, 'x5', 'y6');
            f.AddPathCommand(3, 'wd32', 'hR', '0', '-5400000');
            f.AddPathCommand(2, 'x3', 'y1');
            f.AddPathCommand(3, 'wd32', 'hR', 'cd4', 'cd2');
            f.AddPathCommand(2, 'x5', 'y2');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'x6', 'y6');
            f.AddPathCommand(3, 'wd32', 'hR', 'cd2', 'cd4');
            f.AddPathCommand(2, 'x8', 'y1');
            f.AddPathCommand(3, 'wd32', 'hR', 'cd4', '-10800000');
            f.AddPathCommand(2, 'x6', 'y2');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'b');
            f.AddPathCommand(2, 'wd8', 'y3');
            f.AddPathCommand(2, 'l', 'y4');
            f.AddPathCommand(2, 'x2', 'y4');
            f.AddPathCommand(2, 'x2', 'hR');
            f.AddPathCommand(3, 'wd32', 'hR', 'cd2', 'cd4');
            f.AddPathCommand(2, 'x8', 't');
            f.AddPathCommand(3, 'wd32', 'hR', '_3cd4', 'cd4');
            f.AddPathCommand(2, 'x9', 'y4');
            f.AddPathCommand(2, 'x9', 'y4');
            f.AddPathCommand(2, 'r', 'y4');
            f.AddPathCommand(2, 'x10', 'y3');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'x7', 'b');
            f.AddPathCommand(3, 'wd32', 'hR', 'cd4', 'cd2');
            f.AddPathCommand(2, 'x8', 'y1');
            f.AddPathCommand(3, 'wd32', 'hR', 'cd4', '-10800000');
            f.AddPathCommand(2, 'x3', 'y2');
            f.AddPathCommand(3, 'wd32', 'hR', '_3cd4', '-10800000');
            f.AddPathCommand(2, 'x4', 'y1');
            f.AddPathCommand(3, 'wd32', 'hR', '_3cd4', 'cd2');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'x5', 'y2');
            f.AddPathCommand(2, 'x5', 'y6');
            f.AddPathCommand(1, 'x6', 'y6');
            f.AddPathCommand(2, 'x6', 'y2');
            f.AddPathCommand(1, 'x2', 'y7');
            f.AddPathCommand(2, 'x2', 'y4');
            f.AddPathCommand(1, 'x9', 'y4');
            f.AddPathCommand(2, 'x9', 'y7');
            break;
        }
        case 'rightArrow':{
            f.AddAdj('adj1', 15, '50000');
            f.AddAdj('adj2', 15, '50000');
            f.AddGuide('maxAdj2', 0, '100000', 'w', 'ss');
            f.AddGuide('a1', 10, '0', 'adj1', '100000');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('dx1', 0, 'ss', 'a2', '100000');
            f.AddGuide('x1', 1, 'r', '0', 'dx1');
            f.AddGuide('dy1', 0, 'h', 'a1', '200000');
            f.AddGuide('y1', 1, 'vc', '0', 'dy1');
            f.AddGuide('y2', 1, 'vc', 'dy1', '0');
            f.AddGuide('dx2', 0, 'y1', 'dx1', 'hd2');
            f.AddGuide('x2', 1, 'x1', 'dx2', '0');
            f.AddHandleXY(undefined, '0', '0','adj1','0','100000', 'l', 'y1');
            f.AddHandleXY('adj2','0','maxAdj2', undefined, '0', '0', 'x1', 't');
            f.AddCnx('_3cd4', 'x1', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'x1', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('l', 'y1', 'x2', 'y2');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y1');
            f.AddPathCommand(2, 'x1', 'y1');
            f.AddPathCommand(2, 'x1', 't');
            f.AddPathCommand(2, 'r', 'vc');
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(2, 'l', 'y2');
            f.AddPathCommand(6);
            break;
        }
        case 'rightArrowCallout':{
            f.AddAdj('adj1', 15, '25000');
            f.AddAdj('adj2', 15, '25000');
            f.AddAdj('adj3', 15, '25000');
            f.AddAdj('adj4', 15, '64977');
            f.AddGuide('maxAdj2', 0, '50000', 'h', 'ss');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('maxAdj1', 0, 'a2', '2', '1');
            f.AddGuide('a1', 10, '0', 'adj1', 'maxAdj1');
            f.AddGuide('maxAdj3', 0, '100000', 'w', 'ss');
            f.AddGuide('a3', 10, '0', 'adj3', 'maxAdj3');
            f.AddGuide('q2', 0, 'a3', 'ss', 'w');
            f.AddGuide('maxAdj4', 1, '100000', '0', 'q2');
            f.AddGuide('a4', 10, '0', 'adj4', 'maxAdj4');
            f.AddGuide('dy1', 0, 'ss', 'a2', '100000');
            f.AddGuide('dy2', 0, 'ss', 'a1', '200000');
            f.AddGuide('y1', 1, 'vc', '0', 'dy1');
            f.AddGuide('y2', 1, 'vc', '0', 'dy2');
            f.AddGuide('y3', 1, 'vc', 'dy2', '0');
            f.AddGuide('y4', 1, 'vc', 'dy1', '0');
            f.AddGuide('dx3', 0, 'ss', 'a3', '100000');
            f.AddGuide('x3', 1, 'r', '0', 'dx3');
            f.AddGuide('x2', 0, 'w', 'a4', '100000');
            f.AddGuide('x1', 0, 'x2', '1', '2');
            f.AddHandleXY(undefined, '0', '0','adj1','0','maxAdj1', 'x3', 'y2');
            f.AddHandleXY(undefined, '0', '0','adj2','0','maxAdj2', 'r', 'y1');
            f.AddHandleXY('adj3','0','maxAdj3', undefined, '0', '0', 'x3', 't');
            f.AddHandleXY('adj4','0','maxAdj4', undefined, '0', '0', 'x2', 'b');
            f.AddCnx('_3cd4', 'x1', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'x1', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('l', 't', 'x2', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x3', 'y2');
            f.AddPathCommand(2, 'x3', 'y1');
            f.AddPathCommand(2, 'r', 'vc');
            f.AddPathCommand(2, 'x3', 'y4');
            f.AddPathCommand(2, 'x3', 'y3');
            f.AddPathCommand(2, 'x2', 'y3');
            f.AddPathCommand(2, 'x2', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'rightBrace':{
            f.AddAdj('adj1', 15, '8333');
            f.AddAdj('adj2', 15, '50000');
            f.AddGuide('a2', 10, '0', 'adj2', '100000');
            f.AddGuide('q1', 1, '100000', '0', 'a2');
            f.AddGuide('q2', 16, 'q1', 'a2');
            f.AddGuide('q3', 0, 'q2', '1', '2');
            f.AddGuide('maxAdj1', 0, 'q3', 'h', 'ss');
            f.AddGuide('a1', 10, '0', 'adj1', 'maxAdj1');
            f.AddGuide('y1', 0, 'ss', 'a1', '100000');
            f.AddGuide('y3', 0, 'h', 'a2', '100000');
            f.AddGuide('y2', 1, 'y3', '0', 'y1');
            f.AddGuide('y4', 1, 'b', '0', 'y1');
            f.AddGuide('dx1', 7, 'wd2', '2700000');
            f.AddGuide('dy1', 12, 'y1', '2700000');
            f.AddGuide('ir', 1, 'l', 'dx1', '0');
            f.AddGuide('it', 1, 'y1', '0', 'dy1');
            f.AddGuide('ib', 1, 'b', 'dy1', 'y1');
            f.AddHandleXY(undefined, '0', '0','adj1','0','maxAdj1', 'hc', 'y1');
            f.AddHandleXY(undefined, '0', '0','adj2','0','100000', 'r', 'y3');
            f.AddCnx('cd4', 'l', 't');
            f.AddCnx('cd2', 'r', 'y3');
            f.AddCnx('_3cd4', 'l', 'b');
            f.AddRect('l', 'it', 'ir', 'ib');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(3, 'wd2', 'y1', '_3cd4', 'cd4');
            f.AddPathCommand(2, 'hc', 'y2');
            f.AddPathCommand(3, 'wd2', 'y1', 'cd2', '-5400000');
            f.AddPathCommand(3, 'wd2', 'y1', '_3cd4', '-5400000');
            f.AddPathCommand(2, 'hc', 'y4');
            f.AddPathCommand(3, 'wd2', 'y1', '0', 'cd4');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(3, 'wd2', 'y1', '_3cd4', 'cd4');
            f.AddPathCommand(2, 'hc', 'y2');
            f.AddPathCommand(3, 'wd2', 'y1', 'cd2', '-5400000');
            f.AddPathCommand(3, 'wd2', 'y1', '_3cd4', '-5400000');
            f.AddPathCommand(2, 'hc', 'y4');
            f.AddPathCommand(3, 'wd2', 'y1', '0', 'cd4');
            break;
        }
        case 'rightBracket':{
            f.AddAdj('adj', 15, '8333');
            f.AddGuide('maxAdj', 0, '50000', 'h', 'ss');
            f.AddGuide('a', 10, '0', 'adj', 'maxAdj');
            f.AddGuide('y1', 0, 'ss', 'a', '100000');
            f.AddGuide('y2', 1, 'b', '0', 'y1');
            f.AddGuide('dx1', 7, 'w', '2700000');
            f.AddGuide('dy1', 12, 'y1', '2700000');
            f.AddGuide('ir', 1, 'l', 'dx1', '0');
            f.AddGuide('it', 1, 'y1', '0', 'dy1');
            f.AddGuide('ib', 1, 'b', 'dy1', 'y1');
            f.AddHandleXY(undefined, '0', '0','adj','0','maxAdj', 'r', 'y1');
            f.AddCnx('cd4', 'l', 't');
            f.AddCnx('_3cd4', 'l', 'b');
            f.AddCnx('cd2', 'r', 'vc');
            f.AddRect('l', 'it', 'ir', 'ib');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(3, 'w', 'y1', '_3cd4', 'cd4');
            f.AddPathCommand(2, 'r', 'y2');
            f.AddPathCommand(3, 'w', 'y1', '0', 'cd4');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(3, 'w', 'y1', '_3cd4', 'cd4');
            f.AddPathCommand(2, 'r', 'y2');
            f.AddPathCommand(3, 'w', 'y1', '0', 'cd4');
            break;
        }
        case 'round1Rect':{
            f.AddAdj('adj', 15, '16667');
            f.AddGuide('a', 10, '0', 'adj', '50000');
            f.AddGuide('dx1', 0, 'ss', 'a', '100000');
            f.AddGuide('x1', 1, 'r', '0', 'dx1');
            f.AddGuide('idx', 0, 'dx1', '29289', '100000');
            f.AddGuide('ir', 1, 'r', '0', 'idx');
            f.AddHandleXY('adj','0','50000', undefined, '0', '0', 'x1', 't');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('l', 't', 'ir', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'x1', 't');
            f.AddPathCommand(3, 'dx1', 'dx1', '_3cd4', 'cd4');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'round2DiagRect':{
            f.AddAdj('adj1', 15, '16667');
            f.AddAdj('adj2', 15, '0');
            f.AddGuide('a1', 10, '0', 'adj1', '50000');
            f.AddGuide('a2', 10, '0', 'adj2', '50000');
            f.AddGuide('x1', 0, 'ss', 'a1', '100000');
            f.AddGuide('y1', 1, 'b', '0', 'x1');
            f.AddGuide('a', 0, 'ss', 'a2', '100000');
            f.AddGuide('x2', 1, 'r', '0', 'a');
            f.AddGuide('y2', 1, 'b', '0', 'a');
            f.AddGuide('dx1', 0, 'x1', '29289', '100000');
            f.AddGuide('dx2', 0, 'a', '29289', '100000');
            f.AddGuide('d', 1, 'dx1', '0', 'dx2');
            f.AddGuide('dx', 3, 'd', 'dx1', 'dx2');
            f.AddGuide('ir', 1, 'r', '0', 'dx');
            f.AddGuide('ib', 1, 'b', '0', 'dx');
            f.AddHandleXY('adj1','0','50000', undefined, '0', '0', 'x1', 't');
            f.AddHandleXY('adj2','0','50000', undefined, '0', '0', 'x2', 't');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('dx', 'dx', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 't');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(3, 'a', 'a', '_3cd4', 'cd4');
            f.AddPathCommand(2, 'r', 'y1');
            f.AddPathCommand(3, 'x1', 'x1', '0', 'cd4');
            f.AddPathCommand(2, 'a', 'b');
            f.AddPathCommand(3, 'a', 'a', 'cd4', 'cd4');
            f.AddPathCommand(2, 'l', 'x1');
            f.AddPathCommand(3, 'x1', 'x1', 'cd2', 'cd4');
            f.AddPathCommand(6);
            break;
        }
        case 'round2SameRect':{
            f.AddAdj('adj1', 15, '16667');
            f.AddAdj('adj2', 15, '0');
            f.AddGuide('a1', 10, '0', 'adj1', '50000');
            f.AddGuide('a2', 10, '0', 'adj2', '50000');
            f.AddGuide('tx1', 0, 'ss', 'a1', '100000');
            f.AddGuide('tx2', 1, 'r', '0', 'tx1');
            f.AddGuide('bx1', 0, 'ss', 'a2', '100000');
            f.AddGuide('bx2', 1, 'r', '0', 'bx1');
            f.AddGuide('by1', 1, 'b', '0', 'bx1');
            f.AddGuide('d', 1, 'tx1', '0', 'bx1');
            f.AddGuide('tdx', 0, 'tx1', '29289', '100000');
            f.AddGuide('bdx', 0, 'bx1', '29289', '100000');
            f.AddGuide('il', 3, 'd', 'tdx', 'bdx');
            f.AddGuide('ir', 1, 'r', '0', 'il');
            f.AddGuide('ib', 1, 'b', '0', 'bdx');
            f.AddHandleXY('adj1','0','50000', undefined, '0', '0', 'tx2', 't');
            f.AddHandleXY('adj2','0','50000', undefined, '0', '0', 'bx1', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('il', 'tdx', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'tx1', 't');
            f.AddPathCommand(2, 'tx2', 't');
            f.AddPathCommand(3, 'tx1', 'tx1', '_3cd4', 'cd4');
            f.AddPathCommand(2, 'r', 'by1');
            f.AddPathCommand(3, 'bx1', 'bx1', '0', 'cd4');
            f.AddPathCommand(2, 'bx1', 'b');
            f.AddPathCommand(3, 'bx1', 'bx1', 'cd4', 'cd4');
            f.AddPathCommand(2, 'l', 'tx1');
            f.AddPathCommand(3, 'tx1', 'tx1', 'cd2', 'cd4');
            f.AddPathCommand(6);
            break;
        }
        case 'roundRect':{
            f.AddAdj('adj', 15, '16667');
            f.AddGuide('a', 10, '0', 'adj', '50000');
            f.AddGuide('x1', 0, 'ss', 'a', '100000');
            f.AddGuide('x2', 1, 'r', '0', 'x1');
            f.AddGuide('y2', 1, 'b', '0', 'x1');
            f.AddGuide('il', 0, 'x1', '29289', '100000');
            f.AddGuide('ir', 1, 'r', '0', 'il');
            f.AddGuide('ib', 1, 'b', '0', 'il');
            f.AddHandleXY('adj','0','50000', undefined, '0', '0', 'x1', 't');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('il', 'il', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'x1');
            f.AddPathCommand(3, 'x1', 'x1', 'cd2', 'cd4');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(3, 'x1', 'x1', '_3cd4', 'cd4');
            f.AddPathCommand(2, 'r', 'y2');
            f.AddPathCommand(3, 'x1', 'x1', '0', 'cd4');
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(3, 'x1', 'x1', 'cd4', 'cd4');
            f.AddPathCommand(6);
            break;
        }
        case 'rtTriangle':{
            f.AddGuide('it', 0, 'h', '7', '12');
            f.AddGuide('ir', 0, 'w', '7', '12');
            f.AddGuide('ib', 0, 'h', '11', '12');
            f.AddCnx('_3cd4', 'l', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'l', 'b');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd4', 'r', 'b');
            f.AddCnx('0', 'hc', 'vc');
            f.AddRect('wd12', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'b');
            f.AddPathCommand(2, 'l', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'smileyFace':{
            f.AddAdj('adj', 15, '4653');
            f.AddGuide('a', 10, '-4653', 'adj', '4653');
            f.AddGuide('x1', 0, 'w', '4969', '21699');
            f.AddGuide('x2', 0, 'w', '6215', '21600');
            f.AddGuide('x3', 0, 'w', '13135', '21600');
            f.AddGuide('x4', 0, 'w', '16640', '21600');
            f.AddGuide('y1', 0, 'h', '7570', '21600');
            f.AddGuide('y3', 0, 'h', '16515', '21600');
            f.AddGuide('dy2', 0, 'h', 'a', '100000');
            f.AddGuide('y2', 1, 'y3', '0', 'dy2');
            f.AddGuide('y4', 1, 'y3', 'dy2', '0');
            f.AddGuide('dy3', 0, 'h', 'a', '50000');
            f.AddGuide('y5', 1, 'y4', 'dy3', '0');
            f.AddGuide('idx', 7, 'wd2', '2700000');
            f.AddGuide('idy', 12, 'hd2', '2700000');
            f.AddGuide('il', 1, 'hc', '0', 'idx');
            f.AddGuide('ir', 1, 'hc', 'idx', '0');
            f.AddGuide('it', 1, 'vc', '0', 'idy');
            f.AddGuide('ib', 1, 'vc', 'idy', '0');
            f.AddGuide('wR', 0, 'w', '1125', '21600');
            f.AddGuide('hR', 0, 'h', '1125', '21600');
            f.AddHandleXY(undefined, '0', '0','adj','-4653','4653', 'hc', 'y4');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('_3cd4', 'il', 'it');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'il', 'ib');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd4', 'ir', 'ib');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('_3cd4', 'ir', 'it');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(3, 'wd2', 'hd2', 'cd2', '21600000');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darkenLess', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x2', 'y1');
            f.AddPathCommand(3, 'wR', 'hR', 'cd2', '21600000');
            f.AddPathCommand(1, 'x3', 'y1');
            f.AddPathCommand(3, 'wR', 'hR', 'cd2', '21600000');
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y2');
            f.AddPathCommand(4, 'hc', 'y5', 'x4', 'y2');
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(3, 'wd2', 'hd2', 'cd2', '21600000');
            f.AddPathCommand(6);
            break;
        }
        case 'snip1Rect':{
            f.AddAdj('adj', 15, '16667');
            f.AddGuide('a', 10, '0', 'adj', '50000');
            f.AddGuide('dx1', 0, 'ss', 'a', '100000');
            f.AddGuide('x1', 1, 'r', '0', 'dx1');
            f.AddGuide('it', 0, 'dx1', '1', '2');
            f.AddGuide('ir', 2, 'x1', 'r', '2');
            f.AddHandleXY('adj','0','50000', undefined, '0', '0', 'x1', 't');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('l', 'it', 'ir', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'x1', 't');
            f.AddPathCommand(2, 'r', 'dx1');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'snip2DiagRect':{
            f.AddAdj('adj1', 15, '0');
            f.AddAdj('adj2', 15, '16667');
            f.AddGuide('a1', 10, '0', 'adj1', '50000');
            f.AddGuide('a2', 10, '0', 'adj2', '50000');
            f.AddGuide('lx1', 0, 'ss', 'a1', '100000');
            f.AddGuide('lx2', 1, 'r', '0', 'lx1');
            f.AddGuide('ly1', 1, 'b', '0', 'lx1');
            f.AddGuide('rx1', 0, 'ss', 'a2', '100000');
            f.AddGuide('rx2', 1, 'r', '0', 'rx1');
            f.AddGuide('ry1', 1, 'b', '0', 'rx1');
            f.AddGuide('d', 1, 'lx1', '0', 'rx1');
            f.AddGuide('dx', 3, 'd', 'lx1', 'rx1');
            f.AddGuide('il', 0, 'dx', '1', '2');
            f.AddGuide('ir', 1, 'r', '0', 'il');
            f.AddGuide('ib', 1, 'b', '0', 'il');
            f.AddHandleXY('adj1','0','50000', undefined, '0', '0', 'lx1', 't');
            f.AddHandleXY('adj2','0','50000', undefined, '0', '0', 'rx2', 't');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('il', 'il', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'lx1', 't');
            f.AddPathCommand(2, 'rx2', 't');
            f.AddPathCommand(2, 'r', 'rx1');
            f.AddPathCommand(2, 'r', 'ly1');
            f.AddPathCommand(2, 'lx2', 'b');
            f.AddPathCommand(2, 'rx1', 'b');
            f.AddPathCommand(2, 'l', 'ry1');
            f.AddPathCommand(2, 'l', 'lx1');
            f.AddPathCommand(6);
            break;
        }
        case 'snip2SameRect':{
            f.AddAdj('adj1', 15, '16667');
            f.AddAdj('adj2', 15, '0');
            f.AddGuide('a1', 10, '0', 'adj1', '50000');
            f.AddGuide('a2', 10, '0', 'adj2', '50000');
            f.AddGuide('tx1', 0, 'ss', 'a1', '100000');
            f.AddGuide('tx2', 1, 'r', '0', 'tx1');
            f.AddGuide('bx1', 0, 'ss', 'a2', '100000');
            f.AddGuide('bx2', 1, 'r', '0', 'bx1');
            f.AddGuide('by1', 1, 'b', '0', 'bx1');
            f.AddGuide('d', 1, 'tx1', '0', 'bx1');
            f.AddGuide('dx', 3, 'd', 'tx1', 'bx1');
            f.AddGuide('il', 0, 'dx', '1', '2');
            f.AddGuide('ir', 1, 'r', '0', 'il');
            f.AddGuide('it', 0, 'tx1', '1', '2');
            f.AddGuide('ib', 2, 'by1', 'b', '2');
            f.AddHandleXY('adj1','0','50000', undefined, '0', '0', 'tx2', 't');
            f.AddHandleXY('adj2','0','50000', undefined, '0', '0', 'bx1', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'tx1', 't');
            f.AddPathCommand(2, 'tx2', 't');
            f.AddPathCommand(2, 'r', 'tx1');
            f.AddPathCommand(2, 'r', 'by1');
            f.AddPathCommand(2, 'bx2', 'b');
            f.AddPathCommand(2, 'bx1', 'b');
            f.AddPathCommand(2, 'l', 'by1');
            f.AddPathCommand(2, 'l', 'tx1');
            f.AddPathCommand(6);
            break;
        }
        case 'snipRoundRect':{
            f.AddAdj('adj1', 15, '16667');
            f.AddAdj('adj2', 15, '16667');
            f.AddGuide('a1', 10, '0', 'adj1', '50000');
            f.AddGuide('a2', 10, '0', 'adj2', '50000');
            f.AddGuide('x1', 0, 'ss', 'a1', '100000');
            f.AddGuide('dx2', 0, 'ss', 'a2', '100000');
            f.AddGuide('x2', 1, 'r', '0', 'dx2');
            f.AddGuide('il', 0, 'x1', '29289', '100000');
            f.AddGuide('ir', 2, 'x2', 'r', '2');
            f.AddHandleXY('adj1','0','50000', undefined, '0', '0', 'x1', 't');
            f.AddHandleXY('adj2','0','50000', undefined, '0', '0', 'x2', 't');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('il', 'il', 'ir', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 't');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(2, 'r', 'dx2');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(2, 'l', 'x1');
            f.AddPathCommand(3, 'x1', 'x1', 'cd2', 'cd4');
            f.AddPathCommand(6);
            break;
        }
        case 'squareTabs':{
            f.AddGuide('md', 9, 'w', 'h', '0');
            f.AddGuide('dx', 0, '1', 'md', '20');
            f.AddGuide('y1', 1, '0', 'b', 'dx');
            f.AddGuide('x1', 1, '0', 'r', 'dx');
            f.AddCnx('cd2', 'l', 't');
            f.AddCnx('cd2', 'l', 'dx');
            f.AddCnx('cd2', 'l', 'y1');
            f.AddCnx('cd2', 'l', 'b');
            f.AddCnx('cd2', 'dx', 'dx');
            f.AddCnx('cd2', 'dx', 'x1');
            f.AddCnx('_3cd4', 'dx', 't');
            f.AddCnx('_3cd4', 'x1', 't');
            f.AddCnx('cd4', 'dx', 'b');
            f.AddCnx('cd4', 'x1', 'b');
            f.AddCnx('0', 'r', 't');
            f.AddCnx('0', 'r', 'dx');
            f.AddCnx('0', 'r', 'y1');
            f.AddCnx('0', 'r', 'b');
            f.AddCnx('0', 'x1', 'dx');
            f.AddCnx('0', 'x1', 'y1');
            f.AddRect('dx', 'dx', 'x1', 'y1');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'dx', 't');
            f.AddPathCommand(2, 'dx', 'dx');
            f.AddPathCommand(2, 'l', 'dx');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y1');
            f.AddPathCommand(2, 'dx', 'y1');
            f.AddPathCommand(2, 'dx', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'dx');
            f.AddPathCommand(2, 'x1', 'dx');
            f.AddPathCommand(6);
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(2, 'r', 'y1');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'star10':{
            f.AddAdj('adj', 15, '42533');
            f.AddAdj('hf', 15, '105146');
            f.AddGuide('a', 10, '0', 'adj', '50000');
            f.AddGuide('swd2', 0, 'wd2', 'hf', '100000');
            f.AddGuide('dx1', 0, 'swd2', '95106', '100000');
            f.AddGuide('dx2', 0, 'swd2', '58779', '100000');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x2', 1, 'hc', '0', 'dx2');
            f.AddGuide('x3', 1, 'hc', 'dx2', '0');
            f.AddGuide('x4', 1, 'hc', 'dx1', '0');
            f.AddGuide('dy1', 0, 'hd2', '80902', '100000');
            f.AddGuide('dy2', 0, 'hd2', '30902', '100000');
            f.AddGuide('y1', 1, 'vc', '0', 'dy1');
            f.AddGuide('y2', 1, 'vc', '0', 'dy2');
            f.AddGuide('y3', 1, 'vc', 'dy2', '0');
            f.AddGuide('y4', 1, 'vc', 'dy1', '0');
            f.AddGuide('iwd2', 0, 'swd2', 'a', '50000');
            f.AddGuide('ihd2', 0, 'hd2', 'a', '50000');
            f.AddGuide('sdx1', 0, 'iwd2', '80902', '100000');
            f.AddGuide('sdx2', 0, 'iwd2', '30902', '100000');
            f.AddGuide('sdy1', 0, 'ihd2', '95106', '100000');
            f.AddGuide('sdy2', 0, 'ihd2', '58779', '100000');
            f.AddGuide('sx1', 1, 'hc', '0', 'iwd2');
            f.AddGuide('sx2', 1, 'hc', '0', 'sdx1');
            f.AddGuide('sx3', 1, 'hc', '0', 'sdx2');
            f.AddGuide('sx4', 1, 'hc', 'sdx2', '0');
            f.AddGuide('sx5', 1, 'hc', 'sdx1', '0');
            f.AddGuide('sx6', 1, 'hc', 'iwd2', '0');
            f.AddGuide('sy1', 1, 'vc', '0', 'sdy1');
            f.AddGuide('sy2', 1, 'vc', '0', 'sdy2');
            f.AddGuide('sy3', 1, 'vc', 'sdy2', '0');
            f.AddGuide('sy4', 1, 'vc', 'sdy1', '0');
            f.AddGuide('yAdj', 1, 'vc', '0', 'ihd2');
            f.AddHandleXY(undefined, '0', '0','adj','0','50000', 'hc', 'yAdj');
            f.AddCnx('0', 'x4', 'y2');
            f.AddCnx('0', 'x4', 'y3');
            f.AddCnx('cd4', 'x3', 'y4');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd4', 'x2', 'y4');
            f.AddCnx('cd2', 'x1', 'y3');
            f.AddCnx('cd2', 'x1', 'y2');
            f.AddCnx('_3cd4', 'x2', 'y1');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('_3cd4', 'x3', 'y1');
            f.AddRect('sx2', 'sy2', 'sx5', 'sy3');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y2');
            f.AddPathCommand(2, 'sx2', 'sy2');
            f.AddPathCommand(2, 'x2', 'y1');
            f.AddPathCommand(2, 'sx3', 'sy1');
            f.AddPathCommand(2, 'hc', 't');
            f.AddPathCommand(2, 'sx4', 'sy1');
            f.AddPathCommand(2, 'x3', 'y1');
            f.AddPathCommand(2, 'sx5', 'sy2');
            f.AddPathCommand(2, 'x4', 'y2');
            f.AddPathCommand(2, 'sx6', 'vc');
            f.AddPathCommand(2, 'x4', 'y3');
            f.AddPathCommand(2, 'sx5', 'sy3');
            f.AddPathCommand(2, 'x3', 'y4');
            f.AddPathCommand(2, 'sx4', 'sy4');
            f.AddPathCommand(2, 'hc', 'b');
            f.AddPathCommand(2, 'sx3', 'sy4');
            f.AddPathCommand(2, 'x2', 'y4');
            f.AddPathCommand(2, 'sx2', 'sy3');
            f.AddPathCommand(2, 'x1', 'y3');
            f.AddPathCommand(2, 'sx1', 'vc');
            f.AddPathCommand(6);
            break;
        }
        case 'star12':{
            f.AddAdj('adj', 15, '37500');
            f.AddGuide('a', 10, '0', 'adj', '50000');
            f.AddGuide('dx1', 7, 'wd2', '1800000');
            f.AddGuide('dy1', 12, 'hd2', '3600000');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x3', 0, 'w', '3', '4');
            f.AddGuide('x4', 1, 'hc', 'dx1', '0');
            f.AddGuide('y1', 1, 'vc', '0', 'dy1');
            f.AddGuide('y3', 0, 'h', '3', '4');
            f.AddGuide('y4', 1, 'vc', 'dy1', '0');
            f.AddGuide('iwd2', 0, 'wd2', 'a', '50000');
            f.AddGuide('ihd2', 0, 'hd2', 'a', '50000');
            f.AddGuide('sdx1', 7, 'iwd2', '900000');
            f.AddGuide('sdx2', 7, 'iwd2', '2700000');
            f.AddGuide('sdx3', 7, 'iwd2', '4500000');
            f.AddGuide('sdy1', 12, 'ihd2', '4500000');
            f.AddGuide('sdy2', 12, 'ihd2', '2700000');
            f.AddGuide('sdy3', 12, 'ihd2', '900000');
            f.AddGuide('sx1', 1, 'hc', '0', 'sdx1');
            f.AddGuide('sx2', 1, 'hc', '0', 'sdx2');
            f.AddGuide('sx3', 1, 'hc', '0', 'sdx3');
            f.AddGuide('sx4', 1, 'hc', 'sdx3', '0');
            f.AddGuide('sx5', 1, 'hc', 'sdx2', '0');
            f.AddGuide('sx6', 1, 'hc', 'sdx1', '0');
            f.AddGuide('sy1', 1, 'vc', '0', 'sdy1');
            f.AddGuide('sy2', 1, 'vc', '0', 'sdy2');
            f.AddGuide('sy3', 1, 'vc', '0', 'sdy3');
            f.AddGuide('sy4', 1, 'vc', 'sdy3', '0');
            f.AddGuide('sy5', 1, 'vc', 'sdy2', '0');
            f.AddGuide('sy6', 1, 'vc', 'sdy1', '0');
            f.AddGuide('yAdj', 1, 'vc', '0', 'ihd2');
            f.AddHandleXY(undefined, '0', '0','adj','0','50000', 'hc', 'yAdj');
            f.AddCnx('0', 'x4', 'hd4');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('0', 'x4', 'y3');
            f.AddCnx('cd4', 'x3', 'y4');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd4', 'wd4', 'y4');
            f.AddCnx('cd2', 'x1', 'y3');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd2', 'x1', 'hd4');
            f.AddCnx('_3cd4', 'wd4', 'y1');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('_3cd4', 'x3', 'y1');
            f.AddRect('sx2', 'sy2', 'sx5', 'sy5');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(2, 'sx1', 'sy3');
            f.AddPathCommand(2, 'x1', 'hd4');
            f.AddPathCommand(2, 'sx2', 'sy2');
            f.AddPathCommand(2, 'wd4', 'y1');
            f.AddPathCommand(2, 'sx3', 'sy1');
            f.AddPathCommand(2, 'hc', 't');
            f.AddPathCommand(2, 'sx4', 'sy1');
            f.AddPathCommand(2, 'x3', 'y1');
            f.AddPathCommand(2, 'sx5', 'sy2');
            f.AddPathCommand(2, 'x4', 'hd4');
            f.AddPathCommand(2, 'sx6', 'sy3');
            f.AddPathCommand(2, 'r', 'vc');
            f.AddPathCommand(2, 'sx6', 'sy4');
            f.AddPathCommand(2, 'x4', 'y3');
            f.AddPathCommand(2, 'sx5', 'sy5');
            f.AddPathCommand(2, 'x3', 'y4');
            f.AddPathCommand(2, 'sx4', 'sy6');
            f.AddPathCommand(2, 'hc', 'b');
            f.AddPathCommand(2, 'sx3', 'sy6');
            f.AddPathCommand(2, 'wd4', 'y4');
            f.AddPathCommand(2, 'sx2', 'sy5');
            f.AddPathCommand(2, 'x1', 'y3');
            f.AddPathCommand(2, 'sx1', 'sy4');
            f.AddPathCommand(6);
            break;
        }
        case 'star16':{
            f.AddAdj('adj', 15, '37500');
            f.AddGuide('a', 10, '0', 'adj', '50000');
            f.AddGuide('dx1', 0, 'wd2', '92388', '100000');
            f.AddGuide('dx2', 0, 'wd2', '70711', '100000');
            f.AddGuide('dx3', 0, 'wd2', '38268', '100000');
            f.AddGuide('dy1', 0, 'hd2', '92388', '100000');
            f.AddGuide('dy2', 0, 'hd2', '70711', '100000');
            f.AddGuide('dy3', 0, 'hd2', '38268', '100000');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x2', 1, 'hc', '0', 'dx2');
            f.AddGuide('x3', 1, 'hc', '0', 'dx3');
            f.AddGuide('x4', 1, 'hc', 'dx3', '0');
            f.AddGuide('x5', 1, 'hc', 'dx2', '0');
            f.AddGuide('x6', 1, 'hc', 'dx1', '0');
            f.AddGuide('y1', 1, 'vc', '0', 'dy1');
            f.AddGuide('y2', 1, 'vc', '0', 'dy2');
            f.AddGuide('y3', 1, 'vc', '0', 'dy3');
            f.AddGuide('y4', 1, 'vc', 'dy3', '0');
            f.AddGuide('y5', 1, 'vc', 'dy2', '0');
            f.AddGuide('y6', 1, 'vc', 'dy1', '0');
            f.AddGuide('iwd2', 0, 'wd2', 'a', '50000');
            f.AddGuide('ihd2', 0, 'hd2', 'a', '50000');
            f.AddGuide('sdx1', 0, 'iwd2', '98079', '100000');
            f.AddGuide('sdx2', 0, 'iwd2', '83147', '100000');
            f.AddGuide('sdx3', 0, 'iwd2', '55557', '100000');
            f.AddGuide('sdx4', 0, 'iwd2', '19509', '100000');
            f.AddGuide('sdy1', 0, 'ihd2', '98079', '100000');
            f.AddGuide('sdy2', 0, 'ihd2', '83147', '100000');
            f.AddGuide('sdy3', 0, 'ihd2', '55557', '100000');
            f.AddGuide('sdy4', 0, 'ihd2', '19509', '100000');
            f.AddGuide('sx1', 1, 'hc', '0', 'sdx1');
            f.AddGuide('sx2', 1, 'hc', '0', 'sdx2');
            f.AddGuide('sx3', 1, 'hc', '0', 'sdx3');
            f.AddGuide('sx4', 1, 'hc', '0', 'sdx4');
            f.AddGuide('sx5', 1, 'hc', 'sdx4', '0');
            f.AddGuide('sx6', 1, 'hc', 'sdx3', '0');
            f.AddGuide('sx7', 1, 'hc', 'sdx2', '0');
            f.AddGuide('sx8', 1, 'hc', 'sdx1', '0');
            f.AddGuide('sy1', 1, 'vc', '0', 'sdy1');
            f.AddGuide('sy2', 1, 'vc', '0', 'sdy2');
            f.AddGuide('sy3', 1, 'vc', '0', 'sdy3');
            f.AddGuide('sy4', 1, 'vc', '0', 'sdy4');
            f.AddGuide('sy5', 1, 'vc', 'sdy4', '0');
            f.AddGuide('sy6', 1, 'vc', 'sdy3', '0');
            f.AddGuide('sy7', 1, 'vc', 'sdy2', '0');
            f.AddGuide('sy8', 1, 'vc', 'sdy1', '0');
            f.AddGuide('idx', 7, 'iwd2', '2700000');
            f.AddGuide('idy', 12, 'ihd2', '2700000');
            f.AddGuide('il', 1, 'hc', '0', 'idx');
            f.AddGuide('it', 1, 'vc', '0', 'idy');
            f.AddGuide('ir', 1, 'hc', 'idx', '0');
            f.AddGuide('ib', 1, 'vc', 'idy', '0');
            f.AddGuide('yAdj', 1, 'vc', '0', 'ihd2');
            f.AddHandleXY(undefined, '0', '0','adj','0','50000', 'hc', 'yAdj');
            f.AddCnx('0', 'x5', 'y2');
            f.AddCnx('0', 'x6', 'y3');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('0', 'x6', 'y4');
            f.AddCnx('0', 'x5', 'y5');
            f.AddCnx('cd4', 'x4', 'y6');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd4', 'x3', 'y6');
            f.AddCnx('cd2', 'x2', 'y5');
            f.AddCnx('cd2', 'x1', 'y4');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd2', 'x1', 'y3');
            f.AddCnx('cd2', 'x2', 'y2');
            f.AddCnx('_3cd4', 'x3', 'y1');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('_3cd4', 'x4', 'y1');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(2, 'sx1', 'sy4');
            f.AddPathCommand(2, 'x1', 'y3');
            f.AddPathCommand(2, 'sx2', 'sy3');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'sx3', 'sy2');
            f.AddPathCommand(2, 'x3', 'y1');
            f.AddPathCommand(2, 'sx4', 'sy1');
            f.AddPathCommand(2, 'hc', 't');
            f.AddPathCommand(2, 'sx5', 'sy1');
            f.AddPathCommand(2, 'x4', 'y1');
            f.AddPathCommand(2, 'sx6', 'sy2');
            f.AddPathCommand(2, 'x5', 'y2');
            f.AddPathCommand(2, 'sx7', 'sy3');
            f.AddPathCommand(2, 'x6', 'y3');
            f.AddPathCommand(2, 'sx8', 'sy4');
            f.AddPathCommand(2, 'r', 'vc');
            f.AddPathCommand(2, 'sx8', 'sy5');
            f.AddPathCommand(2, 'x6', 'y4');
            f.AddPathCommand(2, 'sx7', 'sy6');
            f.AddPathCommand(2, 'x5', 'y5');
            f.AddPathCommand(2, 'sx6', 'sy7');
            f.AddPathCommand(2, 'x4', 'y6');
            f.AddPathCommand(2, 'sx5', 'sy8');
            f.AddPathCommand(2, 'hc', 'b');
            f.AddPathCommand(2, 'sx4', 'sy8');
            f.AddPathCommand(2, 'x3', 'y6');
            f.AddPathCommand(2, 'sx3', 'sy7');
            f.AddPathCommand(2, 'x2', 'y5');
            f.AddPathCommand(2, 'sx2', 'sy6');
            f.AddPathCommand(2, 'x1', 'y4');
            f.AddPathCommand(2, 'sx1', 'sy5');
            f.AddPathCommand(6);
            break;
        }
        case 'star24':{
            f.AddAdj('adj', 15, '37500');
            f.AddGuide('a', 10, '0', 'adj', '50000');
            f.AddGuide('dx1', 7, 'wd2', '900000');
            f.AddGuide('dx2', 7, 'wd2', '1800000');
            f.AddGuide('dx3', 7, 'wd2', '2700000');
            f.AddGuide('dx4', 15, 'wd4');
            f.AddGuide('dx5', 7, 'wd2', '4500000');
            f.AddGuide('dy1', 12, 'hd2', '4500000');
            f.AddGuide('dy2', 12, 'hd2', '3600000');
            f.AddGuide('dy3', 12, 'hd2', '2700000');
            f.AddGuide('dy4', 15, 'hd4');
            f.AddGuide('dy5', 12, 'hd2', '900000');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x2', 1, 'hc', '0', 'dx2');
            f.AddGuide('x3', 1, 'hc', '0', 'dx3');
            f.AddGuide('x4', 1, 'hc', '0', 'dx4');
            f.AddGuide('x5', 1, 'hc', '0', 'dx5');
            f.AddGuide('x6', 1, 'hc', 'dx5', '0');
            f.AddGuide('x7', 1, 'hc', 'dx4', '0');
            f.AddGuide('x8', 1, 'hc', 'dx3', '0');
            f.AddGuide('x9', 1, 'hc', 'dx2', '0');
            f.AddGuide('x10', 1, 'hc', 'dx1', '0');
            f.AddGuide('y1', 1, 'vc', '0', 'dy1');
            f.AddGuide('y2', 1, 'vc', '0', 'dy2');
            f.AddGuide('y3', 1, 'vc', '0', 'dy3');
            f.AddGuide('y4', 1, 'vc', '0', 'dy4');
            f.AddGuide('y5', 1, 'vc', '0', 'dy5');
            f.AddGuide('y6', 1, 'vc', 'dy5', '0');
            f.AddGuide('y7', 1, 'vc', 'dy4', '0');
            f.AddGuide('y8', 1, 'vc', 'dy3', '0');
            f.AddGuide('y9', 1, 'vc', 'dy2', '0');
            f.AddGuide('y10', 1, 'vc', 'dy1', '0');
            f.AddGuide('iwd2', 0, 'wd2', 'a', '50000');
            f.AddGuide('ihd2', 0, 'hd2', 'a', '50000');
            f.AddGuide('sdx1', 0, 'iwd2', '99144', '100000');
            f.AddGuide('sdx2', 0, 'iwd2', '92388', '100000');
            f.AddGuide('sdx3', 0, 'iwd2', '79335', '100000');
            f.AddGuide('sdx4', 0, 'iwd2', '60876', '100000');
            f.AddGuide('sdx5', 0, 'iwd2', '38268', '100000');
            f.AddGuide('sdx6', 0, 'iwd2', '13053', '100000');
            f.AddGuide('sdy1', 0, 'ihd2', '99144', '100000');
            f.AddGuide('sdy2', 0, 'ihd2', '92388', '100000');
            f.AddGuide('sdy3', 0, 'ihd2', '79335', '100000');
            f.AddGuide('sdy4', 0, 'ihd2', '60876', '100000');
            f.AddGuide('sdy5', 0, 'ihd2', '38268', '100000');
            f.AddGuide('sdy6', 0, 'ihd2', '13053', '100000');
            f.AddGuide('sx1', 1, 'hc', '0', 'sdx1');
            f.AddGuide('sx2', 1, 'hc', '0', 'sdx2');
            f.AddGuide('sx3', 1, 'hc', '0', 'sdx3');
            f.AddGuide('sx4', 1, 'hc', '0', 'sdx4');
            f.AddGuide('sx5', 1, 'hc', '0', 'sdx5');
            f.AddGuide('sx6', 1, 'hc', '0', 'sdx6');
            f.AddGuide('sx7', 1, 'hc', 'sdx6', '0');
            f.AddGuide('sx8', 1, 'hc', 'sdx5', '0');
            f.AddGuide('sx9', 1, 'hc', 'sdx4', '0');
            f.AddGuide('sx10', 1, 'hc', 'sdx3', '0');
            f.AddGuide('sx11', 1, 'hc', 'sdx2', '0');
            f.AddGuide('sx12', 1, 'hc', 'sdx1', '0');
            f.AddGuide('sy1', 1, 'vc', '0', 'sdy1');
            f.AddGuide('sy2', 1, 'vc', '0', 'sdy2');
            f.AddGuide('sy3', 1, 'vc', '0', 'sdy3');
            f.AddGuide('sy4', 1, 'vc', '0', 'sdy4');
            f.AddGuide('sy5', 1, 'vc', '0', 'sdy5');
            f.AddGuide('sy6', 1, 'vc', '0', 'sdy6');
            f.AddGuide('sy7', 1, 'vc', 'sdy6', '0');
            f.AddGuide('sy8', 1, 'vc', 'sdy5', '0');
            f.AddGuide('sy9', 1, 'vc', 'sdy4', '0');
            f.AddGuide('sy10', 1, 'vc', 'sdy3', '0');
            f.AddGuide('sy11', 1, 'vc', 'sdy2', '0');
            f.AddGuide('sy12', 1, 'vc', 'sdy1', '0');
            f.AddGuide('idx', 7, 'iwd2', '2700000');
            f.AddGuide('idy', 12, 'ihd2', '2700000');
            f.AddGuide('il', 1, 'hc', '0', 'idx');
            f.AddGuide('it', 1, 'vc', '0', 'idy');
            f.AddGuide('ir', 1, 'hc', 'idx', '0');
            f.AddGuide('ib', 1, 'vc', 'idy', '0');
            f.AddGuide('yAdj', 1, 'vc', '0', 'ihd2');
            f.AddHandleXY(undefined, '0', '0','adj','0','50000', 'hc', 'yAdj');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(2, 'sx1', 'sy6');
            f.AddPathCommand(2, 'x1', 'y5');
            f.AddPathCommand(2, 'sx2', 'sy5');
            f.AddPathCommand(2, 'x2', 'y4');
            f.AddPathCommand(2, 'sx3', 'sy4');
            f.AddPathCommand(2, 'x3', 'y3');
            f.AddPathCommand(2, 'sx4', 'sy3');
            f.AddPathCommand(2, 'x4', 'y2');
            f.AddPathCommand(2, 'sx5', 'sy2');
            f.AddPathCommand(2, 'x5', 'y1');
            f.AddPathCommand(2, 'sx6', 'sy1');
            f.AddPathCommand(2, 'hc', 't');
            f.AddPathCommand(2, 'sx7', 'sy1');
            f.AddPathCommand(2, 'x6', 'y1');
            f.AddPathCommand(2, 'sx8', 'sy2');
            f.AddPathCommand(2, 'x7', 'y2');
            f.AddPathCommand(2, 'sx9', 'sy3');
            f.AddPathCommand(2, 'x8', 'y3');
            f.AddPathCommand(2, 'sx10', 'sy4');
            f.AddPathCommand(2, 'x9', 'y4');
            f.AddPathCommand(2, 'sx11', 'sy5');
            f.AddPathCommand(2, 'x10', 'y5');
            f.AddPathCommand(2, 'sx12', 'sy6');
            f.AddPathCommand(2, 'r', 'vc');
            f.AddPathCommand(2, 'sx12', 'sy7');
            f.AddPathCommand(2, 'x10', 'y6');
            f.AddPathCommand(2, 'sx11', 'sy8');
            f.AddPathCommand(2, 'x9', 'y7');
            f.AddPathCommand(2, 'sx10', 'sy9');
            f.AddPathCommand(2, 'x8', 'y8');
            f.AddPathCommand(2, 'sx9', 'sy10');
            f.AddPathCommand(2, 'x7', 'y9');
            f.AddPathCommand(2, 'sx8', 'sy11');
            f.AddPathCommand(2, 'x6', 'y10');
            f.AddPathCommand(2, 'sx7', 'sy12');
            f.AddPathCommand(2, 'hc', 'b');
            f.AddPathCommand(2, 'sx6', 'sy12');
            f.AddPathCommand(2, 'x5', 'y10');
            f.AddPathCommand(2, 'sx5', 'sy11');
            f.AddPathCommand(2, 'x4', 'y9');
            f.AddPathCommand(2, 'sx4', 'sy10');
            f.AddPathCommand(2, 'x3', 'y8');
            f.AddPathCommand(2, 'sx3', 'sy9');
            f.AddPathCommand(2, 'x2', 'y7');
            f.AddPathCommand(2, 'sx2', 'sy8');
            f.AddPathCommand(2, 'x1', 'y6');
            f.AddPathCommand(2, 'sx1', 'sy7');
            f.AddPathCommand(6);
            break;
        }
        case 'star32':{
            f.AddAdj('adj', 15, '37500');
            f.AddGuide('a', 10, '0', 'adj', '50000');
            f.AddGuide('dx1', 0, 'wd2', '98079', '100000');
            f.AddGuide('dx2', 0, 'wd2', '92388', '100000');
            f.AddGuide('dx3', 0, 'wd2', '83147', '100000');
            f.AddGuide('dx4', 7, 'wd2', '2700000');
            f.AddGuide('dx5', 0, 'wd2', '55557', '100000');
            f.AddGuide('dx6', 0, 'wd2', '38268', '100000');
            f.AddGuide('dx7', 0, 'wd2', '19509', '100000');
            f.AddGuide('dy1', 0, 'hd2', '98079', '100000');
            f.AddGuide('dy2', 0, 'hd2', '92388', '100000');
            f.AddGuide('dy3', 0, 'hd2', '83147', '100000');
            f.AddGuide('dy4', 12, 'hd2', '2700000');
            f.AddGuide('dy5', 0, 'hd2', '55557', '100000');
            f.AddGuide('dy6', 0, 'hd2', '38268', '100000');
            f.AddGuide('dy7', 0, 'hd2', '19509', '100000');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x2', 1, 'hc', '0', 'dx2');
            f.AddGuide('x3', 1, 'hc', '0', 'dx3');
            f.AddGuide('x4', 1, 'hc', '0', 'dx4');
            f.AddGuide('x5', 1, 'hc', '0', 'dx5');
            f.AddGuide('x6', 1, 'hc', '0', 'dx6');
            f.AddGuide('x7', 1, 'hc', '0', 'dx7');
            f.AddGuide('x8', 1, 'hc', 'dx7', '0');
            f.AddGuide('x9', 1, 'hc', 'dx6', '0');
            f.AddGuide('x10', 1, 'hc', 'dx5', '0');
            f.AddGuide('x11', 1, 'hc', 'dx4', '0');
            f.AddGuide('x12', 1, 'hc', 'dx3', '0');
            f.AddGuide('x13', 1, 'hc', 'dx2', '0');
            f.AddGuide('x14', 1, 'hc', 'dx1', '0');
            f.AddGuide('y1', 1, 'vc', '0', 'dy1');
            f.AddGuide('y2', 1, 'vc', '0', 'dy2');
            f.AddGuide('y3', 1, 'vc', '0', 'dy3');
            f.AddGuide('y4', 1, 'vc', '0', 'dy4');
            f.AddGuide('y5', 1, 'vc', '0', 'dy5');
            f.AddGuide('y6', 1, 'vc', '0', 'dy6');
            f.AddGuide('y7', 1, 'vc', '0', 'dy7');
            f.AddGuide('y8', 1, 'vc', 'dy7', '0');
            f.AddGuide('y9', 1, 'vc', 'dy6', '0');
            f.AddGuide('y10', 1, 'vc', 'dy5', '0');
            f.AddGuide('y11', 1, 'vc', 'dy4', '0');
            f.AddGuide('y12', 1, 'vc', 'dy3', '0');
            f.AddGuide('y13', 1, 'vc', 'dy2', '0');
            f.AddGuide('y14', 1, 'vc', 'dy1', '0');
            f.AddGuide('iwd2', 0, 'wd2', 'a', '50000');
            f.AddGuide('ihd2', 0, 'hd2', 'a', '50000');
            f.AddGuide('sdx1', 0, 'iwd2', '99518', '100000');
            f.AddGuide('sdx2', 0, 'iwd2', '95694', '100000');
            f.AddGuide('sdx3', 0, 'iwd2', '88192', '100000');
            f.AddGuide('sdx4', 0, 'iwd2', '77301', '100000');
            f.AddGuide('sdx5', 0, 'iwd2', '63439', '100000');
            f.AddGuide('sdx6', 0, 'iwd2', '47140', '100000');
            f.AddGuide('sdx7', 0, 'iwd2', '29028', '100000');
            f.AddGuide('sdx8', 0, 'iwd2', '9802', '100000');
            f.AddGuide('sdy1', 0, 'ihd2', '99518', '100000');
            f.AddGuide('sdy2', 0, 'ihd2', '95694', '100000');
            f.AddGuide('sdy3', 0, 'ihd2', '88192', '100000');
            f.AddGuide('sdy4', 0, 'ihd2', '77301', '100000');
            f.AddGuide('sdy5', 0, 'ihd2', '63439', '100000');
            f.AddGuide('sdy6', 0, 'ihd2', '47140', '100000');
            f.AddGuide('sdy7', 0, 'ihd2', '29028', '100000');
            f.AddGuide('sdy8', 0, 'ihd2', '9802', '100000');
            f.AddGuide('sx1', 1, 'hc', '0', 'sdx1');
            f.AddGuide('sx2', 1, 'hc', '0', 'sdx2');
            f.AddGuide('sx3', 1, 'hc', '0', 'sdx3');
            f.AddGuide('sx4', 1, 'hc', '0', 'sdx4');
            f.AddGuide('sx5', 1, 'hc', '0', 'sdx5');
            f.AddGuide('sx6', 1, 'hc', '0', 'sdx6');
            f.AddGuide('sx7', 1, 'hc', '0', 'sdx7');
            f.AddGuide('sx8', 1, 'hc', '0', 'sdx8');
            f.AddGuide('sx9', 1, 'hc', 'sdx8', '0');
            f.AddGuide('sx10', 1, 'hc', 'sdx7', '0');
            f.AddGuide('sx11', 1, 'hc', 'sdx6', '0');
            f.AddGuide('sx12', 1, 'hc', 'sdx5', '0');
            f.AddGuide('sx13', 1, 'hc', 'sdx4', '0');
            f.AddGuide('sx14', 1, 'hc', 'sdx3', '0');
            f.AddGuide('sx15', 1, 'hc', 'sdx2', '0');
            f.AddGuide('sx16', 1, 'hc', 'sdx1', '0');
            f.AddGuide('sy1', 1, 'vc', '0', 'sdy1');
            f.AddGuide('sy2', 1, 'vc', '0', 'sdy2');
            f.AddGuide('sy3', 1, 'vc', '0', 'sdy3');
            f.AddGuide('sy4', 1, 'vc', '0', 'sdy4');
            f.AddGuide('sy5', 1, 'vc', '0', 'sdy5');
            f.AddGuide('sy6', 1, 'vc', '0', 'sdy6');
            f.AddGuide('sy7', 1, 'vc', '0', 'sdy7');
            f.AddGuide('sy8', 1, 'vc', '0', 'sdy8');
            f.AddGuide('sy9', 1, 'vc', 'sdy8', '0');
            f.AddGuide('sy10', 1, 'vc', 'sdy7', '0');
            f.AddGuide('sy11', 1, 'vc', 'sdy6', '0');
            f.AddGuide('sy12', 1, 'vc', 'sdy5', '0');
            f.AddGuide('sy13', 1, 'vc', 'sdy4', '0');
            f.AddGuide('sy14', 1, 'vc', 'sdy3', '0');
            f.AddGuide('sy15', 1, 'vc', 'sdy2', '0');
            f.AddGuide('sy16', 1, 'vc', 'sdy1', '0');
            f.AddGuide('idx', 7, 'iwd2', '2700000');
            f.AddGuide('idy', 12, 'ihd2', '2700000');
            f.AddGuide('il', 1, 'hc', '0', 'idx');
            f.AddGuide('it', 1, 'vc', '0', 'idy');
            f.AddGuide('ir', 1, 'hc', 'idx', '0');
            f.AddGuide('ib', 1, 'vc', 'idy', '0');
            f.AddGuide('yAdj', 1, 'vc', '0', 'ihd2');
            f.AddHandleXY(undefined, '0', '0','adj','0','50000', 'hc', 'yAdj');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(2, 'sx1', 'sy8');
            f.AddPathCommand(2, 'x1', 'y7');
            f.AddPathCommand(2, 'sx2', 'sy7');
            f.AddPathCommand(2, 'x2', 'y6');
            f.AddPathCommand(2, 'sx3', 'sy6');
            f.AddPathCommand(2, 'x3', 'y5');
            f.AddPathCommand(2, 'sx4', 'sy5');
            f.AddPathCommand(2, 'x4', 'y4');
            f.AddPathCommand(2, 'sx5', 'sy4');
            f.AddPathCommand(2, 'x5', 'y3');
            f.AddPathCommand(2, 'sx6', 'sy3');
            f.AddPathCommand(2, 'x6', 'y2');
            f.AddPathCommand(2, 'sx7', 'sy2');
            f.AddPathCommand(2, 'x7', 'y1');
            f.AddPathCommand(2, 'sx8', 'sy1');
            f.AddPathCommand(2, 'hc', 't');
            f.AddPathCommand(2, 'sx9', 'sy1');
            f.AddPathCommand(2, 'x8', 'y1');
            f.AddPathCommand(2, 'sx10', 'sy2');
            f.AddPathCommand(2, 'x9', 'y2');
            f.AddPathCommand(2, 'sx11', 'sy3');
            f.AddPathCommand(2, 'x10', 'y3');
            f.AddPathCommand(2, 'sx12', 'sy4');
            f.AddPathCommand(2, 'x11', 'y4');
            f.AddPathCommand(2, 'sx13', 'sy5');
            f.AddPathCommand(2, 'x12', 'y5');
            f.AddPathCommand(2, 'sx14', 'sy6');
            f.AddPathCommand(2, 'x13', 'y6');
            f.AddPathCommand(2, 'sx15', 'sy7');
            f.AddPathCommand(2, 'x14', 'y7');
            f.AddPathCommand(2, 'sx16', 'sy8');
            f.AddPathCommand(2, 'r', 'vc');
            f.AddPathCommand(2, 'sx16', 'sy9');
            f.AddPathCommand(2, 'x14', 'y8');
            f.AddPathCommand(2, 'sx15', 'sy10');
            f.AddPathCommand(2, 'x13', 'y9');
            f.AddPathCommand(2, 'sx14', 'sy11');
            f.AddPathCommand(2, 'x12', 'y10');
            f.AddPathCommand(2, 'sx13', 'sy12');
            f.AddPathCommand(2, 'x11', 'y11');
            f.AddPathCommand(2, 'sx12', 'sy13');
            f.AddPathCommand(2, 'x10', 'y12');
            f.AddPathCommand(2, 'sx11', 'sy14');
            f.AddPathCommand(2, 'x9', 'y13');
            f.AddPathCommand(2, 'sx10', 'sy15');
            f.AddPathCommand(2, 'x8', 'y14');
            f.AddPathCommand(2, 'sx9', 'sy16');
            f.AddPathCommand(2, 'hc', 'b');
            f.AddPathCommand(2, 'sx8', 'sy16');
            f.AddPathCommand(2, 'x7', 'y14');
            f.AddPathCommand(2, 'sx7', 'sy15');
            f.AddPathCommand(2, 'x6', 'y13');
            f.AddPathCommand(2, 'sx6', 'sy14');
            f.AddPathCommand(2, 'x5', 'y12');
            f.AddPathCommand(2, 'sx5', 'sy13');
            f.AddPathCommand(2, 'x4', 'y11');
            f.AddPathCommand(2, 'sx4', 'sy12');
            f.AddPathCommand(2, 'x3', 'y10');
            f.AddPathCommand(2, 'sx3', 'sy11');
            f.AddPathCommand(2, 'x2', 'y9');
            f.AddPathCommand(2, 'sx2', 'sy10');
            f.AddPathCommand(2, 'x1', 'y8');
            f.AddPathCommand(2, 'sx1', 'sy9');
            f.AddPathCommand(6);
            break;
        }
        case 'star4':{
            f.AddAdj('adj', 15, '12500');
            f.AddGuide('a', 10, '0', 'adj', '50000');
            f.AddGuide('iwd2', 0, 'wd2', 'a', '50000');
            f.AddGuide('ihd2', 0, 'hd2', 'a', '50000');
            f.AddGuide('sdx', 7, 'iwd2', '2700000');
            f.AddGuide('sdy', 12, 'ihd2', '2700000');
            f.AddGuide('sx1', 1, 'hc', '0', 'sdx');
            f.AddGuide('sx2', 1, 'hc', 'sdx', '0');
            f.AddGuide('sy1', 1, 'vc', '0', 'sdy');
            f.AddGuide('sy2', 1, 'vc', 'sdy', '0');
            f.AddGuide('yAdj', 1, 'vc', '0', 'ihd2');
            f.AddHandleXY(undefined, '0', '0','adj','0','50000', 'hc', 'yAdj');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('sx1', 'sy1', 'sx2', 'sy2');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(2, 'sx1', 'sy1');
            f.AddPathCommand(2, 'hc', 't');
            f.AddPathCommand(2, 'sx2', 'sy1');
            f.AddPathCommand(2, 'r', 'vc');
            f.AddPathCommand(2, 'sx2', 'sy2');
            f.AddPathCommand(2, 'hc', 'b');
            f.AddPathCommand(2, 'sx1', 'sy2');
            f.AddPathCommand(6);
            break;
        }
        case 'star5':{
            f.AddAdj('adj', 15, '19098');
            f.AddAdj('hf', 15, '105146');
            f.AddAdj('vf', 15, '110557');
            f.AddGuide('a', 10, '0', 'adj', '50000');
            f.AddGuide('swd2', 0, 'wd2', 'hf', '100000');
            f.AddGuide('shd2', 0, 'hd2', 'vf', '100000');
            f.AddGuide('svc', 0, 'vc', 'vf', '100000');
            f.AddGuide('dx1', 7, 'swd2', '1080000');
            f.AddGuide('dx2', 7, 'swd2', '18360000');
            f.AddGuide('dy1', 12, 'shd2', '1080000');
            f.AddGuide('dy2', 12, 'shd2', '18360000');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x2', 1, 'hc', '0', 'dx2');
            f.AddGuide('x3', 1, 'hc', 'dx2', '0');
            f.AddGuide('x4', 1, 'hc', 'dx1', '0');
            f.AddGuide('y1', 1, 'svc', '0', 'dy1');
            f.AddGuide('y2', 1, 'svc', '0', 'dy2');
            f.AddGuide('iwd2', 0, 'swd2', 'a', '50000');
            f.AddGuide('ihd2', 0, 'shd2', 'a', '50000');
            f.AddGuide('sdx1', 7, 'iwd2', '20520000');
            f.AddGuide('sdx2', 7, 'iwd2', '3240000');
            f.AddGuide('sdy1', 12, 'ihd2', '3240000');
            f.AddGuide('sdy2', 12, 'ihd2', '20520000');
            f.AddGuide('sx1', 1, 'hc', '0', 'sdx1');
            f.AddGuide('sx2', 1, 'hc', '0', 'sdx2');
            f.AddGuide('sx3', 1, 'hc', 'sdx2', '0');
            f.AddGuide('sx4', 1, 'hc', 'sdx1', '0');
            f.AddGuide('sy1', 1, 'svc', '0', 'sdy1');
            f.AddGuide('sy2', 1, 'svc', '0', 'sdy2');
            f.AddGuide('sy3', 1, 'svc', 'ihd2', '0');
            f.AddGuide('yAdj', 1, 'svc', '0', 'ihd2');
            f.AddHandleXY(undefined, '0', '0','adj','0','50000', 'hc', 'yAdj');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'x1', 'y1');
            f.AddCnx('cd4', 'x2', 'y2');
            f.AddCnx('cd4', 'x3', 'y2');
            f.AddCnx('0', 'x4', 'y1');
            f.AddRect('sx1', 'sy1', 'sx4', 'sy3');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y1');
            f.AddPathCommand(2, 'sx2', 'sy1');
            f.AddPathCommand(2, 'hc', 't');
            f.AddPathCommand(2, 'sx3', 'sy1');
            f.AddPathCommand(2, 'x4', 'y1');
            f.AddPathCommand(2, 'sx4', 'sy2');
            f.AddPathCommand(2, 'x3', 'y2');
            f.AddPathCommand(2, 'hc', 'sy3');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'sx1', 'sy2');
            f.AddPathCommand(6);
            break;
        }
        case 'star6':{
            f.AddAdj('adj', 15, '28868');
            f.AddAdj('hf', 15, '115470');
            f.AddGuide('a', 10, '0', 'adj', '50000');
            f.AddGuide('swd2', 0, 'wd2', 'hf', '100000');
            f.AddGuide('dx1', 7, 'swd2', '1800000');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x2', 1, 'hc', 'dx1', '0');
            f.AddGuide('y2', 1, 'vc', 'hd4', '0');
            f.AddGuide('iwd2', 0, 'swd2', 'a', '50000');
            f.AddGuide('ihd2', 0, 'hd2', 'a', '50000');
            f.AddGuide('sdx2', 0, 'iwd2', '1', '2');
            f.AddGuide('sx1', 1, 'hc', '0', 'iwd2');
            f.AddGuide('sx2', 1, 'hc', '0', 'sdx2');
            f.AddGuide('sx3', 1, 'hc', 'sdx2', '0');
            f.AddGuide('sx4', 1, 'hc', 'iwd2', '0');
            f.AddGuide('sdy1', 12, 'ihd2', '3600000');
            f.AddGuide('sy1', 1, 'vc', '0', 'sdy1');
            f.AddGuide('sy2', 1, 'vc', 'sdy1', '0');
            f.AddGuide('yAdj', 1, 'vc', '0', 'ihd2');
            f.AddHandleXY(undefined, '0', '0','adj','0','50000', 'hc', 'yAdj');
            f.AddCnx('0', 'x2', 'hd4');
            f.AddCnx('0', 'x2', 'y2');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'x1', 'y2');
            f.AddCnx('cd2', 'x1', 'hd4');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('sx1', 'sy1', 'sx4', 'sy2');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'hd4');
            f.AddPathCommand(2, 'sx2', 'sy1');
            f.AddPathCommand(2, 'hc', 't');
            f.AddPathCommand(2, 'sx3', 'sy1');
            f.AddPathCommand(2, 'x2', 'hd4');
            f.AddPathCommand(2, 'sx4', 'vc');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'sx3', 'sy2');
            f.AddPathCommand(2, 'hc', 'b');
            f.AddPathCommand(2, 'sx2', 'sy2');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(2, 'sx1', 'vc');
            f.AddPathCommand(6);
            break;
        }
        case 'star7':{
            f.AddAdj('adj', 15, '34601');
            f.AddAdj('hf', 15, '102572');
            f.AddAdj('vf', 15, '105210');
            f.AddGuide('a', 10, '0', 'adj', '50000');
            f.AddGuide('swd2', 0, 'wd2', 'hf', '100000');
            f.AddGuide('shd2', 0, 'hd2', 'vf', '100000');
            f.AddGuide('svc', 0, 'vc', 'vf', '100000');
            f.AddGuide('dx1', 0, 'swd2', '97493', '100000');
            f.AddGuide('dx2', 0, 'swd2', '78183', '100000');
            f.AddGuide('dx3', 0, 'swd2', '43388', '100000');
            f.AddGuide('dy1', 0, 'shd2', '62349', '100000');
            f.AddGuide('dy2', 0, 'shd2', '22252', '100000');
            f.AddGuide('dy3', 0, 'shd2', '90097', '100000');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x2', 1, 'hc', '0', 'dx2');
            f.AddGuide('x3', 1, 'hc', '0', 'dx3');
            f.AddGuide('x4', 1, 'hc', 'dx3', '0');
            f.AddGuide('x5', 1, 'hc', 'dx2', '0');
            f.AddGuide('x6', 1, 'hc', 'dx1', '0');
            f.AddGuide('y1', 1, 'svc', '0', 'dy1');
            f.AddGuide('y2', 1, 'svc', 'dy2', '0');
            f.AddGuide('y3', 1, 'svc', 'dy3', '0');
            f.AddGuide('iwd2', 0, 'swd2', 'a', '50000');
            f.AddGuide('ihd2', 0, 'shd2', 'a', '50000');
            f.AddGuide('sdx1', 0, 'iwd2', '97493', '100000');
            f.AddGuide('sdx2', 0, 'iwd2', '78183', '100000');
            f.AddGuide('sdx3', 0, 'iwd2', '43388', '100000');
            f.AddGuide('sx1', 1, 'hc', '0', 'sdx1');
            f.AddGuide('sx2', 1, 'hc', '0', 'sdx2');
            f.AddGuide('sx3', 1, 'hc', '0', 'sdx3');
            f.AddGuide('sx4', 1, 'hc', 'sdx3', '0');
            f.AddGuide('sx5', 1, 'hc', 'sdx2', '0');
            f.AddGuide('sx6', 1, 'hc', 'sdx1', '0');
            f.AddGuide('sdy1', 0, 'ihd2', '90097', '100000');
            f.AddGuide('sdy2', 0, 'ihd2', '22252', '100000');
            f.AddGuide('sdy3', 0, 'ihd2', '62349', '100000');
            f.AddGuide('sy1', 1, 'svc', '0', 'sdy1');
            f.AddGuide('sy2', 1, 'svc', '0', 'sdy2');
            f.AddGuide('sy3', 1, 'svc', 'sdy3', '0');
            f.AddGuide('sy4', 1, 'svc', 'ihd2', '0');
            f.AddGuide('yAdj', 1, 'svc', '0', 'ihd2');
            f.AddHandleXY(undefined, '0', '0','adj','0','50000', 'hc', 'yAdj');
            f.AddCnx('0', 'x5', 'y1');
            f.AddCnx('0', 'x6', 'y2');
            f.AddCnx('cd4', 'x4', 'y3');
            f.AddCnx('cd4', 'x3', 'y3');
            f.AddCnx('cd2', 'x1', 'y2');
            f.AddCnx('cd2', 'x2', 'y1');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddRect('sx2', 'sy1', 'sx5', 'sy3');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x1', 'y2');
            f.AddPathCommand(2, 'sx1', 'sy2');
            f.AddPathCommand(2, 'x2', 'y1');
            f.AddPathCommand(2, 'sx3', 'sy1');
            f.AddPathCommand(2, 'hc', 't');
            f.AddPathCommand(2, 'sx4', 'sy1');
            f.AddPathCommand(2, 'x5', 'y1');
            f.AddPathCommand(2, 'sx6', 'sy2');
            f.AddPathCommand(2, 'x6', 'y2');
            f.AddPathCommand(2, 'sx5', 'sy3');
            f.AddPathCommand(2, 'x4', 'y3');
            f.AddPathCommand(2, 'hc', 'sy4');
            f.AddPathCommand(2, 'x3', 'y3');
            f.AddPathCommand(2, 'sx2', 'sy3');
            f.AddPathCommand(6);
            break;
        }
        case 'star8':{
            f.AddAdj('adj', 15, '37500');
            f.AddGuide('a', 10, '0', 'adj', '50000');
            f.AddGuide('dx1', 7, 'wd2', '2700000');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x2', 1, 'hc', 'dx1', '0');
            f.AddGuide('dy1', 12, 'hd2', '2700000');
            f.AddGuide('y1', 1, 'vc', '0', 'dy1');
            f.AddGuide('y2', 1, 'vc', 'dy1', '0');
            f.AddGuide('iwd2', 0, 'wd2', 'a', '50000');
            f.AddGuide('ihd2', 0, 'hd2', 'a', '50000');
            f.AddGuide('sdx1', 0, 'iwd2', '92388', '100000');
            f.AddGuide('sdx2', 0, 'iwd2', '38268', '100000');
            f.AddGuide('sdy1', 0, 'ihd2', '92388', '100000');
            f.AddGuide('sdy2', 0, 'ihd2', '38268', '100000');
            f.AddGuide('sx1', 1, 'hc', '0', 'sdx1');
            f.AddGuide('sx2', 1, 'hc', '0', 'sdx2');
            f.AddGuide('sx3', 1, 'hc', 'sdx2', '0');
            f.AddGuide('sx4', 1, 'hc', 'sdx1', '0');
            f.AddGuide('sy1', 1, 'vc', '0', 'sdy1');
            f.AddGuide('sy2', 1, 'vc', '0', 'sdy2');
            f.AddGuide('sy3', 1, 'vc', 'sdy2', '0');
            f.AddGuide('sy4', 1, 'vc', 'sdy1', '0');
            f.AddGuide('yAdj', 1, 'vc', '0', 'ihd2');
            f.AddHandleXY(undefined, '0', '0','adj','0','50000', 'hc', 'yAdj');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'x2', 'y2');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd4', 'x1', 'y2');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'x1', 'y1');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('_3cd4', 'x2', 'y1');
            f.AddRect('sx1', 'sy1', 'sx4', 'sy4');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(2, 'sx1', 'sy2');
            f.AddPathCommand(2, 'x1', 'y1');
            f.AddPathCommand(2, 'sx2', 'sy1');
            f.AddPathCommand(2, 'hc', 't');
            f.AddPathCommand(2, 'sx3', 'sy1');
            f.AddPathCommand(2, 'x2', 'y1');
            f.AddPathCommand(2, 'sx4', 'sy2');
            f.AddPathCommand(2, 'r', 'vc');
            f.AddPathCommand(2, 'sx4', 'sy3');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'sx3', 'sy4');
            f.AddPathCommand(2, 'hc', 'b');
            f.AddPathCommand(2, 'sx2', 'sy4');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(2, 'sx1', 'sy3');
            f.AddPathCommand(6);
            break;
        }
        case 'straightConnector1':{
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,undefined, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'r', 'b');
            break;
        }
        case 'stripedRightArrow':{
            f.AddAdj('adj1', 15, '50000');
            f.AddAdj('adj2', 15, '50000');
            f.AddGuide('maxAdj2', 0, '84375', 'w', 'ss');
            f.AddGuide('a1', 10, '0', 'adj1', '100000');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('x4', 0, 'ss', '5', '32');
            f.AddGuide('dx5', 0, 'ss', 'a2', '100000');
            f.AddGuide('x5', 1, 'r', '0', 'dx5');
            f.AddGuide('dy1', 0, 'h', 'a1', '200000');
            f.AddGuide('y1', 1, 'vc', '0', 'dy1');
            f.AddGuide('y2', 1, 'vc', 'dy1', '0');
            f.AddGuide('dx6', 0, 'dy1', 'dx5', 'hd2');
            f.AddGuide('x6', 1, 'r', '0', 'dx6');
            f.AddHandleXY(undefined, '0', '0','adj1','0','100000', 'l', 'y1');
            f.AddHandleXY('adj2','0','maxAdj2', undefined, '0', '0', 'x5', 't');
            f.AddCnx('_3cd4', 'x5', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'x5', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('x4', 'y1', 'x6', 'y2');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y1');
            f.AddPathCommand(2, 'ssd32', 'y1');
            f.AddPathCommand(2, 'ssd32', 'y2');
            f.AddPathCommand(2, 'l', 'y2');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'ssd16', 'y1');
            f.AddPathCommand(2, 'ssd8', 'y1');
            f.AddPathCommand(2, 'ssd8', 'y2');
            f.AddPathCommand(2, 'ssd16', 'y2');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'x4', 'y1');
            f.AddPathCommand(2, 'x5', 'y1');
            f.AddPathCommand(2, 'x5', 't');
            f.AddPathCommand(2, 'r', 'vc');
            f.AddPathCommand(2, 'x5', 'b');
            f.AddPathCommand(2, 'x5', 'y2');
            f.AddPathCommand(2, 'x4', 'y2');
            f.AddPathCommand(6);
            break;
        }
        case 'sun':{
            f.AddAdj('adj', 15, '25000');
            f.AddGuide('a', 10, '12500', 'adj', '46875');
            f.AddGuide('g0', 1, '50000', '0', 'a');
            f.AddGuide('g1', 0, 'g0', '30274', '32768');
            f.AddGuide('g2', 0, 'g0', '12540', '32768');
            f.AddGuide('g3', 1, 'g1', '50000', '0');
            f.AddGuide('g4', 1, 'g2', '50000', '0');
            f.AddGuide('g5', 1, '50000', '0', 'g1');
            f.AddGuide('g6', 1, '50000', '0', 'g2');
            f.AddGuide('g7', 0, 'g0', '23170', '32768');
            f.AddGuide('g8', 1, '50000', 'g7', '0');
            f.AddGuide('g9', 1, '50000', '0', 'g7');
            f.AddGuide('g10', 0, 'g5', '3', '4');
            f.AddGuide('g11', 0, 'g6', '3', '4');
            f.AddGuide('g12', 1, 'g10', '3662', '0');
            f.AddGuide('g13', 1, 'g11', '3662', '0');
            f.AddGuide('g14', 1, 'g11', '12500', '0');
            f.AddGuide('g15', 1, '100000', '0', 'g10');
            f.AddGuide('g16', 1, '100000', '0', 'g12');
            f.AddGuide('g17', 1, '100000', '0', 'g13');
            f.AddGuide('g18', 1, '100000', '0', 'g14');
            f.AddGuide('ox1', 0, 'w', '18436', '21600');
            f.AddGuide('oy1', 0, 'h', '3163', '21600');
            f.AddGuide('ox2', 0, 'w', '3163', '21600');
            f.AddGuide('oy2', 0, 'h', '18436', '21600');
            f.AddGuide('x8', 0, 'w', 'g8', '100000');
            f.AddGuide('x9', 0, 'w', 'g9', '100000');
            f.AddGuide('x10', 0, 'w', 'g10', '100000');
            f.AddGuide('x12', 0, 'w', 'g12', '100000');
            f.AddGuide('x13', 0, 'w', 'g13', '100000');
            f.AddGuide('x14', 0, 'w', 'g14', '100000');
            f.AddGuide('x15', 0, 'w', 'g15', '100000');
            f.AddGuide('x16', 0, 'w', 'g16', '100000');
            f.AddGuide('x17', 0, 'w', 'g17', '100000');
            f.AddGuide('x18', 0, 'w', 'g18', '100000');
            f.AddGuide('x19', 0, 'w', 'a', '100000');
            f.AddGuide('wR', 0, 'w', 'g0', '100000');
            f.AddGuide('hR', 0, 'h', 'g0', '100000');
            f.AddGuide('y8', 0, 'h', 'g8', '100000');
            f.AddGuide('y9', 0, 'h', 'g9', '100000');
            f.AddGuide('y10', 0, 'h', 'g10', '100000');
            f.AddGuide('y12', 0, 'h', 'g12', '100000');
            f.AddGuide('y13', 0, 'h', 'g13', '100000');
            f.AddGuide('y14', 0, 'h', 'g14', '100000');
            f.AddGuide('y15', 0, 'h', 'g15', '100000');
            f.AddGuide('y16', 0, 'h', 'g16', '100000');
            f.AddGuide('y17', 0, 'h', 'g17', '100000');
            f.AddGuide('y18', 0, 'h', 'g18', '100000');
            f.AddHandleXY('adj','12500','46875', undefined, '0', '0', 'x19', 'vc');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('x9', 'y9', 'x8', 'y8');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'r', 'vc');
            f.AddPathCommand(2, 'x15', 'y18');
            f.AddPathCommand(2, 'x15', 'y14');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'ox1', 'oy1');
            f.AddPathCommand(2, 'x16', 'y13');
            f.AddPathCommand(2, 'x17', 'y12');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'hc', 't');
            f.AddPathCommand(2, 'x18', 'y10');
            f.AddPathCommand(2, 'x14', 'y10');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'ox2', 'oy1');
            f.AddPathCommand(2, 'x13', 'y12');
            f.AddPathCommand(2, 'x12', 'y13');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(2, 'x10', 'y14');
            f.AddPathCommand(2, 'x10', 'y18');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'ox2', 'oy2');
            f.AddPathCommand(2, 'x12', 'y17');
            f.AddPathCommand(2, 'x13', 'y16');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'hc', 'b');
            f.AddPathCommand(2, 'x14', 'y15');
            f.AddPathCommand(2, 'x18', 'y15');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'ox1', 'oy2');
            f.AddPathCommand(2, 'x17', 'y16');
            f.AddPathCommand(2, 'x16', 'y17');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'x19', 'vc');
            f.AddPathCommand(3, 'wR', 'hR', 'cd2', '21600000');
            f.AddPathCommand(6);
            break;
        }
        case 'swooshArrow':{
            f.AddAdj('adj1', 15, '25000');
            f.AddAdj('adj2', 15, '16667');
            f.AddGuide('a1', 10, '1', 'adj1', '75000');
            f.AddGuide('maxAdj2', 0, '70000', 'w', 'ss');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('ad1', 0, 'h', 'a1', '100000');
            f.AddGuide('ad2', 0, 'ss', 'a2', '100000');
            f.AddGuide('xB', 1, 'r', '0', 'ad2');
            f.AddGuide('yB', 1, 't', 'ssd8', '0');
            f.AddGuide('alfa', 0, 'cd4', '1', '14');
            f.AddGuide('dx0', 14, 'ssd8', 'alfa');
            f.AddGuide('xC', 1, 'xB', '0', 'dx0');
            f.AddGuide('dx1', 14, 'ad1', 'alfa');
            f.AddGuide('yF', 1, 'yB', 'ad1', '0');
            f.AddGuide('xF', 1, 'xB', 'dx1', '0');
            f.AddGuide('xE', 1, 'xF', 'dx0', '0');
            f.AddGuide('yE', 1, 'yF', 'ssd8', '0');
            f.AddGuide('dy2', 1, 'yE', '0', 't');
            f.AddGuide('dy22', 0, 'dy2', '1', '2');
            f.AddGuide('dy3', 0, 'h', '1', '20');
            f.AddGuide('yD', 1, 't', 'dy22', 'dy3');
            f.AddGuide('dy4', 0, 'hd6', '1', '1');
            f.AddGuide('yP1', 1, 'hd6', 'dy4', '0');
            f.AddGuide('xP1', 15, 'wd6');
            f.AddGuide('dy5', 0, 'hd6', '1', '2');
            f.AddGuide('yP2', 1, 'yF', 'dy5', '0');
            f.AddGuide('xP2', 15, 'wd4');
            f.AddHandleXY(undefined, '0', '0','adj1','1','75000', 'xF', 'yF');
            f.AddHandleXY('adj2','0','maxAdj2', undefined, '0', '0', 'xB', 'yB');
            f.AddCnx('cd4', 'l', 'b');
            f.AddCnx('_3cd4', 'xC', 't');
            f.AddCnx('0', 'r', 'yD');
            f.AddCnx('cd4', 'xE', 'yE');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'b');
            f.AddPathCommand(4, 'xP1', 'yP1', 'xB', 'yB');
            f.AddPathCommand(2, 'xC', 't');
            f.AddPathCommand(2, 'r', 'yD');
            f.AddPathCommand(2, 'xE', 'yE');
            f.AddPathCommand(2, 'xF', 'yF');
            f.AddPathCommand(4, 'xP2', 'yP2', 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'teardrop':{
            f.AddAdj('adj', 15, '100000');
            f.AddGuide('a', 10, '0', 'adj', '200000');
            f.AddGuide('r2', 13, '2');
            f.AddGuide('tw', 0, 'wd2', 'r2', '1');
            f.AddGuide('th', 0, 'hd2', 'r2', '1');
            f.AddGuide('sw', 0, 'tw', 'a', '100000');
            f.AddGuide('sh', 0, 'th', 'a', '100000');
            f.AddGuide('dx1', 7, 'sw', '2700000');
            f.AddGuide('dy1', 12, 'sh', '2700000');
            f.AddGuide('x1', 1, 'hc', 'dx1', '0');
            f.AddGuide('y1', 1, 'vc', '0', 'dy1');
            f.AddGuide('x2', 2, 'hc', 'x1', '2');
            f.AddGuide('y2', 2, 'vc', 'y1', '2');
            f.AddGuide('idx', 7, 'wd2', '2700000');
            f.AddGuide('idy', 12, 'hd2', '2700000');
            f.AddGuide('il', 1, 'hc', '0', 'idx');
            f.AddGuide('ir', 1, 'hc', 'idx', '0');
            f.AddGuide('it', 1, 'vc', '0', 'idy');
            f.AddGuide('ib', 1, 'vc', 'idy', '0');
            f.AddHandleXY('adj','0','200000', undefined, '0', '0', 'x1', 't');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'ir', 'ib');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd4', 'il', 'ib');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('_3cd4', 'il', 'it');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('_3cd4', 'x1', 'y1');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'vc');
            f.AddPathCommand(3, 'wd2', 'hd2', 'cd2', 'cd4');
            f.AddPathCommand(4, 'x2', 't', 'x1', 'y1');
            f.AddPathCommand(4, 'r', 'y2', 'r', 'vc');
            f.AddPathCommand(3, 'wd2', 'hd2', '0', 'cd4');
            f.AddPathCommand(3, 'wd2', 'hd2', 'cd4', 'cd4');
            f.AddPathCommand(6);
            break;
        }
        case 'trapezoid':{
            f.AddAdj('adj', 15, '25000');
            f.AddGuide('maxAdj', 0, '50000', 'w', 'ss');
            f.AddGuide('a', 10, '0', 'adj', 'maxAdj');
            f.AddGuide('x1', 0, 'ss', 'a', '200000');
            f.AddGuide('x2', 0, 'ss', 'a', '100000');
            f.AddGuide('x3', 1, 'r', '0', 'x2');
            f.AddGuide('x4', 1, 'r', '0', 'x1');
            f.AddGuide('il', 0, 'wd3', 'a', 'maxAdj');
            f.AddGuide('it', 0, 'hd3', 'a', 'maxAdj');
            f.AddGuide('ir', 1, 'r', '0', 'il');
            f.AddHandleXY('adj','0','maxAdj', undefined, '0', '0', 'x2', 't');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'x1', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'x4', 'vc');
            f.AddRect('il', 'it', 'ir', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'b');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(2, 'x3', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'triangle':{
            f.AddAdj('adj', 15, '50000');
            f.AddGuide('a', 10, '0', 'adj', '100000');
            f.AddGuide('x1', 0, 'w', 'a', '200000');
            f.AddGuide('x2', 0, 'w', 'a', '100000');
            f.AddGuide('x3', 1, 'x1', 'wd2', '0');
            f.AddHandleXY('adj','0','100000', undefined, '0', '0', 'x2', 't');
            f.AddCnx('_3cd4', 'x2', 't');
            f.AddCnx('cd2', 'x1', 'vc');
            f.AddCnx('cd4', 'l', 'b');
            f.AddCnx('cd4', 'x2', 'b');
            f.AddCnx('cd4', 'r', 'b');
            f.AddCnx('0', 'x3', 'vc');
            f.AddRect('x1', 'vc', 'x3', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'b');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'upArrowCallout':{
            f.AddAdj('adj1', 15, '25000');
            f.AddAdj('adj2', 15, '25000');
            f.AddAdj('adj3', 15, '25000');
            f.AddAdj('adj4', 15, '64977');
            f.AddGuide('maxAdj2', 0, '50000', 'w', 'ss');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('maxAdj1', 0, 'a2', '2', '1');
            f.AddGuide('a1', 10, '0', 'adj1', 'maxAdj1');
            f.AddGuide('maxAdj3', 0, '100000', 'h', 'ss');
            f.AddGuide('a3', 10, '0', 'adj3', 'maxAdj3');
            f.AddGuide('q2', 0, 'a3', 'ss', 'h');
            f.AddGuide('maxAdj4', 1, '100000', '0', 'q2');
            f.AddGuide('a4', 10, '0', 'adj4', 'maxAdj4');
            f.AddGuide('dx1', 0, 'ss', 'a2', '100000');
            f.AddGuide('dx2', 0, 'ss', 'a1', '200000');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x2', 1, 'hc', '0', 'dx2');
            f.AddGuide('x3', 1, 'hc', 'dx2', '0');
            f.AddGuide('x4', 1, 'hc', 'dx1', '0');
            f.AddGuide('y1', 0, 'ss', 'a3', '100000');
            f.AddGuide('dy2', 0, 'h', 'a4', '100000');
            f.AddGuide('y2', 1, 'b', '0', 'dy2');
            f.AddGuide('y3', 2, 'y2', 'b', '2');
            f.AddHandleXY('adj1','0','maxAdj1', undefined, '0', '0', 'x2', 'y1');
            f.AddHandleXY('adj2','0','maxAdj2', undefined, '0', '0', 'x1', 't');
            f.AddHandleXY(undefined, '0', '0','adj3','0','maxAdj3', 'r', 'y1');
            f.AddHandleXY(undefined, '0', '0','adj4','0','maxAdj4', 'l', 'y2');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'y2');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'y2');
            f.AddRect('l', 'y2', 'r', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y2');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x2', 'y1');
            f.AddPathCommand(2, 'x1', 'y1');
            f.AddPathCommand(2, 'hc', 't');
            f.AddPathCommand(2, 'x4', 'y1');
            f.AddPathCommand(2, 'x3', 'y1');
            f.AddPathCommand(2, 'x3', 'y2');
            f.AddPathCommand(2, 'r', 'y2');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'upDownArrow':{
            f.AddAdj('adj1', 15, '50000');
            f.AddAdj('adj2', 15, '50000');
            f.AddGuide('maxAdj2', 0, '50000', 'h', 'ss');
            f.AddGuide('a1', 10, '0', 'adj1', '100000');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('y2', 0, 'ss', 'a2', '100000');
            f.AddGuide('y3', 1, 'b', '0', 'y2');
            f.AddGuide('dx1', 0, 'w', 'a1', '200000');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x2', 1, 'hc', 'dx1', '0');
            f.AddGuide('dy1', 0, 'x1', 'y2', 'wd2');
            f.AddGuide('y1', 1, 'y2', '0', 'dy1');
            f.AddGuide('y4', 1, 'y3', 'dy1', '0');
            f.AddHandleXY('adj1','0','100000', undefined, '0', '0', 'x1', 'y3');
            f.AddHandleXY(undefined, '0', '0','adj2','0','maxAdj2', 'l', 'y2');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'y2');
            f.AddCnx('cd2', 'x1', 'vc');
            f.AddCnx('cd2', 'l', 'y3');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'y3');
            f.AddCnx('0', 'x2', 'vc');
            f.AddCnx('0', 'r', 'y2');
            f.AddRect('x1', 'y1', 'x2', 'y4');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y2');
            f.AddPathCommand(2, 'hc', 't');
            f.AddPathCommand(2, 'r', 'y2');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x2', 'y3');
            f.AddPathCommand(2, 'r', 'y3');
            f.AddPathCommand(2, 'hc', 'b');
            f.AddPathCommand(2, 'l', 'y3');
            f.AddPathCommand(2, 'x1', 'y3');
            f.AddPathCommand(2, 'x1', 'y2');
            f.AddPathCommand(6);
            break;
        }
        case 'upDownArrowCallout':{
            f.AddAdj('adj1', 15, '25000');
            f.AddAdj('adj2', 15, '25000');
            f.AddAdj('adj3', 15, '25000');
            f.AddAdj('adj4', 15, '48123');
            f.AddGuide('maxAdj2', 0, '50000', 'w', 'ss');
            f.AddGuide('a2', 10, '0', 'adj2', 'maxAdj2');
            f.AddGuide('maxAdj1', 0, 'a2', '2', '1');
            f.AddGuide('a1', 10, '0', 'adj1', 'maxAdj1');
            f.AddGuide('maxAdj3', 0, '50000', 'h', 'ss');
            f.AddGuide('a3', 10, '0', 'adj3', 'maxAdj3');
            f.AddGuide('q2', 0, 'a3', 'ss', 'hd2');
            f.AddGuide('maxAdj4', 1, '100000', '0', 'q2');
            f.AddGuide('a4', 10, '0', 'adj4', 'maxAdj4');
            f.AddGuide('dx1', 0, 'ss', 'a2', '100000');
            f.AddGuide('dx2', 0, 'ss', 'a1', '200000');
            f.AddGuide('x1', 1, 'hc', '0', 'dx1');
            f.AddGuide('x2', 1, 'hc', '0', 'dx2');
            f.AddGuide('x3', 1, 'hc', 'dx2', '0');
            f.AddGuide('x4', 1, 'hc', 'dx1', '0');
            f.AddGuide('y1', 0, 'ss', 'a3', '100000');
            f.AddGuide('y4', 1, 'b', '0', 'y1');
            f.AddGuide('dy2', 0, 'h', 'a4', '200000');
            f.AddGuide('y2', 1, 'vc', '0', 'dy2');
            f.AddGuide('y3', 1, 'vc', 'dy2', '0');
            f.AddHandleXY('adj1','0','maxAdj1', undefined, '0', '0', 'x2', 'y1');
            f.AddHandleXY('adj2','0','maxAdj2', undefined, '0', '0', 'x1', 't');
            f.AddHandleXY(undefined, '0', '0','adj3','0','maxAdj3', 'r', 'y1');
            f.AddHandleXY(undefined, '0', '0','adj4','0','maxAdj4', 'l', 'y2');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddRect('l', 'y2', 'r', 'y3');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'y2');
            f.AddPathCommand(2, 'x2', 'y2');
            f.AddPathCommand(2, 'x2', 'y1');
            f.AddPathCommand(2, 'x1', 'y1');
            f.AddPathCommand(2, 'hc', 't');
            f.AddPathCommand(2, 'x4', 'y1');
            f.AddPathCommand(2, 'x3', 'y1');
            f.AddPathCommand(2, 'x3', 'y2');
            f.AddPathCommand(2, 'r', 'y2');
            f.AddPathCommand(2, 'r', 'y3');
            f.AddPathCommand(2, 'x3', 'y3');
            f.AddPathCommand(2, 'x3', 'y4');
            f.AddPathCommand(2, 'x4', 'y4');
            f.AddPathCommand(2, 'hc', 'b');
            f.AddPathCommand(2, 'x1', 'y4');
            f.AddPathCommand(2, 'x2', 'y4');
            f.AddPathCommand(2, 'x2', 'y3');
            f.AddPathCommand(2, 'l', 'y3');
            f.AddPathCommand(6);
            break;
        }
        case 'uturnArrow':{
            f.AddAdj('adj1', 15, '25000');
            f.AddAdj('adj2', 15, '25000');
            f.AddAdj('adj3', 15, '25000');
            f.AddAdj('adj4', 15, '43750');
            f.AddAdj('adj5', 15, '75000');
            f.AddGuide('a2', 10, '0', 'adj2', '25000');
            f.AddGuide('maxAdj1', 0, 'a2', '2', '1');
            f.AddGuide('a1', 10, '0', 'adj1', 'maxAdj1');
            f.AddGuide('q2', 0, 'a1', 'ss', 'h');
            f.AddGuide('q3', 1, '100000', '0', 'q2');
            f.AddGuide('maxAdj3', 0, 'q3', 'h', 'ss');
            f.AddGuide('a3', 10, '0', 'adj3', 'maxAdj3');
            f.AddGuide('q1', 1, 'a3', 'a1', '0');
            f.AddGuide('minAdj5', 0, 'q1', 'ss', 'h');
            f.AddGuide('a5', 10, 'minAdj5', 'adj5', '100000');
            f.AddGuide('th', 0, 'ss', 'a1', '100000');
            f.AddGuide('aw2', 0, 'ss', 'a2', '100000');
            f.AddGuide('th2', 0, 'th', '1', '2');
            f.AddGuide('dh2', 1, 'aw2', '0', 'th2');
            f.AddGuide('y5', 0, 'h', 'a5', '100000');
            f.AddGuide('ah', 0, 'ss', 'a3', '100000');
            f.AddGuide('y4', 1, 'y5', '0', 'ah');
            f.AddGuide('x9', 1, 'r', '0', 'dh2');
            f.AddGuide('bw', 0, 'x9', '1', '2');
            f.AddGuide('bs', 16, 'bw', 'y4');
            f.AddGuide('maxAdj4', 0, 'bs', '100000', 'ss');
            f.AddGuide('a4', 10, '0', 'adj4', 'maxAdj4');
            f.AddGuide('bd', 0, 'ss', 'a4', '100000');
            f.AddGuide('bd3', 1, 'bd', '0', 'th');
            f.AddGuide('bd2', 8, 'bd3', '0');
            f.AddGuide('x3', 1, 'th', 'bd2', '0');
            f.AddGuide('x8', 1, 'r', '0', 'aw2');
            f.AddGuide('x6', 1, 'x8', '0', 'aw2');
            f.AddGuide('x7', 1, 'x6', 'dh2', '0');
            f.AddGuide('x4', 1, 'x9', '0', 'bd');
            f.AddGuide('x5', 1, 'x7', '0', 'bd2');
            f.AddGuide('cx', 2, 'th', 'x7', '2');
            f.AddHandleXY('adj1','0','maxAdj1', undefined, '0', '0', 'th', 'b');
            f.AddHandleXY('adj2','0','25000', undefined, '0', '0', 'x6', 'b');
            f.AddHandleXY(undefined, '0', '0','adj3','0','maxAdj3', 'x6', 'y4');
            f.AddHandleXY('adj4','0','maxAdj4', undefined, '0', '0', 'bd', 't');
            f.AddHandleXY(undefined, '0', '0','adj5','minAdj5','100000', 'r', 'y5');
            f.AddCnx('cd4', 'x6', 'y4');
            f.AddCnx('cd4', 'x8', 'y5');
            f.AddCnx('0', 'r', 'y4');
            f.AddCnx('_3cd4', 'cx', 't');
            f.AddCnx('cd4', 'th2', 'b');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'b');
            f.AddPathCommand(2, 'l', 'bd');
            f.AddPathCommand(3, 'bd', 'bd', 'cd2', 'cd4');
            f.AddPathCommand(2, 'x4', 't');
            f.AddPathCommand(3, 'bd', 'bd', '_3cd4', 'cd4');
            f.AddPathCommand(2, 'x9', 'y4');
            f.AddPathCommand(2, 'r', 'y4');
            f.AddPathCommand(2, 'x8', 'y5');
            f.AddPathCommand(2, 'x6', 'y4');
            f.AddPathCommand(2, 'x7', 'y4');
            f.AddPathCommand(2, 'x7', 'x3');
            f.AddPathCommand(3, 'bd2', 'bd2', '0', '-5400000');
            f.AddPathCommand(2, 'x3', 'th');
            f.AddPathCommand(3, 'bd2', 'bd2', '_3cd4', '-5400000');
            f.AddPathCommand(2, 'th', 'b');
            f.AddPathCommand(6);
            break;
        }
        case 'verticalScroll':{
            f.AddAdj('adj', 15, '12500');
            f.AddGuide('a', 10, '0', 'adj', '25000');
            f.AddGuide('ch', 0, 'ss', 'a', '100000');
            f.AddGuide('ch2', 0, 'ch', '1', '2');
            f.AddGuide('ch4', 0, 'ch', '1', '4');
            f.AddGuide('x3', 1, 'ch', 'ch2', '0');
            f.AddGuide('x4', 1, 'ch', 'ch', '0');
            f.AddGuide('x6', 1, 'r', '0', 'ch');
            f.AddGuide('x7', 1, 'r', '0', 'ch2');
            f.AddGuide('x5', 1, 'x6', '0', 'ch2');
            f.AddGuide('y3', 1, 'b', '0', 'ch');
            f.AddGuide('y4', 1, 'b', '0', 'ch2');
            f.AddHandleXY(undefined, '0', '0','adj','0','25000', 'l', 'ch');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('0', 'ch', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd2', 'x6', 'vc');
            f.AddRect('ch', 'ch', 'x6', 'y4');
            f.AddPathCommand(0,false, undefined, false, undefined, undefined);
            f.AddPathCommand(1, 'ch2', 'b');
            f.AddPathCommand(3, 'ch2', 'ch2', 'cd4', '-5400000');
            f.AddPathCommand(2, 'ch2', 'y4');
            f.AddPathCommand(3, 'ch4', 'ch4', 'cd4', '-10800000');
            f.AddPathCommand(2, 'ch', 'y3');
            f.AddPathCommand(2, 'ch', 'ch2');
            f.AddPathCommand(3, 'ch2', 'ch2', 'cd2', 'cd4');
            f.AddPathCommand(2, 'x7', 't');
            f.AddPathCommand(3, 'ch2', 'ch2', '_3cd4', 'cd2');
            f.AddPathCommand(2, 'x6', 'ch');
            f.AddPathCommand(2, 'x6', 'y4');
            f.AddPathCommand(3, 'ch2', 'ch2', '0', 'cd4');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'x4', 'ch2');
            f.AddPathCommand(3, 'ch2', 'ch2', '0', 'cd4');
            f.AddPathCommand(3, 'ch4', 'ch4', 'cd4', 'cd2');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'darkenLess', false, undefined, undefined);
            f.AddPathCommand(1, 'x4', 'ch2');
            f.AddPathCommand(3, 'ch2', 'ch2', '0', 'cd4');
            f.AddPathCommand(3, 'ch4', 'ch4', 'cd4', 'cd2');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'ch', 'y4');
            f.AddPathCommand(3, 'ch2', 'ch2', '0', '_3cd4');
            f.AddPathCommand(3, 'ch4', 'ch4', '_3cd4', 'cd2');
            f.AddPathCommand(6);
            f.AddPathCommand(0,false, 'none', undefined, undefined, undefined);
            f.AddPathCommand(1, 'ch', 'y3');
            f.AddPathCommand(2, 'ch', 'ch2');
            f.AddPathCommand(3, 'ch2', 'ch2', 'cd2', 'cd4');
            f.AddPathCommand(2, 'x7', 't');
            f.AddPathCommand(3, 'ch2', 'ch2', '_3cd4', 'cd2');
            f.AddPathCommand(2, 'x6', 'ch');
            f.AddPathCommand(2, 'x6', 'y4');
            f.AddPathCommand(3, 'ch2', 'ch2', '0', 'cd4');
            f.AddPathCommand(2, 'ch2', 'b');
            f.AddPathCommand(3, 'ch2', 'ch2', 'cd4', 'cd2');
            f.AddPathCommand(6);
            f.AddPathCommand(1, 'x3', 't');
            f.AddPathCommand(3, 'ch2', 'ch2', '_3cd4', 'cd2');
            f.AddPathCommand(3, 'ch4', 'ch4', 'cd4', 'cd2');
            f.AddPathCommand(2, 'x4', 'ch2');
            f.AddPathCommand(1, 'x6', 'ch');
            f.AddPathCommand(2, 'x3', 'ch');
            f.AddPathCommand(1, 'ch2', 'y3');
            f.AddPathCommand(3, 'ch4', 'ch4', '_3cd4', 'cd2');
            f.AddPathCommand(2, 'ch', 'y4');
            f.AddPathCommand(1, 'ch2', 'b');
            f.AddPathCommand(3, 'ch2', 'ch2', 'cd4', '-5400000');
            f.AddPathCommand(2, 'ch', 'y3');
            break;
        }
        case 'wave':{
            f.AddAdj('adj1', 15, '12500');
            f.AddAdj('adj2', 15, '0');
            f.AddGuide('a1', 10, '0', 'adj1', '20000');
            f.AddGuide('a2', 10, '-10000', 'adj2', '10000');
            f.AddGuide('y1', 0, 'h', 'a1', '100000');
            f.AddGuide('dy2', 0, 'y1', '10', '3');
            f.AddGuide('y2', 1, 'y1', '0', 'dy2');
            f.AddGuide('y3', 1, 'y1', 'dy2', '0');
            f.AddGuide('y4', 1, 'b', '0', 'y1');
            f.AddGuide('y5', 1, 'y4', '0', 'dy2');
            f.AddGuide('y6', 1, 'y4', 'dy2', '0');
            f.AddGuide('dx1', 0, 'w', 'a2', '100000');
            f.AddGuide('of2', 0, 'w', 'a2', '50000');
            f.AddGuide('x1', 4, 'dx1');
            f.AddGuide('dx2', 3, 'of2', '0', 'of2');
            f.AddGuide('x2', 1, 'l', '0', 'dx2');
            f.AddGuide('dx5', 3, 'of2', 'of2', '0');
            f.AddGuide('x5', 1, 'r', '0', 'dx5');
            f.AddGuide('dx3', 2, 'dx2', 'x5', '3');
            f.AddGuide('x3', 1, 'x2', 'dx3', '0');
            f.AddGuide('x4', 2, 'x3', 'x5', '2');
            f.AddGuide('x6', 1, 'l', 'dx5', '0');
            f.AddGuide('x10', 1, 'r', 'dx2', '0');
            f.AddGuide('x7', 1, 'x6', 'dx3', '0');
            f.AddGuide('x8', 2, 'x7', 'x10', '2');
            f.AddGuide('x9', 1, 'r', '0', 'x1');
            f.AddGuide('xAdj', 1, 'hc', 'dx1', '0');
            f.AddGuide('xAdj2', 1, 'hc', '0', 'dx1');
            f.AddGuide('il', 8, 'x2', 'x6');
            f.AddGuide('ir', 16, 'x5', 'x10');
            f.AddGuide('it', 0, 'h', 'a1', '50000');
            f.AddGuide('ib', 1, 'b', '0', 'it');
            f.AddHandleXY(undefined, '0', '0','adj1','0','20000', 'l', 'y1');
            f.AddHandleXY('adj2','-10000','10000', undefined, '0', '0', 'xAdj', 'b');
            f.AddCnx('cd4', 'xAdj2', 'y1');
            f.AddCnx('cd2', 'x1', 'vc');
            f.AddCnx('_3cd4', 'xAdj', 'y4');
            f.AddCnx('0', 'x9', 'vc');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'x2', 'y1');
            f.AddPathCommand(5, 'x3', 'y2', 'x4', 'y3', 'x5', 'y1');
            f.AddPathCommand(2, 'x10', 'y4');
            f.AddPathCommand(5, 'x8', 'y6', 'x7', 'y5', 'x6', 'y4');
            f.AddPathCommand(6);
            break;
        }
        case 'wedgeEllipseCallout':{
            f.AddAdj('adj1', 15, '-20833');
            f.AddAdj('adj2', 15, '62500');
            f.AddGuide('dxPos', 0, 'w', 'adj1', '100000');
            f.AddGuide('dyPos', 0, 'h', 'adj2', '100000');
            f.AddGuide('xPos', 1, 'hc', 'dxPos', '0');
            f.AddGuide('yPos', 1, 'vc', 'dyPos', '0');
            f.AddGuide('sdx', 0, 'dxPos', 'h', '1');
            f.AddGuide('sdy', 0, 'dyPos', 'w', '1');
            f.AddGuide('pang', 5, 'sdx', 'sdy');
            f.AddGuide('stAng', 1, 'pang', '660000', '0');
            f.AddGuide('enAng', 1, 'pang', '0', '660000');
            f.AddGuide('dx1', 7, 'wd2', 'stAng');
            f.AddGuide('dy1', 12, 'hd2', 'stAng');
            f.AddGuide('x1', 1, 'hc', 'dx1', '0');
            f.AddGuide('y1', 1, 'vc', 'dy1', '0');
            f.AddGuide('dx2', 7, 'wd2', 'enAng');
            f.AddGuide('dy2', 12, 'hd2', 'enAng');
            f.AddGuide('x2', 1, 'hc', 'dx2', '0');
            f.AddGuide('y2', 1, 'vc', 'dy2', '0');
            f.AddGuide('stAng1', 5, 'dx1', 'dy1');
            f.AddGuide('enAng1', 5, 'dx2', 'dy2');
            f.AddGuide('swAng1', 1, 'enAng1', '0', 'stAng1');
            f.AddGuide('swAng2', 1, 'swAng1', '21600000', '0');
            f.AddGuide('swAng', 3, 'swAng1', 'swAng1', 'swAng2');
            f.AddGuide('idx', 7, 'wd2', '2700000');
            f.AddGuide('idy', 12, 'hd2', '2700000');
            f.AddGuide('il', 1, 'hc', '0', 'idx');
            f.AddGuide('ir', 1, 'hc', 'idx', '0');
            f.AddGuide('it', 1, 'vc', '0', 'idy');
            f.AddGuide('ib', 1, 'vc', 'idy', '0');
            f.AddHandleXY('adj1','-**********','**********','adj2','-**********','**********', 'xPos', 'yPos');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('_3cd4', 'il', 'it');
            f.AddCnx('cd4', 'il', 'ib');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('cd4', 'ir', 'ib');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('_3cd4', 'ir', 'it');
            f.AddCnx('pang', 'xPos', 'yPos');
            f.AddRect('il', 'it', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'xPos', 'yPos');
            f.AddPathCommand(2, 'x1', 'y1');
            f.AddPathCommand(3, 'wd2', 'hd2', 'stAng1', 'swAng');
            f.AddPathCommand(6);
            break;
        }
        case 'wedgeRectCallout':{
            f.AddAdj('adj1', 15, '-20833');
            f.AddAdj('adj2', 15, '62500');
            f.AddGuide('dxPos', 0, 'w', 'adj1', '100000');
            f.AddGuide('dyPos', 0, 'h', 'adj2', '100000');
            f.AddGuide('xPos', 1, 'hc', 'dxPos', '0');
            f.AddGuide('yPos', 1, 'vc', 'dyPos', '0');
            f.AddGuide('dx', 1, 'xPos', '0', 'hc');
            f.AddGuide('dy', 1, 'yPos', '0', 'vc');
            f.AddGuide('dq', 0, 'dxPos', 'h', 'w');
            f.AddGuide('ady', 4, 'dyPos');
            f.AddGuide('adq', 4, 'dq');
            f.AddGuide('dz', 1, 'ady', '0', 'adq');
            f.AddGuide('xg1', 3, 'dxPos', '7', '2');
            f.AddGuide('xg2', 3, 'dxPos', '10', '5');
            f.AddGuide('x1', 0, 'w', 'xg1', '12');
            f.AddGuide('x2', 0, 'w', 'xg2', '12');
            f.AddGuide('yg1', 3, 'dyPos', '7', '2');
            f.AddGuide('yg2', 3, 'dyPos', '10', '5');
            f.AddGuide('y1', 0, 'h', 'yg1', '12');
            f.AddGuide('y2', 0, 'h', 'yg2', '12');
            f.AddGuide('t1', 3, 'dxPos', 'l', 'xPos');
            f.AddGuide('xl', 3, 'dz', 'l', 't1');
            f.AddGuide('t2', 3, 'dyPos', 'x1', 'xPos');
            f.AddGuide('xt', 3, 'dz', 't2', 'x1');
            f.AddGuide('t3', 3, 'dxPos', 'xPos', 'r');
            f.AddGuide('xr', 3, 'dz', 'r', 't3');
            f.AddGuide('t4', 3, 'dyPos', 'xPos', 'x1');
            f.AddGuide('xb', 3, 'dz', 't4', 'x1');
            f.AddGuide('t5', 3, 'dxPos', 'y1', 'yPos');
            f.AddGuide('yl', 3, 'dz', 'y1', 't5');
            f.AddGuide('t6', 3, 'dyPos', 't', 'yPos');
            f.AddGuide('yt', 3, 'dz', 't6', 't');
            f.AddGuide('t7', 3, 'dxPos', 'yPos', 'y1');
            f.AddGuide('yr', 3, 'dz', 'y1', 't7');
            f.AddGuide('t8', 3, 'dyPos', 'yPos', 'b');
            f.AddGuide('yb', 3, 'dz', 't8', 'b');
            f.AddHandleXY('adj1','-**********','**********','adj2','-**********','**********', 'xPos', 'yPos');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'xPos', 'yPos');
            f.AddRect('l', 't', 'r', 'b');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 't');
            f.AddPathCommand(2, 'x1', 't');
            f.AddPathCommand(2, 'xt', 'yt');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(2, 'r', 't');
            f.AddPathCommand(2, 'r', 'y1');
            f.AddPathCommand(2, 'xr', 'yr');
            f.AddPathCommand(2, 'r', 'y2');
            f.AddPathCommand(2, 'r', 'b');
            f.AddPathCommand(2, 'x2', 'b');
            f.AddPathCommand(2, 'xb', 'yb');
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(2, 'l', 'b');
            f.AddPathCommand(2, 'l', 'y2');
            f.AddPathCommand(2, 'xl', 'yl');
            f.AddPathCommand(2, 'l', 'y1');
            f.AddPathCommand(6);
            break;
        }
        case 'wedgeRoundRectCallout':{
            f.AddAdj('adj1', 15, '-20833');
            f.AddAdj('adj2', 15, '62500');
            f.AddAdj('adj3', 15, '16667');
            f.AddGuide('dxPos', 0, 'w', 'adj1', '100000');
            f.AddGuide('dyPos', 0, 'h', 'adj2', '100000');
            f.AddGuide('xPos', 1, 'hc', 'dxPos', '0');
            f.AddGuide('yPos', 1, 'vc', 'dyPos', '0');
            f.AddGuide('dq', 0, 'dxPos', 'h', 'w');
            f.AddGuide('ady', 4, 'dyPos');
            f.AddGuide('adq', 4, 'dq');
            f.AddGuide('dz', 1, 'ady', '0', 'adq');
            f.AddGuide('xg1', 3, 'dxPos', '7', '2');
            f.AddGuide('xg2', 3, 'dxPos', '10', '5');
            f.AddGuide('x1', 0, 'w', 'xg1', '12');
            f.AddGuide('x2', 0, 'w', 'xg2', '12');
            f.AddGuide('yg1', 3, 'dyPos', '7', '2');
            f.AddGuide('yg2', 3, 'dyPos', '10', '5');
            f.AddGuide('y1', 0, 'h', 'yg1', '12');
            f.AddGuide('y2', 0, 'h', 'yg2', '12');
            f.AddGuide('t1', 3, 'dxPos', 'l', 'xPos');
            f.AddGuide('xl', 3, 'dz', 'l', 't1');
            f.AddGuide('t2', 3, 'dyPos', 'x1', 'xPos');
            f.AddGuide('xt', 3, 'dz', 't2', 'x1');
            f.AddGuide('t3', 3, 'dxPos', 'xPos', 'r');
            f.AddGuide('xr', 3, 'dz', 'r', 't3');
            f.AddGuide('t4', 3, 'dyPos', 'xPos', 'x1');
            f.AddGuide('xb', 3, 'dz', 't4', 'x1');
            f.AddGuide('t5', 3, 'dxPos', 'y1', 'yPos');
            f.AddGuide('yl', 3, 'dz', 'y1', 't5');
            f.AddGuide('t6', 3, 'dyPos', 't', 'yPos');
            f.AddGuide('yt', 3, 'dz', 't6', 't');
            f.AddGuide('t7', 3, 'dxPos', 'yPos', 'y1');
            f.AddGuide('yr', 3, 'dz', 'y1', 't7');
            f.AddGuide('t8', 3, 'dyPos', 'yPos', 'b');
            f.AddGuide('yb', 3, 'dz', 't8', 'b');
            f.AddGuide('u1', 0, 'ss', 'adj3', '100000');
            f.AddGuide('u2', 1, 'r', '0', 'u1');
            f.AddGuide('v2', 1, 'b', '0', 'u1');
            f.AddGuide('il', 0, 'u1', '29289', '100000');
            f.AddGuide('ir', 1, 'r', '0', 'il');
            f.AddGuide('ib', 1, 'b', '0', 'il');
            f.AddHandleXY('adj1','-**********','**********','adj2','-**********','**********', 'xPos', 'yPos');
            f.AddCnx('_3cd4', 'hc', 't');
            f.AddCnx('cd2', 'l', 'vc');
            f.AddCnx('cd4', 'hc', 'b');
            f.AddCnx('0', 'r', 'vc');
            f.AddCnx('cd4', 'xPos', 'yPos');
            f.AddRect('il', 'il', 'ir', 'ib');
            f.AddPathCommand(0,undefined, undefined, undefined, undefined, undefined);
            f.AddPathCommand(1, 'l', 'u1');
            f.AddPathCommand(3, 'u1', 'u1', 'cd2', 'cd4');
            f.AddPathCommand(2, 'x1', 't');
            f.AddPathCommand(2, 'xt', 'yt');
            f.AddPathCommand(2, 'x2', 't');
            f.AddPathCommand(2, 'u2', 't');
            f.AddPathCommand(3, 'u1', 'u1', '_3cd4', 'cd4');
            f.AddPathCommand(2, 'r', 'y1');
            f.AddPathCommand(2, 'xr', 'yr');
            f.AddPathCommand(2, 'r', 'y2');
            f.AddPathCommand(2, 'r', 'v2');
            f.AddPathCommand(3, 'u1', 'u1', '0', 'cd4');
            f.AddPathCommand(2, 'x2', 'b');
            f.AddPathCommand(2, 'xb', 'yb');
            f.AddPathCommand(2, 'x1', 'b');
            f.AddPathCommand(2, 'u1', 'b');
            f.AddPathCommand(3, 'u1', 'u1', 'cd4', 'cd4');
            f.AddPathCommand(2, 'l', 'y2');
            f.AddPathCommand(2, 'xl', 'yl');
            f.AddPathCommand(2, 'l', 'y1');
            f.AddPathCommand(6);
            break;
        }
    }
    if(typeof prst === "string" && prst.length > 0)
    {
        f.setPreset(prst);
    }
    return f;
}

function getPrstByNumber(nPreset)
{
    switch(nPreset)
    {
         case  0:{
            return  "textArchDown";
    }
             case  1:{
                return  "textArchDownPour";
    }
             case  2:{
                return  "textArchUp";
    }
             case  3:{
                return  "textArchUpPour";
    }
             case  4:{
                return  "textButton";
    }
             case  5:{
                return  "textButtonPour";
    }
             case  6:{
                return  "textCanDown";
    }
             case  7:{
                return  "textCanUp";
    }
             case  8:{
                return  "textCascadeDown";
    }
             case  9:{
                return  "textCascadeUp";
    }
             case  10:{
                return  "textChevron";
    }
             case  11:{
                return  "textChevronInverted";
    }
             case  12:{
                return  "textCircle";
    }
             case  13:{
                return  "textCirclePour";
    }
             case  14:{
                return  "textCurveDown";
    }
             case  15:{
                return  "textCurveUp";
    }
             case  16:{
                return  "textDeflate";
    }
             case  17:{
                return  "textDeflateBottom";
    }
             case  18:{
                return  "textDeflateInflate";
    }
             case  19:{
                return  "textDeflateInflateDeflate";
    }
             case  20:{
                return  "textDeflateTop";
    }
             case  21:{
                return  "textDoubleWave1";
    }
             case  22:{
                return  "textFadeDown";
    }
             case  23:{
                return  "textFadeLeft";
    }
             case  24:{
                return  "textFadeRight";
    }
             case  25:{
                return  "textFadeUp";
    }
             case  26:{
                return  "textInflate";
    }
             case  27:{
                return  "textInflateBottom";
    }
             case  28:{
                return  "textInflateTop";
    }
             case  29:{
                return  "textNoShape";
    }
             case  30:{
                return  "textPlain";
    }
             case  31:{
                return  "textRingInside";
    }
             case  32:{
                return  "textRingOutside";
    }
             case  33:{
                return  "textSlantDown";
    }
             case  34:{
                return  "textSlantUp";
    }
             case  35:{
                return  "textStop";
    }
             case  36:{
                return  "textTriangle";
    }
             case  37:{
                return  "textTriangleInverted";
    }
             case  38:{
                return  "textWave1";
    }
             case  39:{
                return  "textWave2";
    }
             case  40:{
                return  "textWave4";
    }
    }
    return  "textNoShape";
}


function getNumByTxPrst(sPreset)
{
    if ("textArchDown" == sPreset) return 0;
    if ("textArchDownPour" == sPreset) return 1;
    if ("textArchUp" == sPreset) return 2;
    if ("textArchUpPour" == sPreset) return 3;
    if ("textButton" == sPreset) return 4;
    if ("textButtonPour" == sPreset) return 5;
    if ("textCanDown" == sPreset) return 6;
    if ("textCanUp" == sPreset) return 7;
    if ("textCascadeDown" == sPreset) return 8;
    if ("textCascadeUp" == sPreset) return 9;
    if ("textChevron" == sPreset) return 10;
    if ("textChevronInverted" == sPreset) return 11;
    if ("textCircle" == sPreset) return 12;
    if ("textCirclePour" == sPreset) return 13;
    if ("textCurveDown" == sPreset) return 14;
    if ("textCurveUp" == sPreset) return 15;
    if ("textDeflate" == sPreset) return 16;
    if ("textDeflateBottom" == sPreset) return 17;
    if ("textDeflateInflate" == sPreset) return 18;
    if ("textDeflateInflateDeflate" == sPreset) return 19;
    if ("textDeflateTop" == sPreset) return 20;
    if ("textDoubleWave1" == sPreset) return 21;
    if ("textFadeDown" == sPreset) return 22;
    if ("textFadeLeft" == sPreset) return 23;
    if ("textFadeRight" == sPreset) return 24;
    if ("textFadeUp" == sPreset) return 25;
    if ("textInflate" == sPreset) return 26;
    if ("textInflateBottom" == sPreset) return 27;
    if ("textInflateTop" == sPreset) return 28;
    if ("textNoShape" == sPreset) return 29;
    if ("textPlain" == sPreset) return 30;
    if ("textRingInside" == sPreset) return 31;
    if ("textRingOutside" == sPreset) return 32;
    if ("textSlantDown" == sPreset) return 33;
    if ("textSlantUp" == sPreset) return 34;
    if ("textStop" == sPreset) return 35;
    if ("textTriangle" == sPreset) return 36;
    if ("textTriangleInverted" == sPreset) return 37;
    if ("textWave1" == sPreset) return 38;
    if ("textWave2" == sPreset) return 39;
    if ("textWave4" == sPreset) return 40;
    return 29;
}

function CreatePrstTxWarpGeometry(prst, oGeom)
{
    return AscFormat.ExecuteNoHistory(function(){
        var f = oGeom || (new AscFormat.Geometry());
        switch(prst)
        {
            case 'textArchDown':{
                f.AddAdj('adj', 15, '0');
                f.AddGuide('adval', 10, '0', 'adj', '21599999');
                f.AddGuide('v1', 1, '10800000', '0', 'adval');
                f.AddGuide('v2', 1, '32400000', '0', 'adval');
                f.AddGuide('nv1', 1, '0', '0', 'v1');
                f.AddGuide('stAng', 3, 'nv1', 'v2', 'v1');
                f.AddGuide('w1', 1, '5400000', '0', 'adval');
                f.AddGuide('w2', 1, '16200000', '0', 'adval');
                f.AddGuide('d1', 1, 'adval', '0', 'stAng');
                f.AddGuide('d2', 1, 'd1', '0', '21600000');
                f.AddGuide('v3', 1, '0', '0', '10800000');
                f.AddGuide('c2', 3, 'w2', 'd1', 'd2');
                f.AddGuide('c1', 3, 'v1', 'd2', 'c2');
                f.AddGuide('c0', 3, 'w1', 'd1', 'c1');
                f.AddGuide('swAng', 3, 'stAng', 'c0', 'v3');
                f.AddGuide('wt1', 12, 'wd2', 'adj');
                f.AddGuide('ht1', 7, 'hd2', 'adj');
                f.AddGuide('dx1', 6, 'wd2', 'ht1', 'wt1');
                f.AddGuide('dy1', 11, 'hd2', 'ht1', 'wt1');
                f.AddGuide('x1', 1, 'hc', 'dx1', '0');
                f.AddGuide('y1', 1, 'vc', 'dy1', '0');
                f.AddGuide('wt2', 12, 'wd2', 'stAng');
                f.AddGuide('ht2', 7, 'hd2', 'stAng');
                f.AddGuide('dx2', 6, 'wd2', 'ht2', 'wt2');
                f.AddGuide('dy2', 11, 'hd2', 'ht2', 'wt2');
                f.AddGuide('x2', 1, 'hc', 'dx2', '0');
                f.AddGuide('y2', 1, 'vc', 'dy2', '0');
                f.AddHandlePolar('adj', '0', '21599999', undefined, '0', '0', 'x1', 'y1');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x2', 'y2');
                f.AddPathCommand(3, 'wd2', 'hd2', 'stAng', 'swAng');
                break;
            }
            case 'textArchDownPour':{
                f.AddAdj('adj1', 15, '0');
                f.AddAdj('adj2', 15, '25000');
                f.AddGuide('adval', 10, '0', 'adj1', '21599999');
                f.AddGuide('v1', 1, '10800000', '0', 'adval');
                f.AddGuide('v2', 1, '32400000', '0', 'adval');
                f.AddGuide('nv1', 1, '0', '0', 'v1');
                f.AddGuide('stAng', 3, 'nv1', 'v2', 'v1');
                f.AddGuide('w1', 1, '5400000', '0', 'adval');
                f.AddGuide('w2', 1, '16200000', '0', 'adval');
                f.AddGuide('d1', 1, 'adval', '0', 'stAng');
                f.AddGuide('d2', 1, 'd1', '0', '21600000');
                f.AddGuide('v3', 1, '0', '0', '10800000');
                f.AddGuide('c2', 3, 'w2', 'd1', 'd2');
                f.AddGuide('c1', 3, 'v1', 'd2', 'c2');
                f.AddGuide('c0', 3, 'w1', 'd1', 'c1');
                f.AddGuide('swAng', 3, 'stAng', 'c0', 'v3');
                f.AddGuide('wt1', 12, 'wd2', 'stAng');
                f.AddGuide('ht1', 7, 'hd2', 'stAng');
                f.AddGuide('dx1', 6, 'wd2', 'ht1', 'wt1');
                f.AddGuide('dy1', 11, 'hd2', 'ht1', 'wt1');
                f.AddGuide('x1', 1, 'hc', 'dx1', '0');
                f.AddGuide('y1', 1, 'vc', 'dy1', '0');
                f.AddGuide('adval2', 10, '0', 'adj2', '99000');
                f.AddGuide('ratio', 0, 'adval2', '1', '100000');
                f.AddGuide('iwd2', 0, 'wd2', 'ratio', '1');
                f.AddGuide('ihd2', 0, 'hd2', 'ratio', '1');
                f.AddGuide('wt2', 12, 'iwd2', 'adval');
                f.AddGuide('ht2', 7, 'ihd2', 'adval');
                f.AddGuide('dx2', 6, 'iwd2', 'ht2', 'wt2');
                f.AddGuide('dy2', 11, 'ihd2', 'ht2', 'wt2');
                f.AddGuide('x2', 1, 'hc', 'dx2', '0');
                f.AddGuide('y2', 1, 'vc', 'dy2', '0');
                f.AddGuide('wt3', 12, 'iwd2', 'stAng');
                f.AddGuide('ht3', 7, 'ihd2', 'stAng');
                f.AddGuide('dx3', 6, 'iwd2', 'ht3', 'wt3');
                f.AddGuide('dy3', 11, 'ihd2', 'ht3', 'wt3');
                f.AddGuide('x3', 1, 'hc', 'dx3', '0');
                f.AddGuide('y3', 1, 'vc', 'dy3', '0');
                f.AddHandlePolar('adj1', '0', '21599999', 'adj2', '0', '100000', 'x2', 'y2');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x3', 'y3');
                f.AddPathCommand(3, 'iwd2', 'ihd2', 'stAng', 'swAng');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x1', 'y1');
                f.AddPathCommand(3, 'wd2', 'hd2', 'stAng', 'swAng');
                break;
            }
            case 'textArchUp':{
                f.AddAdj('adj', 15, 'cd2');
                f.AddGuide('adval', 10, '0', 'adj', '21599999');
                f.AddGuide('v1', 1, '10800000', '0', 'adval');
                f.AddGuide('v2', 1, '32400000', '0', 'adval');
                f.AddGuide('end', 3, 'v1', 'v1', 'v2');
                f.AddGuide('w1', 1, '5400000', '0', 'adval');
                f.AddGuide('w2', 1, '16200000', '0', 'adval');
                f.AddGuide('d1', 1, 'end', '0', 'adval');
                f.AddGuide('d2', 1, '21600000', 'd1', '0');
                f.AddGuide('c2', 3, 'w2', 'd1', 'd2');
                f.AddGuide('c1', 3, 'v1', 'd2', 'c2');
                f.AddGuide('swAng', 3, 'w1', 'd1', 'c1');
                f.AddGuide('wt1', 12, 'wd2', 'adj');
                f.AddGuide('ht1', 7, 'hd2', 'adj');
                f.AddGuide('dx1', 6, 'wd2', 'ht1', 'wt1');
                f.AddGuide('dy1', 11, 'hd2', 'ht1', 'wt1');
                f.AddGuide('x1', 1, 'hc', 'dx1', '0');
                f.AddGuide('y1', 1, 'vc', 'dy1', '0');
                f.AddHandlePolar('adj', '0', '21599999', undefined, '0', '0', 'x1', 'y1');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x1', 'y1');
                f.AddPathCommand(3, 'wd2', 'hd2', 'adval', 'swAng');
                break;
            }
            case 'textArchUpPour':{
                f.AddAdj('adj1', 15, 'cd2');
                f.AddAdj('adj2', 15, '50000');
                f.AddGuide('adval', 10, '0', 'adj1', '21599999');
                f.AddGuide('v1', 1, '10800000', '0', 'adval');
                f.AddGuide('v2', 1, '32400000', '0', 'adval');
                f.AddGuide('end', 3, 'v1', 'v1', 'v2');
                f.AddGuide('w1', 1, '5400000', '0', 'adval');
                f.AddGuide('w2', 1, '16200000', '0', 'adval');
                f.AddGuide('d1', 1, 'end', '0', 'adval');
                f.AddGuide('d2', 1, '21600000', 'd1', '0');
                f.AddGuide('c2', 3, 'w2', 'd1', 'd2');
                f.AddGuide('c1', 3, 'v1', 'd2', 'c2');
                f.AddGuide('swAng', 3, 'w1', 'd1', 'c1');
                f.AddGuide('wt1', 12, 'wd2', 'adval');
                f.AddGuide('ht1', 7, 'hd2', 'adval');
                f.AddGuide('dx1', 6, 'wd2', 'ht1', 'wt1');
                f.AddGuide('dy1', 11, 'hd2', 'ht1', 'wt1');
                f.AddGuide('x1', 1, 'hc', 'dx1', '0');
                f.AddGuide('y1', 1, 'vc', 'dy1', '0');
                f.AddGuide('adval2', 10, '0', 'adj2', '99000');
                f.AddGuide('ratio', 0, 'adval2', '1', '100000');
                f.AddGuide('iwd2', 0, 'wd2', 'ratio', '1');
                f.AddGuide('ihd2', 0, 'hd2', 'ratio', '1');
                f.AddGuide('wt2', 12, 'iwd2', 'adval');
                f.AddGuide('ht2', 7, 'ihd2', 'adval');
                f.AddGuide('dx2', 6, 'iwd2', 'ht2', 'wt2');
                f.AddGuide('dy2', 11, 'ihd2', 'ht2', 'wt2');
                f.AddGuide('x2', 1, 'hc', 'dx2', '0');
                f.AddGuide('y2', 1, 'vc', 'dy2', '0');
                f.AddHandlePolar('adj1', '0', '21599999', 'adj2', '0', '100000', 'x2', 'y2');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x1', 'y1');
                f.AddPathCommand(3, 'wd2', 'hd2', 'adval', 'swAng');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x2', 'y2');
                f.AddPathCommand(3, 'iwd2', 'ihd2', 'adval', 'swAng');
                break;
            }
            case 'textButton':{
                f.AddAdj('adj', 15, '10800000');
                f.AddGuide('adval', 10, '0', 'adj', '21599999');
                f.AddGuide('bot', 1, '5400000', '0', 'adval');
                f.AddGuide('lef', 1, '10800000', '0', 'adval');
                f.AddGuide('top', 1, '16200000', '0', 'adval');
                f.AddGuide('rig', 1, '21600000', '0', 'adval');
                f.AddGuide('c3', 3, 'top', 'adval', '0');
                f.AddGuide('c2', 3, 'lef', '10800000', 'c3');
                f.AddGuide('c1', 3, 'bot', 'rig', 'c2');
                f.AddGuide('stAng', 3, 'adval', 'c1', '0');
                f.AddGuide('w1', 1, '21600000', '0', 'stAng');
                f.AddGuide('stAngB', 3, 'stAng', 'w1', '0');
                f.AddGuide('td1', 0, 'bot', '2', '1');
                f.AddGuide('td2', 0, 'top', '2', '1');
                f.AddGuide('ntd2', 1, '0', '0', 'td2');
                f.AddGuide('w2', 1, '0', '0', '10800000');
                f.AddGuide('c6', 3, 'top', 'ntd2', 'w2');
                f.AddGuide('c5', 3, 'lef', '10800000', 'c6');
                f.AddGuide('c4', 3, 'bot', 'td1', 'c5');
                f.AddGuide('v1', 3, 'adval', 'c4', '10800000');
                f.AddGuide('swAngT', 1, '0', '0', 'v1');
                f.AddGuide('stT', 3, 'lef', 'stAngB', 'stAng');
                f.AddGuide('stB', 3, 'lef', 'stAng', 'stAngB');
                f.AddGuide('swT', 3, 'lef', 'v1', 'swAngT');
                f.AddGuide('swB', 3, 'lef', 'swAngT', 'v1');
                f.AddGuide('wt1', 12, 'wd2', 'stT');
                f.AddGuide('ht1', 7, 'hd2', 'stT');
                f.AddGuide('dx1', 6, 'wd2', 'ht1', 'wt1');
                f.AddGuide('dy1', 11, 'hd2', 'ht1', 'wt1');
                f.AddGuide('x1', 1, 'hc', 'dx1', '0');
                f.AddGuide('y1', 1, 'vc', 'dy1', '0');
                f.AddGuide('wt2', 12, 'wd2', 'stB');
                f.AddGuide('ht2', 7, 'hd2', 'stB');
                f.AddGuide('dx2', 6, 'wd2', 'ht2', 'wt2');
                f.AddGuide('dy2', 11, 'hd2', 'ht2', 'wt2');
                f.AddGuide('x2', 1, 'hc', 'dx2', '0');
                f.AddGuide('y2', 1, 'vc', 'dy2', '0');
                f.AddGuide('wt3', 12, 'wd2', 'adj');
                f.AddGuide('ht3', 7, 'hd2', 'adj');
                f.AddGuide('dx3', 6, 'wd2', 'ht3', 'wt3');
                f.AddGuide('dy3', 11, 'hd2', 'ht3', 'wt3');
                f.AddGuide('x3', 1, 'hc', 'dx3', '0');
                f.AddGuide('y3', 1, 'vc', 'dy3', '0');
                f.AddHandlePolar('adj', '0', '21599999', undefined, '0', '0', 'x3', 'y3');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x1', 'y1');
                f.AddPathCommand(3, 'wd2', 'hd2', 'stT', 'swT');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'vc');
                f.AddPathCommand(2, 'r', 'vc');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x2', 'y2');
                f.AddPathCommand(3, 'wd2', 'hd2', 'stB', 'swB');
                break;
            }
            case 'textButtonPour':{
                f.AddAdj('adj1', 15, 'cd2');
                f.AddAdj('adj2', 15, '50000');
                f.AddGuide('adval', 10, '0', 'adj1', '21599999');
                f.AddGuide('bot', 1, '5400000', '0', 'adval');
                f.AddGuide('lef', 1, '10800000', '0', 'adval');
                f.AddGuide('top', 1, '16200000', '0', 'adval');
                f.AddGuide('rig', 1, '21600000', '0', 'adval');
                f.AddGuide('c3', 3, 'top', 'adval', '0');
                f.AddGuide('c2', 3, 'lef', '10800000', 'c3');
                f.AddGuide('c1', 3, 'bot', 'rig', 'c2');
                f.AddGuide('stAng', 3, 'adval', 'c1', '0');
                f.AddGuide('w1', 1, '21600000', '0', 'stAng');
                f.AddGuide('stAngB', 3, 'stAng', 'w1', '0');
                f.AddGuide('td1', 0, 'bot', '2', '1');
                f.AddGuide('td2', 0, 'top', '2', '1');
                f.AddGuide('ntd2', 1, '0', '0', 'td2');
                f.AddGuide('w2', 1, '0', '0', '10800000');
                f.AddGuide('c6', 3, 'top', 'ntd2', 'w2');
                f.AddGuide('c5', 3, 'lef', '10800000', 'c6');
                f.AddGuide('c4', 3, 'bot', 'td1', 'c5');
                f.AddGuide('v1', 3, 'adval', 'c4', '10800000');
                f.AddGuide('swAngT', 1, '0', '0', 'v1');
                f.AddGuide('stT', 3, 'lef', 'stAngB', 'stAng');
                f.AddGuide('stB', 3, 'lef', 'stAng', 'stAngB');
                f.AddGuide('swT', 3, 'lef', 'v1', 'swAngT');
                f.AddGuide('swB', 3, 'lef', 'swAngT', 'v1');
                f.AddGuide('wt1', 12, 'wd2', 'stT');
                f.AddGuide('ht1', 7, 'hd2', 'stT');
                f.AddGuide('dx1', 6, 'wd2', 'ht1', 'wt1');
                f.AddGuide('dy1', 11, 'hd2', 'ht1', 'wt1');
                f.AddGuide('x1', 1, 'hc', 'dx1', '0');
                f.AddGuide('y1', 1, 'vc', 'dy1', '0');
                f.AddGuide('wt6', 12, 'wd2', 'stB');
                f.AddGuide('ht6', 7, 'hd2', 'stB');
                f.AddGuide('dx6', 6, 'wd2', 'ht6', 'wt6');
                f.AddGuide('dy6', 11, 'hd2', 'ht6', 'wt6');
                f.AddGuide('x6', 1, 'hc', 'dx6', '0');
                f.AddGuide('y6', 1, 'vc', 'dy6', '0');
                f.AddGuide('adval2', 10, '40000', 'adj2', '99000');
                f.AddGuide('ratio', 0, 'adval2', '1', '100000');
                f.AddGuide('iwd2', 0, 'wd2', 'ratio', '1');
                f.AddGuide('ihd2', 0, 'hd2', 'ratio', '1');
                f.AddGuide('wt2', 12, 'iwd2', 'stT');
                f.AddGuide('ht2', 7, 'ihd2', 'stT');
                f.AddGuide('dx2', 6, 'iwd2', 'ht2', 'wt2');
                f.AddGuide('dy2', 11, 'ihd2', 'ht2', 'wt2');
                f.AddGuide('x2', 1, 'hc', 'dx2', '0');
                f.AddGuide('y2', 1, 'vc', 'dy2', '0');
                f.AddGuide('wt5', 12, 'iwd2', 'stB');
                f.AddGuide('ht5', 7, 'ihd2', 'stB');
                f.AddGuide('dx5', 6, 'iwd2', 'ht5', 'wt5');
                f.AddGuide('dy5', 11, 'ihd2', 'ht5', 'wt5');
                f.AddGuide('x5', 1, 'hc', 'dx5', '0');
                f.AddGuide('y5', 1, 'vc', 'dy5', '0');
                f.AddGuide('d1', 1, 'hd2', '0', 'ihd2');
                f.AddGuide('d12', 0, 'd1', '1', '2');
                f.AddGuide('yu', 1, 'vc', '0', 'd12');
                f.AddGuide('yd', 1, 'vc', 'd12', '0');
                f.AddGuide('v1', 0, 'd12', 'd12', '1');
                f.AddGuide('v2', 0, 'ihd2', 'ihd2', '1');
                f.AddGuide('v3', 0, 'v1', '1', 'v2');
                f.AddGuide('v4', 1, '1', '0', 'v3');
                f.AddGuide('v5', 0, 'iwd2', 'iwd2', '1');
                f.AddGuide('v6', 0, 'v4', 'v5', '1');
                f.AddGuide('v7', 13, 'v6');
                f.AddGuide('xl', 1, 'hc', '0', 'v7');
                f.AddGuide('xr', 1, 'hc', 'v7', '0');
                f.AddGuide('wtadj', 12, 'iwd2', 'adj1');
                f.AddGuide('htadj', 7, 'ihd2', 'adj1');
                f.AddGuide('dxadj', 6, 'iwd2', 'htadj', 'wtadj');
                f.AddGuide('dyadj', 11, 'ihd2', 'htadj', 'wtadj');
                f.AddGuide('xadj', 1, 'hc', 'dxadj', '0');
                f.AddGuide('yadj', 1, 'vc', 'dyadj', '0');
                f.AddHandlePolar('adj1', '0', '21599999', 'adj2', '0', '100000', 'xadj', 'yadj');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x1', 'y1');
                f.AddPathCommand(3, 'wd2', 'hd2', 'stT', 'swT');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x2', 'y2');
                f.AddPathCommand(3, 'iwd2', 'ihd2', 'stT', 'swT');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'xl', 'yu');
                f.AddPathCommand(2, 'xr', 'yu');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'xl', 'yd');
                f.AddPathCommand(2, 'xr', 'yd');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x5', 'y5');
                f.AddPathCommand(3, 'iwd2', 'ihd2', 'stB', 'swB');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x6', 'y6');
                f.AddPathCommand(3, 'wd2', 'hd2', 'stB', 'swB');
                break;
            }
            case 'textCanDown':{
                f.AddAdj('adj', 15, '14286');
                f.AddGuide('a', 10, '0', 'adj', '33333');
                f.AddGuide('dy', 0, 'a', 'h', '100000');
                f.AddGuide('y0', 1, 't', 'dy', '0');
                f.AddGuide('y1', 1, 'b', '0', 'dy');
                f.AddGuide('ncd2', 0, 'cd2', '-1', '1');
                f.AddHandleXY(undefined, '0', '0', 'adj', '0', '33333', 'hc', 'y0');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 't');
                f.AddPathCommand(3, 'wd2', 'y0', 'cd2', 'ncd2');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'y1');
                f.AddPathCommand(3, 'wd2', 'y0', 'cd2', 'ncd2');
                break;
            }
            case 'textCanUp':{
                f.AddAdj('adj', 15, '85714');
                f.AddGuide('a', 10, '66667', 'adj', '100000');
                f.AddGuide('dy1', 0, 'a', 'h', '100000');
                f.AddGuide('dy', 1, 'h', '0', 'dy1');
                f.AddGuide('y0', 1, 't', 'dy1', '0');
                f.AddGuide('y1', 1, 't', 'dy', '0');
                f.AddHandleXY(undefined, '0', '0', 'adj', '66667', '100000', 'hc', 'y0');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'y1');
                f.AddPathCommand(3, 'wd2', 'y1', 'cd2', 'cd2');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'b');
                f.AddPathCommand(3, 'wd2', 'y1', 'cd2', 'cd2');
                break;
            }
            case 'textCascadeDown':{
                f.AddAdj('adj', 15, '44444');
                f.AddGuide('a', 10, '28570', 'adj', '100000');
                f.AddGuide('dy', 0, 'a', 'h', '100000');
                f.AddGuide('y1', 1, 't', 'dy', '0');
                f.AddGuide('dy2', 1, 'h', '0', 'dy');
                f.AddGuide('dy3', 0, 'dy2', '1', '4');
                f.AddGuide('y2', 1, 't', 'dy3', '0');
                f.AddHandleXY(undefined, '0', '0', 'adj', '28570', '100000', 'l', 'y1');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 't');
                f.AddPathCommand(2, 'r', 'y2');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'y1');
                f.AddPathCommand(2, 'r', 'b');
                break;
            }
            case 'textCascadeUp':{
                f.AddAdj('adj', 15, '44444');
                f.AddGuide('a', 10, '28570', 'adj', '100000');
                f.AddGuide('dy', 0, 'a', 'h', '100000');
                f.AddGuide('y1', 1, 't', 'dy', '0');
                f.AddGuide('dy2', 1, 'h', '0', 'dy');
                f.AddGuide('dy3', 0, 'dy2', '1', '4');
                f.AddGuide('y2', 1, 't', 'dy3', '0');
                f.AddHandleXY(undefined, '0', '0', 'adj', '28570', '100000', 'r', 'y1');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'y2');
                f.AddPathCommand(2, 'r', 't');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'b');
                f.AddPathCommand(2, 'r', 'y1');
                break;
            }
            case 'textChevron':{
                f.AddAdj('adj', 15, '25000');
                f.AddGuide('a', 10, '0', 'adj', '50000');
                f.AddGuide('y', 0, 'a', 'h', '100000');
                f.AddGuide('y1', 1, 't', 'b', 'y');
                f.AddHandleXY(undefined, '0', '0', 'adj', '0', '50000', 'l', 'y');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'y');
                f.AddPathCommand(2, 'hc', 't');
                f.AddPathCommand(2, 'r', 'y');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'b');
                f.AddPathCommand(2, 'hc', 'y1');
                f.AddPathCommand(2, 'r', 'b');
                break;
            }
            case 'textChevronInverted':{
                f.AddAdj('adj', 15, '75000');
                f.AddGuide('a', 10, '50000', 'adj', '100000');
                f.AddGuide('y', 0, 'a', 'h', '100000');
                f.AddGuide('y1', 1, 'b', '0', 'y');
                f.AddHandleXY(undefined, '0', '0', 'adj', '50000', '100000', 'l', 'y');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 't');
                f.AddPathCommand(2, 'hc', 'y1');
                f.AddPathCommand(2, 'r', 't');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'y');
                f.AddPathCommand(2, 'hc', 'b');
                f.AddPathCommand(2, 'r', 'y');
                break;
            }
            case 'textCircle':{
                f.AddAdj('adj', 15, '10800000');
                f.AddGuide('adval', 10, '0', 'adj', '21599999');
                f.AddGuide('d0', 1, 'adval', '0', '10800000');
                f.AddGuide('d1', 1, '10800000', '0', 'adval');
                f.AddGuide('d2', 1, '21600000', '0', 'adval');
                f.AddGuide('d3', 3, 'd1', 'd1', '10799999');
                f.AddGuide('d4', 3, 'd0', 'd2', 'd3');
                f.AddGuide('swAng', 0, 'd4', '2', '1');
                f.AddGuide('wt1', 12, 'wd2', 'adj');
                f.AddGuide('ht1', 7, 'hd2', 'adj');
                f.AddGuide('dx1', 6, 'wd2', 'ht1', 'wt1');
                f.AddGuide('dy1', 11, 'hd2', 'ht1', 'wt1');
                f.AddGuide('x1', 1, 'hc', 'dx1', '0');
                f.AddGuide('y1', 1, 'vc', 'dy1', '0');
                f.AddHandlePolar('adj', '0', '21599999', undefined, '0', '0', 'x1', 'y1');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x1', 'y1');
                f.AddPathCommand(3, 'wd2', 'hd2', 'adval', 'swAng');
                break;
            }
            case 'textCirclePour':{
                f.AddAdj('adj1', 15, 'cd2');
                f.AddAdj('adj2', 15, '50000');
                f.AddGuide('adval', 10, '0', 'adj1', '21599999');
                f.AddGuide('d0', 1, 'adval', '0', '10800000');
                f.AddGuide('d1', 1, '10800000', '0', 'adval');
                f.AddGuide('d2', 1, '21600000', '0', 'adval');
                f.AddGuide('d3', 3, 'd1', 'd1', '10799999');
                f.AddGuide('d4', 3, 'd0', 'd2', 'd3');
                f.AddGuide('swAng', 0, 'd4', '2', '1');
                f.AddGuide('wt1', 12, 'wd2', 'adval');
                f.AddGuide('ht1', 7, 'hd2', 'adval');
                f.AddGuide('dx1', 6, 'wd2', 'ht1', 'wt1');
                f.AddGuide('dy1', 11, 'hd2', 'ht1', 'wt1');
                f.AddGuide('x1', 1, 'hc', 'dx1', '0');
                f.AddGuide('y1', 1, 'vc', 'dy1', '0');
                f.AddGuide('adval2', 10, '0', 'adj2', '99000');
                f.AddGuide('ratio', 0, 'adval2', '1', '100000');
                f.AddGuide('iwd2', 0, 'wd2', 'ratio', '1');
                f.AddGuide('ihd2', 0, 'hd2', 'ratio', '1');
                f.AddGuide('wt2', 12, 'iwd2', 'adval');
                f.AddGuide('ht2', 7, 'ihd2', 'adval');
                f.AddGuide('dx2', 6, 'iwd2', 'ht2', 'wt2');
                f.AddGuide('dy2', 11, 'ihd2', 'ht2', 'wt2');
                f.AddGuide('x2', 1, 'hc', 'dx2', '0');
                f.AddGuide('y2', 1, 'vc', 'dy2', '0');
                f.AddHandlePolar('adj1', '0', '21599999', 'adj2', '0', '100000', 'x2', 'y2');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x1', 'y1');
                f.AddPathCommand(3, 'wd2', 'hd2', 'adval', 'swAng');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x2', 'y2');
                f.AddPathCommand(3, 'iwd2', 'ihd2', 'adval', 'swAng');
                break;
            }
            case 'textCurveDown':{
                f.AddAdj('adj', 15, '45977');
                f.AddGuide('a', 10, '0', 'adj', '56338');
                f.AddGuide('dy', 0, 'a', 'h', '100000');
                f.AddGuide('gd1', 0, 'dy', '3', '4');
                f.AddGuide('gd2', 0, 'dy', '5', '4');
                f.AddGuide('gd3', 0, 'dy', '3', '8');
                f.AddGuide('gd4', 0, 'dy', '1', '8');
                f.AddGuide('gd5', 1, 'h', '0', 'gd3');
                f.AddGuide('gd6', 1, 'gd4', 'h', '0');
                f.AddGuide('y0', 1, 't', 'dy', '0');
                f.AddGuide('y1', 1, 't', 'gd1', '0');
                f.AddGuide('y2', 1, 't', 'gd2', '0');
                f.AddGuide('y3', 1, 't', 'gd3', '0');
                f.AddGuide('y4', 1, 't', 'gd4', '0');
                f.AddGuide('y5', 1, 't', 'gd5', '0');
                f.AddGuide('y6', 1, 't', 'gd6', '0');
                f.AddGuide('x1', 1, 'l', 'wd3', '0');
                f.AddGuide('x2', 1, 'r', '0', 'wd3');
                f.AddHandleXY(undefined, '0', '0', 'adj', '0', '56338', 'r', 'y0');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 't');
                f.AddPathCommand(5, 'x1', 'y1', 'x2', 'y2', 'r', 'y0');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'y5');
                f.AddPathCommand(5, 'x1', 'y6', 'x2', 'y6', 'r', 'y5');
                break;
            }
            case 'textCurveUp':{
                f.AddAdj('adj', 15, '45977');
                f.AddGuide('a', 10, '0', 'adj', '56338');
                f.AddGuide('dy', 0, 'a', 'h', '100000');
                f.AddGuide('gd1', 0, 'dy', '3', '4');
                f.AddGuide('gd2', 0, 'dy', '5', '4');
                f.AddGuide('gd3', 0, 'dy', '3', '8');
                f.AddGuide('gd4', 0, 'dy', '1', '8');
                f.AddGuide('gd5', 1, 'h', '0', 'gd3');
                f.AddGuide('gd6', 1, 'gd4', 'h', '0');
                f.AddGuide('y0', 1, 't', 'dy', '0');
                f.AddGuide('y1', 1, 't', 'gd1', '0');
                f.AddGuide('y2', 1, 't', 'gd2', '0');
                f.AddGuide('y3', 1, 't', 'gd3', '0');
                f.AddGuide('y4', 1, 't', 'gd4', '0');
                f.AddGuide('y5', 1, 't', 'gd5', '0');
                f.AddGuide('y6', 1, 't', 'gd6', '0');
                f.AddGuide('x1', 1, 'l', 'wd3', '0');
                f.AddGuide('x2', 1, 'r', '0', 'wd3');
                f.AddHandleXY(undefined, '0', '0', 'adj', '0', '56338', 'l', 'y0');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'y0');
                f.AddPathCommand(5, 'x1', 'y2', 'x2', 'y1', 'r', 't');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'y5');
                f.AddPathCommand(5, 'x1', 'y6', 'x2', 'y6', 'r', 'y5');
                break;
            }
            case 'textDeflate':{
                f.AddAdj('adj', 15, '18750');
                f.AddGuide('a', 10, '0', 'adj', '37500');
                f.AddGuide('dy', 0, 'a', 'ss', '100000');
                f.AddGuide('gd0', 0, 'dy', '4', '3');
                f.AddGuide('gd1', 1, 'h', '0', 'gd0');
                f.AddGuide('adjY', 1, 't', 'dy', '0');
                f.AddGuide('y0', 1, 't', 'gd0', '0');
                f.AddGuide('y1', 1, 't', 'gd1', '0');
                f.AddGuide('x0', 1, 'l', 'wd3', '0');
                f.AddGuide('x1', 1, 'r', '0', 'wd3');
                f.AddHandleXY(undefined, '0', '0', 'adj', '0', '37500', 'hc', 'adjY');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 't');
                f.AddPathCommand(5, 'x0', 'y0', 'x1', 'y0', 'r', 't');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'b');
                f.AddPathCommand(5, 'x0', 'y1', 'x1', 'y1', 'r', 'b');
                break;
            }
            case 'textDeflateBottom':{
                f.AddAdj('adj', 15, '50000');
                f.AddGuide('a', 10, '6250', 'adj', '100000');
                f.AddGuide('dy', 0, 'a', 'ss', '100000');
                f.AddGuide('dy2', 1, 'h', '0', 'dy');
                f.AddGuide('y1', 1, 't', 'dy', '0');
                f.AddGuide('cp', 1, 'y1', '0', 'dy2');
                f.AddHandleXY(undefined, '0', '0', 'adj', '6250', '100000', 'hc', 'y1');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 't');
                f.AddPathCommand(2, 'r', 't');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'b');
                f.AddPathCommand(4, 'hc', 'cp', 'r', 'b');
                break;
            }
            case 'textDeflateInflate':{
                f.AddAdj('adj', 15, '35000');
                f.AddGuide('a', 10, '5000', 'adj', '95000');
                f.AddGuide('dy', 0, 'a', 'h', '100000');
                f.AddGuide('del', 0, 'h', '5', '100');
                f.AddGuide('dh1', 0, 'h', '45', '100');
                f.AddGuide('dh2', 0, 'h', '55', '100');
                f.AddGuide('yh', 1, 'dy', '0', 'del');
                f.AddGuide('yl', 1, 'dy', 'del', '0');
                f.AddGuide('y3', 1, 'yh', 'yh', 'dh1');
                f.AddGuide('y4', 1, 'yl', 'yl', 'dh2');
                f.AddHandleXY(undefined, '0', '0', 'adj', '5000', '95000', 'hc', 'dy');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 't');
                f.AddPathCommand(2, 'r', 't');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'dh1');
                f.AddPathCommand(4, 'hc', 'y3', 'r', 'dh1');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'dh2');
                f.AddPathCommand(4, 'hc', 'y4', 'r', 'dh2');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'b');
                f.AddPathCommand(2, 'r', 'b');
                break;
            }
            case 'textDeflateInflateDeflate':{
                f.AddAdj('adj', 15, '25000');
                f.AddGuide('a', 10, '3000', 'adj', '47000');
                f.AddGuide('dy', 0, 'a', 'h', '100000');
                f.AddGuide('del', 0, 'h', '3', '100');
                f.AddGuide('ey1', 0, 'h', '30', '100');
                f.AddGuide('ey2', 0, 'h', '36', '100');
                f.AddGuide('ey3', 0, 'h', '63', '100');
                f.AddGuide('ey4', 0, 'h', '70', '100');
                f.AddGuide('by', 1, 'b', '0', 'dy');
                f.AddGuide('yh1', 1, 'dy', '0', 'del');
                f.AddGuide('yl1', 1, 'dy', 'del', '0');
                f.AddGuide('yh2', 1, 'by', '0', 'del');
                f.AddGuide('yl2', 1, 'by', 'del', '0');
                f.AddGuide('y1', 1, 'yh1', 'yh1', 'ey1');
                f.AddGuide('y2', 1, 'yl1', 'yl1', 'ey2');
                f.AddGuide('y3', 1, 'yh2', 'yh2', 'ey3');
                f.AddGuide('y4', 1, 'yl2', 'yl2', 'ey4');
                f.AddHandleXY(undefined, '0', '0', 'adj', '3000', '47000', 'hc', 'dy');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 't');
                f.AddPathCommand(2, 'r', 't');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'ey1');
                f.AddPathCommand(4, 'hc', 'y1', 'r', 'ey1');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'ey2');
                f.AddPathCommand(4, 'hc', 'y2', 'r', 'ey2');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'ey3');
                f.AddPathCommand(4, 'hc', 'y3', 'r', 'ey3');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'ey4');
                f.AddPathCommand(4, 'hc', 'y4', 'r', 'ey4');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'b');
                f.AddPathCommand(2, 'r', 'b');
                break;
            }
            case 'textDeflateTop':{
                f.AddAdj('adj', 15, '50000');
                f.AddGuide('a', 10, '0', 'adj', '93750');
                f.AddGuide('dy', 0, 'a', 'h', '100000');
                f.AddGuide('y1', 1, 't', 'dy', '0');
                f.AddGuide('cp', 1, 'y1', 'dy', '0');
                f.AddHandleXY(undefined, '0', '0', 'adj', '0', '93750', 'hc', 'y1');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 't');
                f.AddPathCommand(4, 'hc', 'cp', 'r', 't');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'b');
                f.AddPathCommand(2, 'r', 'b');
                break;
            }
            case 'textDoubleWave1':{
                f.AddAdj('adj1', 15, '6250');
                f.AddAdj('adj2', 15, '0');
                f.AddGuide('a1', 10, '0', 'adj1', '12500');
                f.AddGuide('a2', 10, '-10000', 'adj2', '10000');
                f.AddGuide('y1', 0, 'h', 'a1', '100000');
                f.AddGuide('dy2', 0, 'y1', '10', '3');
                f.AddGuide('y2', 1, 'y1', '0', 'dy2');
                f.AddGuide('y3', 1, 'y1', 'dy2', '0');
                f.AddGuide('y4', 1, 'b', '0', 'y1');
                f.AddGuide('y5', 1, 'y4', '0', 'dy2');
                f.AddGuide('y6', 1, 'y4', 'dy2', '0');
                f.AddGuide('of', 0, 'w', 'a2', '100000');
                f.AddGuide('of2', 0, 'w', 'a2', '50000');
                f.AddGuide('x1', 4, 'of');
                f.AddGuide('dx2', 3, 'of2', '0', 'of2');
                f.AddGuide('x2', 1, 'l', '0', 'dx2');
                f.AddGuide('dx8', 3, 'of2', 'of2', '0');
                f.AddGuide('x8', 1, 'r', '0', 'dx8');
                f.AddGuide('dx3', 2, 'dx2', 'x8', '6');
                f.AddGuide('x3', 1, 'x2', 'dx3', '0');
                f.AddGuide('dx4', 2, 'dx2', 'x8', '3');
                f.AddGuide('x4', 1, 'x2', 'dx4', '0');
                f.AddGuide('x5', 2, 'x2', 'x8', '2');
                f.AddGuide('x6', 1, 'x5', 'dx3', '0');
                f.AddGuide('x7', 2, 'x6', 'x8', '2');
                f.AddGuide('x9', 1, 'l', 'dx8', '0');
                f.AddGuide('x15', 1, 'r', 'dx2', '0');
                f.AddGuide('x10', 1, 'x9', 'dx3', '0');
                f.AddGuide('x11', 1, 'x9', 'dx4', '0');
                f.AddGuide('x12', 2, 'x9', 'x15', '2');
                f.AddGuide('x13', 1, 'x12', 'dx3', '0');
                f.AddGuide('x14', 2, 'x13', 'x15', '2');
                f.AddGuide('x16', 1, 'r', '0', 'x1');
                f.AddGuide('xAdj', 1, 'hc', 'of', '0');
                f.AddHandleXY(undefined, '0', '0', 'adj1', '0', '12500', 'l', 'y1');
                f.AddHandleXY('adj2', '-10000', '10000', undefined, '0', '0', 'xAdj', 'b');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x2', 'y1');
                f.AddPathCommand(5, 'x3', 'y2', 'x4', 'y3', 'x5', 'y1');
                f.AddPathCommand(5, 'x6', 'y2', 'x7', 'y3', 'x8', 'y1');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x9', 'y4');
                f.AddPathCommand(5, 'x10', 'y5', 'x11', 'y6', 'x12', 'y4');
                f.AddPathCommand(5, 'x13', 'y5', 'x14', 'y6', 'x15', 'y4');
                break;
            }
            case 'textFadeDown':{
                f.AddAdj('adj', 15, '33333');
                f.AddGuide('a', 10, '0', 'adj', '49999');
                f.AddGuide('dx', 0, 'a', 'w', '100000');
                f.AddGuide('x1', 1, 'l', 'dx', '0');
                f.AddGuide('x2', 1, 'r', '0', 'dx');
                f.AddHandleXY('adj', '0', '49999', undefined, '0', '0', 'x1', 'b');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 't');
                f.AddPathCommand(2, 'r', 't');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x1', 'b');
                f.AddPathCommand(2, 'x2', 'b');
                break;
            }
            case 'textFadeLeft':{
                f.AddAdj('adj', 15, '33333');
                f.AddGuide('a', 10, '0', 'adj', '49999');
                f.AddGuide('dy', 0, 'a', 'h', '100000');
                f.AddGuide('y1', 1, 't', 'dy', '0');
                f.AddGuide('y2', 1, 'b', '0', 'dy');
                f.AddHandleXY(undefined, '0', '0', 'adj', '0', '49999', 'l', 'y1');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'y1');
                f.AddPathCommand(2, 'r', 't');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'y2');
                f.AddPathCommand(2, 'r', 'b');
                break;
            }
            case 'textFadeRight':{
                f.AddAdj('adj', 15, '33333');
                f.AddGuide('a', 10, '0', 'adj', '49999');
                f.AddGuide('dy', 0, 'a', 'h', '100000');
                f.AddGuide('y1', 1, 't', 'dy', '0');
                f.AddGuide('y2', 1, 'b', '0', 'dy');
                f.AddHandleXY(undefined, '0', '0', 'adj', '0', '49999', 'r', 'y1');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 't');
                f.AddPathCommand(2, 'r', 'y1');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'b');
                f.AddPathCommand(2, 'r', 'y2');
                break;
            }
            case 'textFadeUp':{
                f.AddAdj('adj', 15, '33333');
                f.AddGuide('a', 10, '0', 'adj', '49999');
                f.AddGuide('dx', 0, 'a', 'w', '100000');
                f.AddGuide('x1', 1, 'l', 'dx', '0');
                f.AddGuide('x2', 1, 'r', '0', 'dx');
                f.AddHandleXY('adj', '0', '49999', undefined, '0', '0', 'x1', 't');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x1', 't');
                f.AddPathCommand(2, 'x2', 't');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'b');
                f.AddPathCommand(2, 'r', 'b');
                break;
            }
            case 'textInflate':{
                f.AddAdj('adj', 15, '18750');
                f.AddGuide('a', 10, '0', 'adj', '20000');
                f.AddGuide('dy', 0, 'a', 'h', '100000');
                f.AddGuide('gd', 0, 'dy', '1', '3');
                f.AddGuide('gd0', 1, '0', '0', 'gd');
                f.AddGuide('gd1', 1, 'h', '0', 'gd0');
                f.AddGuide('ty', 1, 't', 'dy', '0');
                f.AddGuide('by', 1, 'b', '0', 'dy');
                f.AddGuide('y0', 1, 't', 'gd0', '0');
                f.AddGuide('y1', 1, 't', 'gd1', '0');
                f.AddGuide('x0', 1, 'l', 'wd3', '0');
                f.AddGuide('x1', 1, 'r', '0', 'wd3');
                f.AddHandleXY(undefined, '0', '0', 'adj', '0', '20000', 'l', 'ty');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'ty');
                f.AddPathCommand(5, 'x0', 'y0', 'x1', 'y0', 'r', 'ty');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'by');
                f.AddPathCommand(5, 'x0', 'y1', 'x1', 'y1', 'r', 'by');
                break;
            }
            case 'textInflateBottom':{
                f.AddAdj('adj', 15, '60000');
                f.AddGuide('a', 10, '60000', 'adj', '100000');
                f.AddGuide('dy', 0, 'a', 'h', '100000');
                f.AddGuide('ty', 1, 't', 'dy', '0');
                f.AddHandleXY(undefined, '0', '0', 'adj', '60000', '100000', 'l', 'ty');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 't');
                f.AddPathCommand(2, 'r', 't');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'ty');
                f.AddPathCommand(4, 'hc', 'b', 'r', 'ty');
                break;
            }
            case 'textInflateTop':{
                f.AddAdj('adj', 15, '40000');
                f.AddGuide('a', 10, '0', 'adj', '50000');
                f.AddGuide('dy', 0, 'a', 'h', '100000');
                f.AddGuide('ty', 1, 't', 'dy', '0');
                f.AddHandleXY(undefined, '0', '0', 'adj', '0', '50000', 'l', 'ty');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'ty');
                f.AddPathCommand(4, 'hc', 't', 'r', 'ty');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'b');
                f.AddPathCommand(2, 'r', 'b');
                break;
            }
            case 'textPlain':{
                f.AddAdj('adj', 15, '50000');
                f.AddGuide('a', 10, '30000', 'adj', '70000');
                f.AddGuide('mid', 0, 'a', 'w', '100000');
                f.AddGuide('midDir', 1, 'mid', '0', 'hc');
                f.AddGuide('dl', 1, 'mid', '0', 'l');
                f.AddGuide('dr', 1, 'r', '0', 'mid');
                f.AddGuide('dl2', 0, 'dl', '2', '1');
                f.AddGuide('dr2', 0, 'dr', '2', '1');
                f.AddGuide('dx', 3, 'midDir', 'dr2', 'dl2');
                f.AddGuide('xr', 1, 'l', 'dx', '0');
                f.AddGuide('xl', 1, 'r', '0', 'dx');
                f.AddGuide('tlx', 3, 'midDir', 'l', 'xl');
                f.AddGuide('trx', 3, 'midDir', 'xr', 'r');
                f.AddGuide('blx', 3, 'midDir', 'xl', 'l');
                f.AddGuide('brx', 3, 'midDir', 'r', 'xr');
                f.AddHandleXY('adj', '30000', '70000', undefined, '0', '0', 'mid', 'b');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'tlx', 't');
                f.AddPathCommand(2, 'trx', 't');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'blx', 'b');
                f.AddPathCommand(2, 'brx', 'b');
                break;
            }
            case 'textRingInside':{
                f.AddAdj('adj', 15, '60000');
                f.AddGuide('a', 10, '50000', 'adj', '99000');
                f.AddGuide('dy', 0, 'a', 'h', '100000');
                f.AddGuide('y', 1, 't', 'dy', '0');
                f.AddGuide('r', 0, 'dy', '1', '2');
                f.AddGuide('y1', 1, 't', 'r', '0');
                f.AddGuide('y2', 1, 'b', '0', 'r');
                f.AddHandleXY(undefined, '0', '0', 'adj', '50000', '99000', 'hc', 'y');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'y1');
                f.AddPathCommand(3, 'wd2', 'y1', '10800000', '21599999');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'y2');
                f.AddPathCommand(3, 'wd2', 'r', '10800000', '21599999');
                break;
            }
            case 'textRingOutside':{
                f.AddAdj('adj', 15, '60000');
                f.AddGuide('a', 10, '50000', 'adj', '99000');
                f.AddGuide('dy', 0, 'a', 'h', '100000');
                f.AddGuide('y', 1, 't', 'dy', '0');
                f.AddGuide('r', 0, 'dy', '1', '2');
                f.AddGuide('y1', 1, 't', 'r', '0');
                f.AddGuide('y2', 1, 'b', '0', 'r');
                f.AddHandleXY(undefined, '0', '0', 'adj', '50000', '99000', 'hc', 'y');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'y1');
                f.AddPathCommand(3, 'wd2', 'y1', '10800000', '-21599999');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'y2');
                f.AddPathCommand(3, 'wd2', 'r', '10800000', '-21599999');
                break;
            }
            case 'textSlantDown':{
                f.AddAdj('adj', 15, '44445');
                f.AddGuide('a', 10, '28569', 'adj', '100000');
                f.AddGuide('dy', 0, 'a', 'h', '100000');
                f.AddGuide('y1', 1, 't', 'dy', '0');
                f.AddGuide('y2', 1, 'b', '0', 'dy');
                f.AddHandleXY(undefined, '0', '0', 'adj', '28569', '100000', 'l', 'y1');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 't');
                f.AddPathCommand(2, 'r', 'y2');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'y1');
                f.AddPathCommand(2, 'r', 'b');
                break;
            }
            case 'textSlantUp':{
                f.AddAdj('adj', 15, '55555');
                f.AddGuide('a', 10, '0', 'adj', '71431');
                f.AddGuide('dy', 0, 'a', 'h', '100000');
                f.AddGuide('y1', 1, 't', 'dy', '0');
                f.AddGuide('y2', 1, 'b', '0', 'dy');
                f.AddHandleXY(undefined, '0', '0', 'adj', '0', '71431', 'l', 'y1');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'y1');
                f.AddPathCommand(2, 'r', 't');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'b');
                f.AddPathCommand(2, 'r', 'y2');
                break;
            }
            case 'textStop':{
                f.AddAdj('adj', 15, '25000');
                f.AddGuide('a', 10, '14286', 'adj', '50000');
                f.AddGuide('dx', 0, 'w', '1', '3');
                f.AddGuide('dy', 0, 'a', 'h', '100000');
                f.AddGuide('x1', 1, 'l', 'dx', '0');
                f.AddGuide('x2', 1, 'r', '0', 'dx');
                f.AddGuide('y1', 1, 't', 'dy', '0');
                f.AddGuide('y2', 1, 'b', '0', 'dy');
                f.AddHandleXY(undefined, '0', '0', 'adj', '14286', '50000', 'l', 'dy');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'y1');
                f.AddPathCommand(2, 'x1', 't');
                f.AddPathCommand(2, 'x2', 't');
                f.AddPathCommand(2, 'r', 'y1');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'y2');
                f.AddPathCommand(2, 'x1', 'b');
                f.AddPathCommand(2, 'x2', 'b');
                f.AddPathCommand(2, 'r', 'y2');
                break;
            }
            case 'textTriangle':{
                f.AddAdj('adj', 15, '50000');
                f.AddGuide('a', 10, '0', 'adj', '100000');
                f.AddGuide('y', 0, 'a', 'h', '100000');
                f.AddHandleXY(undefined, '0', '0', 'adj', '0', '100000', 'l', 'y');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'y');
                f.AddPathCommand(2, 'hc', 't');
                f.AddPathCommand(2, 'r', 'y');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'b');
                f.AddPathCommand(2, 'r', 'b');
                break;
            }
            case 'textTriangleInverted':{
                f.AddAdj('adj', 15, '50000');
                f.AddGuide('a', 10, '0', 'adj', '100000');
                f.AddGuide('y', 0, 'a', 'h', '100000');
                f.AddHandleXY(undefined, '0', '0', 'adj', '0', '100000', 'l', 'y');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 't');
                f.AddPathCommand(2, 'r', 't');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'l', 'y');
                f.AddPathCommand(2, 'hc', 'b');
                f.AddPathCommand(2, 'r', 'y');
                break;
            }
            case 'textWave1':{
                f.AddAdj('adj1', 15, '12500');
                f.AddAdj('adj2', 15, '0');
                f.AddGuide('a1', 10, '0', 'adj1', '20000');
                f.AddGuide('a2', 10, '-10000', 'adj2', '10000');
                f.AddGuide('y1', 0, 'h', 'a1', '100000');
                f.AddGuide('dy2', 0, 'y1', '10', '3');
                f.AddGuide('y2', 1, 'y1', '0', 'dy2');
                f.AddGuide('y3', 1, 'y1', 'dy2', '0');
                f.AddGuide('y4', 1, 'b', '0', 'y1');
                f.AddGuide('y5', 1, 'y4', '0', 'dy2');
                f.AddGuide('y6', 1, 'y4', 'dy2', '0');
                f.AddGuide('of', 0, 'w', 'a2', '100000');
                f.AddGuide('of2', 0, 'w', 'a2', '50000');
                f.AddGuide('x1', 4, 'of');
                f.AddGuide('dx2', 3, 'of2', '0', 'of2');
                f.AddGuide('x2', 1, 'l', '0', 'dx2');
                f.AddGuide('dx5', 3, 'of2', 'of2', '0');
                f.AddGuide('x5', 1, 'r', '0', 'dx5');
                f.AddGuide('dx3', 2, 'dx2', 'x5', '3');
                f.AddGuide('x3', 1, 'x2', 'dx3', '0');
                f.AddGuide('x4', 2, 'x3', 'x5', '2');
                f.AddGuide('x6', 1, 'l', 'dx5', '0');
                f.AddGuide('x10', 1, 'r', 'dx2', '0');
                f.AddGuide('x7', 1, 'x6', 'dx3', '0');
                f.AddGuide('x8', 2, 'x7', 'x10', '2');
                f.AddGuide('x9', 1, 'r', '0', 'x1');
                f.AddGuide('xAdj', 1, 'hc', 'of', '0');
                f.AddHandleXY(undefined, '0', '0', 'adj1', '0', '20000', 'l', 'y1');
                f.AddHandleXY('adj2', '-10000', '10000', undefined, '0', '0', 'xAdj', 'b');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x2', 'y1');
                f.AddPathCommand(5, 'x3', 'y2', 'x4', 'y3', 'x5', 'y1');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x6', 'y4');
                f.AddPathCommand(5, 'x7', 'y5', 'x8', 'y6', 'x10', 'y4');
                break;
            }
            case 'textWave2':{
                f.AddAdj('adj1', 15, '12500');
                f.AddAdj('adj2', 15, '0');
                f.AddGuide('a1', 10, '0', 'adj1', '20000');
                f.AddGuide('a2', 10, '-10000', 'adj2', '10000');
                f.AddGuide('y1', 0, 'h', 'a1', '100000');
                f.AddGuide('dy2', 0, 'y1', '10', '3');
                f.AddGuide('y2', 1, 'y1', '0', 'dy2');
                f.AddGuide('y3', 1, 'y1', 'dy2', '0');
                f.AddGuide('y4', 1, 'b', '0', 'y1');
                f.AddGuide('y5', 1, 'y4', '0', 'dy2');
                f.AddGuide('y6', 1, 'y4', 'dy2', '0');
                f.AddGuide('of', 0, 'w', 'a2', '100000');
                f.AddGuide('of2', 0, 'w', 'a2', '50000');
                f.AddGuide('x1', 4, 'of');
                f.AddGuide('dx2', 3, 'of2', '0', 'of2');
                f.AddGuide('x2', 1, 'l', '0', 'dx2');
                f.AddGuide('dx5', 3, 'of2', 'of2', '0');
                f.AddGuide('x5', 1, 'r', '0', 'dx5');
                f.AddGuide('dx3', 2, 'dx2', 'x5', '3');
                f.AddGuide('x3', 1, 'x2', 'dx3', '0');
                f.AddGuide('x4', 2, 'x3', 'x5', '2');
                f.AddGuide('x6', 1, 'l', 'dx5', '0');
                f.AddGuide('x10', 1, 'r', 'dx2', '0');
                f.AddGuide('x7', 1, 'x6', 'dx3', '0');
                f.AddGuide('x8', 2, 'x7', 'x10', '2');
                f.AddGuide('x9', 1, 'r', '0', 'x1');
                f.AddGuide('xAdj', 1, 'hc', 'of', '0');
                f.AddHandleXY(undefined, '0', '0', 'adj1', '0', '20000', 'l', 'y1');
                f.AddHandleXY('adj2', '-10000', '10000', undefined, '0', '0', 'xAdj', 'b');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x2', 'y1');
                f.AddPathCommand(5, 'x3', 'y3', 'x4', 'y2', 'x5', 'y1');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x6', 'y4');
                f.AddPathCommand(5, 'x7', 'y6', 'x8', 'y5', 'x10', 'y4');
                break;
            }
            case 'textWave4':{
                f.AddAdj('adj1', 15, '6250');
                f.AddAdj('adj2', 15, '0');
                f.AddGuide('a1', 10, '0', 'adj1', '12500');
                f.AddGuide('a2', 10, '-10000', 'adj2', '10000');
                f.AddGuide('y1', 0, 'h', 'a1', '100000');
                f.AddGuide('dy2', 0, 'y1', '10', '3');
                f.AddGuide('y2', 1, 'y1', '0', 'dy2');
                f.AddGuide('y3', 1, 'y1', 'dy2', '0');
                f.AddGuide('y4', 1, 'b', '0', 'y1');
                f.AddGuide('y5', 1, 'y4', '0', 'dy2');
                f.AddGuide('y6', 1, 'y4', 'dy2', '0');
                f.AddGuide('of', 0, 'w', 'a2', '100000');
                f.AddGuide('of2', 0, 'w', 'a2', '50000');
                f.AddGuide('x1', 4, 'of');
                f.AddGuide('dx2', 3, 'of2', '0', 'of2');
                f.AddGuide('x2', 1, 'l', '0', 'dx2');
                f.AddGuide('dx8', 3, 'of2', 'of2', '0');
                f.AddGuide('x8', 1, 'r', '0', 'dx8');
                f.AddGuide('dx3', 2, 'dx2', 'x8', '6');
                f.AddGuide('x3', 1, 'x2', 'dx3', '0');
                f.AddGuide('dx4', 2, 'dx2', 'x8', '3');
                f.AddGuide('x4', 1, 'x2', 'dx4', '0');
                f.AddGuide('x5', 2, 'x2', 'x8', '2');
                f.AddGuide('x6', 1, 'x5', 'dx3', '0');
                f.AddGuide('x7', 2, 'x6', 'x8', '2');
                f.AddGuide('x9', 1, 'l', 'dx8', '0');
                f.AddGuide('x15', 1, 'r', 'dx2', '0');
                f.AddGuide('x10', 1, 'x9', 'dx3', '0');
                f.AddGuide('x11', 1, 'x9', 'dx4', '0');
                f.AddGuide('x12', 2, 'x9', 'x15', '2');
                f.AddGuide('x13', 1, 'x12', 'dx3', '0');
                f.AddGuide('x14', 2, 'x13', 'x15', '2');
                f.AddGuide('x16', 1, 'r', '0', 'x1');
                f.AddGuide('xAdj', 1, 'hc', 'of', '0');
                f.AddHandleXY(undefined, '0', '0', 'adj1', '0', '12500', 'l', 'y1');
                f.AddHandleXY('adj2', '-10000', '10000', undefined, '0', '0', 'xAdj', 'b');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x2', 'y1');
                f.AddPathCommand(5, 'x3', 'y3', 'x4', 'y2', 'x5', 'y1');
                f.AddPathCommand(5, 'x6', 'y3', 'x7', 'y2', 'x8', 'y1');
                f.AddPathCommand(0, false, 'none', undefined, undefined, undefined);
                f.AddPathCommand(1, 'x9', 'y4');
                f.AddPathCommand(5, 'x10', 'y6', 'x11', 'y5', 'x12', 'y4');
                f.AddPathCommand(5, 'x13', 'y6', 'x14', 'y5', 'x15', 'y4');
                break;
            }
        }
        if(typeof prst === "string" && prst.length > 0)
        {
            f.setPreset(prst);
        }
        return f;
    }, this, []);

}

    //--------------------------------------------------------export----------------------------------------------------
    window['AscFormat'] = window['AscFormat'] || {};
    window['AscFormat'].CreateGeometry = CreateGeometry;
    window['AscFormat'].getPrstByNumber = getPrstByNumber;
    window['AscFormat'].getNumByTxPrst = getNumByTxPrst;
    window['AscFormat'].CreatePrstTxWarpGeometry = CreatePrstTxWarpGeometry;
})(window);
