/*
 * (c) Copyright Ascensio System SIA 2010-2019
 *
 * This program is a free software product. You can redistribute it and/or
 * modify it under the terms of the GNU Affero General Public License (AGPL)
 * version 3 as published by the Free Software Foundation. In accordance with
 * Section 7(a) of the GNU AGPL its Section 15 shall be amended to the effect
 * that Ascensio System SIA expressly excludes the warranty of non-infringement
 * of any third-party rights.
 *
 * This program is distributed WITHOUT ANY WARRANTY; without even the implied
 * warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR  PURPOSE. For
 * details, see the GNU AGPL at: http://www.gnu.org/licenses/agpl-3.0.html
 *
 * You can contact Ascensio System SIA at 20A-12 Ernesta Birznieka-Upisha
 * street, Riga, Latvia, EU, LV-1050.
 *
 * The  interactive user interfaces in modified source and object code versions
 * of the Program must display Appropriate Legal Notices, as required under
 * Section 5 of the GNU AGPL version 3.
 *
 * Pursuant to Section 7(b) of the License you must retain the original Product
 * logo when distributing the program. Pursuant to Section 7(e) we decline to
 * grant you any rights under trademark law for use of our trademarks.
 *
 * All the Product's GUI elements, including illustrations and icon sets, as
 * well as technical writing content are licensed under the terms of the
 * Creative Commons Attribution-ShareAlike 4.0 International. See the License
 * terms at http://creativecommons.org/licenses/by-sa/4.0/legalcode
 *
 */

//This file containes definition of object which used in api.js
//It need to prevent minimize the name of object's method.

var c_oAscLockTypeElemPresentation;
var CThemeLoadInfo;
var CShowPr;
var CPresentation;
var MasterSlide;
var SlideLayout;
var Slide;
var CWriteCommentData;
var CAscSlideTransition;
var c_oAscSlideTransitionTypes;
var c_oAscSlideTransitionParams;
var CCommentAuthor;
var PresentationSelectedContent;
var DrawingCopyObject;

var RULER_OBJECT_TYPE_PARAGRAPH;
var RULER_OBJECT_TYPE_HEADER;
var RULER_OBJECT_TYPE_FOOTER;
var RULER_OBJECT_TYPE_TABLE;
var RULER_OBJECT_TYPE_COLUMNS;

var CColumnsMarkup;
var c_oAscCollaborativeMarksShowType;
var c_oAscTableSelectionType;
var c_oAscHyperlinkAnchor;
var CPolygon;
var c_oAscSdtLevelType;
var c_oAscRevisionsObjectType;
var c_oAscWrapStyle2;
var DRAWING_ARRAY_TYPE_INLINE;
var DRAWING_ARRAY_TYPE_BEHIND;
var DRAWING_ARRAY_TYPE_WRAPPING;
var DRAWING_ARRAY_TYPE_BEFORE;
var TABLE_STYLE_WIDTH_PIX;
var TABLE_STYLE_HEIGHT_PIX;
var CTab;
var c_oAscMouseMoveLockedObjectType;
var CParagraphSearchMark;
var c_oAscTableLayout;
var c_oAscVertAlignJc;
