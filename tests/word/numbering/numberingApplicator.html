<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />

    <title>Document calculation tests</title>

    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <link type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/qunit/2.16.0/qunit.css" rel="stylesheet" media="screen" />
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/qunit/2.16.0/qunit.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/xregexp/3.2.0/xregexp-all.min.js"></script>


    <script type="text/javascript" src="../../../develop/sdkjs/word/scripts.js"></script>
    <script type="text/javascript">
		window.sdk_scripts.forEach(function(item){
			document.write('<script type="text/javascript" src="' + item + '"><\/script>');
		});
    </script>

    <script type="text/javascript" src="../common/common.js"></script>
    <script type="text/javascript" src="../common/editor.js"></script>
    <script type="text/javascript" src="../common/document.js"></script>
    <script type="text/javascript" src="../common/measurer.js"></script>

    <script type="text/javascript" src="numberingApplicator.js"></script>
</head>
<body>
<h1 id="qunit-header">Table</h1>
<h2 id="qunit-banner"></h2>
<div id="qunit-testrunner-toolbar"></div>
<h2 id="qunit-userAgent"></h2>
<ol id="qunit-tests"></ol>
<div id="qunit-fixture">test markup, will be hidden</div>
</body>
</html>
