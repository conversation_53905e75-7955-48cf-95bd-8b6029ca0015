/*
 * (c) Copyright Ascensio System SIA 2010-2019
 *
 * This program is a free software product. You can redistribute it and/or
 * modify it under the terms of the GNU Affero General Public License (AGPL)
 * version 3 as published by the Free Software Foundation. In accordance with
 * Section 7(a) of the GNU AGPL its Section 15 shall be amended to the effect
 * that Ascensio System SIA expressly excludes the warranty of non-infringement
 * of any third-party rights.
 *
 * This program is distributed WITHOUT ANY WARRANTY; without even the implied
 * warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR  PURPOSE. For
 * details, see the GNU AGPL at: http://www.gnu.org/licenses/agpl-3.0.html
 *
 * You can contact Ascensio System SIA at 20A-12 Ernesta Birznieka-Upisha
 * street, Riga, Latvia, EU, LV-1050.
 *
 * The  interactive user interfaces in modified source and object code versions
 * of the Program must display Appropriate Legal Notices, as required under
 * Section 5 of the GNU AGPL version 3.
 *
 * Pursuant to Section 7(b) of the License you must retain the original Product
 * logo when distributing the program. Pursuant to Section 7(e) we decline to
 * grant you any rights under trademark law for use of our trademarks.
 *
 * All the Product's GUI elements, including illustrations and icon sets, as
 * well as technical writing content are licensed under the terms of the
 * Creative Commons Attribution-ShareAlike 4.0 International. See the License
 * terms at http://creativecommons.org/licenses/by-sa/4.0/legalcode
 *
 */

$(function () {

	var ver = 2;

	module("CellFormat");

	/*test("parse", function test_parse() {
		var test_expr1 = "[Green][>1]\\;#,##0.00?_);[Blue][<0.5]$(#,##0.00?);\"x\"0.0??_);[Red]@x-\"err\"-y\\\"-\\@@";
		var expr = window.Asc.CellFormatParser.parse(test_expr1);

		strictEqual(expr.length, 4, "Number of Sections");

		strictEqual(expr[0].color, "Green", "Section 1 color");
		strictEqual(expr[0].condition(2), true, "Section 1 condition 2 > 1");
		strictEqual(expr[0].condition(1.0001), true, "Section 1 condition 1.0001 > 1");
		strictEqual(expr[0].condition(1), false, "Section 1 condition 1 > 1");
		strictEqual(expr[0].condition(0.5), false, "Section 1 condition 0.5 > 1");
		strictEqual(expr[0].condition(-1), false, "Section 1 condition -1 > 1");
		strictEqual(expr[0].format, "\\;#,##0.00?_)", "Section 1 format");

		strictEqual(expr[1].color, "Blue", "Section 2 color");
		strictEqual(expr[1].condition(1), false, "Section 2 condition 1 < 0.5");
		strictEqual(expr[1].condition(0.5), false, "Section 2 condition 0.5 < 0.5");
		strictEqual(expr[1].condition(0.4999), true, "Section 2 condition 0.4999 < 0.5");
		strictEqual(expr[1].condition(0), true, "Section 2 condition 0 < 0.5");
		strictEqual(expr[1].condition(-1), true, "Section 2 condition -1 < 0.5");
		strictEqual(expr[1].format, "$(#,##0.00?)", "Section 2 format");

		strictEqual(expr[2].color, "", "Section 3 color");
		strictEqual(expr[2].condition, "", "Section 3 condition is empty");
		strictEqual(expr[2].format, "\"x\"0.0??_)", "Section 3 format");

		strictEqual(expr[3].color, "Red", "Section 4 color");
		strictEqual(expr[3].condition, undefined, "Section 4 condition is undefined");
		strictEqual(expr[3].format.length, 3, "Section 3 format length");
		strictEqual(expr[3].format[0], "@", "Section 3 format[0]");
		strictEqual(expr[3].format[1], "x-err-y\"-@", "Section 3 format[1]");
		strictEqual(expr[3].format[2], "@", "Section 3 format[2]");
	});*/

	function check_numeric_format(fmtStr, nArr, res) {
		var expr, fmt, i;

		switch (ver) {
			case 1:
				expr = window.Asc.CellFormatParser.parse(fmtStr);
				fmt  = formatData(expr[0].format);
				for (i = 0; i < nArr.length; ++i) {
					strictEqual(fmt(nArr[i], "n"), res[i], "format(\"" + fmtStr + "\", " + nArr[i] + ")");
				}
				break;

			case 2:
				expr = new AscCommon.CellFormat(fmtStr);
				for (i = 0; i < nArr.length; ++i) {
					var aFormated = expr.format(nArr[i]);
					var text = "";
					for(var j = 0, length = aFormated.length; j < length; ++j)
						text += aFormated[j].text;
					strictEqual(text, res[i], "format(\"" + fmtStr + "\", " + nArr[i] + ")");
				}
				break;
		}
	}

	test("format1", function test_format1() {
		var numbers = [0, 1, 11, 15, 19, 200, 220, 250, 280, 1234, -1, -11, -15, -19, -200, -220, -250, -280, -9876, 0.1, 0.12, 0.151, 0.181, 0.5, 0.53, 0.555, 0.575, 0.9, 0.94, 0.959, 0.969, -0.1, -0.12, -0.151, -0.181, -0.5, -0.53, -0.555, -0.575, -0.9, -0.94, -0.959, -0.969, 3.4, 4.56, 4.32, 4.567, 4.321, -3.4, -4.56, -4.32, -4.567, -4.321];

		check_numeric_format("0", numbers,
				["0", "1", "11", "15", "19", "200", "220", "250", "280", "1234", "-1", "-11", "-15", "-19", "-200", "-220", "-250", "-280", "-9876", "0", "0", "0", "0", "1", "1", "1", "1", "1", "1", "1", "1", "0", "0", "0", "0", "-1", "-1", "-1", "-1", "-1", "-1", "-1", "-1", "3", "5", "4", "5", "4", "-3", "-5", "-4", "-5", "-4"]);
		check_numeric_format("0.", numbers,
				["0.", "1.", "11.", "15.", "19.", "200.", "220.", "250.", "280.", "1234.", "-1.", "-11.", "-15.", "-19.", "-200.", "-220.", "-250.", "-280.", "-9876.", "0.", "0.", "0.", "0.", "1.", "1.", "1.", "1.", "1.", "1.", "1.", "1.", "0.", "0.", "0.", "0.", "-1.", "-1.", "-1.", "-1.", "-1.", "-1.", "-1.", "-1.", "3.", "5.", "4.", "5.", "4.", "-3.", "-5.", "-4.", "-5.", "-4."]);
		check_numeric_format(".0", numbers,
				[".0", "1.0", "11.0", "15.0", "19.0", "200.0", "220.0", "250.0", "280.0", "1234.0", "-1.0", "-11.0", "-15.0", "-19.0", "-200.0", "-220.0", "-250.0", "-280.0", "-9876.0", ".1", ".1", ".2", ".2", ".5", ".5", ".6", ".6", ".9", ".9", "1.0", "1.0", "-.1", "-.1", "-.2", "-.2", "-.5", "-.5", "-.6", "-.6", "-.9", "-.9", "-1.0", "-1.0", "3.4", "4.6", "4.3", "4.6", "4.3", "-3.4", "-4.6", "-4.3", "-4.6", "-4.3"]);
		check_numeric_format("0.0", numbers,
				["0.0", "1.0", "11.0", "15.0", "19.0", "200.0", "220.0", "250.0", "280.0", "1234.0", "-1.0", "-11.0", "-15.0", "-19.0", "-200.0", "-220.0", "-250.0", "-280.0", "-9876.0", "0.1", "0.1", "0.2", "0.2", "0.5", "0.5", "0.6", "0.6", "0.9", "0.9", "1.0", "1.0", "-0.1", "-0.1", "-0.2", "-0.2", "-0.5", "-0.5", "-0.6", "-0.6", "-0.9", "-0.9", "-1.0", "-1.0", "3.4", "4.6", "4.3", "4.6", "4.3", "-3.4", "-4.6", "-4.3", "-4.6", "-4.3"]);
		check_numeric_format("0E-0", numbers,
				["0E0", "1E0", "1E1", "2E1", "2E1", "2E2", "2E2", "3E2", "3E2", "1E3", "-1E0", "-1E1", "-2E1", "-2E1", "-2E2", "-2E2", "-3E2", "-3E2", "-1E4", "1E-1", "1E-1", "2E-1", "2E-1", "5E-1", "5E-1", "6E-1", "6E-1", "9E-1", "9E-1", "1E0", "1E0", "-1E-1", "-1E-1", "-2E-1", "-2E-1", "-5E-1", "-5E-1", "-6E-1", "-6E-1", "-9E-1", "-9E-1", "-1E0", "-1E0", "3E0", "5E0", "4E0", "5E0", "4E0", "-3E0", "-5E0", "-4E0", "-5E0", "-4E0"]);
		check_numeric_format("0E+0", numbers,
				["0E+0", "1E+0", "1E+1", "2E+1", "2E+1", "2E+2", "2E+2", "3E+2", "3E+2", "1E+3", "-1E+0", "-1E+1", "-2E+1", "-2E+1", "-2E+2", "-2E+2", "-3E+2", "-3E+2", "-1E+4", "1E-1", "1E-1", "2E-1", "2E-1", "5E-1", "5E-1", "6E-1", "6E-1", "9E-1", "9E-1", "1E+0", "1E+0", "-1E-1", "-1E-1", "-2E-1", "-2E-1", "-5E-1", "-5E-1", "-6E-1", "-6E-1", "-9E-1", "-9E-1", "-1E+0", "-1E+0", "3E+0", "5E+0", "4E+0", "5E+0", "4E+0", "-3E+0", "-5E+0", "-4E+0", "-5E+0", "-4E+0"]);
		check_numeric_format(".0E-0", numbers,
				[".0E0", ".1E1", ".1E2", ".2E2", ".2E2", ".2E3", ".2E3", ".3E3", ".3E3", ".1E4", "-.1E1", "-.1E2", "-.2E2", "-.2E2", "-.2E3", "-.2E3", "-.3E3", "-.3E3", "-.1E5", ".1E0", ".1E0", ".2E0", ".2E0", ".5E0", ".5E0", ".6E0", ".6E0", ".9E0", ".9E0", ".1E1", ".1E1", "-.1E0", "-.1E0", "-.2E0", "-.2E0", "-.5E0", "-.5E0", "-.6E0", "-.6E0", "-.9E0", "-.9E0", "-.1E1", "-.1E1", ".3E1", ".5E1", ".4E1", ".5E1", ".4E1", "-.3E1", "-.5E1", "-.4E1", "-.5E1", "-.4E1"]);
		check_numeric_format(".0E+0", numbers,
				[".0E+0", ".1E+1", ".1E+2", ".2E+2", ".2E+2", ".2E+3", ".2E+3", ".3E+3", ".3E+3", ".1E+4", "-.1E+1", "-.1E+2", "-.2E+2", "-.2E+2", "-.2E+3", "-.2E+3", "-.3E+3", "-.3E+3", "-.1E+5", ".1E+0", ".1E+0", ".2E+0", ".2E+0", ".5E+0", ".5E+0", ".6E+0", ".6E+0", ".9E+0", ".9E+0", ".1E+1", ".1E+1", "-.1E+0", "-.1E+0", "-.2E+0", "-.2E+0", "-.5E+0", "-.5E+0", "-.6E+0", "-.6E+0", "-.9E+0", "-.9E+0", "-.1E+1", "-.1E+1", ".3E+1", ".5E+1", ".4E+1", ".5E+1", ".4E+1", "-.3E+1", "-.5E+1", "-.4E+1", "-.5E+1", "-.4E+1"]);
		check_numeric_format("0.0E-0", numbers,
				["0.0E0", "1.0E0", "1.1E1", "1.5E1", "1.9E1", "2.0E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1.0E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2.0E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1.0E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.0E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.0E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.0E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.0E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.0E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format("0.0E+0", numbers,
				["0.0E+0", "1.0E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2.0E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1.0E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2.0E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1.0E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.0E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.0E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.0E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.0E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.0E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
		check_numeric_format("0.#", numbers,
				["0.", "1.", "11.", "15.", "19.", "200.", "220.", "250.", "280.", "1234.", "-1.", "-11.", "-15.", "-19.", "-200.", "-220.", "-250.", "-280.", "-9876.", "0.1", "0.1", "0.2", "0.2", "0.5", "0.5", "0.6", "0.6", "0.9", "0.9", "1.", "1.", "-0.1", "-0.1", "-0.2", "-0.2", "-0.5", "-0.5", "-0.6", "-0.6", "-0.9", "-0.9", "-1.", "-1.", "3.4", "4.6", "4.3", "4.6", "4.3", "-3.4", "-4.6", "-4.3", "-4.6", "-4.3"]);
		check_numeric_format("#.0", numbers,
				[".0", "1.0", "11.0", "15.0", "19.0", "200.0", "220.0", "250.0", "280.0", "1234.0", "-1.0", "-11.0", "-15.0", "-19.0", "-200.0", "-220.0", "-250.0", "-280.0", "-9876.0", ".1", ".1", ".2", ".2", ".5", ".5", ".6", ".6", ".9", ".9", "1.0", "1.0", "-.1", "-.1", "-.2", "-.2", "-.5", "-.5", "-.6", "-.6", "-.9", "-.9", "-1.0", "-1.0", "3.4", "4.6", "4.3", "4.6", "4.3", "-3.4", "-4.6", "-4.3", "-4.6", "-4.3"]);
		check_numeric_format("0E-#", numbers,
				["0E0", "1E0", "1E1", "2E1", "2E1", "2E2", "2E2", "3E2", "3E2", "1E3", "-1E0", "-1E1", "-2E1", "-2E1", "-2E2", "-2E2", "-3E2", "-3E2", "-1E4", "1E-1", "1E-1", "2E-1", "2E-1", "5E-1", "5E-1", "6E-1", "6E-1", "9E-1", "9E-1", "1E0", "1E0", "-1E-1", "-1E-1", "-2E-1", "-2E-1", "-5E-1", "-5E-1", "-6E-1", "-6E-1", "-9E-1", "-9E-1", "-1E0", "-1E0", "3E0", "5E0", "4E0", "5E0", "4E0", "-3E0", "-5E0", "-4E0", "-5E0", "-4E0"]);
		check_numeric_format("#E-0", numbers,
				["0E0", "1E0", "1E1", "2E1", "2E1", "2E2", "2E2", "3E2", "3E2", "1E3", "-1E0", "-1E1", "-2E1", "-2E1", "-2E2", "-2E2", "-3E2", "-3E2", "-1E4", "1E-1", "1E-1", "2E-1", "2E-1", "5E-1", "5E-1", "6E-1", "6E-1", "9E-1", "9E-1", "1E0", "1E0", "-1E-1", "-1E-1", "-2E-1", "-2E-1", "-5E-1", "-5E-1", "-6E-1", "-6E-1", "-9E-1", "-9E-1", "-1E0", "-1E0", "3E0", "5E0", "4E0", "5E0", "4E0", "-3E0", "-5E0", "-4E0", "-5E0", "-4E0"]);
		check_numeric_format("0E+#", numbers,
				["0E+0", "1E+0", "1E+1", "2E+1", "2E+1", "2E+2", "2E+2", "3E+2", "3E+2", "1E+3", "-1E+0", "-1E+1", "-2E+1", "-2E+1", "-2E+2", "-2E+2", "-3E+2", "-3E+2", "-1E+4", "1E-1", "1E-1", "2E-1", "2E-1", "5E-1", "5E-1", "6E-1", "6E-1", "9E-1", "9E-1", "1E+0", "1E+0", "-1E-1", "-1E-1", "-2E-1", "-2E-1", "-5E-1", "-5E-1", "-6E-1", "-6E-1", "-9E-1", "-9E-1", "-1E+0", "-1E+0", "3E+0", "5E+0", "4E+0", "5E+0", "4E+0", "-3E+0", "-5E+0", "-4E+0", "-5E+0", "-4E+0"]);
		check_numeric_format("#E+0", numbers,
				["0E+0", "1E+0", "1E+1", "2E+1", "2E+1", "2E+2", "2E+2", "3E+2", "3E+2", "1E+3", "-1E+0", "-1E+1", "-2E+1", "-2E+1", "-2E+2", "-2E+2", "-3E+2", "-3E+2", "-1E+4", "1E-1", "1E-1", "2E-1", "2E-1", "5E-1", "5E-1", "6E-1", "6E-1", "9E-1", "9E-1", "1E+0", "1E+0", "-1E-1", "-1E-1", "-2E-1", "-2E-1", "-5E-1", "-5E-1", "-6E-1", "-6E-1", "-9E-1", "-9E-1", "-1E+0", "-1E+0", "3E+0", "5E+0", "4E+0", "5E+0", "4E+0", "-3E+0", "-5E+0", "-4E+0", "-5E+0", "-4E+0"]);
		check_numeric_format(".0E-#", numbers,
				[".0E0", ".1E1", ".1E2", ".2E2", ".2E2", ".2E3", ".2E3", ".3E3", ".3E3", ".1E4", "-.1E1", "-.1E2", "-.2E2", "-.2E2", "-.2E3", "-.2E3", "-.3E3", "-.3E3", "-.1E5", ".1E0", ".1E0", ".2E0", ".2E0", ".5E0", ".5E0", ".6E0", ".6E0", ".9E0", ".9E0", ".1E1", ".1E1", "-.1E0", "-.1E0", "-.2E0", "-.2E0", "-.5E0", "-.5E0", "-.6E0", "-.6E0", "-.9E0", "-.9E0", "-.1E1", "-.1E1", ".3E1", ".5E1", ".4E1", ".5E1", ".4E1", "-.3E1", "-.5E1", "-.4E1", "-.5E1", "-.4E1"]);
		check_numeric_format(".#E-0", numbers,
				[".E0", ".1E1", ".1E2", ".2E2", ".2E2", ".2E3", ".2E3", ".3E3", ".3E3", ".1E4", "-.1E1", "-.1E2", "-.2E2", "-.2E2", "-.2E3", "-.2E3", "-.3E3", "-.3E3", "-.1E5", ".1E0", ".1E0", ".2E0", ".2E0", ".5E0", ".5E0", ".6E0", ".6E0", ".9E0", ".9E0", ".1E1", ".1E1", "-.1E0", "-.1E0", "-.2E0", "-.2E0", "-.5E0", "-.5E0", "-.6E0", "-.6E0", "-.9E0", "-.9E0", "-.1E1", "-.1E1", ".3E1", ".5E1", ".4E1", ".5E1", ".4E1", "-.3E1", "-.5E1", "-.4E1", "-.5E1", "-.4E1"]);
		check_numeric_format(".0E+#", numbers,
				[".0E+0", ".1E+1", ".1E+2", ".2E+2", ".2E+2", ".2E+3", ".2E+3", ".3E+3", ".3E+3", ".1E+4", "-.1E+1", "-.1E+2", "-.2E+2", "-.2E+2", "-.2E+3", "-.2E+3", "-.3E+3", "-.3E+3", "-.1E+5", ".1E+0", ".1E+0", ".2E+0", ".2E+0", ".5E+0", ".5E+0", ".6E+0", ".6E+0", ".9E+0", ".9E+0", ".1E+1", ".1E+1", "-.1E+0", "-.1E+0", "-.2E+0", "-.2E+0", "-.5E+0", "-.5E+0", "-.6E+0", "-.6E+0", "-.9E+0", "-.9E+0", "-.1E+1", "-.1E+1", ".3E+1", ".5E+1", ".4E+1", ".5E+1", ".4E+1", "-.3E+1", "-.5E+1", "-.4E+1", "-.5E+1", "-.4E+1"]);
		check_numeric_format(".#E+0", numbers,
				[".E+0", ".1E+1", ".1E+2", ".2E+2", ".2E+2", ".2E+3", ".2E+3", ".3E+3", ".3E+3", ".1E+4", "-.1E+1", "-.1E+2", "-.2E+2", "-.2E+2", "-.2E+3", "-.2E+3", "-.3E+3", "-.3E+3", "-.1E+5", ".1E+0", ".1E+0", ".2E+0", ".2E+0", ".5E+0", ".5E+0", ".6E+0", ".6E+0", ".9E+0", ".9E+0", ".1E+1", ".1E+1", "-.1E+0", "-.1E+0", "-.2E+0", "-.2E+0", "-.5E+0", "-.5E+0", "-.6E+0", "-.6E+0", "-.9E+0", "-.9E+0", "-.1E+1", "-.1E+1", ".3E+1", ".5E+1", ".4E+1", ".5E+1", ".4E+1", "-.3E+1", "-.5E+1", "-.4E+1", "-.5E+1", "-.4E+1"]);
		check_numeric_format("0.0E-#", numbers,
				["0.0E0", "1.0E0", "1.1E1", "1.5E1", "1.9E1", "2.0E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1.0E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2.0E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1.0E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.0E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.0E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.0E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.0E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.0E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format("0.#E-0", numbers,
				["0.E0", "1.E0", "1.1E1", "1.5E1", "1.9E1", "2.E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1.E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2.E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1.E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format("#.0E-0", numbers,
				["0.0E0", "1.0E0", "1.1E1", "1.5E1", "1.9E1", "2.0E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1.0E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2.0E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1.0E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.0E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.0E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.0E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.0E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.0E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format("0.0E+#", numbers,
				["0.0E+0", "1.0E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2.0E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1.0E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2.0E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1.0E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.0E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.0E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.0E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.0E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.0E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
		check_numeric_format("0.#E+0", numbers,
				["0.E+0", "1.E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2.E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1.E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2.E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1.E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
		check_numeric_format("#.0E+0", numbers,
				["0.0E+0", "1.0E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2.0E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1.0E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2.0E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1.0E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.0E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.0E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.0E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.0E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.0E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
		check_numeric_format("0.?", numbers,
				["0. ", "1. ", "11. ", "15. ", "19. ", "200. ", "220. ", "250. ", "280. ", "1234. ", "-1. ", "-11. ", "-15. ", "-19. ", "-200. ", "-220. ", "-250. ", "-280. ", "-9876. ", "0.1", "0.1", "0.2", "0.2", "0.5", "0.5", "0.6", "0.6", "0.9", "0.9", "1. ", "1. ", "-0.1", "-0.1", "-0.2", "-0.2", "-0.5", "-0.5", "-0.6", "-0.6", "-0.9", "-0.9", "-1. ", "-1. ", "3.4", "4.6", "4.3", "4.6", "4.3", "-3.4", "-4.6", "-4.3", "-4.6", "-4.3"]);
		check_numeric_format("?.0", numbers,
				[" .0", "1.0", "11.0", "15.0", "19.0", "200.0", "220.0", "250.0", "280.0", "1234.0", "-1.0", "-11.0", "-15.0", "-19.0", "-200.0", "-220.0", "-250.0", "-280.0", "-9876.0", " .1", " .1", " .2", " .2", " .5", " .5", " .6", " .6", " .9", " .9", "1.0", "1.0", "- .1", "- .1", "- .2", "- .2", "- .5", "- .5", "- .6", "- .6", "- .9", "- .9", "-1.0", "-1.0", "3.4", "4.6", "4.3", "4.6", "4.3", "-3.4", "-4.6", "-4.3", "-4.6", "-4.3"]);
		check_numeric_format("0E-?", numbers,
				["0E0", "1E0", "1E1", "2E1", "2E1", "2E2", "2E2", "3E2", "3E2", "1E3", "-1E0", "-1E1", "-2E1", "-2E1", "-2E2", "-2E2", "-3E2", "-3E2", "-1E4", "1E-1", "1E-1", "2E-1", "2E-1", "5E-1", "5E-1", "6E-1", "6E-1", "9E-1", "9E-1", "1E0", "1E0", "-1E-1", "-1E-1", "-2E-1", "-2E-1", "-5E-1", "-5E-1", "-6E-1", "-6E-1", "-9E-1", "-9E-1", "-1E0", "-1E0", "3E0", "5E0", "4E0", "5E0", "4E0", "-3E0", "-5E0", "-4E0", "-5E0", "-4E0"]);
		check_numeric_format("?E-0", numbers,
				["0E0", "1E0", "1E1", "2E1", "2E1", "2E2", "2E2", "3E2", "3E2", "1E3", "-1E0", "-1E1", "-2E1", "-2E1", "-2E2", "-2E2", "-3E2", "-3E2", "-1E4", "1E-1", "1E-1", "2E-1", "2E-1", "5E-1", "5E-1", "6E-1", "6E-1", "9E-1", "9E-1", "1E0", "1E0", "-1E-1", "-1E-1", "-2E-1", "-2E-1", "-5E-1", "-5E-1", "-6E-1", "-6E-1", "-9E-1", "-9E-1", "-1E0", "-1E0", "3E0", "5E0", "4E0", "5E0", "4E0", "-3E0", "-5E0", "-4E0", "-5E0", "-4E0"]);
		check_numeric_format("0E+?", numbers,
				["0E+0", "1E+0", "1E+1", "2E+1", "2E+1", "2E+2", "2E+2", "3E+2", "3E+2", "1E+3", "-1E+0", "-1E+1", "-2E+1", "-2E+1", "-2E+2", "-2E+2", "-3E+2", "-3E+2", "-1E+4", "1E-1", "1E-1", "2E-1", "2E-1", "5E-1", "5E-1", "6E-1", "6E-1", "9E-1", "9E-1", "1E+0", "1E+0", "-1E-1", "-1E-1", "-2E-1", "-2E-1", "-5E-1", "-5E-1", "-6E-1", "-6E-1", "-9E-1", "-9E-1", "-1E+0", "-1E+0", "3E+0", "5E+0", "4E+0", "5E+0", "4E+0", "-3E+0", "-5E+0", "-4E+0", "-5E+0", "-4E+0"]);
		check_numeric_format("?E+0", numbers,
				["0E+0", "1E+0", "1E+1", "2E+1", "2E+1", "2E+2", "2E+2", "3E+2", "3E+2", "1E+3", "-1E+0", "-1E+1", "-2E+1", "-2E+1", "-2E+2", "-2E+2", "-3E+2", "-3E+2", "-1E+4", "1E-1", "1E-1", "2E-1", "2E-1", "5E-1", "5E-1", "6E-1", "6E-1", "9E-1", "9E-1", "1E+0", "1E+0", "-1E-1", "-1E-1", "-2E-1", "-2E-1", "-5E-1", "-5E-1", "-6E-1", "-6E-1", "-9E-1", "-9E-1", "-1E+0", "-1E+0", "3E+0", "5E+0", "4E+0", "5E+0", "4E+0", "-3E+0", "-5E+0", "-4E+0", "-5E+0", "-4E+0"]);
		check_numeric_format(".0E-?", numbers,
				[".0E0", ".1E1", ".1E2", ".2E2", ".2E2", ".2E3", ".2E3", ".3E3", ".3E3", ".1E4", "-.1E1", "-.1E2", "-.2E2", "-.2E2", "-.2E3", "-.2E3", "-.3E3", "-.3E3", "-.1E5", ".1E0", ".1E0", ".2E0", ".2E0", ".5E0", ".5E0", ".6E0", ".6E0", ".9E0", ".9E0", ".1E1", ".1E1", "-.1E0", "-.1E0", "-.2E0", "-.2E0", "-.5E0", "-.5E0", "-.6E0", "-.6E0", "-.9E0", "-.9E0", "-.1E1", "-.1E1", ".3E1", ".5E1", ".4E1", ".5E1", ".4E1", "-.3E1", "-.5E1", "-.4E1", "-.5E1", "-.4E1"]);
		check_numeric_format(".?E-0", numbers,
				[". E0", ".1E1", ".1E2", ".2E2", ".2E2", ".2E3", ".2E3", ".3E3", ".3E3", ".1E4", "-.1E1", "-.1E2", "-.2E2", "-.2E2", "-.2E3", "-.2E3", "-.3E3", "-.3E3", "-.1E5", ".1E0", ".1E0", ".2E0", ".2E0", ".5E0", ".5E0", ".6E0", ".6E0", ".9E0", ".9E0", ".1E1", ".1E1", "-.1E0", "-.1E0", "-.2E0", "-.2E0", "-.5E0", "-.5E0", "-.6E0", "-.6E0", "-.9E0", "-.9E0", "-.1E1", "-.1E1", ".3E1", ".5E1", ".4E1", ".5E1", ".4E1", "-.3E1", "-.5E1", "-.4E1", "-.5E1", "-.4E1"]);
		check_numeric_format(".0E+?", numbers,
				[".0E+0", ".1E+1", ".1E+2", ".2E+2", ".2E+2", ".2E+3", ".2E+3", ".3E+3", ".3E+3", ".1E+4", "-.1E+1", "-.1E+2", "-.2E+2", "-.2E+2", "-.2E+3", "-.2E+3", "-.3E+3", "-.3E+3", "-.1E+5", ".1E+0", ".1E+0", ".2E+0", ".2E+0", ".5E+0", ".5E+0", ".6E+0", ".6E+0", ".9E+0", ".9E+0", ".1E+1", ".1E+1", "-.1E+0", "-.1E+0", "-.2E+0", "-.2E+0", "-.5E+0", "-.5E+0", "-.6E+0", "-.6E+0", "-.9E+0", "-.9E+0", "-.1E+1", "-.1E+1", ".3E+1", ".5E+1", ".4E+1", ".5E+1", ".4E+1", "-.3E+1", "-.5E+1", "-.4E+1", "-.5E+1", "-.4E+1"]);
		check_numeric_format(".?E+0", numbers,
				[". E+0", ".1E+1", ".1E+2", ".2E+2", ".2E+2", ".2E+3", ".2E+3", ".3E+3", ".3E+3", ".1E+4", "-.1E+1", "-.1E+2", "-.2E+2", "-.2E+2", "-.2E+3", "-.2E+3", "-.3E+3", "-.3E+3", "-.1E+5", ".1E+0", ".1E+0", ".2E+0", ".2E+0", ".5E+0", ".5E+0", ".6E+0", ".6E+0", ".9E+0", ".9E+0", ".1E+1", ".1E+1", "-.1E+0", "-.1E+0", "-.2E+0", "-.2E+0", "-.5E+0", "-.5E+0", "-.6E+0", "-.6E+0", "-.9E+0", "-.9E+0", "-.1E+1", "-.1E+1", ".3E+1", ".5E+1", ".4E+1", ".5E+1", ".4E+1", "-.3E+1", "-.5E+1", "-.4E+1", "-.5E+1", "-.4E+1"]);
		check_numeric_format("0.0E-?", numbers,
				["0.0E0", "1.0E0", "1.1E1", "1.5E1", "1.9E1", "2.0E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1.0E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2.0E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1.0E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.0E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.0E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.0E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.0E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.0E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format("0.?E-0", numbers,
				["0. E0", "1. E0", "1.1E1", "1.5E1", "1.9E1", "2. E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1. E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2. E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1. E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5. E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9. E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1. E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5. E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9. E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format("?.0E-0", numbers,
				["0.0E0", "1.0E0", "1.1E1", "1.5E1", "1.9E1", "2.0E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1.0E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2.0E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1.0E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.0E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.0E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.0E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.0E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.0E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format("0.0E+?", numbers,
				["0.0E+0", "1.0E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2.0E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1.0E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2.0E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1.0E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.0E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.0E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.0E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.0E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.0E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
		check_numeric_format("0.?E+0", numbers,
				["0. E+0", "1. E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2. E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1. E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2. E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1. E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5. E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9. E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1. E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5. E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9. E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
		check_numeric_format("?.0E+0", numbers,
				["0.0E+0", "1.0E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2.0E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1.0E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2.0E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1.0E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.0E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.0E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.0E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.0E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.0E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
		check_numeric_format("#", numbers,
				["", "1", "11", "15", "19", "200", "220", "250", "280", "1234", "-1", "-11", "-15", "-19", "-200", "-220", "-250", "-280", "-9876", "", "", "", "", "1", "1", "1", "1", "1", "1", "1", "1", "", "", "", "", "-1", "-1", "-1", "-1", "-1", "-1", "-1", "-1", "3", "5", "4", "5", "4", "-3", "-5", "-4", "-5", "-4"]);
		check_numeric_format("#.", numbers,
				[".", "1.", "11.", "15.", "19.", "200.", "220.", "250.", "280.", "1234.", "-1.", "-11.", "-15.", "-19.", "-200.", "-220.", "-250.", "-280.", "-9876.", ".", ".", ".", ".", "1.", "1.", "1.", "1.", "1.", "1.", "1.", "1.", ".", ".", ".", ".", "-1.", "-1.", "-1.", "-1.", "-1.", "-1.", "-1.", "-1.", "3.", "5.", "4.", "5.", "4.", "-3.", "-5.", "-4.", "-5.", "-4."]);
		check_numeric_format(".#", numbers,
				[".", "1.", "11.", "15.", "19.", "200.", "220.", "250.", "280.", "1234.", "-1.", "-11.", "-15.", "-19.", "-200.", "-220.", "-250.", "-280.", "-9876.", ".1", ".1", ".2", ".2", ".5", ".5", ".6", ".6", ".9", ".9", "1.", "1.", "-.1", "-.1", "-.2", "-.2", "-.5", "-.5", "-.6", "-.6", "-.9", "-.9", "-1.", "-1.", "3.4", "4.6", "4.3", "4.6", "4.3", "-3.4", "-4.6", "-4.3", "-4.6", "-4.3"]);
		check_numeric_format("#.#", numbers,
				[".", "1.", "11.", "15.", "19.", "200.", "220.", "250.", "280.", "1234.", "-1.", "-11.", "-15.", "-19.", "-200.", "-220.", "-250.", "-280.", "-9876.", ".1", ".1", ".2", ".2", ".5", ".5", ".6", ".6", ".9", ".9", "1.", "1.", "-.1", "-.1", "-.2", "-.2", "-.5", "-.5", "-.6", "-.6", "-.9", "-.9", "-1.", "-1.", "3.4", "4.6", "4.3", "4.6", "4.3", "-3.4", "-4.6", "-4.3", "-4.6", "-4.3"]);
		check_numeric_format("#E-#", numbers,
				["0E0", "1E0", "1E1", "2E1", "2E1", "2E2", "2E2", "3E2", "3E2", "1E3", "-1E0", "-1E1", "-2E1", "-2E1", "-2E2", "-2E2", "-3E2", "-3E2", "-1E4", "1E-1", "1E-1", "2E-1", "2E-1", "5E-1", "5E-1", "6E-1", "6E-1", "9E-1", "9E-1", "1E0", "1E0", "-1E-1", "-1E-1", "-2E-1", "-2E-1", "-5E-1", "-5E-1", "-6E-1", "-6E-1", "-9E-1", "-9E-1", "-1E0", "-1E0", "3E0", "5E0", "4E0", "5E0", "4E0", "-3E0", "-5E0", "-4E0", "-5E0", "-4E0"]);
		check_numeric_format("#E+#", numbers,
				["0E+0", "1E+0", "1E+1", "2E+1", "2E+1", "2E+2", "2E+2", "3E+2", "3E+2", "1E+3", "-1E+0", "-1E+1", "-2E+1", "-2E+1", "-2E+2", "-2E+2", "-3E+2", "-3E+2", "-1E+4", "1E-1", "1E-1", "2E-1", "2E-1", "5E-1", "5E-1", "6E-1", "6E-1", "9E-1", "9E-1", "1E+0", "1E+0", "-1E-1", "-1E-1", "-2E-1", "-2E-1", "-5E-1", "-5E-1", "-6E-1", "-6E-1", "-9E-1", "-9E-1", "-1E+0", "-1E+0", "3E+0", "5E+0", "4E+0", "5E+0", "4E+0", "-3E+0", "-5E+0", "-4E+0", "-5E+0", "-4E+0"]);
		check_numeric_format(".#E-#", numbers,
				[".E0", ".1E1", ".1E2", ".2E2", ".2E2", ".2E3", ".2E3", ".3E3", ".3E3", ".1E4", "-.1E1", "-.1E2", "-.2E2", "-.2E2", "-.2E3", "-.2E3", "-.3E3", "-.3E3", "-.1E5", ".1E0", ".1E0", ".2E0", ".2E0", ".5E0", ".5E0", ".6E0", ".6E0", ".9E0", ".9E0", ".1E1", ".1E1", "-.1E0", "-.1E0", "-.2E0", "-.2E0", "-.5E0", "-.5E0", "-.6E0", "-.6E0", "-.9E0", "-.9E0", "-.1E1", "-.1E1", ".3E1", ".5E1", ".4E1", ".5E1", ".4E1", "-.3E1", "-.5E1", "-.4E1", "-.5E1", "-.4E1"]);
		check_numeric_format(".#E+#", numbers,
				[".E+0", ".1E+1", ".1E+2", ".2E+2", ".2E+2", ".2E+3", ".2E+3", ".3E+3", ".3E+3", ".1E+4", "-.1E+1", "-.1E+2", "-.2E+2", "-.2E+2", "-.2E+3", "-.2E+3", "-.3E+3", "-.3E+3", "-.1E+5", ".1E+0", ".1E+0", ".2E+0", ".2E+0", ".5E+0", ".5E+0", ".6E+0", ".6E+0", ".9E+0", ".9E+0", ".1E+1", ".1E+1", "-.1E+0", "-.1E+0", "-.2E+0", "-.2E+0", "-.5E+0", "-.5E+0", "-.6E+0", "-.6E+0", "-.9E+0", "-.9E+0", "-.1E+1", "-.1E+1", ".3E+1", ".5E+1", ".4E+1", ".5E+1", ".4E+1", "-.3E+1", "-.5E+1", "-.4E+1", "-.5E+1", "-.4E+1"]);
		check_numeric_format("#.#E-#", numbers,
				["0.E0", "1.E0", "1.1E1", "1.5E1", "1.9E1", "2.E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1.E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2.E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1.E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format("#.#E+#", numbers,
				["0.E+0", "1.E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2.E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1.E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2.E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1.E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
		check_numeric_format("#.#E-0", numbers,
				["0.E0", "1.E0", "1.1E1", "1.5E1", "1.9E1", "2.E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1.E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2.E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1.E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format("#.0E-#", numbers,
				["0.0E0", "1.0E0", "1.1E1", "1.5E1", "1.9E1", "2.0E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1.0E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2.0E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1.0E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.0E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.0E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.0E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.0E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.0E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format("0.#E-#", numbers,
				["0.E0", "1.E0", "1.1E1", "1.5E1", "1.9E1", "2.E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1.E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2.E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1.E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format("#.#E+0", numbers,
				["0.E+0", "1.E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2.E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1.E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2.E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1.E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
		check_numeric_format("#.0E+#", numbers,
				["0.0E+0", "1.0E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2.0E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1.0E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2.0E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1.0E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.0E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.0E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.0E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.0E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.0E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
		check_numeric_format("0.#E+#", numbers,
				["0.E+0", "1.E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2.E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1.E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2.E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1.E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
		check_numeric_format("#.?", numbers,
				[". ", "1. ", "11. ", "15. ", "19. ", "200. ", "220. ", "250. ", "280. ", "1234. ", "-1. ", "-11. ", "-15. ", "-19. ", "-200. ", "-220. ", "-250. ", "-280. ", "-9876. ", ".1", ".1", ".2", ".2", ".5", ".5", ".6", ".6", ".9", ".9", "1. ", "1. ", "-.1", "-.1", "-.2", "-.2", "-.5", "-.5", "-.6", "-.6", "-.9", "-.9", "-1. ", "-1. ", "3.4", "4.6", "4.3", "4.6", "4.3", "-3.4", "-4.6", "-4.3", "-4.6", "-4.3"]);
		check_numeric_format("?.#", numbers,
				[" .", "1.", "11.", "15.", "19.", "200.", "220.", "250.", "280.", "1234.", "-1.", "-11.", "-15.", "-19.", "-200.", "-220.", "-250.", "-280.", "-9876.", " .1", " .1", " .2", " .2", " .5", " .5", " .6", " .6", " .9", " .9", "1.", "1.", "- .1", "- .1", "- .2", "- .2", "- .5", "- .5", "- .6", "- .6", "- .9", "- .9", "-1.", "-1.", "3.4", "4.6", "4.3", "4.6", "4.3", "-3.4", "-4.6", "-4.3", "-4.6", "-4.3"]);
		check_numeric_format("#E-?", numbers,
				["0E0", "1E0", "1E1", "2E1", "2E1", "2E2", "2E2", "3E2", "3E2", "1E3", "-1E0", "-1E1", "-2E1", "-2E1", "-2E2", "-2E2", "-3E2", "-3E2", "-1E4", "1E-1", "1E-1", "2E-1", "2E-1", "5E-1", "5E-1", "6E-1", "6E-1", "9E-1", "9E-1", "1E0", "1E0", "-1E-1", "-1E-1", "-2E-1", "-2E-1", "-5E-1", "-5E-1", "-6E-1", "-6E-1", "-9E-1", "-9E-1", "-1E0", "-1E0", "3E0", "5E0", "4E0", "5E0", "4E0", "-3E0", "-5E0", "-4E0", "-5E0", "-4E0"]);
		check_numeric_format("?E-#", numbers,
				["0E0", "1E0", "1E1", "2E1", "2E1", "2E2", "2E2", "3E2", "3E2", "1E3", "-1E0", "-1E1", "-2E1", "-2E1", "-2E2", "-2E2", "-3E2", "-3E2", "-1E4", "1E-1", "1E-1", "2E-1", "2E-1", "5E-1", "5E-1", "6E-1", "6E-1", "9E-1", "9E-1", "1E0", "1E0", "-1E-1", "-1E-1", "-2E-1", "-2E-1", "-5E-1", "-5E-1", "-6E-1", "-6E-1", "-9E-1", "-9E-1", "-1E0", "-1E0", "3E0", "5E0", "4E0", "5E0", "4E0", "-3E0", "-5E0", "-4E0", "-5E0", "-4E0"]);
		check_numeric_format("#E+?", numbers,
				["0E+0", "1E+0", "1E+1", "2E+1", "2E+1", "2E+2", "2E+2", "3E+2", "3E+2", "1E+3", "-1E+0", "-1E+1", "-2E+1", "-2E+1", "-2E+2", "-2E+2", "-3E+2", "-3E+2", "-1E+4", "1E-1", "1E-1", "2E-1", "2E-1", "5E-1", "5E-1", "6E-1", "6E-1", "9E-1", "9E-1", "1E+0", "1E+0", "-1E-1", "-1E-1", "-2E-1", "-2E-1", "-5E-1", "-5E-1", "-6E-1", "-6E-1", "-9E-1", "-9E-1", "-1E+0", "-1E+0", "3E+0", "5E+0", "4E+0", "5E+0", "4E+0", "-3E+0", "-5E+0", "-4E+0", "-5E+0", "-4E+0"]);
		check_numeric_format("?E+#", numbers,
				["0E+0", "1E+0", "1E+1", "2E+1", "2E+1", "2E+2", "2E+2", "3E+2", "3E+2", "1E+3", "-1E+0", "-1E+1", "-2E+1", "-2E+1", "-2E+2", "-2E+2", "-3E+2", "-3E+2", "-1E+4", "1E-1", "1E-1", "2E-1", "2E-1", "5E-1", "5E-1", "6E-1", "6E-1", "9E-1", "9E-1", "1E+0", "1E+0", "-1E-1", "-1E-1", "-2E-1", "-2E-1", "-5E-1", "-5E-1", "-6E-1", "-6E-1", "-9E-1", "-9E-1", "-1E+0", "-1E+0", "3E+0", "5E+0", "4E+0", "5E+0", "4E+0", "-3E+0", "-5E+0", "-4E+0", "-5E+0", "-4E+0"]);
		check_numeric_format(".#E-?", numbers,
				[".E0", ".1E1", ".1E2", ".2E2", ".2E2", ".2E3", ".2E3", ".3E3", ".3E3", ".1E4", "-.1E1", "-.1E2", "-.2E2", "-.2E2", "-.2E3", "-.2E3", "-.3E3", "-.3E3", "-.1E5", ".1E0", ".1E0", ".2E0", ".2E0", ".5E0", ".5E0", ".6E0", ".6E0", ".9E0", ".9E0", ".1E1", ".1E1", "-.1E0", "-.1E0", "-.2E0", "-.2E0", "-.5E0", "-.5E0", "-.6E0", "-.6E0", "-.9E0", "-.9E0", "-.1E1", "-.1E1", ".3E1", ".5E1", ".4E1", ".5E1", ".4E1", "-.3E1", "-.5E1", "-.4E1", "-.5E1", "-.4E1"]);
		check_numeric_format(".?E-#", numbers,
				[". E0", ".1E1", ".1E2", ".2E2", ".2E2", ".2E3", ".2E3", ".3E3", ".3E3", ".1E4", "-.1E1", "-.1E2", "-.2E2", "-.2E2", "-.2E3", "-.2E3", "-.3E3", "-.3E3", "-.1E5", ".1E0", ".1E0", ".2E0", ".2E0", ".5E0", ".5E0", ".6E0", ".6E0", ".9E0", ".9E0", ".1E1", ".1E1", "-.1E0", "-.1E0", "-.2E0", "-.2E0", "-.5E0", "-.5E0", "-.6E0", "-.6E0", "-.9E0", "-.9E0", "-.1E1", "-.1E1", ".3E1", ".5E1", ".4E1", ".5E1", ".4E1", "-.3E1", "-.5E1", "-.4E1", "-.5E1", "-.4E1"]);
		check_numeric_format(".#E+?", numbers,
				[".E+0", ".1E+1", ".1E+2", ".2E+2", ".2E+2", ".2E+3", ".2E+3", ".3E+3", ".3E+3", ".1E+4", "-.1E+1", "-.1E+2", "-.2E+2", "-.2E+2", "-.2E+3", "-.2E+3", "-.3E+3", "-.3E+3", "-.1E+5", ".1E+0", ".1E+0", ".2E+0", ".2E+0", ".5E+0", ".5E+0", ".6E+0", ".6E+0", ".9E+0", ".9E+0", ".1E+1", ".1E+1", "-.1E+0", "-.1E+0", "-.2E+0", "-.2E+0", "-.5E+0", "-.5E+0", "-.6E+0", "-.6E+0", "-.9E+0", "-.9E+0", "-.1E+1", "-.1E+1", ".3E+1", ".5E+1", ".4E+1", ".5E+1", ".4E+1", "-.3E+1", "-.5E+1", "-.4E+1", "-.5E+1", "-.4E+1"]);
		check_numeric_format(".?E+#", numbers,
				[". E+0", ".1E+1", ".1E+2", ".2E+2", ".2E+2", ".2E+3", ".2E+3", ".3E+3", ".3E+3", ".1E+4", "-.1E+1", "-.1E+2", "-.2E+2", "-.2E+2", "-.2E+3", "-.2E+3", "-.3E+3", "-.3E+3", "-.1E+5", ".1E+0", ".1E+0", ".2E+0", ".2E+0", ".5E+0", ".5E+0", ".6E+0", ".6E+0", ".9E+0", ".9E+0", ".1E+1", ".1E+1", "-.1E+0", "-.1E+0", "-.2E+0", "-.2E+0", "-.5E+0", "-.5E+0", "-.6E+0", "-.6E+0", "-.9E+0", "-.9E+0", "-.1E+1", "-.1E+1", ".3E+1", ".5E+1", ".4E+1", ".5E+1", ".4E+1", "-.3E+1", "-.5E+1", "-.4E+1", "-.5E+1", "-.4E+1"]);
		check_numeric_format("#.#E-?", numbers,
				["0.E0", "1.E0", "1.1E1", "1.5E1", "1.9E1", "2.E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1.E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2.E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1.E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format("#.?E-#", numbers,
				["0. E0", "1. E0", "1.1E1", "1.5E1", "1.9E1", "2. E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1. E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2. E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1. E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5. E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9. E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1. E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5. E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9. E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format("?.#E-#", numbers,
				["0.E0", "1.E0", "1.1E1", "1.5E1", "1.9E1", "2.E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1.E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2.E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1.E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format("#.#E+?", numbers,
				["0.E+0", "1.E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2.E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1.E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2.E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1.E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
		check_numeric_format("#.?E+#", numbers,
				["0. E+0", "1. E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2. E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1. E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2. E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1. E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5. E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9. E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1. E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5. E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9. E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
		check_numeric_format("?.#E+#", numbers,
				["0.E+0", "1.E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2.E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1.E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2.E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1.E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
		check_numeric_format("?", numbers,
				[" ", "1", "11", "15", "19", "200", "220", "250", "280", "1234", "-1", "-11", "-15", "-19", "-200", "-220", "-250", "-280", "-9876", " ", " ", " ", " ", "1", "1", "1", "1", "1", "1", "1", "1", " ", " ", " ", " ", "-1", "-1", "-1", "-1", "-1", "-1", "-1", "-1", "3", "5", "4", "5", "4", "-3", "-5", "-4", "-5", "-4"]);
		check_numeric_format("?.", numbers,
				[" .", "1.", "11.", "15.", "19.", "200.", "220.", "250.", "280.", "1234.", "-1.", "-11.", "-15.", "-19.", "-200.", "-220.", "-250.", "-280.", "-9876.", " .", " .", " .", " .", "1.", "1.", "1.", "1.", "1.", "1.", "1.", "1.", " .", " .", " .", " .", "-1.", "-1.", "-1.", "-1.", "-1.", "-1.", "-1.", "-1.", "3.", "5.", "4.", "5.", "4.", "-3.", "-5.", "-4.", "-5.", "-4."]);
		check_numeric_format(".?", numbers,
				[". ", "1. ", "11. ", "15. ", "19. ", "200. ", "220. ", "250. ", "280. ", "1234. ", "-1. ", "-11. ", "-15. ", "-19. ", "-200. ", "-220. ", "-250. ", "-280. ", "-9876. ", ".1", ".1", ".2", ".2", ".5", ".5", ".6", ".6", ".9", ".9", "1. ", "1. ", "-.1", "-.1", "-.2", "-.2", "-.5", "-.5", "-.6", "-.6", "-.9", "-.9", "-1. ", "-1. ", "3.4", "4.6", "4.3", "4.6", "4.3", "-3.4", "-4.6", "-4.3", "-4.6", "-4.3"]);
		check_numeric_format("?.?", numbers,
				[" . ", "1. ", "11. ", "15. ", "19. ", "200. ", "220. ", "250. ", "280. ", "1234. ", "-1. ", "-11. ", "-15. ", "-19. ", "-200. ", "-220. ", "-250. ", "-280. ", "-9876. ", " .1", " .1", " .2", " .2", " .5", " .5", " .6", " .6", " .9", " .9", "1. ", "1. ", "- .1", "- .1", "- .2", "- .2", "- .5", "- .5", "- .6", "- .6", "- .9", "- .9", "-1. ", "-1. ", "3.4", "4.6", "4.3", "4.6", "4.3", "-3.4", "-4.6", "-4.3", "-4.6", "-4.3"]);
		check_numeric_format("?E-?", numbers,
				["0E0", "1E0", "1E1", "2E1", "2E1", "2E2", "2E2", "3E2", "3E2", "1E3", "-1E0", "-1E1", "-2E1", "-2E1", "-2E2", "-2E2", "-3E2", "-3E2", "-1E4", "1E-1", "1E-1", "2E-1", "2E-1", "5E-1", "5E-1", "6E-1", "6E-1", "9E-1", "9E-1", "1E0", "1E0", "-1E-1", "-1E-1", "-2E-1", "-2E-1", "-5E-1", "-5E-1", "-6E-1", "-6E-1", "-9E-1", "-9E-1", "-1E0", "-1E0", "3E0", "5E0", "4E0", "5E0", "4E0", "-3E0", "-5E0", "-4E0", "-5E0", "-4E0"]);
		check_numeric_format("?E+?", numbers,
				["0E+0", "1E+0", "1E+1", "2E+1", "2E+1", "2E+2", "2E+2", "3E+2", "3E+2", "1E+3", "-1E+0", "-1E+1", "-2E+1", "-2E+1", "-2E+2", "-2E+2", "-3E+2", "-3E+2", "-1E+4", "1E-1", "1E-1", "2E-1", "2E-1", "5E-1", "5E-1", "6E-1", "6E-1", "9E-1", "9E-1", "1E+0", "1E+0", "-1E-1", "-1E-1", "-2E-1", "-2E-1", "-5E-1", "-5E-1", "-6E-1", "-6E-1", "-9E-1", "-9E-1", "-1E+0", "-1E+0", "3E+0", "5E+0", "4E+0", "5E+0", "4E+0", "-3E+0", "-5E+0", "-4E+0", "-5E+0", "-4E+0"]);
		check_numeric_format(".?E-?", numbers,
				[". E0", ".1E1", ".1E2", ".2E2", ".2E2", ".2E3", ".2E3", ".3E3", ".3E3", ".1E4", "-.1E1", "-.1E2", "-.2E2", "-.2E2", "-.2E3", "-.2E3", "-.3E3", "-.3E3", "-.1E5", ".1E0", ".1E0", ".2E0", ".2E0", ".5E0", ".5E0", ".6E0", ".6E0", ".9E0", ".9E0", ".1E1", ".1E1", "-.1E0", "-.1E0", "-.2E0", "-.2E0", "-.5E0", "-.5E0", "-.6E0", "-.6E0", "-.9E0", "-.9E0", "-.1E1", "-.1E1", ".3E1", ".5E1", ".4E1", ".5E1", ".4E1", "-.3E1", "-.5E1", "-.4E1", "-.5E1", "-.4E1"]);
		check_numeric_format(".?E+?", numbers,
				[". E+0", ".1E+1", ".1E+2", ".2E+2", ".2E+2", ".2E+3", ".2E+3", ".3E+3", ".3E+3", ".1E+4", "-.1E+1", "-.1E+2", "-.2E+2", "-.2E+2", "-.2E+3", "-.2E+3", "-.3E+3", "-.3E+3", "-.1E+5", ".1E+0", ".1E+0", ".2E+0", ".2E+0", ".5E+0", ".5E+0", ".6E+0", ".6E+0", ".9E+0", ".9E+0", ".1E+1", ".1E+1", "-.1E+0", "-.1E+0", "-.2E+0", "-.2E+0", "-.5E+0", "-.5E+0", "-.6E+0", "-.6E+0", "-.9E+0", "-.9E+0", "-.1E+1", "-.1E+1", ".3E+1", ".5E+1", ".4E+1", ".5E+1", ".4E+1", "-.3E+1", "-.5E+1", "-.4E+1", "-.5E+1", "-.4E+1"]);
		check_numeric_format("?.?E-?", numbers,
				["0. E0", "1. E0", "1.1E1", "1.5E1", "1.9E1", "2. E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1. E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2. E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1. E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5. E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9. E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1. E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5. E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9. E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format("?.?E+?", numbers,
				["0. E+0", "1. E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2. E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1. E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2. E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1. E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5. E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9. E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1. E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5. E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9. E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
		check_numeric_format("?.?E-0", numbers,
				["0. E0", "1. E0", "1.1E1", "1.5E1", "1.9E1", "2. E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1. E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2. E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1. E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5. E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9. E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1. E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5. E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9. E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format("?.0E-?", numbers,
				["0.0E0", "1.0E0", "1.1E1", "1.5E1", "1.9E1", "2.0E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1.0E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2.0E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1.0E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.0E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.0E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.0E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.0E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.0E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format("0.?E-?", numbers,
				["0. E0", "1. E0", "1.1E1", "1.5E1", "1.9E1", "2. E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1. E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2. E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1. E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5. E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9. E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1. E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5. E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9. E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format("?.?E+0", numbers,
				["0. E+0", "1. E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2. E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1. E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2. E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1. E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5. E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9. E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1. E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5. E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9. E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
		check_numeric_format("?.0E+?", numbers,
				["0.0E+0", "1.0E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2.0E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1.0E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2.0E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1.0E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.0E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.0E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.0E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.0E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.0E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
		check_numeric_format("0.?E+?", numbers,
				["0. E+0", "1. E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2. E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1. E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2. E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1. E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5. E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9. E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1. E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5. E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9. E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
		check_numeric_format("?.?E-#", numbers,
				["0. E0", "1. E0", "1.1E1", "1.5E1", "1.9E1", "2. E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1. E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2. E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1. E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5. E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9. E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1. E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5. E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9. E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format("?.#E-?", numbers,
				["0.E0", "1.E0", "1.1E1", "1.5E1", "1.9E1", "2.E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1.E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2.E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1.E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format("#.?E-?", numbers,
				["0. E0", "1. E0", "1.1E1", "1.5E1", "1.9E1", "2. E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1. E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2. E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1. E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5. E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9. E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1. E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5. E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9. E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format("?.?E+#", numbers,
				["0. E+0", "1. E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2. E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1. E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2. E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1. E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5. E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9. E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1. E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5. E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9. E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
		check_numeric_format("?.#E+?", numbers,
				["0.E+0", "1.E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2.E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1.E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2.E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1.E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
		check_numeric_format("#.?E+?", numbers,
				["0. E+0", "1. E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2. E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1. E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2. E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1. E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5. E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9. E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1. E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5. E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9. E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
	});

	test("format2", function test_format2() {
		var numbers = [0, 1, 11, 15, 19, 200, 220, 250, 280, 1234, -1, -11, -15, -19, -200, -220, -250, -280, -9876, 0.1, 0.12, 0.151, 0.181, 0.5, 0.53, 0.555, 0.575, 0.9, 0.94, 0.959, 0.969, -0.1, -0.12, -0.151, -0.181, -0.5, -0.53, -0.555, -0.575, -0.9, -0.94, -0.959, -0.969, 3.4, 4.56, 4.32, 4.567, 4.321, -3.4, -4.56, -4.32, -4.567, -4.321];

		check_numeric_format("0,", numbers,
				["0", "0", "0", "0", "0", "0", "0", "0", "0", "1", "0", "0", "0", "0", "0", "0", "0", "0", "-10", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"]);
		check_numeric_format(",0", numbers,
				[",0", ",1", ",11", ",15", ",19", ",200", ",220", ",250", ",280", ",1234", "-,1", "-,11", "-,15", "-,19", "-,200", "-,220", "-,250", "-,280", "-,9876", ",0", ",0", ",0", ",0", ",1", ",1", ",1", ",1", ",1", ",1", ",1", ",1", ",0", ",0", ",0", ",0", "-,1", "-,1", "-,1", "-,1", "-,1", "-,1", "-,1", "-,1", ",3", ",5", ",4", ",5", ",4", "-,3", "-,5", "-,4", "-,5", "-,4"]);
		check_numeric_format(",0,", numbers,
				[",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",1", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", "-,10", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0", ",0"]);
		check_numeric_format("0.", numbers,
				["0.", "1.", "11.", "15.", "19.", "200.", "220.", "250.", "280.", "1234.", "-1.", "-11.", "-15.", "-19.", "-200.", "-220.", "-250.", "-280.", "-9876.", "0.", "0.", "0.", "0.", "1.", "1.", "1.", "1.", "1.", "1.", "1.", "1.", "0.", "0.", "0.", "0.", "-1.", "-1.", "-1.", "-1.", "-1.", "-1.", "-1.", "-1.", "3.", "5.", "4.", "5.", "4.", "-3.", "-5.", "-4.", "-5.", "-4."]);
		check_numeric_format(".0", numbers,
				[".0", "1.0", "11.0", "15.0", "19.0", "200.0", "220.0", "250.0", "280.0", "1234.0", "-1.0", "-11.0", "-15.0", "-19.0", "-200.0", "-220.0", "-250.0", "-280.0", "-9876.0", ".1", ".1", ".2", ".2", ".5", ".5", ".6", ".6", ".9", ".9", "1.0", "1.0", "-.1", "-.1", "-.2", "-.2", "-.5", "-.5", "-.6", "-.6", "-.9", "-.9", "-1.0", "-1.0", "3.4", "4.6", "4.3", "4.6", "4.3", "-3.4", "-4.6", "-4.3", "-4.6", "-4.3"]);
		check_numeric_format(".0.", numbers,
				[".0.", "1.0.", "11.0.", "15.0.", "19.0.", "200.0.", "220.0.", "250.0.", "280.0.", "1234.0.", "-1.0.", "-11.0.", "-15.0.", "-19.0.", "-200.0.", "-220.0.", "-250.0.", "-280.0.", "-9876.0.", ".1.", ".1.", ".2.", ".2.", ".5.", ".5.", ".6.", ".6.", ".9.", ".9.", "1.0.", "1.0.", "-.1.", "-.1.", "-.2.", "-.2.", "-.5.", "-.5.", "-.6.", "-.6.", "-.9.", "-.9.", "-1.0.", "-1.0.", "3.4.", "4.6.", "4.3.", "4.6.", "4.3.", "-3.4.", "-4.6.", "-4.3.", "-4.6.", "-4.3."]);
		check_numeric_format("0!", numbers,
				["0!", "1!", "11!", "15!", "19!", "200!", "220!", "250!", "280!", "1234!", "-1!", "-11!", "-15!", "-19!", "-200!", "-220!", "-250!", "-280!", "-9876!", "0!", "0!", "0!", "0!", "1!", "1!", "1!", "1!", "1!", "1!", "1!", "1!", "0!", "0!", "0!", "0!", "-1!", "-1!", "-1!", "-1!", "-1!", "-1!", "-1!", "-1!", "3!", "5!", "4!", "5!", "4!", "-3!", "-5!", "-4!", "-5!", "-4!"]);
		check_numeric_format("!0", numbers,
				["!0", "!1", "!11", "!15", "!19", "!200", "!220", "!250", "!280", "!1234", "-!1", "-!11", "-!15", "-!19", "-!200", "-!220", "-!250", "-!280", "-!9876", "!0", "!0", "!0", "!0", "!1", "!1", "!1", "!1", "!1", "!1", "!1", "!1", "!0", "!0", "!0", "!0", "-!1", "-!1", "-!1", "-!1", "-!1", "-!1", "-!1", "-!1", "!3", "!5", "!4", "!5", "!4", "-!3", "-!5", "-!4", "-!5", "-!4"]);
		check_numeric_format("!0!", numbers,
				["!0!", "!1!", "!11!", "!15!", "!19!", "!200!", "!220!", "!250!", "!280!", "!1234!", "-!1!", "-!11!", "-!15!", "-!19!", "-!200!", "-!220!", "-!250!", "-!280!", "-!9876!", "!0!", "!0!", "!0!", "!0!", "!1!", "!1!", "!1!", "!1!", "!1!", "!1!", "!1!", "!1!", "!0!", "!0!", "!0!", "!0!", "-!1!", "-!1!", "-!1!", "-!1!", "-!1!", "-!1!", "-!1!", "-!1!", "!3!", "!5!", "!4!", "!5!", "!4!", "-!3!", "-!5!", "-!4!", "-!5!", "-!4!"]);
		check_numeric_format("#,#00.", numbers,
				["00.", "01.", "11.", "15.", "19.", "200.", "220.", "250.", "280.", "1,234.", "-01.", "-11.", "-15.", "-19.", "-200.", "-220.", "-250.", "-280.", "-9,876.", "00.", "00.", "00.", "00.", "01.", "01.", "01.", "01.", "01.", "01.", "01.", "01.", "00.", "00.", "00.", "00.", "-01.", "-01.", "-01.", "-01.", "-01.", "-01.", "-01.", "-01.", "03.", "05.", "04.", "05.", "04.", "-03.", "-05.", "-04.", "-05.", "-04."]);
		check_numeric_format(",00.", numbers,
				[",00.", ",01.", ",11.", ",15.", ",19.", ",200.", ",220.", ",250.", ",280.", ",1234.", "-,01.", "-,11.", "-,15.", "-,19.", "-,200.", "-,220.", "-,250.", "-,280.", "-,9876.", ",00.", ",00.", ",00.", ",00.", ",01.", ",01.", ",01.", ",01.", ",01.", ",01.", ",01.", ",01.", ",00.", ",00.", ",00.", ",00.", "-,01.", "-,01.", "-,01.", "-,01.", "-,01.", "-,01.", "-,01.", "-,01.", ",03.", ",05.", ",04.", ",05.", ",04.", "-,03.", "-,05.", "-,04.", "-,05.", "-,04."]);
		check_numeric_format(",#,#00.", numbers,
				[",00.", ",01.", ",11.", ",15.", ",19.", ",200.", ",220.", ",250.", ",280.", ",1,234.", "-,01.", "-,11.", "-,15.", "-,19.", "-,200.", "-,220.", "-,250.", "-,280.", "-,9,876.", ",00.", ",00.", ",00.", ",00.", ",01.", ",01.", ",01.", ",01.", ",01.", ",01.", ",01.", ",01.", ",00.", ",00.", ",00.", ",00.", "-,01.", "-,01.", "-,01.", "-,01.", "-,01.", "-,01.", "-,01.", "-,01.", ",03.", ",05.", ",04.", ",05.", ",04.", "-,03.", "-,05.", "-,04.", "-,05.", "-,04."]);
		check_numeric_format("0.0.", numbers,
				["0.0.", "1.0.", "11.0.", "15.0.", "19.0.", "200.0.", "220.0.", "250.0.", "280.0.", "1234.0.", "-1.0.", "-11.0.", "-15.0.", "-19.0.", "-200.0.", "-220.0.", "-250.0.", "-280.0.", "-9876.0.", "0.1.", "0.1.", "0.2.", "0.2.", "0.5.", "0.5.", "0.6.", "0.6.", "0.9.", "0.9.", "1.0.", "1.0.", "-0.1.", "-0.1.", "-0.2.", "-0.2.", "-0.5.", "-0.5.", "-0.6.", "-0.6.", "-0.9.", "-0.9.", "-1.0.", "-1.0.", "3.4.", "4.6.", "4.3.", "4.6.", "4.3.", "-3.4.", "-4.6.", "-4.3.", "-4.6.", "-4.3."]);
		check_numeric_format(".00.", numbers,
				[".00.", "1.00.", "11.00.", "15.00.", "19.00.", "200.00.", "220.00.", "250.00.", "280.00.", "1234.00.", "-1.00.", "-11.00.", "-15.00.", "-19.00.", "-200.00.", "-220.00.", "-250.00.", "-280.00.", "-9876.00.", ".10.", ".12.", ".15.", ".18.", ".50.", ".53.", ".56.", ".58.", ".90.", ".94.", ".96.", ".97.", "-.10.", "-.12.", "-.15.", "-.18.", "-.50.", "-.53.", "-.56.", "-.58.", "-.90.", "-.94.", "-.96.", "-.97.", "3.40.", "4.56.", "4.32.", "4.57.", "4.32.", "-3.40.", "-4.56.", "-4.32.", "-4.57.", "-4.32."]);
		check_numeric_format(".0.0.", numbers,
				[".0.0.", "1.0.0.", "11.0.0.", "15.0.0.", "19.0.0.", "200.0.0.", "220.0.0.", "250.0.0.", "280.0.0.", "1234.0.0.", "-1.0.0.", "-11.0.0.", "-15.0.0.", "-19.0.0.", "-200.0.0.", "-220.0.0.", "-250.0.0.", "-280.0.0.", "-9876.0.0.", ".1.0.", ".1.2.", ".1.5.", ".1.8.", ".5.0.", ".5.3.", ".5.6.", ".5.8.", ".9.0.", ".9.4.", ".9.6.", ".9.7.", "-.1.0.", "-.1.2.", "-.1.5.", "-.1.8.", "-.5.0.", "-.5.3.", "-.5.6.", "-.5.8.", "-.9.0.", "-.9.4.", "-.9.6.", "-.9.7.", "3.4.0.", "4.5.6.", "4.3.2.", "4.5.7.", "4.3.2.", "-3.4.0.", "-4.5.6.", "-4.3.2.", "-4.5.7.", "-4.3.2."]);
		check_numeric_format("0!0.", numbers,
				["0!0.", "0!1.", "1!1.", "1!5.", "1!9.", "20!0.", "22!0.", "25!0.", "28!0.", "123!4.", "-0!1.", "-1!1.", "-1!5.", "-1!9.", "-20!0.", "-22!0.", "-25!0.", "-28!0.", "-987!6.", "0!0.", "0!0.", "0!0.", "0!0.", "0!1.", "0!1.", "0!1.", "0!1.", "0!1.", "0!1.", "0!1.", "0!1.", "0!0.", "0!0.", "0!0.", "0!0.", "-0!1.", "-0!1.", "-0!1.", "-0!1.", "-0!1.", "-0!1.", "-0!1.", "-0!1.", "0!3.", "0!5.", "0!4.", "0!5.", "0!4.", "-0!3.", "-0!5.", "-0!4.", "-0!5.", "-0!4."]);
		check_numeric_format("!00.", numbers,
				["!00.", "!01.", "!11.", "!15.", "!19.", "!200.", "!220.", "!250.", "!280.", "!1234.", "-!01.", "-!11.", "-!15.", "-!19.", "-!200.", "-!220.", "-!250.", "-!280.", "-!9876.", "!00.", "!00.", "!00.", "!00.", "!01.", "!01.", "!01.", "!01.", "!01.", "!01.", "!01.", "!01.", "!00.", "!00.", "!00.", "!00.", "-!01.", "-!01.", "-!01.", "-!01.", "-!01.", "-!01.", "-!01.", "-!01.", "!03.", "!05.", "!04.", "!05.", "!04.", "-!03.", "-!05.", "-!04.", "-!05.", "-!04."]);
		check_numeric_format("!0!0.", numbers,
				["!0!0.", "!0!1.", "!1!1.", "!1!5.", "!1!9.", "!20!0.", "!22!0.", "!25!0.", "!28!0.", "!123!4.", "-!0!1.", "-!1!1.", "-!1!5.", "-!1!9.", "-!20!0.", "-!22!0.", "-!25!0.", "-!28!0.", "-!987!6.", "!0!0.", "!0!0.", "!0!0.", "!0!0.", "!0!1.", "!0!1.", "!0!1.", "!0!1.", "!0!1.", "!0!1.", "!0!1.", "!0!1.", "!0!0.", "!0!0.", "!0!0.", "!0!0.", "-!0!1.", "-!0!1.", "-!0!1.", "-!0!1.", "-!0!1.", "-!0!1.", "-!0!1.", "-!0!1.", "!0!3.", "!0!5.", "!0!4.", "!0!5.", "!0!4.", "-!0!3.", "-!0!5.", "-!0!4.", "-!0!5.", "-!0!4."]);
		check_numeric_format(".0,", numbers,
				[".0", ".0", ".0", ".0", ".0", ".2", ".2", ".3", ".3", "1.2", ".0", ".0", ".0", ".0", "-.2", "-.2", "-.3", "-.3", "-9.9", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0"]);
		check_numeric_format(".0", numbers,
				[".0", "1.0", "11.0", "15.0", "19.0", "200.0", "220.0", "250.0", "280.0", "1234.0", "-1.0", "-11.0", "-15.0", "-19.0", "-200.0", "-220.0", "-250.0", "-280.0", "-9876.0", ".1", ".1", ".2", ".2", ".5", ".5", ".6", ".6", ".9", ".9", "1.0", "1.0", "-.1", "-.1", "-.2", "-.2", "-.5", "-.5", "-.6", "-.6", "-.9", "-.9", "-1.0", "-1.0", "3.4", "4.6", "4.3", "4.6", "4.3", "-3.4", "-4.6", "-4.3", "-4.6", "-4.3"]);
		check_numeric_format(".0,", numbers,
				[".0", ".0", ".0", ".0", ".0", ".2", ".2", ".3", ".3", "1.2", ".0", ".0", ".0", ".0", "-.2", "-.2", "-.3", "-.3", "-9.9", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0", ".0"]);
		check_numeric_format(".0.", numbers,
				[".0.", "1.0.", "11.0.", "15.0.", "19.0.", "200.0.", "220.0.", "250.0.", "280.0.", "1234.0.", "-1.0.", "-11.0.", "-15.0.", "-19.0.", "-200.0.", "-220.0.", "-250.0.", "-280.0.", "-9876.0.", ".1.", ".1.", ".2.", ".2.", ".5.", ".5.", ".6.", ".6.", ".9.", ".9.", "1.0.", "1.0.", "-.1.", "-.1.", "-.2.", "-.2.", "-.5.", "-.5.", "-.6.", "-.6.", "-.9.", "-.9.", "-1.0.", "-1.0.", "3.4.", "4.6.", "4.3.", "4.6.", "4.3.", "-3.4.", "-4.6.", "-4.3.", "-4.6.", "-4.3."]);
		check_numeric_format("..0", numbers,
				["..0", "1..0", "11..0", "15..0", "19..0", "200..0", "220..0", "250..0", "280..0", "1234..0", "-1..0", "-11..0", "-15..0", "-19..0", "-200..0", "-220..0", "-250..0", "-280..0", "-9876..0", "..1", "..1", "..2", "..2", "..5", "..5", "..6", "..6", "..9", "..9", "1..0", "1..0", "-..1", "-..1", "-..2", "-..2", "-..5", "-..5", "-..6", "-..6", "-..9", "-..9", "-1..0", "-1..0", "3..4", "4..6", "4..3", "4..6", "4..3", "-3..4", "-4..6", "-4..3", "-4..6", "-4..3"]);
		check_numeric_format("..0.", numbers,
				["..0.", "1..0.", "11..0.", "15..0.", "19..0.", "200..0.", "220..0.", "250..0.", "280..0.", "1234..0.", "-1..0.", "-11..0.", "-15..0.", "-19..0.", "-200..0.", "-220..0.", "-250..0.", "-280..0.", "-9876..0.", "..1.", "..1.", "..2.", "..2.", "..5.", "..5.", "..6.", "..6.", "..9.", "..9.", "1..0.", "1..0.", "-..1.", "-..1.", "-..2.", "-..2.", "-..5.", "-..5.", "-..6.", "-..6.", "-..9.", "-..9.", "-1..0.", "-1..0.", "3..4.", "4..6.", "4..3.", "4..6.", "4..3.", "-3..4.", "-4..6.", "-4..3.", "-4..6.", "-4..3."]);
		check_numeric_format(".0!", numbers,
				[".0!", "1.0!", "11.0!", "15.0!", "19.0!", "200.0!", "220.0!", "250.0!", "280.0!", "1234.0!", "-1.0!", "-11.0!", "-15.0!", "-19.0!", "-200.0!", "-220.0!", "-250.0!", "-280.0!", "-9876.0!", ".1!", ".1!", ".2!", ".2!", ".5!", ".5!", ".6!", ".6!", ".9!", ".9!", "1.0!", "1.0!", "-.1!", "-.1!", "-.2!", "-.2!", "-.5!", "-.5!", "-.6!", "-.6!", "-.9!", "-.9!", "-1.0!", "-1.0!", "3.4!", "4.6!", "4.3!", "4.6!", "4.3!", "-3.4!", "-4.6!", "-4.3!", "-4.6!", "-4.3!"]);
		check_numeric_format(".!0", numbers,
				[".!0", "1.!0", "11.!0", "15.!0", "19.!0", "200.!0", "220.!0", "250.!0", "280.!0", "1234.!0", "-1.!0", "-11.!0", "-15.!0", "-19.!0", "-200.!0", "-220.!0", "-250.!0", "-280.!0", "-9876.!0", ".!1", ".!1", ".!2", ".!2", ".!5", ".!5", ".!6", ".!6", ".!9", ".!9", "1.!0", "1.!0", "-.!1", "-.!1", "-.!2", "-.!2", "-.!5", "-.!5", "-.!6", "-.!6", "-.!9", "-.!9", "-1.!0", "-1.!0", "3.!4", "4.!6", "4.!3", "4.!6", "4.!3", "-3.!4", "-4.!6", "-4.!3", "-4.!6", "-4.!3"]);
		check_numeric_format(".!0!", numbers,
				[".!0!", "1.!0!", "11.!0!", "15.!0!", "19.!0!", "200.!0!", "220.!0!", "250.!0!", "280.!0!", "1234.!0!", "-1.!0!", "-11.!0!", "-15.!0!", "-19.!0!", "-200.!0!", "-220.!0!", "-250.!0!", "-280.!0!", "-9876.!0!", ".!1!", ".!1!", ".!2!", ".!2!", ".!5!", ".!5!", ".!6!", ".!6!", ".!9!", ".!9!", "1.!0!", "1.!0!", "-.!1!", "-.!1!", "-.!2!", "-.!2!", "-.!5!", "-.!5!", "-.!6!", "-.!6!", "-.!9!", "-.!9!", "-1.!0!", "-1.!0!", "3.!4!", "4.!6!", "4.!3!", "4.!6!", "4.!3!", "-3.!4!", "-4.!6!", "-4.!3!", "-4.!6!", "-4.!3!"]);
		check_numeric_format("#,#00.0", numbers,
				["00.0", "01.0", "11.0", "15.0", "19.0", "200.0", "220.0", "250.0", "280.0", "1,234.0", "-01.0", "-11.0", "-15.0", "-19.0", "-200.0", "-220.0", "-250.0", "-280.0", "-9,876.0", "00.1", "00.1", "00.2", "00.2", "00.5", "00.5", "00.6", "00.6", "00.9", "00.9", "01.0", "01.0", "-00.1", "-00.1", "-00.2", "-00.2", "-00.5", "-00.5", "-00.6", "-00.6", "-00.9", "-00.9", "-01.0", "-01.0", "03.4", "04.6", "04.3", "04.6", "04.3", "-03.4", "-04.6", "-04.3", "-04.6", "-04.3"]);
		check_numeric_format(",00.0", numbers,
				[",00.0", ",01.0", ",11.0", ",15.0", ",19.0", ",200.0", ",220.0", ",250.0", ",280.0", ",1234.0", "-,01.0", "-,11.0", "-,15.0", "-,19.0", "-,200.0", "-,220.0", "-,250.0", "-,280.0", "-,9876.0", ",00.1", ",00.1", ",00.2", ",00.2", ",00.5", ",00.5", ",00.6", ",00.6", ",00.9", ",00.9", ",01.0", ",01.0", "-,00.1", "-,00.1", "-,00.2", "-,00.2", "-,00.5", "-,00.5", "-,00.6", "-,00.6", "-,00.9", "-,00.9", "-,01.0", "-,01.0", ",03.4", ",04.6", ",04.3", ",04.6", ",04.3", "-,03.4", "-,04.6", "-,04.3", "-,04.6", "-,04.3"]);
		check_numeric_format(",#,#00.0", numbers,
				[",00.0", ",01.0", ",11.0", ",15.0", ",19.0", ",200.0", ",220.0", ",250.0", ",280.0", ",1,234.0", "-,01.0", "-,11.0", "-,15.0", "-,19.0", "-,200.0", "-,220.0", "-,250.0", "-,280.0", "-,9,876.0", ",00.1", ",00.1", ",00.2", ",00.2", ",00.5", ",00.5", ",00.6", ",00.6", ",00.9", ",00.9", ",01.0", ",01.0", "-,00.1", "-,00.1", "-,00.2", "-,00.2", "-,00.5", "-,00.5", "-,00.6", "-,00.6", "-,00.9", "-,00.9", "-,01.0", "-,01.0", ",03.4", ",04.6", ",04.3", ",04.6", ",04.3", "-,03.4", "-,04.6", "-,04.3", "-,04.6", "-,04.3"]);
		check_numeric_format("0.0,", numbers,
				["0.0", "0.0", "0.0", "0.0", "0.0", "0.2", "0.2", "0.3", "0.3", "1.2", "0.0", "0.0", "0.0", "0.0", "-0.2", "-0.2", "-0.3", "-0.3", "-9.9", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0"]);
		check_numeric_format("0.0", numbers,
				["0.0", "1.0", "11.0", "15.0", "19.0", "200.0", "220.0", "250.0", "280.0", "1234.0", "-1.0", "-11.0", "-15.0", "-19.0", "-200.0", "-220.0", "-250.0", "-280.0", "-9876.0", "0.1", "0.1", "0.2", "0.2", "0.5", "0.5", "0.6", "0.6", "0.9", "0.9", "1.0", "1.0", "-0.1", "-0.1", "-0.2", "-0.2", "-0.5", "-0.5", "-0.6", "-0.6", "-0.9", "-0.9", "-1.0", "-1.0", "3.4", "4.6", "4.3", "4.6", "4.3", "-3.4", "-4.6", "-4.3", "-4.6", "-4.3"]);
		check_numeric_format("0.0,", numbers,
				["0.0", "0.0", "0.0", "0.0", "0.0", "0.2", "0.2", "0.3", "0.3", "1.2", "0.0", "0.0", "0.0", "0.0", "-0.2", "-0.2", "-0.3", "-0.3", "-9.9", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0"]);
		check_numeric_format("0.0.0", numbers,
				["0.0.0", "1.0.0", "11.0.0", "15.0.0", "19.0.0", "200.0.0", "220.0.0", "250.0.0", "280.0.0", "1234.0.0", "-1.0.0", "-11.0.0", "-15.0.0", "-19.0.0", "-200.0.0", "-220.0.0", "-250.0.0", "-280.0.0", "-9876.0.0", "0.1.0", "0.1.2", "0.1.5", "0.1.8", "0.5.0", "0.5.3", "0.5.6", "0.5.8", "0.9.0", "0.9.4", "0.9.6", "0.9.7", "-0.1.0", "-0.1.2", "-0.1.5", "-0.1.8", "-0.5.0", "-0.5.3", "-0.5.6", "-0.5.8", "-0.9.0", "-0.9.4", "-0.9.6", "-0.9.7", "3.4.0", "4.5.6", "4.3.2", "4.5.7", "4.3.2", "-3.4.0", "-4.5.6", "-4.3.2", "-4.5.7", "-4.3.2"]);
		check_numeric_format(".00.0", numbers,
				[".00.0", "1.00.0", "11.00.0", "15.00.0", "19.00.0", "200.00.0", "220.00.0", "250.00.0", "280.00.0", "1234.00.0", "-1.00.0", "-11.00.0", "-15.00.0", "-19.00.0", "-200.00.0", "-220.00.0", "-250.00.0", "-280.00.0", "-9876.00.0", ".10.0", ".12.0", ".15.1", ".18.1", ".50.0", ".53.0", ".55.5", ".57.5", ".90.0", ".94.0", ".95.9", ".96.9", "-.10.0", "-.12.0", "-.15.1", "-.18.1", "-.50.0", "-.53.0", "-.55.5", "-.57.5", "-.90.0", "-.94.0", "-.95.9", "-.96.9", "3.40.0", "4.56.0", "4.32.0", "4.56.7", "4.32.1", "-3.40.0", "-4.56.0", "-4.32.0", "-4.56.7", "-4.32.1"]);
		check_numeric_format(".0.0.0", numbers,
				[".0.0.0", "1.0.0.0", "11.0.0.0", "15.0.0.0", "19.0.0.0", "200.0.0.0", "220.0.0.0", "250.0.0.0", "280.0.0.0", "1234.0.0.0", "-1.0.0.0", "-11.0.0.0", "-15.0.0.0", "-19.0.0.0", "-200.0.0.0", "-220.0.0.0", "-250.0.0.0", "-280.0.0.0", "-9876.0.0.0", ".1.0.0", ".1.2.0", ".1.5.1", ".1.8.1", ".5.0.0", ".5.3.0", ".5.5.5", ".5.7.5", ".9.0.0", ".9.4.0", ".9.5.9", ".9.6.9", "-.1.0.0", "-.1.2.0", "-.1.5.1", "-.1.8.1", "-.5.0.0", "-.5.3.0", "-.5.5.5", "-.5.7.5", "-.9.0.0", "-.9.4.0", "-.9.5.9", "-.9.6.9", "3.4.0.0", "4.5.6.0", "4.3.2.0", "4.5.6.7", "4.3.2.1", "-3.4.0.0", "-4.5.6.0", "-4.3.2.0", "-4.5.6.7", "-4.3.2.1"]);
		check_numeric_format("0.0.", numbers,
				["0.0.", "1.0.", "11.0.", "15.0.", "19.0.", "200.0.", "220.0.", "250.0.", "280.0.", "1234.0.", "-1.0.", "-11.0.", "-15.0.", "-19.0.", "-200.0.", "-220.0.", "-250.0.", "-280.0.", "-9876.0.", "0.1.", "0.1.", "0.2.", "0.2.", "0.5.", "0.5.", "0.6.", "0.6.", "0.9.", "0.9.", "1.0.", "1.0.", "-0.1.", "-0.1.", "-0.2.", "-0.2.", "-0.5.", "-0.5.", "-0.6.", "-0.6.", "-0.9.", "-0.9.", "-1.0.", "-1.0.", "3.4.", "4.6.", "4.3.", "4.6.", "4.3.", "-3.4.", "-4.6.", "-4.3.", "-4.6.", "-4.3."]);
		check_numeric_format("0..0", numbers,
				["0..0", "1..0", "11..0", "15..0", "19..0", "200..0", "220..0", "250..0", "280..0", "1234..0", "-1..0", "-11..0", "-15..0", "-19..0", "-200..0", "-220..0", "-250..0", "-280..0", "-9876..0", "0..1", "0..1", "0..2", "0..2", "0..5", "0..5", "0..6", "0..6", "0..9", "0..9", "1..0", "1..0", "-0..1", "-0..1", "-0..2", "-0..2", "-0..5", "-0..5", "-0..6", "-0..6", "-0..9", "-0..9", "-1..0", "-1..0", "3..4", "4..6", "4..3", "4..6", "4..3", "-3..4", "-4..6", "-4..3", "-4..6", "-4..3"]);
		check_numeric_format("0..0.", numbers,
				["0..0.", "1..0.", "11..0.", "15..0.", "19..0.", "200..0.", "220..0.", "250..0.", "280..0.", "1234..0.", "-1..0.", "-11..0.", "-15..0.", "-19..0.", "-200..0.", "-220..0.", "-250..0.", "-280..0.", "-9876..0.", "0..1.", "0..1.", "0..2.", "0..2.", "0..5.", "0..5.", "0..6.", "0..6.", "0..9.", "0..9.", "1..0.", "1..0.", "-0..1.", "-0..1.", "-0..2.", "-0..2.", "-0..5.", "-0..5.", "-0..6.", "-0..6.", "-0..9.", "-0..9.", "-1..0.", "-1..0.", "3..4.", "4..6.", "4..3.", "4..6.", "4..3.", "-3..4.", "-4..6.", "-4..3.", "-4..6.", "-4..3."]);
		check_numeric_format("0!0.0", numbers,
				["0!0.0", "0!1.0", "1!1.0", "1!5.0", "1!9.0", "20!0.0", "22!0.0", "25!0.0", "28!0.0", "123!4.0", "-0!1.0", "-1!1.0", "-1!5.0", "-1!9.0", "-20!0.0", "-22!0.0", "-25!0.0", "-28!0.0", "-987!6.0", "0!0.1", "0!0.1", "0!0.2", "0!0.2", "0!0.5", "0!0.5", "0!0.6", "0!0.6", "0!0.9", "0!0.9", "0!1.0", "0!1.0", "-0!0.1", "-0!0.1", "-0!0.2", "-0!0.2", "-0!0.5", "-0!0.5", "-0!0.6", "-0!0.6", "-0!0.9", "-0!0.9", "-0!1.0", "-0!1.0", "0!3.4", "0!4.6", "0!4.3", "0!4.6", "0!4.3", "-0!3.4", "-0!4.6", "-0!4.3", "-0!4.6", "-0!4.3"]);
		check_numeric_format("!00.0", numbers,
				["!00.0", "!01.0", "!11.0", "!15.0", "!19.0", "!200.0", "!220.0", "!250.0", "!280.0", "!1234.0", "-!01.0", "-!11.0", "-!15.0", "-!19.0", "-!200.0", "-!220.0", "-!250.0", "-!280.0", "-!9876.0", "!00.1", "!00.1", "!00.2", "!00.2", "!00.5", "!00.5", "!00.6", "!00.6", "!00.9", "!00.9", "!01.0", "!01.0", "-!00.1", "-!00.1", "-!00.2", "-!00.2", "-!00.5", "-!00.5", "-!00.6", "-!00.6", "-!00.9", "-!00.9", "-!01.0", "-!01.0", "!03.4", "!04.6", "!04.3", "!04.6", "!04.3", "-!03.4", "-!04.6", "-!04.3", "-!04.6", "-!04.3"]);
		check_numeric_format("!0!0.0", numbers,
				["!0!0.0", "!0!1.0", "!1!1.0", "!1!5.0", "!1!9.0", "!20!0.0", "!22!0.0", "!25!0.0", "!28!0.0", "!123!4.0", "-!0!1.0", "-!1!1.0", "-!1!5.0", "-!1!9.0", "-!20!0.0", "-!22!0.0", "-!25!0.0", "-!28!0.0", "-!987!6.0", "!0!0.1", "!0!0.1", "!0!0.2", "!0!0.2", "!0!0.5", "!0!0.5", "!0!0.6", "!0!0.6", "!0!0.9", "!0!0.9", "!0!1.0", "!0!1.0", "-!0!0.1", "-!0!0.1", "-!0!0.2", "-!0!0.2", "-!0!0.5", "-!0!0.5", "-!0!0.6", "-!0!0.6", "-!0!0.9", "-!0!0.9", "-!0!1.0", "-!0!1.0", "!0!3.4", "!0!4.6", "!0!4.3", "!0!4.6", "!0!4.3", "-!0!3.4", "-!0!4.6", "-!0!4.3", "-!0!4.6", "-!0!4.3"]);
		check_numeric_format("0.0!", numbers,
				["0.0!", "1.0!", "11.0!", "15.0!", "19.0!", "200.0!", "220.0!", "250.0!", "280.0!", "1234.0!", "-1.0!", "-11.0!", "-15.0!", "-19.0!", "-200.0!", "-220.0!", "-250.0!", "-280.0!", "-9876.0!", "0.1!", "0.1!", "0.2!", "0.2!", "0.5!", "0.5!", "0.6!", "0.6!", "0.9!", "0.9!", "1.0!", "1.0!", "-0.1!", "-0.1!", "-0.2!", "-0.2!", "-0.5!", "-0.5!", "-0.6!", "-0.6!", "-0.9!", "-0.9!", "-1.0!", "-1.0!", "3.4!", "4.6!", "4.3!", "4.6!", "4.3!", "-3.4!", "-4.6!", "-4.3!", "-4.6!", "-4.3!"]);
		check_numeric_format("0.!0", numbers,
				["0.!0", "1.!0", "11.!0", "15.!0", "19.!0", "200.!0", "220.!0", "250.!0", "280.!0", "1234.!0", "-1.!0", "-11.!0", "-15.!0", "-19.!0", "-200.!0", "-220.!0", "-250.!0", "-280.!0", "-9876.!0", "0.!1", "0.!1", "0.!2", "0.!2", "0.!5", "0.!5", "0.!6", "0.!6", "0.!9", "0.!9", "1.!0", "1.!0", "-0.!1", "-0.!1", "-0.!2", "-0.!2", "-0.!5", "-0.!5", "-0.!6", "-0.!6", "-0.!9", "-0.!9", "-1.!0", "-1.!0", "3.!4", "4.!6", "4.!3", "4.!6", "4.!3", "-3.!4", "-4.!6", "-4.!3", "-4.!6", "-4.!3"]);
		check_numeric_format("0.!0!", numbers,
				["0.!0!", "1.!0!", "11.!0!", "15.!0!", "19.!0!", "200.!0!", "220.!0!", "250.!0!", "280.!0!", "1234.!0!", "-1.!0!", "-11.!0!", "-15.!0!", "-19.!0!", "-200.!0!", "-220.!0!", "-250.!0!", "-280.!0!", "-9876.!0!", "0.!1!", "0.!1!", "0.!2!", "0.!2!", "0.!5!", "0.!5!", "0.!6!", "0.!6!", "0.!9!", "0.!9!", "1.!0!", "1.!0!", "-0.!1!", "-0.!1!", "-0.!2!", "-0.!2!", "-0.!5!", "-0.!5!", "-0.!6!", "-0.!6!", "-0.!9!", "-0.!9!", "-1.!0!", "-1.!0!", "3.!4!", "4.!6!", "4.!3!", "4.!6!", "4.!3!", "-3.!4!", "-4.!6!", "-4.!3!", "-4.!6!", "-4.!3!"]);
		check_numeric_format("#,#00E-0", numbers,
				["0,000E0", "01E0", "11E0", "15E0", "19E0", "200E0", "220E0", "250E0", "280E0", "1,234E0", "-01E0", "-11E0", "-15E0", "-19E0", "-200E0", "-220E0", "-250E0", "-280E0", "-9,876E0", "1,000E-4", "1,200E-4", "1,510E-4", "1,810E-4", "5,000E-4", "5,300E-4", "5,550E-4", "5,750E-4", "9,000E-4", "9,400E-4", "9,590E-4", "9,690E-4", "-1,000E-4", "-1,200E-4", "-1,510E-4", "-1,810E-4", "-5,000E-4", "-5,300E-4", "-5,550E-4", "-5,750E-4", "-9,000E-4", "-9,400E-4", "-9,590E-4", "-9,690E-4", "03E0", "05E0", "04E0", "05E0", "04E0", "-03E0", "-05E0", "-04E0", "-05E0", "-04E0"]);
		check_numeric_format(",00E-0", numbers,
				[",00E0", ",01E0", ",11E0", ",15E0", ",19E0", ",02E2", ",02E2", ",03E2", ",03E2", ",12E2", "-,01E0", "-,11E0", "-,15E0", "-,19E0", "-,02E2", "-,02E2", "-,03E2", "-,03E2", "-,99E2", ",10E-2", ",12E-2", ",15E-2", ",18E-2", ",50E-2", ",53E-2", ",56E-2", ",58E-2", ",90E-2", ",94E-2", ",96E-2", ",97E-2", "-,10E-2", "-,12E-2", "-,15E-2", "-,18E-2", "-,50E-2", "-,53E-2", "-,56E-2", "-,58E-2", "-,90E-2", "-,94E-2", "-,96E-2", "-,97E-2", ",03E0", ",05E0", ",04E0", ",05E0", ",04E0", "-,03E0", "-,05E0", "-,04E0", "-,05E0", "-,04E0"]);
		check_numeric_format(",#,#00E-0", numbers,
				[",0,000E0", ",01E0", ",11E0", ",15E0", ",19E0", ",200E0", ",220E0", ",250E0", ",280E0", ",1,234E0", "-,01E0", "-,11E0", "-,15E0", "-,19E0", "-,200E0", "-,220E0", "-,250E0", "-,280E0", "-,9,876E0", ",1,000E-4", ",1,200E-4", ",1,510E-4", ",1,810E-4", ",5,000E-4", ",5,300E-4", ",5,550E-4", ",5,750E-4", ",9,000E-4", ",9,400E-4", ",9,590E-4", ",9,690E-4", "-,1,000E-4", "-,1,200E-4", "-,1,510E-4", "-,1,810E-4", "-,5,000E-4", "-,5,300E-4", "-,5,550E-4", "-,5,750E-4", "-,9,000E-4", "-,9,400E-4", "-,9,590E-4", "-,9,690E-4", ",03E0", ",05E0", ",04E0", ",05E0", ",04E0", "-,03E0", "-,05E0", "-,04E0", "-,05E0", "-,04E0"]);
		check_numeric_format("0,E-0", numbers,
				["0E0", "1E0", "1E1", "2E1", "2E1", "2E2", "2E2", "3E2", "3E2", "1E3", "-1E0", "-1E1", "-2E1", "-2E1", "-2E2", "-2E2", "-3E2", "-3E2", "-1E4", "1E-1", "1E-1", "2E-1", "2E-1", "5E-1", "5E-1", "6E-1", "6E-1", "9E-1", "9E-1", "1E0", "1E0", "-1E-1", "-1E-1", "-2E-1", "-2E-1", "-5E-1", "-5E-1", "-6E-1", "-6E-1", "-9E-1", "-9E-1", "-1E0", "-1E0", "3E0", "5E0", "4E0", "5E0", "4E0", "-3E0", "-5E0", "-4E0", "-5E0", "-4E0"]);
		check_numeric_format("0\\E,-0", numbers,
				["0E,0", "1E,0", "1E,1", "2E,1", "2E,1", "2E,2", "2E,2", "3E,2", "3E,2", "1E,3", "-1E,0", "-1E,1", "-2E,1", "-2E,1", "-2E,2", "-2E,2", "-3E,2", "-3E,2", "-1E,4", "1E,-1", "1E,-1", "2E,-1", "2E,-1", "5E,-1", "5E,-1", "6E,-1", "6E,-1", "9E,-1", "9E,-1", "1E,0", "1E,0", "-1E,-1", "-1E,-1", "-2E,-1", "-2E,-1", "-5E,-1", "-5E,-1", "-6E,-1", "-6E,-1", "-9E,-1", "-9E,-1", "-1E,0", "-1E,0", "3E,0", "5E,0", "4E,0", "5E,0", "4E,0", "-3E,0", "-5E,0", "-4E,0", "-5E,0", "-4E,0"]);
		check_numeric_format("0,\\E,-0", numbers,
				["0E,0", "1E,0", "1E,1", "2E,1", "2E,1", "2E,2", "2E,2", "3E,2", "3E,2", "1E,3", "-1E,0", "-1E,1", "-2E,1", "-2E,1", "-2E,2", "-2E,2", "-3E,2", "-3E,2", "-1E,4", "1E,-1", "1E,-1", "2E,-1", "2E,-1", "5E,-1", "5E,-1", "6E,-1", "6E,-1", "9E,-1", "9E,-1", "1E,0", "1E,0", "-1E,-1", "-1E,-1", "-2E,-1", "-2E,-1", "-5E,-1", "-5E,-1", "-6E,-1", "-6E,-1", "-9E,-1", "-9E,-1", "-1E,0", "-1E,0", "3E,0", "5E,0", "4E,0", "5E,0", "4E,0", "-3E,0", "-5E,0", "-4E,0", "-5E,0", "-4E,0"]);
		check_numeric_format("0.0E-0", numbers,
				["0.0E0", "1.0E0", "1.1E1", "1.5E1", "1.9E1", "2.0E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1.0E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2.0E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1.0E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.0E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.0E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.0E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.0E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.0E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format(".00E-0", numbers,
				[".00E0", ".10E1", ".11E2", ".15E2", ".19E2", ".20E3", ".22E3", ".25E3", ".28E3", ".12E4", "-.10E1", "-.11E2", "-.15E2", "-.19E2", "-.20E3", "-.22E3", "-.25E3", "-.28E3", "-.99E4", ".10E0", ".12E0", ".15E0", ".18E0", ".50E0", ".53E0", ".56E0", ".58E0", ".90E0", ".94E0", ".96E0", ".97E0", "-.10E0", "-.12E0", "-.15E0", "-.18E0", "-.50E0", "-.53E0", "-.56E0", "-.58E0", "-.90E0", "-.94E0", "-.96E0", "-.97E0", ".34E1", ".46E1", ".43E1", ".46E1", ".43E1", "-.34E1", "-.46E1", "-.43E1", "-.46E1", "-.43E1"]);
		check_numeric_format(".0.0E-0", numbers,
				[".0.0E0", ".1.0E1", ".1.1E2", ".1.5E2", ".1.9E2", ".2.0E3", ".2.2E3", ".2.5E3", ".2.8E3", ".1.2E4", "-.1.0E1", "-.1.1E2", "-.1.5E2", "-.1.9E2", "-.2.0E3", "-.2.2E3", "-.2.5E3", "-.2.8E3", "-.9.9E4", ".1.0E0", ".1.2E0", ".1.5E0", ".1.8E0", ".5.0E0", ".5.3E0", ".5.6E0", ".5.8E0", ".9.0E0", ".9.4E0", ".9.6E0", ".9.7E0", "-.1.0E0", "-.1.2E0", "-.1.5E0", "-.1.8E0", "-.5.0E0", "-.5.3E0", "-.5.6E0", "-.5.8E0", "-.9.0E0", "-.9.4E0", "-.9.6E0", "-.9.7E0", ".3.4E1", ".4.6E1", ".4.3E1", ".4.6E1", ".4.3E1", "-.3.4E1", "-.4.6E1", "-.4.3E1", "-.4.6E1", "-.4.3E1"]);
		check_numeric_format("0E-0.", numbers,
				["0E0.", "1E0.", "1E1.", "2E1.", "2E1.", "2E2.", "2E2.", "3E2.", "3E2.", "1E3.", "-1E0.", "-1E1.", "-2E1.", "-2E1.", "-2E2.", "-2E2.", "-3E2.", "-3E2.", "-1E4.", "1E-1.", "1E-1.", "2E-1.", "2E-1.", "5E-1.", "5E-1.", "6E-1.", "6E-1.", "9E-1.", "9E-1.", "1E0.", "1E0.", "-1E-1.", "-1E-1.", "-2E-1.", "-2E-1.", "-5E-1.", "-5E-1.", "-6E-1.", "-6E-1.", "-9E-1.", "-9E-1.", "-1E0.", "-1E0.", "3E0.", "5E0.", "4E0.", "5E0.", "4E0.", "-3E0.", "-5E0.", "-4E0.", "-5E0.", "-4E0."]);
		check_numeric_format("0E-.0", numbers,
				["0E0.0", "1E0.0", "1E1.0", "2E1.0", "2E1.0", "2E2.0", "2E2.0", "3E2.0", "3E2.0", "1E3.0", "-1E0.0", "-1E1.0", "-2E1.0", "-2E1.0", "-2E2.0", "-2E2.0", "-3E2.0", "-3E2.0", "-1E4.0", "1E-1.0", "1E-1.0", "2E-1.0", "2E-1.0", "5E-1.0", "5E-1.0", "6E-1.0", "6E-1.0", "9E-1.0", "9E-1.0", "1E0.0", "1E0.0", "-1E-1.0", "-1E-1.0", "-2E-1.0", "-2E-1.0", "-5E-1.0", "-5E-1.0", "-6E-1.0", "-6E-1.0", "-9E-1.0", "-9E-1.0", "-1E0.0", "-1E0.0", "3E0.0", "5E0.0", "4E0.0", "5E0.0", "4E0.0", "-3E0.0", "-5E0.0", "-4E0.0", "-5E0.0", "-4E0.0"]);
		check_numeric_format("0E-.0.", numbers,
				["0E0.0.", "1E0.0.", "1E1.0.", "2E1.0.", "2E1.0.", "2E2.0.", "2E2.0.", "3E2.0.", "3E2.0.", "1E3.0.", "-1E0.0.", "-1E1.0.", "-2E1.0.", "-2E1.0.", "-2E2.0.", "-2E2.0.", "-3E2.0.", "-3E2.0.", "-1E4.0.", "1E-1.0.", "1E-1.0.", "2E-1.0.", "2E-1.0.", "5E-1.0.", "5E-1.0.", "6E-1.0.", "6E-1.0.", "9E-1.0.", "9E-1.0.", "1E0.0.", "1E0.0.", "-1E-1.0.", "-1E-1.0.", "-2E-1.0.", "-2E-1.0.", "-5E-1.0.", "-5E-1.0.", "-6E-1.0.", "-6E-1.0.", "-9E-1.0.", "-9E-1.0.", "-1E0.0.", "-1E0.0.", "3E0.0.", "5E0.0.", "4E0.0.", "5E0.0.", "4E0.0.", "-3E0.0.", "-5E0.0.", "-4E0.0.", "-5E0.0.", "-4E0.0."]);
		check_numeric_format("0!0E-0", numbers,
				["0!0E0", "0!1E0", "1!1E0", "1!5E0", "1!9E0", "0!2E2", "0!2E2", "0!3E2", "0!3E2", "1!2E2", "-0!1E0", "-1!1E0", "-1!5E0", "-1!9E0", "-0!2E2", "-0!2E2", "-0!3E2", "-0!3E2", "-9!9E2", "1!0E-2", "1!2E-2", "1!5E-2", "1!8E-2", "5!0E-2", "5!3E-2", "5!6E-2", "5!8E-2", "9!0E-2", "9!4E-2", "9!6E-2", "9!7E-2", "-1!0E-2", "-1!2E-2", "-1!5E-2", "-1!8E-2", "-5!0E-2", "-5!3E-2", "-5!6E-2", "-5!8E-2", "-9!0E-2", "-9!4E-2", "-9!6E-2", "-9!7E-2", "0!3E0", "0!5E0", "0!4E0", "0!5E0", "0!4E0", "-0!3E0", "-0!5E0", "-0!4E0", "-0!5E0", "-0!4E0"]);
		check_numeric_format("!00E-0", numbers,
				["!00E0", "!01E0", "!11E0", "!15E0", "!19E0", "!02E2", "!02E2", "!03E2", "!03E2", "!12E2", "-!01E0", "-!11E0", "-!15E0", "-!19E0", "-!02E2", "-!02E2", "-!03E2", "-!03E2", "-!99E2", "!10E-2", "!12E-2", "!15E-2", "!18E-2", "!50E-2", "!53E-2", "!56E-2", "!58E-2", "!90E-2", "!94E-2", "!96E-2", "!97E-2", "-!10E-2", "-!12E-2", "-!15E-2", "-!18E-2", "-!50E-2", "-!53E-2", "-!56E-2", "-!58E-2", "-!90E-2", "-!94E-2", "-!96E-2", "-!97E-2", "!03E0", "!05E0", "!04E0", "!05E0", "!04E0", "-!03E0", "-!05E0", "-!04E0", "-!05E0", "-!04E0"]);
		check_numeric_format("!0!0E-0", numbers,
				["!0!0E0", "!0!1E0", "!1!1E0", "!1!5E0", "!1!9E0", "!0!2E2", "!0!2E2", "!0!3E2", "!0!3E2", "!1!2E2", "-!0!1E0", "-!1!1E0", "-!1!5E0", "-!1!9E0", "-!0!2E2", "-!0!2E2", "-!0!3E2", "-!0!3E2", "-!9!9E2", "!1!0E-2", "!1!2E-2", "!1!5E-2", "!1!8E-2", "!5!0E-2", "!5!3E-2", "!5!6E-2", "!5!8E-2", "!9!0E-2", "!9!4E-2", "!9!6E-2", "!9!7E-2", "-!1!0E-2", "-!1!2E-2", "-!1!5E-2", "-!1!8E-2", "-!5!0E-2", "-!5!3E-2", "-!5!6E-2", "-!5!8E-2", "-!9!0E-2", "-!9!4E-2", "-!9!6E-2", "-!9!7E-2", "!0!3E0", "!0!5E0", "!0!4E0", "!0!5E0", "!0!4E0", "-!0!3E0", "-!0!5E0", "-!0!4E0", "-!0!5E0", "-!0!4E0"]);
		check_numeric_format("0E-0!", numbers,
				["0E0!", "1E0!", "1E1!", "2E1!", "2E1!", "2E2!", "2E2!", "3E2!", "3E2!", "1E3!", "-1E0!", "-1E1!", "-2E1!", "-2E1!", "-2E2!", "-2E2!", "-3E2!", "-3E2!", "-1E4!", "1E-1!", "1E-1!", "2E-1!", "2E-1!", "5E-1!", "5E-1!", "6E-1!", "6E-1!", "9E-1!", "9E-1!", "1E0!", "1E0!", "-1E-1!", "-1E-1!", "-2E-1!", "-2E-1!", "-5E-1!", "-5E-1!", "-6E-1!", "-6E-1!", "-9E-1!", "-9E-1!", "-1E0!", "-1E0!", "3E0!", "5E0!", "4E0!", "5E0!", "4E0!", "-3E0!", "-5E0!", "-4E0!", "-5E0!", "-4E0!"]);
		check_numeric_format("0\\E!-0", numbers,
				["0E!0", "1E!0", "1E!1", "2E!1", "2E!1", "2E!2", "2E!2", "3E!2", "3E!2", "1E!3", "-1E!0", "-1E!1", "-2E!1", "-2E!1", "-2E!2", "-2E!2", "-3E!2", "-3E!2", "-1E!4", "1E!-1", "1E!-1", "2E!-1", "2E!-1", "5E!-1", "5E!-1", "6E!-1", "6E!-1", "9E!-1", "9E!-1", "1E!0", "1E!0", "-1E!-1", "-1E!-1", "-2E!-1", "-2E!-1", "-5E!-1", "-5E!-1", "-6E!-1", "-6E!-1", "-9E!-1", "-9E!-1", "-1E!0", "-1E!0", "3E!0", "5E!0", "4E!0", "5E!0", "4E!0", "-3E!0", "-5E!0", "-4E!0", "-5E!0", "-4E!0"]);
		check_numeric_format("0\\E!-0!", numbers,
				["0E!0!", "1E!0!", "1E!1!", "2E!1!", "2E!1!", "2E!2!", "2E!2!", "3E!2!", "3E!2!", "1E!3!", "-1E!0!", "-1E!1!", "-2E!1!", "-2E!1!", "-2E!2!", "-2E!2!", "-3E!2!", "-3E!2!", "-1E!4!", "1E!-1!", "1E!-1!", "2E!-1!", "2E!-1!", "5E!-1!", "5E!-1!", "6E!-1!", "6E!-1!", "9E!-1!", "9E!-1!", "1E!0!", "1E!0!", "-1E!-1!", "-1E!-1!", "-2E!-1!", "-2E!-1!", "-5E!-1!", "-5E!-1!", "-6E!-1!", "-6E!-1!", "-9E!-1!", "-9E!-1!", "-1E!0!", "-1E!0!", "3E!0!", "5E!0!", "4E!0!", "5E!0!", "4E!0!", "-3E!0!", "-5E!0!", "-4E!0!", "-5E!0!", "-4E!0!"]);
		check_numeric_format("#,#00E+0", numbers,
				["0,000E+0", "01E+0", "11E+0", "15E+0", "19E+0", "200E+0", "220E+0", "250E+0", "280E+0", "1,234E+0", "-01E+0", "-11E+0", "-15E+0", "-19E+0", "-200E+0", "-220E+0", "-250E+0", "-280E+0", "-9,876E+0", "1,000E-4", "1,200E-4", "1,510E-4", "1,810E-4", "5,000E-4", "5,300E-4", "5,550E-4", "5,750E-4", "9,000E-4", "9,400E-4", "9,590E-4", "9,690E-4", "-1,000E-4", "-1,200E-4", "-1,510E-4", "-1,810E-4", "-5,000E-4", "-5,300E-4", "-5,550E-4", "-5,750E-4", "-9,000E-4", "-9,400E-4", "-9,590E-4", "-9,690E-4", "03E+0", "05E+0", "04E+0", "05E+0", "04E+0", "-03E+0", "-05E+0", "-04E+0", "-05E+0", "-04E+0"]);
		check_numeric_format(",00E+0", numbers,
				[",00E+0", ",01E+0", ",11E+0", ",15E+0", ",19E+0", ",02E+2", ",02E+2", ",03E+2", ",03E+2", ",12E+2", "-,01E+0", "-,11E+0", "-,15E+0", "-,19E+0", "-,02E+2", "-,02E+2", "-,03E+2", "-,03E+2", "-,99E+2", ",10E-2", ",12E-2", ",15E-2", ",18E-2", ",50E-2", ",53E-2", ",56E-2", ",58E-2", ",90E-2", ",94E-2", ",96E-2", ",97E-2", "-,10E-2", "-,12E-2", "-,15E-2", "-,18E-2", "-,50E-2", "-,53E-2", "-,56E-2", "-,58E-2", "-,90E-2", "-,94E-2", "-,96E-2", "-,97E-2", ",03E+0", ",05E+0", ",04E+0", ",05E+0", ",04E+0", "-,03E+0", "-,05E+0", "-,04E+0", "-,05E+0", "-,04E+0"]);
		check_numeric_format(",#,#00E+0", numbers,
				[",0,000E+0", ",01E+0", ",11E+0", ",15E+0", ",19E+0", ",200E+0", ",220E+0", ",250E+0", ",280E+0", ",1,234E+0", "-,01E+0", "-,11E+0", "-,15E+0", "-,19E+0", "-,200E+0", "-,220E+0", "-,250E+0", "-,280E+0", "-,9,876E+0", ",1,000E-4", ",1,200E-4", ",1,510E-4", ",1,810E-4", ",5,000E-4", ",5,300E-4", ",5,550E-4", ",5,750E-4", ",9,000E-4", ",9,400E-4", ",9,590E-4", ",9,690E-4", "-,1,000E-4", "-,1,200E-4", "-,1,510E-4", "-,1,810E-4", "-,5,000E-4", "-,5,300E-4", "-,5,550E-4", "-,5,750E-4", "-,9,000E-4", "-,9,400E-4", "-,9,590E-4", "-,9,690E-4", ",03E+0", ",05E+0", ",04E+0", ",05E+0", ",04E+0", "-,03E+0", "-,05E+0", "-,04E+0", "-,05E+0", "-,04E+0"]);
		check_numeric_format("0,E+0", numbers,
				["0E+0", "1E+0", "1E+1", "2E+1", "2E+1", "2E+2", "2E+2", "3E+2", "3E+2", "1E+3", "-1E+0", "-1E+1", "-2E+1", "-2E+1", "-2E+2", "-2E+2", "-3E+2", "-3E+2", "-1E+4", "1E-1", "1E-1", "2E-1", "2E-1", "5E-1", "5E-1", "6E-1", "6E-1", "9E-1", "9E-1", "1E+0", "1E+0", "-1E-1", "-1E-1", "-2E-1", "-2E-1", "-5E-1", "-5E-1", "-6E-1", "-6E-1", "-9E-1", "-9E-1", "-1E+0", "-1E+0", "3E+0", "5E+0", "4E+0", "5E+0", "4E+0", "-3E+0", "-5E+0", "-4E+0", "-5E+0", "-4E+0"]);
		check_numeric_format("0\\E,+0", numbers,
				["0E,+0", "1E,+0", "1E,+1", "2E,+1", "2E,+1", "2E,+2", "2E,+2", "3E,+2", "3E,+2", "1E,+3", "-1E,+0", "-1E,+1", "-2E,+1", "-2E,+1", "-2E,+2", "-2E,+2", "-3E,+2", "-3E,+2", "-1E,+4", "1E,-1", "1E,-1", "2E,-1", "2E,-1", "5E,-1", "5E,-1", "6E,-1", "6E,-1", "9E,-1", "9E,-1", "1E,+0", "1E,+0", "-1E,-1", "-1E,-1", "-2E,-1", "-2E,-1", "-5E,-1", "-5E,-1", "-6E,-1", "-6E,-1", "-9E,-1", "-9E,-1", "-1E,+0", "-1E,+0", "3E,+0", "5E,+0", "4E,+0", "5E,+0", "4E,+0", "-3E,+0", "-5E,+0", "-4E,+0", "-5E,+0", "-4E,+0"]);
		check_numeric_format("0,\\E,+0", numbers,
				["0E,+0", "1E,+0", "1E,+1", "2E,+1", "2E,+1", "2E,+2", "2E,+2", "3E,+2", "3E,+2", "1E,+3", "-1E,+0", "-1E,+1", "-2E,+1", "-2E,+1", "-2E,+2", "-2E,+2", "-3E,+2", "-3E,+2", "-1E,+4", "1E,-1", "1E,-1", "2E,-1", "2E,-1", "5E,-1", "5E,-1", "6E,-1", "6E,-1", "9E,-1", "9E,-1", "1E,+0", "1E,+0", "-1E,-1", "-1E,-1", "-2E,-1", "-2E,-1", "-5E,-1", "-5E,-1", "-6E,-1", "-6E,-1", "-9E,-1", "-9E,-1", "-1E,+0", "-1E,+0", "3E,+0", "5E,+0", "4E,+0", "5E,+0", "4E,+0", "-3E,+0", "-5E,+0", "-4E,+0", "-5E,+0", "-4E,+0"]);
		check_numeric_format("0.0E+0", numbers,
				["0.0E+0", "1.0E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2.0E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1.0E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2.0E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1.0E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.0E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.0E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.0E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.0E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.0E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
		check_numeric_format(".00E+0", numbers,
				[".00E+0", ".10E+1", ".11E+2", ".15E+2", ".19E+2", ".20E+3", ".22E+3", ".25E+3", ".28E+3", ".12E+4", "-.10E+1", "-.11E+2", "-.15E+2", "-.19E+2", "-.20E+3", "-.22E+3", "-.25E+3", "-.28E+3", "-.99E+4", ".10E+0", ".12E+0", ".15E+0", ".18E+0", ".50E+0", ".53E+0", ".56E+0", ".58E+0", ".90E+0", ".94E+0", ".96E+0", ".97E+0", "-.10E+0", "-.12E+0", "-.15E+0", "-.18E+0", "-.50E+0", "-.53E+0", "-.56E+0", "-.58E+0", "-.90E+0", "-.94E+0", "-.96E+0", "-.97E+0", ".34E+1", ".46E+1", ".43E+1", ".46E+1", ".43E+1", "-.34E+1", "-.46E+1", "-.43E+1", "-.46E+1", "-.43E+1"]);
		check_numeric_format(".0.0E+0", numbers,
				[".0.0E+0", ".1.0E+1", ".1.1E+2", ".1.5E+2", ".1.9E+2", ".2.0E+3", ".2.2E+3", ".2.5E+3", ".2.8E+3", ".1.2E+4", "-.1.0E+1", "-.1.1E+2", "-.1.5E+2", "-.1.9E+2", "-.2.0E+3", "-.2.2E+3", "-.2.5E+3", "-.2.8E+3", "-.9.9E+4", ".1.0E+0", ".1.2E+0", ".1.5E+0", ".1.8E+0", ".5.0E+0", ".5.3E+0", ".5.6E+0", ".5.8E+0", ".9.0E+0", ".9.4E+0", ".9.6E+0", ".9.7E+0", "-.1.0E+0", "-.1.2E+0", "-.1.5E+0", "-.1.8E+0", "-.5.0E+0", "-.5.3E+0", "-.5.6E+0", "-.5.8E+0", "-.9.0E+0", "-.9.4E+0", "-.9.6E+0", "-.9.7E+0", ".3.4E+1", ".4.6E+1", ".4.3E+1", ".4.6E+1", ".4.3E+1", "-.3.4E+1", "-.4.6E+1", "-.4.3E+1", "-.4.6E+1", "-.4.3E+1"]);
		check_numeric_format("0E+0.", numbers,
				["0E+0.", "1E+0.", "1E+1.", "2E+1.", "2E+1.", "2E+2.", "2E+2.", "3E+2.", "3E+2.", "1E+3.", "-1E+0.", "-1E+1.", "-2E+1.", "-2E+1.", "-2E+2.", "-2E+2.", "-3E+2.", "-3E+2.", "-1E+4.", "1E-1.", "1E-1.", "2E-1.", "2E-1.", "5E-1.", "5E-1.", "6E-1.", "6E-1.", "9E-1.", "9E-1.", "1E+0.", "1E+0.", "-1E-1.", "-1E-1.", "-2E-1.", "-2E-1.", "-5E-1.", "-5E-1.", "-6E-1.", "-6E-1.", "-9E-1.", "-9E-1.", "-1E+0.", "-1E+0.", "3E+0.", "5E+0.", "4E+0.", "5E+0.", "4E+0.", "-3E+0.", "-5E+0.", "-4E+0.", "-5E+0.", "-4E+0."]);
		check_numeric_format("0E+.0", numbers,
				["0E+0.0", "1E+0.0", "1E+1.0", "2E+1.0", "2E+1.0", "2E+2.0", "2E+2.0", "3E+2.0", "3E+2.0", "1E+3.0", "-1E+0.0", "-1E+1.0", "-2E+1.0", "-2E+1.0", "-2E+2.0", "-2E+2.0", "-3E+2.0", "-3E+2.0", "-1E+4.0", "1E-1.0", "1E-1.0", "2E-1.0", "2E-1.0", "5E-1.0", "5E-1.0", "6E-1.0", "6E-1.0", "9E-1.0", "9E-1.0", "1E+0.0", "1E+0.0", "-1E-1.0", "-1E-1.0", "-2E-1.0", "-2E-1.0", "-5E-1.0", "-5E-1.0", "-6E-1.0", "-6E-1.0", "-9E-1.0", "-9E-1.0", "-1E+0.0", "-1E+0.0", "3E+0.0", "5E+0.0", "4E+0.0", "5E+0.0", "4E+0.0", "-3E+0.0", "-5E+0.0", "-4E+0.0", "-5E+0.0", "-4E+0.0"]);
		check_numeric_format("0E+.0.", numbers,
				["0E+0.0.", "1E+0.0.", "1E+1.0.", "2E+1.0.", "2E+1.0.", "2E+2.0.", "2E+2.0.", "3E+2.0.", "3E+2.0.", "1E+3.0.", "-1E+0.0.", "-1E+1.0.", "-2E+1.0.", "-2E+1.0.", "-2E+2.0.", "-2E+2.0.", "-3E+2.0.", "-3E+2.0.", "-1E+4.0.", "1E-1.0.", "1E-1.0.", "2E-1.0.", "2E-1.0.", "5E-1.0.", "5E-1.0.", "6E-1.0.", "6E-1.0.", "9E-1.0.", "9E-1.0.", "1E+0.0.", "1E+0.0.", "-1E-1.0.", "-1E-1.0.", "-2E-1.0.", "-2E-1.0.", "-5E-1.0.", "-5E-1.0.", "-6E-1.0.", "-6E-1.0.", "-9E-1.0.", "-9E-1.0.", "-1E+0.0.", "-1E+0.0.", "3E+0.0.", "5E+0.0.", "4E+0.0.", "5E+0.0.", "4E+0.0.", "-3E+0.0.", "-5E+0.0.", "-4E+0.0.", "-5E+0.0.", "-4E+0.0."]);
		check_numeric_format("0!0E+0", numbers,
				["0!0E+0", "0!1E+0", "1!1E+0", "1!5E+0", "1!9E+0", "0!2E+2", "0!2E+2", "0!3E+2", "0!3E+2", "1!2E+2", "-0!1E+0", "-1!1E+0", "-1!5E+0", "-1!9E+0", "-0!2E+2", "-0!2E+2", "-0!3E+2", "-0!3E+2", "-9!9E+2", "1!0E-2", "1!2E-2", "1!5E-2", "1!8E-2", "5!0E-2", "5!3E-2", "5!6E-2", "5!8E-2", "9!0E-2", "9!4E-2", "9!6E-2", "9!7E-2", "-1!0E-2", "-1!2E-2", "-1!5E-2", "-1!8E-2", "-5!0E-2", "-5!3E-2", "-5!6E-2", "-5!8E-2", "-9!0E-2", "-9!4E-2", "-9!6E-2", "-9!7E-2", "0!3E+0", "0!5E+0", "0!4E+0", "0!5E+0", "0!4E+0", "-0!3E+0", "-0!5E+0", "-0!4E+0", "-0!5E+0", "-0!4E+0"]);
		check_numeric_format("!00E+0", numbers,
				["!00E+0", "!01E+0", "!11E+0", "!15E+0", "!19E+0", "!02E+2", "!02E+2", "!03E+2", "!03E+2", "!12E+2", "-!01E+0", "-!11E+0", "-!15E+0", "-!19E+0", "-!02E+2", "-!02E+2", "-!03E+2", "-!03E+2", "-!99E+2", "!10E-2", "!12E-2", "!15E-2", "!18E-2", "!50E-2", "!53E-2", "!56E-2", "!58E-2", "!90E-2", "!94E-2", "!96E-2", "!97E-2", "-!10E-2", "-!12E-2", "-!15E-2", "-!18E-2", "-!50E-2", "-!53E-2", "-!56E-2", "-!58E-2", "-!90E-2", "-!94E-2", "-!96E-2", "-!97E-2", "!03E+0", "!05E+0", "!04E+0", "!05E+0", "!04E+0", "-!03E+0", "-!05E+0", "-!04E+0", "-!05E+0", "-!04E+0"]);
		check_numeric_format("!0!0E+0", numbers,
				["!0!0E+0", "!0!1E+0", "!1!1E+0", "!1!5E+0", "!1!9E+0", "!0!2E+2", "!0!2E+2", "!0!3E+2", "!0!3E+2", "!1!2E+2", "-!0!1E+0", "-!1!1E+0", "-!1!5E+0", "-!1!9E+0", "-!0!2E+2", "-!0!2E+2", "-!0!3E+2", "-!0!3E+2", "-!9!9E+2", "!1!0E-2", "!1!2E-2", "!1!5E-2", "!1!8E-2", "!5!0E-2", "!5!3E-2", "!5!6E-2", "!5!8E-2", "!9!0E-2", "!9!4E-2", "!9!6E-2", "!9!7E-2", "-!1!0E-2", "-!1!2E-2", "-!1!5E-2", "-!1!8E-2", "-!5!0E-2", "-!5!3E-2", "-!5!6E-2", "-!5!8E-2", "-!9!0E-2", "-!9!4E-2", "-!9!6E-2", "-!9!7E-2", "!0!3E+0", "!0!5E+0", "!0!4E+0", "!0!5E+0", "!0!4E+0", "-!0!3E+0", "-!0!5E+0", "-!0!4E+0", "-!0!5E+0", "-!0!4E+0"]);
		check_numeric_format("0E+0!", numbers,
				["0E+0!", "1E+0!", "1E+1!", "2E+1!", "2E+1!", "2E+2!", "2E+2!", "3E+2!", "3E+2!", "1E+3!", "-1E+0!", "-1E+1!", "-2E+1!", "-2E+1!", "-2E+2!", "-2E+2!", "-3E+2!", "-3E+2!", "-1E+4!", "1E-1!", "1E-1!", "2E-1!", "2E-1!", "5E-1!", "5E-1!", "6E-1!", "6E-1!", "9E-1!", "9E-1!", "1E+0!", "1E+0!", "-1E-1!", "-1E-1!", "-2E-1!", "-2E-1!", "-5E-1!", "-5E-1!", "-6E-1!", "-6E-1!", "-9E-1!", "-9E-1!", "-1E+0!", "-1E+0!", "3E+0!", "5E+0!", "4E+0!", "5E+0!", "4E+0!", "-3E+0!", "-5E+0!", "-4E+0!", "-5E+0!", "-4E+0!"]);
		check_numeric_format("0\\E!+0", numbers,
				["0E!+0", "1E!+0", "1E!+1", "2E!+1", "2E!+1", "2E!+2", "2E!+2", "3E!+2", "3E!+2", "1E!+3", "-1E!+0", "-1E!+1", "-2E!+1", "-2E!+1", "-2E!+2", "-2E!+2", "-3E!+2", "-3E!+2", "-1E!+4", "1E!-1", "1E!-1", "2E!-1", "2E!-1", "5E!-1", "5E!-1", "6E!-1", "6E!-1", "9E!-1", "9E!-1", "1E!+0", "1E!+0", "-1E!-1", "-1E!-1", "-2E!-1", "-2E!-1", "-5E!-1", "-5E!-1", "-6E!-1", "-6E!-1", "-9E!-1", "-9E!-1", "-1E!+0", "-1E!+0", "3E!+0", "5E!+0", "4E!+0", "5E!+0", "4E!+0", "-3E!+0", "-5E!+0", "-4E!+0", "-5E!+0", "-4E!+0"]);
		check_numeric_format("0\\E!+0!", numbers,
				["0E!+0!", "1E!+0!", "1E!+1!", "2E!+1!", "2E!+1!", "2E!+2!", "2E!+2!", "3E!+2!", "3E!+2!", "1E!+3!", "-1E!+0!", "-1E!+1!", "-2E!+1!", "-2E!+1!", "-2E!+2!", "-2E!+2!", "-3E!+2!", "-3E!+2!", "-1E!+4!", "1E!-1!", "1E!-1!", "2E!-1!", "2E!-1!", "5E!-1!", "5E!-1!", "6E!-1!", "6E!-1!", "9E!-1!", "9E!-1!", "1E!+0!", "1E!+0!", "-1E!-1!", "-1E!-1!", "-2E!-1!", "-2E!-1!", "-5E!-1!", "-5E!-1!", "-6E!-1!", "-6E!-1!", "-9E!-1!", "-9E!-1!", "-1E!+0!", "-1E!+0!", "3E!+0!", "5E!+0!", "4E!+0!", "5E!+0!", "4E!+0!", "-3E!+0!", "-5E!+0!", "-4E!+0!", "-5E!+0!", "-4E!+0!"]);
		check_numeric_format(".00E-0", numbers,
				[".00E0", ".10E1", ".11E2", ".15E2", ".19E2", ".20E3", ".22E3", ".25E3", ".28E3", ".12E4", "-.10E1", "-.11E2", "-.15E2", "-.19E2", "-.20E3", "-.22E3", "-.25E3", "-.28E3", "-.99E4", ".10E0", ".12E0", ".15E0", ".18E0", ".50E0", ".53E0", ".56E0", ".58E0", ".90E0", ".94E0", ".96E0", ".97E0", "-.10E0", "-.12E0", "-.15E0", "-.18E0", "-.50E0", "-.53E0", "-.56E0", "-.58E0", "-.90E0", "-.94E0", "-.96E0", "-.97E0", ".34E1", ".46E1", ".43E1", ".46E1", ".43E1", "-.34E1", "-.46E1", "-.43E1", "-.46E1", "-.43E1"]);
		check_numeric_format(".00E-0", numbers,
				[".00E0", ".10E1", ".11E2", ".15E2", ".19E2", ".20E3", ".22E3", ".25E3", ".28E3", ".12E4", "-.10E1", "-.11E2", "-.15E2", "-.19E2", "-.20E3", "-.22E3", "-.25E3", "-.28E3", "-.99E4", ".10E0", ".12E0", ".15E0", ".18E0", ".50E0", ".53E0", ".56E0", ".58E0", ".90E0", ".94E0", ".96E0", ".97E0", "-.10E0", "-.12E0", "-.15E0", "-.18E0", "-.50E0", "-.53E0", "-.56E0", "-.58E0", "-.90E0", "-.94E0", "-.96E0", "-.97E0", ".34E1", ".46E1", ".43E1", ".46E1", ".43E1", "-.34E1", "-.46E1", "-.43E1", "-.46E1", "-.43E1"]);
		check_numeric_format(".00E-0", numbers,
				[".00E0", ".10E1", ".11E2", ".15E2", ".19E2", ".20E3", ".22E3", ".25E3", ".28E3", ".12E4", "-.10E1", "-.11E2", "-.15E2", "-.19E2", "-.20E3", "-.22E3", "-.25E3", "-.28E3", "-.99E4", ".10E0", ".12E0", ".15E0", ".18E0", ".50E0", ".53E0", ".56E0", ".58E0", ".90E0", ".94E0", ".96E0", ".97E0", "-.10E0", "-.12E0", "-.15E0", "-.18E0", "-.50E0", "-.53E0", "-.56E0", "-.58E0", "-.90E0", "-.94E0", "-.96E0", "-.97E0", ".34E1", ".46E1", ".43E1", ".46E1", ".43E1", "-.34E1", "-.46E1", "-.43E1", "-.46E1", "-.43E1"]);
		check_numeric_format(".0,E-0", numbers,
				[".0E0", ".1E1", ".1E2", ".2E2", ".2E2", ".2E3", ".2E3", ".3E3", ".3E3", ".1E4", "-.1E1", "-.1E2", "-.2E2", "-.2E2", "-.2E3", "-.2E3", "-.3E3", "-.3E3", "-.1E5", ".1E0", ".1E0", ".2E0", ".2E0", ".5E0", ".5E0", ".6E0", ".6E0", ".9E0", ".9E0", ".1E1", ".1E1", "-.1E0", "-.1E0", "-.2E0", "-.2E0", "-.5E0", "-.5E0", "-.6E0", "-.6E0", "-.9E0", "-.9E0", "-.1E1", "-.1E1", ".3E1", ".5E1", ".4E1", ".5E1", ".4E1", "-.3E1", "-.5E1", "-.4E1", "-.5E1", "-.4E1"]);
		check_numeric_format(".0\\E,-0", numbers,
				[".0E,0", ".1E,1", ".1E,2", ".2E,2", ".2E,2", ".2E,3", ".2E,3", ".3E,3", ".3E,3", ".1E,4", "-.1E,1", "-.1E,2", "-.2E,2", "-.2E,2", "-.2E,3", "-.2E,3", "-.3E,3", "-.3E,3", "-.1E,5", ".1E,0", ".1E,0", ".2E,0", ".2E,0", ".5E,0", ".5E,0", ".6E,0", ".6E,0", ".9E,0", ".9E,0", ".1E,1", ".1E,1", "-.1E,0", "-.1E,0", "-.2E,0", "-.2E,0", "-.5E,0", "-.5E,0", "-.6E,0", "-.6E,0", "-.9E,0", "-.9E,0", "-.1E,1", "-.1E,1", ".3E,1", ".5E,1", ".4E,1", ".5E,1", ".4E,1", "-.3E,1", "-.5E,1", "-.4E,1", "-.5E,1", "-.4E,1"]);
		check_numeric_format(".0,\\E,-0", numbers,
				[".0E,0", ".1E,1", ".1E,2", ".2E,2", ".2E,2", ".2E,3", ".2E,3", ".3E,3", ".3E,3", ".1E,4", "-.1E,1", "-.1E,2", "-.2E,2", "-.2E,2", "-.2E,3", "-.2E,3", "-.3E,3", "-.3E,3", "-.1E,5", ".1E,0", ".1E,0", ".2E,0", ".2E,0", ".5E,0", ".5E,0", ".6E,0", ".6E,0", ".9E,0", ".9E,0", ".1E,1", ".1E,1", "-.1E,0", "-.1E,0", "-.2E,0", "-.2E,0", "-.5E,0", "-.5E,0", "-.6E,0", "-.6E,0", "-.9E,0", "-.9E,0", "-.1E,1", "-.1E,1", ".3E,1", ".5E,1", ".4E,1", ".5E,1", ".4E,1", "-.3E,1", "-.5E,1", "-.4E,1", "-.5E,1", "-.4E,1"]);
		check_numeric_format(".0.0E-0", numbers,
				[".0.0E0", ".1.0E1", ".1.1E2", ".1.5E2", ".1.9E2", ".2.0E3", ".2.2E3", ".2.5E3", ".2.8E3", ".1.2E4", "-.1.0E1", "-.1.1E2", "-.1.5E2", "-.1.9E2", "-.2.0E3", "-.2.2E3", "-.2.5E3", "-.2.8E3", "-.9.9E4", ".1.0E0", ".1.2E0", ".1.5E0", ".1.8E0", ".5.0E0", ".5.3E0", ".5.6E0", ".5.8E0", ".9.0E0", ".9.4E0", ".9.6E0", ".9.7E0", "-.1.0E0", "-.1.2E0", "-.1.5E0", "-.1.8E0", "-.5.0E0", "-.5.3E0", "-.5.6E0", "-.5.8E0", "-.9.0E0", "-.9.4E0", "-.9.6E0", "-.9.7E0", ".3.4E1", ".4.6E1", ".4.3E1", ".4.6E1", ".4.3E1", "-.3.4E1", "-.4.6E1", "-.4.3E1", "-.4.6E1", "-.4.3E1"]);
		check_numeric_format("..00E-0", numbers,
				["..00E0", "..10E1", "..11E2", "..15E2", "..19E2", "..20E3", "..22E3", "..25E3", "..28E3", "..12E4", "-..10E1", "-..11E2", "-..15E2", "-..19E2", "-..20E3", "-..22E3", "-..25E3", "-..28E3", "-..99E4", "..10E0", "..12E0", "..15E0", "..18E0", "..50E0", "..53E0", "..56E0", "..58E0", "..90E0", "..94E0", "..96E0", "..97E0", "-..10E0", "-..12E0", "-..15E0", "-..18E0", "-..50E0", "-..53E0", "-..56E0", "-..58E0", "-..90E0", "-..94E0", "-..96E0", "-..97E0", "..34E1", "..46E1", "..43E1", "..46E1", "..43E1", "-..34E1", "-..46E1", "-..43E1", "-..46E1", "-..43E1"]);
		check_numeric_format("..0.0E-0", numbers,
				["..0.0E0", "..1.0E1", "..1.1E2", "..1.5E2", "..1.9E2", "..2.0E3", "..2.2E3", "..2.5E3", "..2.8E3", "..1.2E4", "-..1.0E1", "-..1.1E2", "-..1.5E2", "-..1.9E2", "-..2.0E3", "-..2.2E3", "-..2.5E3", "-..2.8E3", "-..9.9E4", "..1.0E0", "..1.2E0", "..1.5E0", "..1.8E0", "..5.0E0", "..5.3E0", "..5.6E0", "..5.8E0", "..9.0E0", "..9.4E0", "..9.6E0", "..9.7E0", "-..1.0E0", "-..1.2E0", "-..1.5E0", "-..1.8E0", "-..5.0E0", "-..5.3E0", "-..5.6E0", "-..5.8E0", "-..9.0E0", "-..9.4E0", "-..9.6E0", "-..9.7E0", "..3.4E1", "..4.6E1", "..4.3E1", "..4.6E1", "..4.3E1", "-..3.4E1", "-..4.6E1", "-..4.3E1", "-..4.6E1", "-..4.3E1"]);
		check_numeric_format(".0E-0.", numbers,
				[".0E0.", ".1E1.", ".1E2.", ".2E2.", ".2E2.", ".2E3.", ".2E3.", ".3E3.", ".3E3.", ".1E4.", "-.1E1.", "-.1E2.", "-.2E2.", "-.2E2.", "-.2E3.", "-.2E3.", "-.3E3.", "-.3E3.", "-.1E5.", ".1E0.", ".1E0.", ".2E0.", ".2E0.", ".5E0.", ".5E0.", ".6E0.", ".6E0.", ".9E0.", ".9E0.", ".1E1.", ".1E1.", "-.1E0.", "-.1E0.", "-.2E0.", "-.2E0.", "-.5E0.", "-.5E0.", "-.6E0.", "-.6E0.", "-.9E0.", "-.9E0.", "-.1E1.", "-.1E1.", ".3E1.", ".5E1.", ".4E1.", ".5E1.", ".4E1.", "-.3E1.", "-.5E1.", "-.4E1.", "-.5E1.", "-.4E1."]);
		check_numeric_format(".0E-.0", numbers,
				[".0E0.0", ".1E1.0", ".1E2.0", ".2E2.0", ".2E2.0", ".2E3.0", ".2E3.0", ".3E3.0", ".3E3.0", ".1E4.0", "-.1E1.0", "-.1E2.0", "-.2E2.0", "-.2E2.0", "-.2E3.0", "-.2E3.0", "-.3E3.0", "-.3E3.0", "-.1E5.0", ".1E0.0", ".1E0.0", ".2E0.0", ".2E0.0", ".5E0.0", ".5E0.0", ".6E0.0", ".6E0.0", ".9E0.0", ".9E0.0", ".1E1.0", ".1E1.0", "-.1E0.0", "-.1E0.0", "-.2E0.0", "-.2E0.0", "-.5E0.0", "-.5E0.0", "-.6E0.0", "-.6E0.0", "-.9E0.0", "-.9E0.0", "-.1E1.0", "-.1E1.0", ".3E1.0", ".5E1.0", ".4E1.0", ".5E1.0", ".4E1.0", "-.3E1.0", "-.5E1.0", "-.4E1.0", "-.5E1.0", "-.4E1.0"]);
		check_numeric_format(".0E-.0.", numbers,
				[".0E0.0.", ".1E1.0.", ".1E2.0.", ".2E2.0.", ".2E2.0.", ".2E3.0.", ".2E3.0.", ".3E3.0.", ".3E3.0.", ".1E4.0.", "-.1E1.0.", "-.1E2.0.", "-.2E2.0.", "-.2E2.0.", "-.2E3.0.", "-.2E3.0.", "-.3E3.0.", "-.3E3.0.", "-.1E5.0.", ".1E0.0.", ".1E0.0.", ".2E0.0.", ".2E0.0.", ".5E0.0.", ".5E0.0.", ".6E0.0.", ".6E0.0.", ".9E0.0.", ".9E0.0.", ".1E1.0.", ".1E1.0.", "-.1E0.0.", "-.1E0.0.", "-.2E0.0.", "-.2E0.0.", "-.5E0.0.", "-.5E0.0.", "-.6E0.0.", "-.6E0.0.", "-.9E0.0.", "-.9E0.0.", "-.1E1.0.", "-.1E1.0.", ".3E1.0.", ".5E1.0.", ".4E1.0.", ".5E1.0.", ".4E1.0.", "-.3E1.0.", "-.5E1.0.", "-.4E1.0.", "-.5E1.0.", "-.4E1.0."]);
		check_numeric_format(".0!0E-0", numbers,
				[".0!0E0", ".1!0E1", ".1!1E2", ".1!5E2", ".1!9E2", ".2!0E3", ".2!2E3", ".2!5E3", ".2!8E3", ".1!2E4", "-.1!0E1", "-.1!1E2", "-.1!5E2", "-.1!9E2", "-.2!0E3", "-.2!2E3", "-.2!5E3", "-.2!8E3", "-.9!9E4", ".1!0E0", ".1!2E0", ".1!5E0", ".1!8E0", ".5!0E0", ".5!3E0", ".5!6E0", ".5!8E0", ".9!0E0", ".9!4E0", ".9!6E0", ".9!7E0", "-.1!0E0", "-.1!2E0", "-.1!5E0", "-.1!8E0", "-.5!0E0", "-.5!3E0", "-.5!6E0", "-.5!8E0", "-.9!0E0", "-.9!4E0", "-.9!6E0", "-.9!7E0", ".3!4E1", ".4!6E1", ".4!3E1", ".4!6E1", ".4!3E1", "-.3!4E1", "-.4!6E1", "-.4!3E1", "-.4!6E1", "-.4!3E1"]);
		check_numeric_format(".!00E-0", numbers,
				[".!00E0", ".!10E1", ".!11E2", ".!15E2", ".!19E2", ".!20E3", ".!22E3", ".!25E3", ".!28E3", ".!12E4", "-.!10E1", "-.!11E2", "-.!15E2", "-.!19E2", "-.!20E3", "-.!22E3", "-.!25E3", "-.!28E3", "-.!99E4", ".!10E0", ".!12E0", ".!15E0", ".!18E0", ".!50E0", ".!53E0", ".!56E0", ".!58E0", ".!90E0", ".!94E0", ".!96E0", ".!97E0", "-.!10E0", "-.!12E0", "-.!15E0", "-.!18E0", "-.!50E0", "-.!53E0", "-.!56E0", "-.!58E0", "-.!90E0", "-.!94E0", "-.!96E0", "-.!97E0", ".!34E1", ".!46E1", ".!43E1", ".!46E1", ".!43E1", "-.!34E1", "-.!46E1", "-.!43E1", "-.!46E1", "-.!43E1"]);
		check_numeric_format(".!0!0E-0", numbers,
				[".!0!0E0", ".!1!0E1", ".!1!1E2", ".!1!5E2", ".!1!9E2", ".!2!0E3", ".!2!2E3", ".!2!5E3", ".!2!8E3", ".!1!2E4", "-.!1!0E1", "-.!1!1E2", "-.!1!5E2", "-.!1!9E2", "-.!2!0E3", "-.!2!2E3", "-.!2!5E3", "-.!2!8E3", "-.!9!9E4", ".!1!0E0", ".!1!2E0", ".!1!5E0", ".!1!8E0", ".!5!0E0", ".!5!3E0", ".!5!6E0", ".!5!8E0", ".!9!0E0", ".!9!4E0", ".!9!6E0", ".!9!7E0", "-.!1!0E0", "-.!1!2E0", "-.!1!5E0", "-.!1!8E0", "-.!5!0E0", "-.!5!3E0", "-.!5!6E0", "-.!5!8E0", "-.!9!0E0", "-.!9!4E0", "-.!9!6E0", "-.!9!7E0", ".!3!4E1", ".!4!6E1", ".!4!3E1", ".!4!6E1", ".!4!3E1", "-.!3!4E1", "-.!4!6E1", "-.!4!3E1", "-.!4!6E1", "-.!4!3E1"]);
		check_numeric_format(".0E-0!", numbers,
				[".0E0!", ".1E1!", ".1E2!", ".2E2!", ".2E2!", ".2E3!", ".2E3!", ".3E3!", ".3E3!", ".1E4!", "-.1E1!", "-.1E2!", "-.2E2!", "-.2E2!", "-.2E3!", "-.2E3!", "-.3E3!", "-.3E3!", "-.1E5!", ".1E0!", ".1E0!", ".2E0!", ".2E0!", ".5E0!", ".5E0!", ".6E0!", ".6E0!", ".9E0!", ".9E0!", ".1E1!", ".1E1!", "-.1E0!", "-.1E0!", "-.2E0!", "-.2E0!", "-.5E0!", "-.5E0!", "-.6E0!", "-.6E0!", "-.9E0!", "-.9E0!", "-.1E1!", "-.1E1!", ".3E1!", ".5E1!", ".4E1!", ".5E1!", ".4E1!", "-.3E1!", "-.5E1!", "-.4E1!", "-.5E1!", "-.4E1!"]);
		check_numeric_format(".0\\E!-0", numbers,
				[".0E!0", ".1E!1", ".1E!2", ".2E!2", ".2E!2", ".2E!3", ".2E!3", ".3E!3", ".3E!3", ".1E!4", "-.1E!1", "-.1E!2", "-.2E!2", "-.2E!2", "-.2E!3", "-.2E!3", "-.3E!3", "-.3E!3", "-.1E!5", ".1E!0", ".1E!0", ".2E!0", ".2E!0", ".5E!0", ".5E!0", ".6E!0", ".6E!0", ".9E!0", ".9E!0", ".1E!1", ".1E!1", "-.1E!0", "-.1E!0", "-.2E!0", "-.2E!0", "-.5E!0", "-.5E!0", "-.6E!0", "-.6E!0", "-.9E!0", "-.9E!0", "-.1E!1", "-.1E!1", ".3E!1", ".5E!1", ".4E!1", ".5E!1", ".4E!1", "-.3E!1", "-.5E!1", "-.4E!1", "-.5E!1", "-.4E!1"]);
		check_numeric_format(".0\\E!-0!", numbers,
				[".0E!0!", ".1E!1!", ".1E!2!", ".2E!2!", ".2E!2!", ".2E!3!", ".2E!3!", ".3E!3!", ".3E!3!", ".1E!4!", "-.1E!1!", "-.1E!2!", "-.2E!2!", "-.2E!2!", "-.2E!3!", "-.2E!3!", "-.3E!3!", "-.3E!3!", "-.1E!5!", ".1E!0!", ".1E!0!", ".2E!0!", ".2E!0!", ".5E!0!", ".5E!0!", ".6E!0!", ".6E!0!", ".9E!0!", ".9E!0!", ".1E!1!", ".1E!1!", "-.1E!0!", "-.1E!0!", "-.2E!0!", "-.2E!0!", "-.5E!0!", "-.5E!0!", "-.6E!0!", "-.6E!0!", "-.9E!0!", "-.9E!0!", "-.1E!1!", "-.1E!1!", ".3E!1!", ".5E!1!", ".4E!1!", ".5E!1!", ".4E!1!", "-.3E!1!", "-.5E!1!", "-.4E!1!", "-.5E!1!", "-.4E!1!"]);
		check_numeric_format(".00E+0", numbers,
				[".00E+0", ".10E+1", ".11E+2", ".15E+2", ".19E+2", ".20E+3", ".22E+3", ".25E+3", ".28E+3", ".12E+4", "-.10E+1", "-.11E+2", "-.15E+2", "-.19E+2", "-.20E+3", "-.22E+3", "-.25E+3", "-.28E+3", "-.99E+4", ".10E+0", ".12E+0", ".15E+0", ".18E+0", ".50E+0", ".53E+0", ".56E+0", ".58E+0", ".90E+0", ".94E+0", ".96E+0", ".97E+0", "-.10E+0", "-.12E+0", "-.15E+0", "-.18E+0", "-.50E+0", "-.53E+0", "-.56E+0", "-.58E+0", "-.90E+0", "-.94E+0", "-.96E+0", "-.97E+0", ".34E+1", ".46E+1", ".43E+1", ".46E+1", ".43E+1", "-.34E+1", "-.46E+1", "-.43E+1", "-.46E+1", "-.43E+1"]);
		check_numeric_format(".00E+0", numbers,
				[".00E+0", ".10E+1", ".11E+2", ".15E+2", ".19E+2", ".20E+3", ".22E+3", ".25E+3", ".28E+3", ".12E+4", "-.10E+1", "-.11E+2", "-.15E+2", "-.19E+2", "-.20E+3", "-.22E+3", "-.25E+3", "-.28E+3", "-.99E+4", ".10E+0", ".12E+0", ".15E+0", ".18E+0", ".50E+0", ".53E+0", ".56E+0", ".58E+0", ".90E+0", ".94E+0", ".96E+0", ".97E+0", "-.10E+0", "-.12E+0", "-.15E+0", "-.18E+0", "-.50E+0", "-.53E+0", "-.56E+0", "-.58E+0", "-.90E+0", "-.94E+0", "-.96E+0", "-.97E+0", ".34E+1", ".46E+1", ".43E+1", ".46E+1", ".43E+1", "-.34E+1", "-.46E+1", "-.43E+1", "-.46E+1", "-.43E+1"]);
		check_numeric_format(".00E+0", numbers,
				[".00E+0", ".10E+1", ".11E+2", ".15E+2", ".19E+2", ".20E+3", ".22E+3", ".25E+3", ".28E+3", ".12E+4", "-.10E+1", "-.11E+2", "-.15E+2", "-.19E+2", "-.20E+3", "-.22E+3", "-.25E+3", "-.28E+3", "-.99E+4", ".10E+0", ".12E+0", ".15E+0", ".18E+0", ".50E+0", ".53E+0", ".56E+0", ".58E+0", ".90E+0", ".94E+0", ".96E+0", ".97E+0", "-.10E+0", "-.12E+0", "-.15E+0", "-.18E+0", "-.50E+0", "-.53E+0", "-.56E+0", "-.58E+0", "-.90E+0", "-.94E+0", "-.96E+0", "-.97E+0", ".34E+1", ".46E+1", ".43E+1", ".46E+1", ".43E+1", "-.34E+1", "-.46E+1", "-.43E+1", "-.46E+1", "-.43E+1"]);
		check_numeric_format(".0,E+0", numbers,
				[".0E+0", ".1E+1", ".1E+2", ".2E+2", ".2E+2", ".2E+3", ".2E+3", ".3E+3", ".3E+3", ".1E+4", "-.1E+1", "-.1E+2", "-.2E+2", "-.2E+2", "-.2E+3", "-.2E+3", "-.3E+3", "-.3E+3", "-.1E+5", ".1E+0", ".1E+0", ".2E+0", ".2E+0", ".5E+0", ".5E+0", ".6E+0", ".6E+0", ".9E+0", ".9E+0", ".1E+1", ".1E+1", "-.1E+0", "-.1E+0", "-.2E+0", "-.2E+0", "-.5E+0", "-.5E+0", "-.6E+0", "-.6E+0", "-.9E+0", "-.9E+0", "-.1E+1", "-.1E+1", ".3E+1", ".5E+1", ".4E+1", ".5E+1", ".4E+1", "-.3E+1", "-.5E+1", "-.4E+1", "-.5E+1", "-.4E+1"]);
		check_numeric_format(".0\\E,+0", numbers,
				[".0E,+0", ".1E,+1", ".1E,+2", ".2E,+2", ".2E,+2", ".2E,+3", ".2E,+3", ".3E,+3", ".3E,+3", ".1E,+4", "-.1E,+1", "-.1E,+2", "-.2E,+2", "-.2E,+2", "-.2E,+3", "-.2E,+3", "-.3E,+3", "-.3E,+3", "-.1E,+5", ".1E,+0", ".1E,+0", ".2E,+0", ".2E,+0", ".5E,+0", ".5E,+0", ".6E,+0", ".6E,+0", ".9E,+0", ".9E,+0", ".1E,+1", ".1E,+1", "-.1E,+0", "-.1E,+0", "-.2E,+0", "-.2E,+0", "-.5E,+0", "-.5E,+0", "-.6E,+0", "-.6E,+0", "-.9E,+0", "-.9E,+0", "-.1E,+1", "-.1E,+1", ".3E,+1", ".5E,+1", ".4E,+1", ".5E,+1", ".4E,+1", "-.3E,+1", "-.5E,+1", "-.4E,+1", "-.5E,+1", "-.4E,+1"]);
		check_numeric_format(".0,\\E,+0", numbers,
				[".0E,+0", ".1E,+1", ".1E,+2", ".2E,+2", ".2E,+2", ".2E,+3", ".2E,+3", ".3E,+3", ".3E,+3", ".1E,+4", "-.1E,+1", "-.1E,+2", "-.2E,+2", "-.2E,+2", "-.2E,+3", "-.2E,+3", "-.3E,+3", "-.3E,+3", "-.1E,+5", ".1E,+0", ".1E,+0", ".2E,+0", ".2E,+0", ".5E,+0", ".5E,+0", ".6E,+0", ".6E,+0", ".9E,+0", ".9E,+0", ".1E,+1", ".1E,+1", "-.1E,+0", "-.1E,+0", "-.2E,+0", "-.2E,+0", "-.5E,+0", "-.5E,+0", "-.6E,+0", "-.6E,+0", "-.9E,+0", "-.9E,+0", "-.1E,+1", "-.1E,+1", ".3E,+1", ".5E,+1", ".4E,+1", ".5E,+1", ".4E,+1", "-.3E,+1", "-.5E,+1", "-.4E,+1", "-.5E,+1", "-.4E,+1"]);
		check_numeric_format(".0.0E+0", numbers,
				[".0.0E+0", ".1.0E+1", ".1.1E+2", ".1.5E+2", ".1.9E+2", ".2.0E+3", ".2.2E+3", ".2.5E+3", ".2.8E+3", ".1.2E+4", "-.1.0E+1", "-.1.1E+2", "-.1.5E+2", "-.1.9E+2", "-.2.0E+3", "-.2.2E+3", "-.2.5E+3", "-.2.8E+3", "-.9.9E+4", ".1.0E+0", ".1.2E+0", ".1.5E+0", ".1.8E+0", ".5.0E+0", ".5.3E+0", ".5.6E+0", ".5.8E+0", ".9.0E+0", ".9.4E+0", ".9.6E+0", ".9.7E+0", "-.1.0E+0", "-.1.2E+0", "-.1.5E+0", "-.1.8E+0", "-.5.0E+0", "-.5.3E+0", "-.5.6E+0", "-.5.8E+0", "-.9.0E+0", "-.9.4E+0", "-.9.6E+0", "-.9.7E+0", ".3.4E+1", ".4.6E+1", ".4.3E+1", ".4.6E+1", ".4.3E+1", "-.3.4E+1", "-.4.6E+1", "-.4.3E+1", "-.4.6E+1", "-.4.3E+1"]);
		check_numeric_format("..00E+0", numbers,
				["..00E+0", "..10E+1", "..11E+2", "..15E+2", "..19E+2", "..20E+3", "..22E+3", "..25E+3", "..28E+3", "..12E+4", "-..10E+1", "-..11E+2", "-..15E+2", "-..19E+2", "-..20E+3", "-..22E+3", "-..25E+3", "-..28E+3", "-..99E+4", "..10E+0", "..12E+0", "..15E+0", "..18E+0", "..50E+0", "..53E+0", "..56E+0", "..58E+0", "..90E+0", "..94E+0", "..96E+0", "..97E+0", "-..10E+0", "-..12E+0", "-..15E+0", "-..18E+0", "-..50E+0", "-..53E+0", "-..56E+0", "-..58E+0", "-..90E+0", "-..94E+0", "-..96E+0", "-..97E+0", "..34E+1", "..46E+1", "..43E+1", "..46E+1", "..43E+1", "-..34E+1", "-..46E+1", "-..43E+1", "-..46E+1", "-..43E+1"]);
		check_numeric_format("..0.0E+0", numbers,
				["..0.0E+0", "..1.0E+1", "..1.1E+2", "..1.5E+2", "..1.9E+2", "..2.0E+3", "..2.2E+3", "..2.5E+3", "..2.8E+3", "..1.2E+4", "-..1.0E+1", "-..1.1E+2", "-..1.5E+2", "-..1.9E+2", "-..2.0E+3", "-..2.2E+3", "-..2.5E+3", "-..2.8E+3", "-..9.9E+4", "..1.0E+0", "..1.2E+0", "..1.5E+0", "..1.8E+0", "..5.0E+0", "..5.3E+0", "..5.6E+0", "..5.8E+0", "..9.0E+0", "..9.4E+0", "..9.6E+0", "..9.7E+0", "-..1.0E+0", "-..1.2E+0", "-..1.5E+0", "-..1.8E+0", "-..5.0E+0", "-..5.3E+0", "-..5.6E+0", "-..5.8E+0", "-..9.0E+0", "-..9.4E+0", "-..9.6E+0", "-..9.7E+0", "..3.4E+1", "..4.6E+1", "..4.3E+1", "..4.6E+1", "..4.3E+1", "-..3.4E+1", "-..4.6E+1", "-..4.3E+1", "-..4.6E+1", "-..4.3E+1"]);
		check_numeric_format(".0E+0.", numbers,
				[".0E+0.", ".1E+1.", ".1E+2.", ".2E+2.", ".2E+2.", ".2E+3.", ".2E+3.", ".3E+3.", ".3E+3.", ".1E+4.", "-.1E+1.", "-.1E+2.", "-.2E+2.", "-.2E+2.", "-.2E+3.", "-.2E+3.", "-.3E+3.", "-.3E+3.", "-.1E+5.", ".1E+0.", ".1E+0.", ".2E+0.", ".2E+0.", ".5E+0.", ".5E+0.", ".6E+0.", ".6E+0.", ".9E+0.", ".9E+0.", ".1E+1.", ".1E+1.", "-.1E+0.", "-.1E+0.", "-.2E+0.", "-.2E+0.", "-.5E+0.", "-.5E+0.", "-.6E+0.", "-.6E+0.", "-.9E+0.", "-.9E+0.", "-.1E+1.", "-.1E+1.", ".3E+1.", ".5E+1.", ".4E+1.", ".5E+1.", ".4E+1.", "-.3E+1.", "-.5E+1.", "-.4E+1.", "-.5E+1.", "-.4E+1."]);
		check_numeric_format(".0E+.0", numbers,
				[".0E+0.0", ".1E+1.0", ".1E+2.0", ".2E+2.0", ".2E+2.0", ".2E+3.0", ".2E+3.0", ".3E+3.0", ".3E+3.0", ".1E+4.0", "-.1E+1.0", "-.1E+2.0", "-.2E+2.0", "-.2E+2.0", "-.2E+3.0", "-.2E+3.0", "-.3E+3.0", "-.3E+3.0", "-.1E+5.0", ".1E+0.0", ".1E+0.0", ".2E+0.0", ".2E+0.0", ".5E+0.0", ".5E+0.0", ".6E+0.0", ".6E+0.0", ".9E+0.0", ".9E+0.0", ".1E+1.0", ".1E+1.0", "-.1E+0.0", "-.1E+0.0", "-.2E+0.0", "-.2E+0.0", "-.5E+0.0", "-.5E+0.0", "-.6E+0.0", "-.6E+0.0", "-.9E+0.0", "-.9E+0.0", "-.1E+1.0", "-.1E+1.0", ".3E+1.0", ".5E+1.0", ".4E+1.0", ".5E+1.0", ".4E+1.0", "-.3E+1.0", "-.5E+1.0", "-.4E+1.0", "-.5E+1.0", "-.4E+1.0"]);
		check_numeric_format(".0E+.0.", numbers,
				[".0E+0.0.", ".1E+1.0.", ".1E+2.0.", ".2E+2.0.", ".2E+2.0.", ".2E+3.0.", ".2E+3.0.", ".3E+3.0.", ".3E+3.0.", ".1E+4.0.", "-.1E+1.0.", "-.1E+2.0.", "-.2E+2.0.", "-.2E+2.0.", "-.2E+3.0.", "-.2E+3.0.", "-.3E+3.0.", "-.3E+3.0.", "-.1E+5.0.", ".1E+0.0.", ".1E+0.0.", ".2E+0.0.", ".2E+0.0.", ".5E+0.0.", ".5E+0.0.", ".6E+0.0.", ".6E+0.0.", ".9E+0.0.", ".9E+0.0.", ".1E+1.0.", ".1E+1.0.", "-.1E+0.0.", "-.1E+0.0.", "-.2E+0.0.", "-.2E+0.0.", "-.5E+0.0.", "-.5E+0.0.", "-.6E+0.0.", "-.6E+0.0.", "-.9E+0.0.", "-.9E+0.0.", "-.1E+1.0.", "-.1E+1.0.", ".3E+1.0.", ".5E+1.0.", ".4E+1.0.", ".5E+1.0.", ".4E+1.0.", "-.3E+1.0.", "-.5E+1.0.", "-.4E+1.0.", "-.5E+1.0.", "-.4E+1.0."]);
		check_numeric_format(".0!0E+0", numbers,
				[".0!0E+0", ".1!0E+1", ".1!1E+2", ".1!5E+2", ".1!9E+2", ".2!0E+3", ".2!2E+3", ".2!5E+3", ".2!8E+3", ".1!2E+4", "-.1!0E+1", "-.1!1E+2", "-.1!5E+2", "-.1!9E+2", "-.2!0E+3", "-.2!2E+3", "-.2!5E+3", "-.2!8E+3", "-.9!9E+4", ".1!0E+0", ".1!2E+0", ".1!5E+0", ".1!8E+0", ".5!0E+0", ".5!3E+0", ".5!6E+0", ".5!8E+0", ".9!0E+0", ".9!4E+0", ".9!6E+0", ".9!7E+0", "-.1!0E+0", "-.1!2E+0", "-.1!5E+0", "-.1!8E+0", "-.5!0E+0", "-.5!3E+0", "-.5!6E+0", "-.5!8E+0", "-.9!0E+0", "-.9!4E+0", "-.9!6E+0", "-.9!7E+0", ".3!4E+1", ".4!6E+1", ".4!3E+1", ".4!6E+1", ".4!3E+1", "-.3!4E+1", "-.4!6E+1", "-.4!3E+1", "-.4!6E+1", "-.4!3E+1"]);
		check_numeric_format(".!00E+0", numbers,
				[".!00E+0", ".!10E+1", ".!11E+2", ".!15E+2", ".!19E+2", ".!20E+3", ".!22E+3", ".!25E+3", ".!28E+3", ".!12E+4", "-.!10E+1", "-.!11E+2", "-.!15E+2", "-.!19E+2", "-.!20E+3", "-.!22E+3", "-.!25E+3", "-.!28E+3", "-.!99E+4", ".!10E+0", ".!12E+0", ".!15E+0", ".!18E+0", ".!50E+0", ".!53E+0", ".!56E+0", ".!58E+0", ".!90E+0", ".!94E+0", ".!96E+0", ".!97E+0", "-.!10E+0", "-.!12E+0", "-.!15E+0", "-.!18E+0", "-.!50E+0", "-.!53E+0", "-.!56E+0", "-.!58E+0", "-.!90E+0", "-.!94E+0", "-.!96E+0", "-.!97E+0", ".!34E+1", ".!46E+1", ".!43E+1", ".!46E+1", ".!43E+1", "-.!34E+1", "-.!46E+1", "-.!43E+1", "-.!46E+1", "-.!43E+1"]);
		check_numeric_format(".!0!0E+0", numbers,
				[".!0!0E+0", ".!1!0E+1", ".!1!1E+2", ".!1!5E+2", ".!1!9E+2", ".!2!0E+3", ".!2!2E+3", ".!2!5E+3", ".!2!8E+3", ".!1!2E+4", "-.!1!0E+1", "-.!1!1E+2", "-.!1!5E+2", "-.!1!9E+2", "-.!2!0E+3", "-.!2!2E+3", "-.!2!5E+3", "-.!2!8E+3", "-.!9!9E+4", ".!1!0E+0", ".!1!2E+0", ".!1!5E+0", ".!1!8E+0", ".!5!0E+0", ".!5!3E+0", ".!5!6E+0", ".!5!8E+0", ".!9!0E+0", ".!9!4E+0", ".!9!6E+0", ".!9!7E+0", "-.!1!0E+0", "-.!1!2E+0", "-.!1!5E+0", "-.!1!8E+0", "-.!5!0E+0", "-.!5!3E+0", "-.!5!6E+0", "-.!5!8E+0", "-.!9!0E+0", "-.!9!4E+0", "-.!9!6E+0", "-.!9!7E+0", ".!3!4E+1", ".!4!6E+1", ".!4!3E+1", ".!4!6E+1", ".!4!3E+1", "-.!3!4E+1", "-.!4!6E+1", "-.!4!3E+1", "-.!4!6E+1", "-.!4!3E+1"]);
		check_numeric_format(".0E+0!", numbers,
				[".0E+0!", ".1E+1!", ".1E+2!", ".2E+2!", ".2E+2!", ".2E+3!", ".2E+3!", ".3E+3!", ".3E+3!", ".1E+4!", "-.1E+1!", "-.1E+2!", "-.2E+2!", "-.2E+2!", "-.2E+3!", "-.2E+3!", "-.3E+3!", "-.3E+3!", "-.1E+5!", ".1E+0!", ".1E+0!", ".2E+0!", ".2E+0!", ".5E+0!", ".5E+0!", ".6E+0!", ".6E+0!", ".9E+0!", ".9E+0!", ".1E+1!", ".1E+1!", "-.1E+0!", "-.1E+0!", "-.2E+0!", "-.2E+0!", "-.5E+0!", "-.5E+0!", "-.6E+0!", "-.6E+0!", "-.9E+0!", "-.9E+0!", "-.1E+1!", "-.1E+1!", ".3E+1!", ".5E+1!", ".4E+1!", ".5E+1!", ".4E+1!", "-.3E+1!", "-.5E+1!", "-.4E+1!", "-.5E+1!", "-.4E+1!"]);
		check_numeric_format(".0\\E!+0", numbers,
				[".0E!+0", ".1E!+1", ".1E!+2", ".2E!+2", ".2E!+2", ".2E!+3", ".2E!+3", ".3E!+3", ".3E!+3", ".1E!+4", "-.1E!+1", "-.1E!+2", "-.2E!+2", "-.2E!+2", "-.2E!+3", "-.2E!+3", "-.3E!+3", "-.3E!+3", "-.1E!+5", ".1E!+0", ".1E!+0", ".2E!+0", ".2E!+0", ".5E!+0", ".5E!+0", ".6E!+0", ".6E!+0", ".9E!+0", ".9E!+0", ".1E!+1", ".1E!+1", "-.1E!+0", "-.1E!+0", "-.2E!+0", "-.2E!+0", "-.5E!+0", "-.5E!+0", "-.6E!+0", "-.6E!+0", "-.9E!+0", "-.9E!+0", "-.1E!+1", "-.1E!+1", ".3E!+1", ".5E!+1", ".4E!+1", ".5E!+1", ".4E!+1", "-.3E!+1", "-.5E!+1", "-.4E!+1", "-.5E!+1", "-.4E!+1"]);
		check_numeric_format(".0\\E!+0!", numbers,
				[".0E!+0!", ".1E!+1!", ".1E!+2!", ".2E!+2!", ".2E!+2!", ".2E!+3!", ".2E!+3!", ".3E!+3!", ".3E!+3!", ".1E!+4!", "-.1E!+1!", "-.1E!+2!", "-.2E!+2!", "-.2E!+2!", "-.2E!+3!", "-.2E!+3!", "-.3E!+3!", "-.3E!+3!", "-.1E!+5!", ".1E!+0!", ".1E!+0!", ".2E!+0!", ".2E!+0!", ".5E!+0!", ".5E!+0!", ".6E!+0!", ".6E!+0!", ".9E!+0!", ".9E!+0!", ".1E!+1!", ".1E!+1!", "-.1E!+0!", "-.1E!+0!", "-.2E!+0!", "-.2E!+0!", "-.5E!+0!", "-.5E!+0!", "-.6E!+0!", "-.6E!+0!", "-.9E!+0!", "-.9E!+0!", "-.1E!+1!", "-.1E!+1!", ".3E!+1!", ".5E!+1!", ".4E!+1!", ".5E!+1!", ".4E!+1!", "-.3E!+1!", "-.5E!+1!", "-.4E!+1!", "-.5E!+1!", "-.4E!+1!"]);
		check_numeric_format("#,#00.0E-0", numbers,
				["0,000.0E0", "01.0E0", "11.0E0", "15.0E0", "19.0E0", "200.0E0", "220.0E0", "250.0E0", "280.0E0", "1,234.0E0", "-01.0E0", "-11.0E0", "-15.0E0", "-19.0E0", "-200.0E0", "-220.0E0", "-250.0E0", "-280.0E0", "-9,876.0E0", "1,000.0E-4", "1,200.0E-4", "1,510.0E-4", "1,810.0E-4", "5,000.0E-4", "5,300.0E-4", "5,550.0E-4", "5,750.0E-4", "9,000.0E-4", "9,400.0E-4", "9,590.0E-4", "9,690.0E-4", "-1,000.0E-4", "-1,200.0E-4", "-1,510.0E-4", "-1,810.0E-4", "-5,000.0E-4", "-5,300.0E-4", "-5,550.0E-4", "-5,750.0E-4", "-9,000.0E-4", "-9,400.0E-4", "-9,590.0E-4", "-9,690.0E-4", "03.4E0", "04.6E0", "04.3E0", "04.6E0", "04.3E0", "-03.4E0", "-04.6E0", "-04.3E0", "-04.6E0", "-04.3E0"]);
		check_numeric_format(",00.0E-0", numbers,
				[",00.0E0", ",01.0E0", ",11.0E0", ",15.0E0", ",19.0E0", ",02.0E2", ",02.2E2", ",02.5E2", ",02.8E2", ",12.3E2", "-,01.0E0", "-,11.0E0", "-,15.0E0", "-,19.0E0", "-,02.0E2", "-,02.2E2", "-,02.5E2", "-,02.8E2", "-,98.8E2", ",10.0E-2", ",12.0E-2", ",15.1E-2", ",18.1E-2", ",50.0E-2", ",53.0E-2", ",55.5E-2", ",57.5E-2", ",90.0E-2", ",94.0E-2", ",95.9E-2", ",96.9E-2", "-,10.0E-2", "-,12.0E-2", "-,15.1E-2", "-,18.1E-2", "-,50.0E-2", "-,53.0E-2", "-,55.5E-2", "-,57.5E-2", "-,90.0E-2", "-,94.0E-2", "-,95.9E-2", "-,96.9E-2", ",03.4E0", ",04.6E0", ",04.3E0", ",04.6E0", ",04.3E0", "-,03.4E0", "-,04.6E0", "-,04.3E0", "-,04.6E0", "-,04.3E0"]);
		check_numeric_format(",#,#00.0E-0", numbers,
				[",0,000.0E0", ",01.0E0", ",11.0E0", ",15.0E0", ",19.0E0", ",200.0E0", ",220.0E0", ",250.0E0", ",280.0E0", ",1,234.0E0", "-,01.0E0", "-,11.0E0", "-,15.0E0", "-,19.0E0", "-,200.0E0", "-,220.0E0", "-,250.0E0", "-,280.0E0", "-,9,876.0E0", ",1,000.0E-4", ",1,200.0E-4", ",1,510.0E-4", ",1,810.0E-4", ",5,000.0E-4", ",5,300.0E-4", ",5,550.0E-4", ",5,750.0E-4", ",9,000.0E-4", ",9,400.0E-4", ",9,590.0E-4", ",9,690.0E-4", "-,1,000.0E-4", "-,1,200.0E-4", "-,1,510.0E-4", "-,1,810.0E-4", "-,5,000.0E-4", "-,5,300.0E-4", "-,5,550.0E-4", "-,5,750.0E-4", "-,9,000.0E-4", "-,9,400.0E-4", "-,9,590.0E-4", "-,9,690.0E-4", ",03.4E0", ",04.6E0", ",04.3E0", ",04.6E0", ",04.3E0", "-,03.4E0", "-,04.6E0", "-,04.3E0", "-,04.6E0", "-,04.3E0"]);
		check_numeric_format("0.00E-0", numbers,
				["0.00E0", "1.00E0", "1.10E1", "1.50E1", "1.90E1", "2.00E2", "2.20E2", "2.50E2", "2.80E2", "1.23E3", "-1.00E0", "-1.10E1", "-1.50E1", "-1.90E1", "-2.00E2", "-2.20E2", "-2.50E2", "-2.80E2", "-9.88E3", "1.00E-1", "1.20E-1", "1.51E-1", "1.81E-1", "5.00E-1", "5.30E-1", "5.55E-1", "5.75E-1", "9.00E-1", "9.40E-1", "9.59E-1", "9.69E-1", "-1.00E-1", "-1.20E-1", "-1.51E-1", "-1.81E-1", "-5.00E-1", "-5.30E-1", "-5.55E-1", "-5.75E-1", "-9.00E-1", "-9.40E-1", "-9.59E-1", "-9.69E-1", "3.40E0", "4.56E0", "4.32E0", "4.57E0", "4.32E0", "-3.40E0", "-4.56E0", "-4.32E0", "-4.57E0", "-4.32E0"]);
		check_numeric_format("0.00E-0", numbers,
				["0.00E0", "1.00E0", "1.10E1", "1.50E1", "1.90E1", "2.00E2", "2.20E2", "2.50E2", "2.80E2", "1.23E3", "-1.00E0", "-1.10E1", "-1.50E1", "-1.90E1", "-2.00E2", "-2.20E2", "-2.50E2", "-2.80E2", "-9.88E3", "1.00E-1", "1.20E-1", "1.51E-1", "1.81E-1", "5.00E-1", "5.30E-1", "5.55E-1", "5.75E-1", "9.00E-1", "9.40E-1", "9.59E-1", "9.69E-1", "-1.00E-1", "-1.20E-1", "-1.51E-1", "-1.81E-1", "-5.00E-1", "-5.30E-1", "-5.55E-1", "-5.75E-1", "-9.00E-1", "-9.40E-1", "-9.59E-1", "-9.69E-1", "3.40E0", "4.56E0", "4.32E0", "4.57E0", "4.32E0", "-3.40E0", "-4.56E0", "-4.32E0", "-4.57E0", "-4.32E0"]);
		check_numeric_format("0.00E-0", numbers,
				["0.00E0", "1.00E0", "1.10E1", "1.50E1", "1.90E1", "2.00E2", "2.20E2", "2.50E2", "2.80E2", "1.23E3", "-1.00E0", "-1.10E1", "-1.50E1", "-1.90E1", "-2.00E2", "-2.20E2", "-2.50E2", "-2.80E2", "-9.88E3", "1.00E-1", "1.20E-1", "1.51E-1", "1.81E-1", "5.00E-1", "5.30E-1", "5.55E-1", "5.75E-1", "9.00E-1", "9.40E-1", "9.59E-1", "9.69E-1", "-1.00E-1", "-1.20E-1", "-1.51E-1", "-1.81E-1", "-5.00E-1", "-5.30E-1", "-5.55E-1", "-5.75E-1", "-9.00E-1", "-9.40E-1", "-9.59E-1", "-9.69E-1", "3.40E0", "4.56E0", "4.32E0", "4.57E0", "4.32E0", "-3.40E0", "-4.56E0", "-4.32E0", "-4.57E0", "-4.32E0"]);
		check_numeric_format("0.0,E-0", numbers,
				["0.0E0", "1.0E0", "1.1E1", "1.5E1", "1.9E1", "2.0E2", "2.2E2", "2.5E2", "2.8E2", "1.2E3", "-1.0E0", "-1.1E1", "-1.5E1", "-1.9E1", "-2.0E2", "-2.2E2", "-2.5E2", "-2.8E2", "-9.9E3", "1.0E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.0E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.0E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.0E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.0E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.0E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E0", "4.6E0", "4.3E0", "4.6E0", "4.3E0", "-3.4E0", "-4.6E0", "-4.3E0", "-4.6E0", "-4.3E0"]);
		check_numeric_format("0.0\\E,-0", numbers,
				["0.0E,0", "1.0E,0", "1.1E,1", "1.5E,1", "1.9E,1", "2.0E,2", "2.2E,2", "2.5E,2", "2.8E,2", "1.2E,3", "-1.0E,0", "-1.1E,1", "-1.5E,1", "-1.9E,1", "-2.0E,2", "-2.2E,2", "-2.5E,2", "-2.8E,2", "-9.9E,3", "1.0E,-1", "1.2E,-1", "1.5E,-1", "1.8E,-1", "5.0E,-1", "5.3E,-1", "5.6E,-1", "5.8E,-1", "9.0E,-1", "9.4E,-1", "9.6E,-1", "9.7E,-1", "-1.0E,-1", "-1.2E,-1", "-1.5E,-1", "-1.8E,-1", "-5.0E,-1", "-5.3E,-1", "-5.6E,-1", "-5.8E,-1", "-9.0E,-1", "-9.4E,-1", "-9.6E,-1", "-9.7E,-1", "3.4E,0", "4.6E,0", "4.3E,0", "4.6E,0", "4.3E,0", "-3.4E,0", "-4.6E,0", "-4.3E,0", "-4.6E,0", "-4.3E,0"]);
		check_numeric_format("0.0,\\E,-0", numbers,
				["0.0E,0", "1.0E,0", "1.1E,1", "1.5E,1", "1.9E,1", "2.0E,2", "2.2E,2", "2.5E,2", "2.8E,2", "1.2E,3", "-1.0E,0", "-1.1E,1", "-1.5E,1", "-1.9E,1", "-2.0E,2", "-2.2E,2", "-2.5E,2", "-2.8E,2", "-9.9E,3", "1.0E,-1", "1.2E,-1", "1.5E,-1", "1.8E,-1", "5.0E,-1", "5.3E,-1", "5.6E,-1", "5.8E,-1", "9.0E,-1", "9.4E,-1", "9.6E,-1", "9.7E,-1", "-1.0E,-1", "-1.2E,-1", "-1.5E,-1", "-1.8E,-1", "-5.0E,-1", "-5.3E,-1", "-5.6E,-1", "-5.8E,-1", "-9.0E,-1", "-9.4E,-1", "-9.6E,-1", "-9.7E,-1", "3.4E,0", "4.6E,0", "4.3E,0", "4.6E,0", "4.3E,0", "-3.4E,0", "-4.6E,0", "-4.3E,0", "-4.6E,0", "-4.3E,0"]);
		check_numeric_format("0.0.0E-0", numbers,
				["0.0.0E0", "1.0.0E0", "1.1.0E1", "1.5.0E1", "1.9.0E1", "2.0.0E2", "2.2.0E2", "2.5.0E2", "2.8.0E2", "1.2.3E3", "-1.0.0E0", "-1.1.0E1", "-1.5.0E1", "-1.9.0E1", "-2.0.0E2", "-2.2.0E2", "-2.5.0E2", "-2.8.0E2", "-9.8.8E3", "1.0.0E-1", "1.2.0E-1", "1.5.1E-1", "1.8.1E-1", "5.0.0E-1", "5.3.0E-1", "5.5.5E-1", "5.7.5E-1", "9.0.0E-1", "9.4.0E-1", "9.5.9E-1", "9.6.9E-1", "-1.0.0E-1", "-1.2.0E-1", "-1.5.1E-1", "-1.8.1E-1", "-5.0.0E-1", "-5.3.0E-1", "-5.5.5E-1", "-5.7.5E-1", "-9.0.0E-1", "-9.4.0E-1", "-9.5.9E-1", "-9.6.9E-1", "3.4.0E0", "4.5.6E0", "4.3.2E0", "4.5.7E0", "4.3.2E0", "-3.4.0E0", "-4.5.6E0", "-4.3.2E0", "-4.5.7E0", "-4.3.2E0"]);
		check_numeric_format(".00.0E-0", numbers,
				[".00.0E0", ".10.0E1", ".11.0E2", ".15.0E2", ".19.0E2", ".20.0E3", ".22.0E3", ".25.0E3", ".28.0E3", ".12.3E4", "-.10.0E1", "-.11.0E2", "-.15.0E2", "-.19.0E2", "-.20.0E3", "-.22.0E3", "-.25.0E3", "-.28.0E3", "-.98.8E4", ".10.0E0", ".12.0E0", ".15.1E0", ".18.1E0", ".50.0E0", ".53.0E0", ".55.5E0", ".57.5E0", ".90.0E0", ".94.0E0", ".95.9E0", ".96.9E0", "-.10.0E0", "-.12.0E0", "-.15.1E0", "-.18.1E0", "-.50.0E0", "-.53.0E0", "-.55.5E0", "-.57.5E0", "-.90.0E0", "-.94.0E0", "-.95.9E0", "-.96.9E0", ".34.0E1", ".45.6E1", ".43.2E1", ".45.7E1", ".43.2E1", "-.34.0E1", "-.45.6E1", "-.43.2E1", "-.45.7E1", "-.43.2E1"]);
		check_numeric_format(".0.0.0E-0", numbers,
				[".0.0.0E0", ".1.0.0E1", ".1.1.0E2", ".1.5.0E2", ".1.9.0E2", ".2.0.0E3", ".2.2.0E3", ".2.5.0E3", ".2.8.0E3", ".1.2.3E4", "-.1.0.0E1", "-.1.1.0E2", "-.1.5.0E2", "-.1.9.0E2", "-.2.0.0E3", "-.2.2.0E3", "-.2.5.0E3", "-.2.8.0E3", "-.9.8.8E4", ".1.0.0E0", ".1.2.0E0", ".1.5.1E0", ".1.8.1E0", ".5.0.0E0", ".5.3.0E0", ".5.5.5E0", ".5.7.5E0", ".9.0.0E0", ".9.4.0E0", ".9.5.9E0", ".9.6.9E0", "-.1.0.0E0", "-.1.2.0E0", "-.1.5.1E0", "-.1.8.1E0", "-.5.0.0E0", "-.5.3.0E0", "-.5.5.5E0", "-.5.7.5E0", "-.9.0.0E0", "-.9.4.0E0", "-.9.5.9E0", "-.9.6.9E0", ".3.4.0E1", ".4.5.6E1", ".4.3.2E1", ".4.5.7E1", ".4.3.2E1", "-.3.4.0E1", "-.4.5.6E1", "-.4.3.2E1", "-.4.5.7E1", "-.4.3.2E1"]);
		check_numeric_format("0.0.0E-0", numbers,
				["0.0.0E0", "1.0.0E0", "1.1.0E1", "1.5.0E1", "1.9.0E1", "2.0.0E2", "2.2.0E2", "2.5.0E2", "2.8.0E2", "1.2.3E3", "-1.0.0E0", "-1.1.0E1", "-1.5.0E1", "-1.9.0E1", "-2.0.0E2", "-2.2.0E2", "-2.5.0E2", "-2.8.0E2", "-9.8.8E3", "1.0.0E-1", "1.2.0E-1", "1.5.1E-1", "1.8.1E-1", "5.0.0E-1", "5.3.0E-1", "5.5.5E-1", "5.7.5E-1", "9.0.0E-1", "9.4.0E-1", "9.5.9E-1", "9.6.9E-1", "-1.0.0E-1", "-1.2.0E-1", "-1.5.1E-1", "-1.8.1E-1", "-5.0.0E-1", "-5.3.0E-1", "-5.5.5E-1", "-5.7.5E-1", "-9.0.0E-1", "-9.4.0E-1", "-9.5.9E-1", "-9.6.9E-1", "3.4.0E0", "4.5.6E0", "4.3.2E0", "4.5.7E0", "4.3.2E0", "-3.4.0E0", "-4.5.6E0", "-4.3.2E0", "-4.5.7E0", "-4.3.2E0"]);
		check_numeric_format("0..00E-0", numbers,
				["0..00E0", "1..00E0", "1..10E1", "1..50E1", "1..90E1", "2..00E2", "2..20E2", "2..50E2", "2..80E2", "1..23E3", "-1..00E0", "-1..10E1", "-1..50E1", "-1..90E1", "-2..00E2", "-2..20E2", "-2..50E2", "-2..80E2", "-9..88E3", "1..00E-1", "1..20E-1", "1..51E-1", "1..81E-1", "5..00E-1", "5..30E-1", "5..55E-1", "5..75E-1", "9..00E-1", "9..40E-1", "9..59E-1", "9..69E-1", "-1..00E-1", "-1..20E-1", "-1..51E-1", "-1..81E-1", "-5..00E-1", "-5..30E-1", "-5..55E-1", "-5..75E-1", "-9..00E-1", "-9..40E-1", "-9..59E-1", "-9..69E-1", "3..40E0", "4..56E0", "4..32E0", "4..57E0", "4..32E0", "-3..40E0", "-4..56E0", "-4..32E0", "-4..57E0", "-4..32E0"]);
		check_numeric_format("0..0.0E-0", numbers,
				["0..0.0E0", "1..0.0E0", "1..1.0E1", "1..5.0E1", "1..9.0E1", "2..0.0E2", "2..2.0E2", "2..5.0E2", "2..8.0E2", "1..2.3E3", "-1..0.0E0", "-1..1.0E1", "-1..5.0E1", "-1..9.0E1", "-2..0.0E2", "-2..2.0E2", "-2..5.0E2", "-2..8.0E2", "-9..8.8E3", "1..0.0E-1", "1..2.0E-1", "1..5.1E-1", "1..8.1E-1", "5..0.0E-1", "5..3.0E-1", "5..5.5E-1", "5..7.5E-1", "9..0.0E-1", "9..4.0E-1", "9..5.9E-1", "9..6.9E-1", "-1..0.0E-1", "-1..2.0E-1", "-1..5.1E-1", "-1..8.1E-1", "-5..0.0E-1", "-5..3.0E-1", "-5..5.5E-1", "-5..7.5E-1", "-9..0.0E-1", "-9..4.0E-1", "-9..5.9E-1", "-9..6.9E-1", "3..4.0E0", "4..5.6E0", "4..3.2E0", "4..5.7E0", "4..3.2E0", "-3..4.0E0", "-4..5.6E0", "-4..3.2E0", "-4..5.7E0", "-4..3.2E0"]);
		check_numeric_format("0.0E-0.", numbers,
				["0.0E0.", "1.0E0.", "1.1E1.", "1.5E1.", "1.9E1.", "2.0E2.", "2.2E2.", "2.5E2.", "2.8E2.", "1.2E3.", "-1.0E0.", "-1.1E1.", "-1.5E1.", "-1.9E1.", "-2.0E2.", "-2.2E2.", "-2.5E2.", "-2.8E2.", "-9.9E3.", "1.0E-1.", "1.2E-1.", "1.5E-1.", "1.8E-1.", "5.0E-1.", "5.3E-1.", "5.6E-1.", "5.8E-1.", "9.0E-1.", "9.4E-1.", "9.6E-1.", "9.7E-1.", "-1.0E-1.", "-1.2E-1.", "-1.5E-1.", "-1.8E-1.", "-5.0E-1.", "-5.3E-1.", "-5.6E-1.", "-5.8E-1.", "-9.0E-1.", "-9.4E-1.", "-9.6E-1.", "-9.7E-1.", "3.4E0.", "4.6E0.", "4.3E0.", "4.6E0.", "4.3E0.", "-3.4E0.", "-4.6E0.", "-4.3E0.", "-4.6E0.", "-4.3E0."]);
		check_numeric_format("0.0E-.0", numbers,
				["0.0E0.0", "1.0E0.0", "1.1E1.0", "1.5E1.0", "1.9E1.0", "2.0E2.0", "2.2E2.0", "2.5E2.0", "2.8E2.0", "1.2E3.0", "-1.0E0.0", "-1.1E1.0", "-1.5E1.0", "-1.9E1.0", "-2.0E2.0", "-2.2E2.0", "-2.5E2.0", "-2.8E2.0", "-9.9E3.0", "1.0E-1.0", "1.2E-1.0", "1.5E-1.0", "1.8E-1.0", "5.0E-1.0", "5.3E-1.0", "5.6E-1.0", "5.8E-1.0", "9.0E-1.0", "9.4E-1.0", "9.6E-1.0", "9.7E-1.0", "-1.0E-1.0", "-1.2E-1.0", "-1.5E-1.0", "-1.8E-1.0", "-5.0E-1.0", "-5.3E-1.0", "-5.6E-1.0", "-5.8E-1.0", "-9.0E-1.0", "-9.4E-1.0", "-9.6E-1.0", "-9.7E-1.0", "3.4E0.0", "4.6E0.0", "4.3E0.0", "4.6E0.0", "4.3E0.0", "-3.4E0.0", "-4.6E0.0", "-4.3E0.0", "-4.6E0.0", "-4.3E0.0"]);
		check_numeric_format("0.0E-.0.", numbers,
				["0.0E0.0.", "1.0E0.0.", "1.1E1.0.", "1.5E1.0.", "1.9E1.0.", "2.0E2.0.", "2.2E2.0.", "2.5E2.0.", "2.8E2.0.", "1.2E3.0.", "-1.0E0.0.", "-1.1E1.0.", "-1.5E1.0.", "-1.9E1.0.", "-2.0E2.0.", "-2.2E2.0.", "-2.5E2.0.", "-2.8E2.0.", "-9.9E3.0.", "1.0E-1.0.", "1.2E-1.0.", "1.5E-1.0.", "1.8E-1.0.", "5.0E-1.0.", "5.3E-1.0.", "5.6E-1.0.", "5.8E-1.0.", "9.0E-1.0.", "9.4E-1.0.", "9.6E-1.0.", "9.7E-1.0.", "-1.0E-1.0.", "-1.2E-1.0.", "-1.5E-1.0.", "-1.8E-1.0.", "-5.0E-1.0.", "-5.3E-1.0.", "-5.6E-1.0.", "-5.8E-1.0.", "-9.0E-1.0.", "-9.4E-1.0.", "-9.6E-1.0.", "-9.7E-1.0.", "3.4E0.0.", "4.6E0.0.", "4.3E0.0.", "4.6E0.0.", "4.3E0.0.", "-3.4E0.0.", "-4.6E0.0.", "-4.3E0.0.", "-4.6E0.0.", "-4.3E0.0."]);
		check_numeric_format("0!0.0E-0", numbers,
				["0!0.0E0", "0!1.0E0", "1!1.0E0", "1!5.0E0", "1!9.0E0", "0!2.0E2", "0!2.2E2", "0!2.5E2", "0!2.8E2", "1!2.3E2", "-0!1.0E0", "-1!1.0E0", "-1!5.0E0", "-1!9.0E0", "-0!2.0E2", "-0!2.2E2", "-0!2.5E2", "-0!2.8E2", "-9!8.8E2", "1!0.0E-2", "1!2.0E-2", "1!5.1E-2", "1!8.1E-2", "5!0.0E-2", "5!3.0E-2", "5!5.5E-2", "5!7.5E-2", "9!0.0E-2", "9!4.0E-2", "9!5.9E-2", "9!6.9E-2", "-1!0.0E-2", "-1!2.0E-2", "-1!5.1E-2", "-1!8.1E-2", "-5!0.0E-2", "-5!3.0E-2", "-5!5.5E-2", "-5!7.5E-2", "-9!0.0E-2", "-9!4.0E-2", "-9!5.9E-2", "-9!6.9E-2", "0!3.4E0", "0!4.6E0", "0!4.3E0", "0!4.6E0", "0!4.3E0", "-0!3.4E0", "-0!4.6E0", "-0!4.3E0", "-0!4.6E0", "-0!4.3E0"]);
		check_numeric_format("!00.0E-0", numbers,
				["!00.0E0", "!01.0E0", "!11.0E0", "!15.0E0", "!19.0E0", "!02.0E2", "!02.2E2", "!02.5E2", "!02.8E2", "!12.3E2", "-!01.0E0", "-!11.0E0", "-!15.0E0", "-!19.0E0", "-!02.0E2", "-!02.2E2", "-!02.5E2", "-!02.8E2", "-!98.8E2", "!10.0E-2", "!12.0E-2", "!15.1E-2", "!18.1E-2", "!50.0E-2", "!53.0E-2", "!55.5E-2", "!57.5E-2", "!90.0E-2", "!94.0E-2", "!95.9E-2", "!96.9E-2", "-!10.0E-2", "-!12.0E-2", "-!15.1E-2", "-!18.1E-2", "-!50.0E-2", "-!53.0E-2", "-!55.5E-2", "-!57.5E-2", "-!90.0E-2", "-!94.0E-2", "-!95.9E-2", "-!96.9E-2", "!03.4E0", "!04.6E0", "!04.3E0", "!04.6E0", "!04.3E0", "-!03.4E0", "-!04.6E0", "-!04.3E0", "-!04.6E0", "-!04.3E0"]);
		check_numeric_format("!0!0.0E-0", numbers,
				["!0!0.0E0", "!0!1.0E0", "!1!1.0E0", "!1!5.0E0", "!1!9.0E0", "!0!2.0E2", "!0!2.2E2", "!0!2.5E2", "!0!2.8E2", "!1!2.3E2", "-!0!1.0E0", "-!1!1.0E0", "-!1!5.0E0", "-!1!9.0E0", "-!0!2.0E2", "-!0!2.2E2", "-!0!2.5E2", "-!0!2.8E2", "-!9!8.8E2", "!1!0.0E-2", "!1!2.0E-2", "!1!5.1E-2", "!1!8.1E-2", "!5!0.0E-2", "!5!3.0E-2", "!5!5.5E-2", "!5!7.5E-2", "!9!0.0E-2", "!9!4.0E-2", "!9!5.9E-2", "!9!6.9E-2", "-!1!0.0E-2", "-!1!2.0E-2", "-!1!5.1E-2", "-!1!8.1E-2", "-!5!0.0E-2", "-!5!3.0E-2", "-!5!5.5E-2", "-!5!7.5E-2", "-!9!0.0E-2", "-!9!4.0E-2", "-!9!5.9E-2", "-!9!6.9E-2", "!0!3.4E0", "!0!4.6E0", "!0!4.3E0", "!0!4.6E0", "!0!4.3E0", "-!0!3.4E0", "-!0!4.6E0", "-!0!4.3E0", "-!0!4.6E0", "-!0!4.3E0"]);
		check_numeric_format("0.0!0E-0", numbers,
				["0.0!0E0", "1.0!0E0", "1.1!0E1", "1.5!0E1", "1.9!0E1", "2.0!0E2", "2.2!0E2", "2.5!0E2", "2.8!0E2", "1.2!3E3", "-1.0!0E0", "-1.1!0E1", "-1.5!0E1", "-1.9!0E1", "-2.0!0E2", "-2.2!0E2", "-2.5!0E2", "-2.8!0E2", "-9.8!8E3", "1.0!0E-1", "1.2!0E-1", "1.5!1E-1", "1.8!1E-1", "5.0!0E-1", "5.3!0E-1", "5.5!5E-1", "5.7!5E-1", "9.0!0E-1", "9.4!0E-1", "9.5!9E-1", "9.6!9E-1", "-1.0!0E-1", "-1.2!0E-1", "-1.5!1E-1", "-1.8!1E-1", "-5.0!0E-1", "-5.3!0E-1", "-5.5!5E-1", "-5.7!5E-1", "-9.0!0E-1", "-9.4!0E-1", "-9.5!9E-1", "-9.6!9E-1", "3.4!0E0", "4.5!6E0", "4.3!2E0", "4.5!7E0", "4.3!2E0", "-3.4!0E0", "-4.5!6E0", "-4.3!2E0", "-4.5!7E0", "-4.3!2E0"]);
		check_numeric_format("0.!00E-0", numbers,
				["0.!00E0", "1.!00E0", "1.!10E1", "1.!50E1", "1.!90E1", "2.!00E2", "2.!20E2", "2.!50E2", "2.!80E2", "1.!23E3", "-1.!00E0", "-1.!10E1", "-1.!50E1", "-1.!90E1", "-2.!00E2", "-2.!20E2", "-2.!50E2", "-2.!80E2", "-9.!88E3", "1.!00E-1", "1.!20E-1", "1.!51E-1", "1.!81E-1", "5.!00E-1", "5.!30E-1", "5.!55E-1", "5.!75E-1", "9.!00E-1", "9.!40E-1", "9.!59E-1", "9.!69E-1", "-1.!00E-1", "-1.!20E-1", "-1.!51E-1", "-1.!81E-1", "-5.!00E-1", "-5.!30E-1", "-5.!55E-1", "-5.!75E-1", "-9.!00E-1", "-9.!40E-1", "-9.!59E-1", "-9.!69E-1", "3.!40E0", "4.!56E0", "4.!32E0", "4.!57E0", "4.!32E0", "-3.!40E0", "-4.!56E0", "-4.!32E0", "-4.!57E0", "-4.!32E0"]);
		check_numeric_format("0.!0!0E-0", numbers,
				["0.!0!0E0", "1.!0!0E0", "1.!1!0E1", "1.!5!0E1", "1.!9!0E1", "2.!0!0E2", "2.!2!0E2", "2.!5!0E2", "2.!8!0E2", "1.!2!3E3", "-1.!0!0E0", "-1.!1!0E1", "-1.!5!0E1", "-1.!9!0E1", "-2.!0!0E2", "-2.!2!0E2", "-2.!5!0E2", "-2.!8!0E2", "-9.!8!8E3", "1.!0!0E-1", "1.!2!0E-1", "1.!5!1E-1", "1.!8!1E-1", "5.!0!0E-1", "5.!3!0E-1", "5.!5!5E-1", "5.!7!5E-1", "9.!0!0E-1", "9.!4!0E-1", "9.!5!9E-1", "9.!6!9E-1", "-1.!0!0E-1", "-1.!2!0E-1", "-1.!5!1E-1", "-1.!8!1E-1", "-5.!0!0E-1", "-5.!3!0E-1", "-5.!5!5E-1", "-5.!7!5E-1", "-9.!0!0E-1", "-9.!4!0E-1", "-9.!5!9E-1", "-9.!6!9E-1", "3.!4!0E0", "4.!5!6E0", "4.!3!2E0", "4.!5!7E0", "4.!3!2E0", "-3.!4!0E0", "-4.!5!6E0", "-4.!3!2E0", "-4.!5!7E0", "-4.!3!2E0"]);
		check_numeric_format("0.0E-0!", numbers,
				["0.0E0!", "1.0E0!", "1.1E1!", "1.5E1!", "1.9E1!", "2.0E2!", "2.2E2!", "2.5E2!", "2.8E2!", "1.2E3!", "-1.0E0!", "-1.1E1!", "-1.5E1!", "-1.9E1!", "-2.0E2!", "-2.2E2!", "-2.5E2!", "-2.8E2!", "-9.9E3!", "1.0E-1!", "1.2E-1!", "1.5E-1!", "1.8E-1!", "5.0E-1!", "5.3E-1!", "5.6E-1!", "5.8E-1!", "9.0E-1!", "9.4E-1!", "9.6E-1!", "9.7E-1!", "-1.0E-1!", "-1.2E-1!", "-1.5E-1!", "-1.8E-1!", "-5.0E-1!", "-5.3E-1!", "-5.6E-1!", "-5.8E-1!", "-9.0E-1!", "-9.4E-1!", "-9.6E-1!", "-9.7E-1!", "3.4E0!", "4.6E0!", "4.3E0!", "4.6E0!", "4.3E0!", "-3.4E0!", "-4.6E0!", "-4.3E0!", "-4.6E0!", "-4.3E0!"]);
		check_numeric_format("0.0\\E!-0", numbers,
				["0.0E!0", "1.0E!0", "1.1E!1", "1.5E!1", "1.9E!1", "2.0E!2", "2.2E!2", "2.5E!2", "2.8E!2", "1.2E!3", "-1.0E!0", "-1.1E!1", "-1.5E!1", "-1.9E!1", "-2.0E!2", "-2.2E!2", "-2.5E!2", "-2.8E!2", "-9.9E!3", "1.0E!-1", "1.2E!-1", "1.5E!-1", "1.8E!-1", "5.0E!-1", "5.3E!-1", "5.6E!-1", "5.8E!-1", "9.0E!-1", "9.4E!-1", "9.6E!-1", "9.7E!-1", "-1.0E!-1", "-1.2E!-1", "-1.5E!-1", "-1.8E!-1", "-5.0E!-1", "-5.3E!-1", "-5.6E!-1", "-5.8E!-1", "-9.0E!-1", "-9.4E!-1", "-9.6E!-1", "-9.7E!-1", "3.4E!0", "4.6E!0", "4.3E!0", "4.6E!0", "4.3E!0", "-3.4E!0", "-4.6E!0", "-4.3E!0", "-4.6E!0", "-4.3E!0"]);
		check_numeric_format("0.0\\E!-0!", numbers,
				["0.0E!0!", "1.0E!0!", "1.1E!1!", "1.5E!1!", "1.9E!1!", "2.0E!2!", "2.2E!2!", "2.5E!2!", "2.8E!2!", "1.2E!3!", "-1.0E!0!", "-1.1E!1!", "-1.5E!1!", "-1.9E!1!", "-2.0E!2!", "-2.2E!2!", "-2.5E!2!", "-2.8E!2!", "-9.9E!3!", "1.0E!-1!", "1.2E!-1!", "1.5E!-1!", "1.8E!-1!", "5.0E!-1!", "5.3E!-1!", "5.6E!-1!", "5.8E!-1!", "9.0E!-1!", "9.4E!-1!", "9.6E!-1!", "9.7E!-1!", "-1.0E!-1!", "-1.2E!-1!", "-1.5E!-1!", "-1.8E!-1!", "-5.0E!-1!", "-5.3E!-1!", "-5.6E!-1!", "-5.8E!-1!", "-9.0E!-1!", "-9.4E!-1!", "-9.6E!-1!", "-9.7E!-1!", "3.4E!0!", "4.6E!0!", "4.3E!0!", "4.6E!0!", "4.3E!0!", "-3.4E!0!", "-4.6E!0!", "-4.3E!0!", "-4.6E!0!", "-4.3E!0!"]);
		check_numeric_format("#,#00.0E+0", numbers,
				["0,000.0E+0", "01.0E+0", "11.0E+0", "15.0E+0", "19.0E+0", "200.0E+0", "220.0E+0", "250.0E+0", "280.0E+0", "1,234.0E+0", "-01.0E+0", "-11.0E+0", "-15.0E+0", "-19.0E+0", "-200.0E+0", "-220.0E+0", "-250.0E+0", "-280.0E+0", "-9,876.0E+0", "1,000.0E-4", "1,200.0E-4", "1,510.0E-4", "1,810.0E-4", "5,000.0E-4", "5,300.0E-4", "5,550.0E-4", "5,750.0E-4", "9,000.0E-4", "9,400.0E-4", "9,590.0E-4", "9,690.0E-4", "-1,000.0E-4", "-1,200.0E-4", "-1,510.0E-4", "-1,810.0E-4", "-5,000.0E-4", "-5,300.0E-4", "-5,550.0E-4", "-5,750.0E-4", "-9,000.0E-4", "-9,400.0E-4", "-9,590.0E-4", "-9,690.0E-4", "03.4E+0", "04.6E+0", "04.3E+0", "04.6E+0", "04.3E+0", "-03.4E+0", "-04.6E+0", "-04.3E+0", "-04.6E+0", "-04.3E+0"]);
		check_numeric_format(",00.0E+0", numbers,
				[",00.0E+0", ",01.0E+0", ",11.0E+0", ",15.0E+0", ",19.0E+0", ",02.0E+2", ",02.2E+2", ",02.5E+2", ",02.8E+2", ",12.3E+2", "-,01.0E+0", "-,11.0E+0", "-,15.0E+0", "-,19.0E+0", "-,02.0E+2", "-,02.2E+2", "-,02.5E+2", "-,02.8E+2", "-,98.8E+2", ",10.0E-2", ",12.0E-2", ",15.1E-2", ",18.1E-2", ",50.0E-2", ",53.0E-2", ",55.5E-2", ",57.5E-2", ",90.0E-2", ",94.0E-2", ",95.9E-2", ",96.9E-2", "-,10.0E-2", "-,12.0E-2", "-,15.1E-2", "-,18.1E-2", "-,50.0E-2", "-,53.0E-2", "-,55.5E-2", "-,57.5E-2", "-,90.0E-2", "-,94.0E-2", "-,95.9E-2", "-,96.9E-2", ",03.4E+0", ",04.6E+0", ",04.3E+0", ",04.6E+0", ",04.3E+0", "-,03.4E+0", "-,04.6E+0", "-,04.3E+0", "-,04.6E+0", "-,04.3E+0"]);
		check_numeric_format(",#,#00.0E+0", numbers,
				[",0,000.0E+0", ",01.0E+0", ",11.0E+0", ",15.0E+0", ",19.0E+0", ",200.0E+0", ",220.0E+0", ",250.0E+0", ",280.0E+0", ",1,234.0E+0", "-,01.0E+0", "-,11.0E+0", "-,15.0E+0", "-,19.0E+0", "-,200.0E+0", "-,220.0E+0", "-,250.0E+0", "-,280.0E+0", "-,9,876.0E+0", ",1,000.0E-4", ",1,200.0E-4", ",1,510.0E-4", ",1,810.0E-4", ",5,000.0E-4", ",5,300.0E-4", ",5,550.0E-4", ",5,750.0E-4", ",9,000.0E-4", ",9,400.0E-4", ",9,590.0E-4", ",9,690.0E-4", "-,1,000.0E-4", "-,1,200.0E-4", "-,1,510.0E-4", "-,1,810.0E-4", "-,5,000.0E-4", "-,5,300.0E-4", "-,5,550.0E-4", "-,5,750.0E-4", "-,9,000.0E-4", "-,9,400.0E-4", "-,9,590.0E-4", "-,9,690.0E-4", ",03.4E+0", ",04.6E+0", ",04.3E+0", ",04.6E+0", ",04.3E+0", "-,03.4E+0", "-,04.6E+0", "-,04.3E+0", "-,04.6E+0", "-,04.3E+0"]);
		check_numeric_format("0.00E+0", numbers,
				["0.00E+0", "1.00E+0", "1.10E+1", "1.50E+1", "1.90E+1", "2.00E+2", "2.20E+2", "2.50E+2", "2.80E+2", "1.23E+3", "-1.00E+0", "-1.10E+1", "-1.50E+1", "-1.90E+1", "-2.00E+2", "-2.20E+2", "-2.50E+2", "-2.80E+2", "-9.88E+3", "1.00E-1", "1.20E-1", "1.51E-1", "1.81E-1", "5.00E-1", "5.30E-1", "5.55E-1", "5.75E-1", "9.00E-1", "9.40E-1", "9.59E-1", "9.69E-1", "-1.00E-1", "-1.20E-1", "-1.51E-1", "-1.81E-1", "-5.00E-1", "-5.30E-1", "-5.55E-1", "-5.75E-1", "-9.00E-1", "-9.40E-1", "-9.59E-1", "-9.69E-1", "3.40E+0", "4.56E+0", "4.32E+0", "4.57E+0", "4.32E+0", "-3.40E+0", "-4.56E+0", "-4.32E+0", "-4.57E+0", "-4.32E+0"]);
		check_numeric_format("0.00E+0", numbers,
				["0.00E+0", "1.00E+0", "1.10E+1", "1.50E+1", "1.90E+1", "2.00E+2", "2.20E+2", "2.50E+2", "2.80E+2", "1.23E+3", "-1.00E+0", "-1.10E+1", "-1.50E+1", "-1.90E+1", "-2.00E+2", "-2.20E+2", "-2.50E+2", "-2.80E+2", "-9.88E+3", "1.00E-1", "1.20E-1", "1.51E-1", "1.81E-1", "5.00E-1", "5.30E-1", "5.55E-1", "5.75E-1", "9.00E-1", "9.40E-1", "9.59E-1", "9.69E-1", "-1.00E-1", "-1.20E-1", "-1.51E-1", "-1.81E-1", "-5.00E-1", "-5.30E-1", "-5.55E-1", "-5.75E-1", "-9.00E-1", "-9.40E-1", "-9.59E-1", "-9.69E-1", "3.40E+0", "4.56E+0", "4.32E+0", "4.57E+0", "4.32E+0", "-3.40E+0", "-4.56E+0", "-4.32E+0", "-4.57E+0", "-4.32E+0"]);
		check_numeric_format("0.00E+0", numbers,
				["0.00E+0", "1.00E+0", "1.10E+1", "1.50E+1", "1.90E+1", "2.00E+2", "2.20E+2", "2.50E+2", "2.80E+2", "1.23E+3", "-1.00E+0", "-1.10E+1", "-1.50E+1", "-1.90E+1", "-2.00E+2", "-2.20E+2", "-2.50E+2", "-2.80E+2", "-9.88E+3", "1.00E-1", "1.20E-1", "1.51E-1", "1.81E-1", "5.00E-1", "5.30E-1", "5.55E-1", "5.75E-1", "9.00E-1", "9.40E-1", "9.59E-1", "9.69E-1", "-1.00E-1", "-1.20E-1", "-1.51E-1", "-1.81E-1", "-5.00E-1", "-5.30E-1", "-5.55E-1", "-5.75E-1", "-9.00E-1", "-9.40E-1", "-9.59E-1", "-9.69E-1", "3.40E+0", "4.56E+0", "4.32E+0", "4.57E+0", "4.32E+0", "-3.40E+0", "-4.56E+0", "-4.32E+0", "-4.57E+0", "-4.32E+0"]);
		check_numeric_format("0.0,E+0", numbers,
				["0.0E+0", "1.0E+0", "1.1E+1", "1.5E+1", "1.9E+1", "2.0E+2", "2.2E+2", "2.5E+2", "2.8E+2", "1.2E+3", "-1.0E+0", "-1.1E+1", "-1.5E+1", "-1.9E+1", "-2.0E+2", "-2.2E+2", "-2.5E+2", "-2.8E+2", "-9.9E+3", "1.0E-1", "1.2E-1", "1.5E-1", "1.8E-1", "5.0E-1", "5.3E-1", "5.6E-1", "5.8E-1", "9.0E-1", "9.4E-1", "9.6E-1", "9.7E-1", "-1.0E-1", "-1.2E-1", "-1.5E-1", "-1.8E-1", "-5.0E-1", "-5.3E-1", "-5.6E-1", "-5.8E-1", "-9.0E-1", "-9.4E-1", "-9.6E-1", "-9.7E-1", "3.4E+0", "4.6E+0", "4.3E+0", "4.6E+0", "4.3E+0", "-3.4E+0", "-4.6E+0", "-4.3E+0", "-4.6E+0", "-4.3E+0"]);
		check_numeric_format("0.0\\E,+0", numbers,
				["0.0E,+0", "1.0E,+0", "1.1E,+1", "1.5E,+1", "1.9E,+1", "2.0E,+2", "2.2E,+2", "2.5E,+2", "2.8E,+2", "1.2E,+3", "-1.0E,+0", "-1.1E,+1", "-1.5E,+1", "-1.9E,+1", "-2.0E,+2", "-2.2E,+2", "-2.5E,+2", "-2.8E,+2", "-9.9E,+3", "1.0E,-1", "1.2E,-1", "1.5E,-1", "1.8E,-1", "5.0E,-1", "5.3E,-1", "5.6E,-1", "5.8E,-1", "9.0E,-1", "9.4E,-1", "9.6E,-1", "9.7E,-1", "-1.0E,-1", "-1.2E,-1", "-1.5E,-1", "-1.8E,-1", "-5.0E,-1", "-5.3E,-1", "-5.6E,-1", "-5.8E,-1", "-9.0E,-1", "-9.4E,-1", "-9.6E,-1", "-9.7E,-1", "3.4E,+0", "4.6E,+0", "4.3E,+0", "4.6E,+0", "4.3E,+0", "-3.4E,+0", "-4.6E,+0", "-4.3E,+0", "-4.6E,+0", "-4.3E,+0"]);
		check_numeric_format("0.0,\\E,+0", numbers,
				["0.0E,+0", "1.0E,+0", "1.1E,+1", "1.5E,+1", "1.9E,+1", "2.0E,+2", "2.2E,+2", "2.5E,+2", "2.8E,+2", "1.2E,+3", "-1.0E,+0", "-1.1E,+1", "-1.5E,+1", "-1.9E,+1", "-2.0E,+2", "-2.2E,+2", "-2.5E,+2", "-2.8E,+2", "-9.9E,+3", "1.0E,-1", "1.2E,-1", "1.5E,-1", "1.8E,-1", "5.0E,-1", "5.3E,-1", "5.6E,-1", "5.8E,-1", "9.0E,-1", "9.4E,-1", "9.6E,-1", "9.7E,-1", "-1.0E,-1", "-1.2E,-1", "-1.5E,-1", "-1.8E,-1", "-5.0E,-1", "-5.3E,-1", "-5.6E,-1", "-5.8E,-1", "-9.0E,-1", "-9.4E,-1", "-9.6E,-1", "-9.7E,-1", "3.4E,+0", "4.6E,+0", "4.3E,+0", "4.6E,+0", "4.3E,+0", "-3.4E,+0", "-4.6E,+0", "-4.3E,+0", "-4.6E,+0", "-4.3E,+0"]);
		check_numeric_format("0.0.0E+0", numbers,
				["0.0.0E+0", "1.0.0E+0", "1.1.0E+1", "1.5.0E+1", "1.9.0E+1", "2.0.0E+2", "2.2.0E+2", "2.5.0E+2", "2.8.0E+2", "1.2.3E+3", "-1.0.0E+0", "-1.1.0E+1", "-1.5.0E+1", "-1.9.0E+1", "-2.0.0E+2", "-2.2.0E+2", "-2.5.0E+2", "-2.8.0E+2", "-9.8.8E+3", "1.0.0E-1", "1.2.0E-1", "1.5.1E-1", "1.8.1E-1", "5.0.0E-1", "5.3.0E-1", "5.5.5E-1", "5.7.5E-1", "9.0.0E-1", "9.4.0E-1", "9.5.9E-1", "9.6.9E-1", "-1.0.0E-1", "-1.2.0E-1", "-1.5.1E-1", "-1.8.1E-1", "-5.0.0E-1", "-5.3.0E-1", "-5.5.5E-1", "-5.7.5E-1", "-9.0.0E-1", "-9.4.0E-1", "-9.5.9E-1", "-9.6.9E-1", "3.4.0E+0", "4.5.6E+0", "4.3.2E+0", "4.5.7E+0", "4.3.2E+0", "-3.4.0E+0", "-4.5.6E+0", "-4.3.2E+0", "-4.5.7E+0", "-4.3.2E+0"]);
		check_numeric_format(".00.0E+0", numbers,
				[".00.0E+0", ".10.0E+1", ".11.0E+2", ".15.0E+2", ".19.0E+2", ".20.0E+3", ".22.0E+3", ".25.0E+3", ".28.0E+3", ".12.3E+4", "-.10.0E+1", "-.11.0E+2", "-.15.0E+2", "-.19.0E+2", "-.20.0E+3", "-.22.0E+3", "-.25.0E+3", "-.28.0E+3", "-.98.8E+4", ".10.0E+0", ".12.0E+0", ".15.1E+0", ".18.1E+0", ".50.0E+0", ".53.0E+0", ".55.5E+0", ".57.5E+0", ".90.0E+0", ".94.0E+0", ".95.9E+0", ".96.9E+0", "-.10.0E+0", "-.12.0E+0", "-.15.1E+0", "-.18.1E+0", "-.50.0E+0", "-.53.0E+0", "-.55.5E+0", "-.57.5E+0", "-.90.0E+0", "-.94.0E+0", "-.95.9E+0", "-.96.9E+0", ".34.0E+1", ".45.6E+1", ".43.2E+1", ".45.7E+1", ".43.2E+1", "-.34.0E+1", "-.45.6E+1", "-.43.2E+1", "-.45.7E+1", "-.43.2E+1"]);
		check_numeric_format(".0.0.0E+0", numbers,
				[".0.0.0E+0", ".1.0.0E+1", ".1.1.0E+2", ".1.5.0E+2", ".1.9.0E+2", ".2.0.0E+3", ".2.2.0E+3", ".2.5.0E+3", ".2.8.0E+3", ".1.2.3E+4", "-.1.0.0E+1", "-.1.1.0E+2", "-.1.5.0E+2", "-.1.9.0E+2", "-.2.0.0E+3", "-.2.2.0E+3", "-.2.5.0E+3", "-.2.8.0E+3", "-.9.8.8E+4", ".1.0.0E+0", ".1.2.0E+0", ".1.5.1E+0", ".1.8.1E+0", ".5.0.0E+0", ".5.3.0E+0", ".5.5.5E+0", ".5.7.5E+0", ".9.0.0E+0", ".9.4.0E+0", ".9.5.9E+0", ".9.6.9E+0", "-.1.0.0E+0", "-.1.2.0E+0", "-.1.5.1E+0", "-.1.8.1E+0", "-.5.0.0E+0", "-.5.3.0E+0", "-.5.5.5E+0", "-.5.7.5E+0", "-.9.0.0E+0", "-.9.4.0E+0", "-.9.5.9E+0", "-.9.6.9E+0", ".3.4.0E+1", ".4.5.6E+1", ".4.3.2E+1", ".4.5.7E+1", ".4.3.2E+1", "-.3.4.0E+1", "-.4.5.6E+1", "-.4.3.2E+1", "-.4.5.7E+1", "-.4.3.2E+1"]);
		check_numeric_format("0.0.0E+0", numbers,
				["0.0.0E+0", "1.0.0E+0", "1.1.0E+1", "1.5.0E+1", "1.9.0E+1", "2.0.0E+2", "2.2.0E+2", "2.5.0E+2", "2.8.0E+2", "1.2.3E+3", "-1.0.0E+0", "-1.1.0E+1", "-1.5.0E+1", "-1.9.0E+1", "-2.0.0E+2", "-2.2.0E+2", "-2.5.0E+2", "-2.8.0E+2", "-9.8.8E+3", "1.0.0E-1", "1.2.0E-1", "1.5.1E-1", "1.8.1E-1", "5.0.0E-1", "5.3.0E-1", "5.5.5E-1", "5.7.5E-1", "9.0.0E-1", "9.4.0E-1", "9.5.9E-1", "9.6.9E-1", "-1.0.0E-1", "-1.2.0E-1", "-1.5.1E-1", "-1.8.1E-1", "-5.0.0E-1", "-5.3.0E-1", "-5.5.5E-1", "-5.7.5E-1", "-9.0.0E-1", "-9.4.0E-1", "-9.5.9E-1", "-9.6.9E-1", "3.4.0E+0", "4.5.6E+0", "4.3.2E+0", "4.5.7E+0", "4.3.2E+0", "-3.4.0E+0", "-4.5.6E+0", "-4.3.2E+0", "-4.5.7E+0", "-4.3.2E+0"]);
		check_numeric_format("0..00E+0", numbers,
				["0..00E+0", "1..00E+0", "1..10E+1", "1..50E+1", "1..90E+1", "2..00E+2", "2..20E+2", "2..50E+2", "2..80E+2", "1..23E+3", "-1..00E+0", "-1..10E+1", "-1..50E+1", "-1..90E+1", "-2..00E+2", "-2..20E+2", "-2..50E+2", "-2..80E+2", "-9..88E+3", "1..00E-1", "1..20E-1", "1..51E-1", "1..81E-1", "5..00E-1", "5..30E-1", "5..55E-1", "5..75E-1", "9..00E-1", "9..40E-1", "9..59E-1", "9..69E-1", "-1..00E-1", "-1..20E-1", "-1..51E-1", "-1..81E-1", "-5..00E-1", "-5..30E-1", "-5..55E-1", "-5..75E-1", "-9..00E-1", "-9..40E-1", "-9..59E-1", "-9..69E-1", "3..40E+0", "4..56E+0", "4..32E+0", "4..57E+0", "4..32E+0", "-3..40E+0", "-4..56E+0", "-4..32E+0", "-4..57E+0", "-4..32E+0"]);
		check_numeric_format("0..0.0E+0", numbers,
				["0..0.0E+0", "1..0.0E+0", "1..1.0E+1", "1..5.0E+1", "1..9.0E+1", "2..0.0E+2", "2..2.0E+2", "2..5.0E+2", "2..8.0E+2", "1..2.3E+3", "-1..0.0E+0", "-1..1.0E+1", "-1..5.0E+1", "-1..9.0E+1", "-2..0.0E+2", "-2..2.0E+2", "-2..5.0E+2", "-2..8.0E+2", "-9..8.8E+3", "1..0.0E-1", "1..2.0E-1", "1..5.1E-1", "1..8.1E-1", "5..0.0E-1", "5..3.0E-1", "5..5.5E-1", "5..7.5E-1", "9..0.0E-1", "9..4.0E-1", "9..5.9E-1", "9..6.9E-1", "-1..0.0E-1", "-1..2.0E-1", "-1..5.1E-1", "-1..8.1E-1", "-5..0.0E-1", "-5..3.0E-1", "-5..5.5E-1", "-5..7.5E-1", "-9..0.0E-1", "-9..4.0E-1", "-9..5.9E-1", "-9..6.9E-1", "3..4.0E+0", "4..5.6E+0", "4..3.2E+0", "4..5.7E+0", "4..3.2E+0", "-3..4.0E+0", "-4..5.6E+0", "-4..3.2E+0", "-4..5.7E+0", "-4..3.2E+0"]);
		check_numeric_format("0.0E+0.", numbers,
				["0.0E+0.", "1.0E+0.", "1.1E+1.", "1.5E+1.", "1.9E+1.", "2.0E+2.", "2.2E+2.", "2.5E+2.", "2.8E+2.", "1.2E+3.", "-1.0E+0.", "-1.1E+1.", "-1.5E+1.", "-1.9E+1.", "-2.0E+2.", "-2.2E+2.", "-2.5E+2.", "-2.8E+2.", "-9.9E+3.", "1.0E-1.", "1.2E-1.", "1.5E-1.", "1.8E-1.", "5.0E-1.", "5.3E-1.", "5.6E-1.", "5.8E-1.", "9.0E-1.", "9.4E-1.", "9.6E-1.", "9.7E-1.", "-1.0E-1.", "-1.2E-1.", "-1.5E-1.", "-1.8E-1.", "-5.0E-1.", "-5.3E-1.", "-5.6E-1.", "-5.8E-1.", "-9.0E-1.", "-9.4E-1.", "-9.6E-1.", "-9.7E-1.", "3.4E+0.", "4.6E+0.", "4.3E+0.", "4.6E+0.", "4.3E+0.", "-3.4E+0.", "-4.6E+0.", "-4.3E+0.", "-4.6E+0.", "-4.3E+0."]);
		check_numeric_format("0.0E+.0", numbers,
				["0.0E+0.0", "1.0E+0.0", "1.1E+1.0", "1.5E+1.0", "1.9E+1.0", "2.0E+2.0", "2.2E+2.0", "2.5E+2.0", "2.8E+2.0", "1.2E+3.0", "-1.0E+0.0", "-1.1E+1.0", "-1.5E+1.0", "-1.9E+1.0", "-2.0E+2.0", "-2.2E+2.0", "-2.5E+2.0", "-2.8E+2.0", "-9.9E+3.0", "1.0E-1.0", "1.2E-1.0", "1.5E-1.0", "1.8E-1.0", "5.0E-1.0", "5.3E-1.0", "5.6E-1.0", "5.8E-1.0", "9.0E-1.0", "9.4E-1.0", "9.6E-1.0", "9.7E-1.0", "-1.0E-1.0", "-1.2E-1.0", "-1.5E-1.0", "-1.8E-1.0", "-5.0E-1.0", "-5.3E-1.0", "-5.6E-1.0", "-5.8E-1.0", "-9.0E-1.0", "-9.4E-1.0", "-9.6E-1.0", "-9.7E-1.0", "3.4E+0.0", "4.6E+0.0", "4.3E+0.0", "4.6E+0.0", "4.3E+0.0", "-3.4E+0.0", "-4.6E+0.0", "-4.3E+0.0", "-4.6E+0.0", "-4.3E+0.0"]);
		check_numeric_format("0.0E+.0.", numbers,
				["0.0E+0.0.", "1.0E+0.0.", "1.1E+1.0.", "1.5E+1.0.", "1.9E+1.0.", "2.0E+2.0.", "2.2E+2.0.", "2.5E+2.0.", "2.8E+2.0.", "1.2E+3.0.", "-1.0E+0.0.", "-1.1E+1.0.", "-1.5E+1.0.", "-1.9E+1.0.", "-2.0E+2.0.", "-2.2E+2.0.", "-2.5E+2.0.", "-2.8E+2.0.", "-9.9E+3.0.", "1.0E-1.0.", "1.2E-1.0.", "1.5E-1.0.", "1.8E-1.0.", "5.0E-1.0.", "5.3E-1.0.", "5.6E-1.0.", "5.8E-1.0.", "9.0E-1.0.", "9.4E-1.0.", "9.6E-1.0.", "9.7E-1.0.", "-1.0E-1.0.", "-1.2E-1.0.", "-1.5E-1.0.", "-1.8E-1.0.", "-5.0E-1.0.", "-5.3E-1.0.", "-5.6E-1.0.", "-5.8E-1.0.", "-9.0E-1.0.", "-9.4E-1.0.", "-9.6E-1.0.", "-9.7E-1.0.", "3.4E+0.0.", "4.6E+0.0.", "4.3E+0.0.", "4.6E+0.0.", "4.3E+0.0.", "-3.4E+0.0.", "-4.6E+0.0.", "-4.3E+0.0.", "-4.6E+0.0.", "-4.3E+0.0."]);
		check_numeric_format("0!0.0E+0", numbers,
				["0!0.0E+0", "0!1.0E+0", "1!1.0E+0", "1!5.0E+0", "1!9.0E+0", "0!2.0E+2", "0!2.2E+2", "0!2.5E+2", "0!2.8E+2", "1!2.3E+2", "-0!1.0E+0", "-1!1.0E+0", "-1!5.0E+0", "-1!9.0E+0", "-0!2.0E+2", "-0!2.2E+2", "-0!2.5E+2", "-0!2.8E+2", "-9!8.8E+2", "1!0.0E-2", "1!2.0E-2", "1!5.1E-2", "1!8.1E-2", "5!0.0E-2", "5!3.0E-2", "5!5.5E-2", "5!7.5E-2", "9!0.0E-2", "9!4.0E-2", "9!5.9E-2", "9!6.9E-2", "-1!0.0E-2", "-1!2.0E-2", "-1!5.1E-2", "-1!8.1E-2", "-5!0.0E-2", "-5!3.0E-2", "-5!5.5E-2", "-5!7.5E-2", "-9!0.0E-2", "-9!4.0E-2", "-9!5.9E-2", "-9!6.9E-2", "0!3.4E+0", "0!4.6E+0", "0!4.3E+0", "0!4.6E+0", "0!4.3E+0", "-0!3.4E+0", "-0!4.6E+0", "-0!4.3E+0", "-0!4.6E+0", "-0!4.3E+0"]);
		check_numeric_format("!00.0E+0", numbers,
				["!00.0E+0", "!01.0E+0", "!11.0E+0", "!15.0E+0", "!19.0E+0", "!02.0E+2", "!02.2E+2", "!02.5E+2", "!02.8E+2", "!12.3E+2", "-!01.0E+0", "-!11.0E+0", "-!15.0E+0", "-!19.0E+0", "-!02.0E+2", "-!02.2E+2", "-!02.5E+2", "-!02.8E+2", "-!98.8E+2", "!10.0E-2", "!12.0E-2", "!15.1E-2", "!18.1E-2", "!50.0E-2", "!53.0E-2", "!55.5E-2", "!57.5E-2", "!90.0E-2", "!94.0E-2", "!95.9E-2", "!96.9E-2", "-!10.0E-2", "-!12.0E-2", "-!15.1E-2", "-!18.1E-2", "-!50.0E-2", "-!53.0E-2", "-!55.5E-2", "-!57.5E-2", "-!90.0E-2", "-!94.0E-2", "-!95.9E-2", "-!96.9E-2", "!03.4E+0", "!04.6E+0", "!04.3E+0", "!04.6E+0", "!04.3E+0", "-!03.4E+0", "-!04.6E+0", "-!04.3E+0", "-!04.6E+0", "-!04.3E+0"]);
		check_numeric_format("!0!0.0E+0", numbers,
				["!0!0.0E+0", "!0!1.0E+0", "!1!1.0E+0", "!1!5.0E+0", "!1!9.0E+0", "!0!2.0E+2", "!0!2.2E+2", "!0!2.5E+2", "!0!2.8E+2", "!1!2.3E+2", "-!0!1.0E+0", "-!1!1.0E+0", "-!1!5.0E+0", "-!1!9.0E+0", "-!0!2.0E+2", "-!0!2.2E+2", "-!0!2.5E+2", "-!0!2.8E+2", "-!9!8.8E+2", "!1!0.0E-2", "!1!2.0E-2", "!1!5.1E-2", "!1!8.1E-2", "!5!0.0E-2", "!5!3.0E-2", "!5!5.5E-2", "!5!7.5E-2", "!9!0.0E-2", "!9!4.0E-2", "!9!5.9E-2", "!9!6.9E-2", "-!1!0.0E-2", "-!1!2.0E-2", "-!1!5.1E-2", "-!1!8.1E-2", "-!5!0.0E-2", "-!5!3.0E-2", "-!5!5.5E-2", "-!5!7.5E-2", "-!9!0.0E-2", "-!9!4.0E-2", "-!9!5.9E-2", "-!9!6.9E-2", "!0!3.4E+0", "!0!4.6E+0", "!0!4.3E+0", "!0!4.6E+0", "!0!4.3E+0", "-!0!3.4E+0", "-!0!4.6E+0", "-!0!4.3E+0", "-!0!4.6E+0", "-!0!4.3E+0"]);
		check_numeric_format("0.0!0E+0", numbers,
				["0.0!0E+0", "1.0!0E+0", "1.1!0E+1", "1.5!0E+1", "1.9!0E+1", "2.0!0E+2", "2.2!0E+2", "2.5!0E+2", "2.8!0E+2", "1.2!3E+3", "-1.0!0E+0", "-1.1!0E+1", "-1.5!0E+1", "-1.9!0E+1", "-2.0!0E+2", "-2.2!0E+2", "-2.5!0E+2", "-2.8!0E+2", "-9.8!8E+3", "1.0!0E-1", "1.2!0E-1", "1.5!1E-1", "1.8!1E-1", "5.0!0E-1", "5.3!0E-1", "5.5!5E-1", "5.7!5E-1", "9.0!0E-1", "9.4!0E-1", "9.5!9E-1", "9.6!9E-1", "-1.0!0E-1", "-1.2!0E-1", "-1.5!1E-1", "-1.8!1E-1", "-5.0!0E-1", "-5.3!0E-1", "-5.5!5E-1", "-5.7!5E-1", "-9.0!0E-1", "-9.4!0E-1", "-9.5!9E-1", "-9.6!9E-1", "3.4!0E+0", "4.5!6E+0", "4.3!2E+0", "4.5!7E+0", "4.3!2E+0", "-3.4!0E+0", "-4.5!6E+0", "-4.3!2E+0", "-4.5!7E+0", "-4.3!2E+0"]);
		check_numeric_format("0.!00E+0", numbers,
				["0.!00E+0", "1.!00E+0", "1.!10E+1", "1.!50E+1", "1.!90E+1", "2.!00E+2", "2.!20E+2", "2.!50E+2", "2.!80E+2", "1.!23E+3", "-1.!00E+0", "-1.!10E+1", "-1.!50E+1", "-1.!90E+1", "-2.!00E+2", "-2.!20E+2", "-2.!50E+2", "-2.!80E+2", "-9.!88E+3", "1.!00E-1", "1.!20E-1", "1.!51E-1", "1.!81E-1", "5.!00E-1", "5.!30E-1", "5.!55E-1", "5.!75E-1", "9.!00E-1", "9.!40E-1", "9.!59E-1", "9.!69E-1", "-1.!00E-1", "-1.!20E-1", "-1.!51E-1", "-1.!81E-1", "-5.!00E-1", "-5.!30E-1", "-5.!55E-1", "-5.!75E-1", "-9.!00E-1", "-9.!40E-1", "-9.!59E-1", "-9.!69E-1", "3.!40E+0", "4.!56E+0", "4.!32E+0", "4.!57E+0", "4.!32E+0", "-3.!40E+0", "-4.!56E+0", "-4.!32E+0", "-4.!57E+0", "-4.!32E+0"]);
		check_numeric_format("0.!0!0E+0", numbers,
				["0.!0!0E+0", "1.!0!0E+0", "1.!1!0E+1", "1.!5!0E+1", "1.!9!0E+1", "2.!0!0E+2", "2.!2!0E+2", "2.!5!0E+2", "2.!8!0E+2", "1.!2!3E+3", "-1.!0!0E+0", "-1.!1!0E+1", "-1.!5!0E+1", "-1.!9!0E+1", "-2.!0!0E+2", "-2.!2!0E+2", "-2.!5!0E+2", "-2.!8!0E+2", "-9.!8!8E+3", "1.!0!0E-1", "1.!2!0E-1", "1.!5!1E-1", "1.!8!1E-1", "5.!0!0E-1", "5.!3!0E-1", "5.!5!5E-1", "5.!7!5E-1", "9.!0!0E-1", "9.!4!0E-1", "9.!5!9E-1", "9.!6!9E-1", "-1.!0!0E-1", "-1.!2!0E-1", "-1.!5!1E-1", "-1.!8!1E-1", "-5.!0!0E-1", "-5.!3!0E-1", "-5.!5!5E-1", "-5.!7!5E-1", "-9.!0!0E-1", "-9.!4!0E-1", "-9.!5!9E-1", "-9.!6!9E-1", "3.!4!0E+0", "4.!5!6E+0", "4.!3!2E+0", "4.!5!7E+0", "4.!3!2E+0", "-3.!4!0E+0", "-4.!5!6E+0", "-4.!3!2E+0", "-4.!5!7E+0", "-4.!3!2E+0"]);
		check_numeric_format("0.0E+0!", numbers,
				["0.0E+0!", "1.0E+0!", "1.1E+1!", "1.5E+1!", "1.9E+1!", "2.0E+2!", "2.2E+2!", "2.5E+2!", "2.8E+2!", "1.2E+3!", "-1.0E+0!", "-1.1E+1!", "-1.5E+1!", "-1.9E+1!", "-2.0E+2!", "-2.2E+2!", "-2.5E+2!", "-2.8E+2!", "-9.9E+3!", "1.0E-1!", "1.2E-1!", "1.5E-1!", "1.8E-1!", "5.0E-1!", "5.3E-1!", "5.6E-1!", "5.8E-1!", "9.0E-1!", "9.4E-1!", "9.6E-1!", "9.7E-1!", "-1.0E-1!", "-1.2E-1!", "-1.5E-1!", "-1.8E-1!", "-5.0E-1!", "-5.3E-1!", "-5.6E-1!", "-5.8E-1!", "-9.0E-1!", "-9.4E-1!", "-9.6E-1!", "-9.7E-1!", "3.4E+0!", "4.6E+0!", "4.3E+0!", "4.6E+0!", "4.3E+0!", "-3.4E+0!", "-4.6E+0!", "-4.3E+0!", "-4.6E+0!", "-4.3E+0!"]);
		check_numeric_format("0.0\\E!+0", numbers,
				["0.0E!+0", "1.0E!+0", "1.1E!+1", "1.5E!+1", "1.9E!+1", "2.0E!+2", "2.2E!+2", "2.5E!+2", "2.8E!+2", "1.2E!+3", "-1.0E!+0", "-1.1E!+1", "-1.5E!+1", "-1.9E!+1", "-2.0E!+2", "-2.2E!+2", "-2.5E!+2", "-2.8E!+2", "-9.9E!+3", "1.0E!-1", "1.2E!-1", "1.5E!-1", "1.8E!-1", "5.0E!-1", "5.3E!-1", "5.6E!-1", "5.8E!-1", "9.0E!-1", "9.4E!-1", "9.6E!-1", "9.7E!-1", "-1.0E!-1", "-1.2E!-1", "-1.5E!-1", "-1.8E!-1", "-5.0E!-1", "-5.3E!-1", "-5.6E!-1", "-5.8E!-1", "-9.0E!-1", "-9.4E!-1", "-9.6E!-1", "-9.7E!-1", "3.4E!+0", "4.6E!+0", "4.3E!+0", "4.6E!+0", "4.3E!+0", "-3.4E!+0", "-4.6E!+0", "-4.3E!+0", "-4.6E!+0", "-4.3E!+0"]);
		check_numeric_format("0.0\\E!+0!", numbers,
				["0.0E!+0!", "1.0E!+0!", "1.1E!+1!", "1.5E!+1!", "1.9E!+1!", "2.0E!+2!", "2.2E!+2!", "2.5E!+2!", "2.8E!+2!", "1.2E!+3!", "-1.0E!+0!", "-1.1E!+1!", "-1.5E!+1!", "-1.9E!+1!", "-2.0E!+2!", "-2.2E!+2!", "-2.5E!+2!", "-2.8E!+2!", "-9.9E!+3!", "1.0E!-1!", "1.2E!-1!", "1.5E!-1!", "1.8E!-1!", "5.0E!-1!", "5.3E!-1!", "5.6E!-1!", "5.8E!-1!", "9.0E!-1!", "9.4E!-1!", "9.6E!-1!", "9.7E!-1!", "-1.0E!-1!", "-1.2E!-1!", "-1.5E!-1!", "-1.8E!-1!", "-5.0E!-1!", "-5.3E!-1!", "-5.6E!-1!", "-5.8E!-1!", "-9.0E!-1!", "-9.4E!-1!", "-9.6E!-1!", "-9.7E!-1!", "3.4E!+0!", "4.6E!+0!", "4.3E!+0!", "4.6E!+0!", "4.3E!+0!", "-3.4E!+0!", "-4.6E!+0!", "-4.3E!+0!", "-4.6E!+0!", "-4.3E!+0!"]);
	});

	test("format3", function test_format3() {
		var numbers = [0, 1, 11, 15, 19, 200, 220, 250, 280, 1234, -1, -11, -15, -19, -200, -220, -250, -280, -9876, 0.1, 0.12, 0.151, 0.181, 0.5, 0.53, 0.555, 0.575, 0.9, 0.94, 0.959, 0.969, -0.1, -0.12, -0.151, -0.181, -0.5, -0.53, -0.555, -0.575, -0.9, -0.94, -0.959, -0.969, 3.4, 4.56, 4.32, 4.567, 4.321, -3.4, -4.56, -4.32, -4.567, -4.321];

		check_numeric_format("0%", numbers,
				["0%", "100%", "1100%", "1500%", "1900%", "20000%", "22000%", "25000%", "28000%", "123400%", "-100%", "-1100%", "-1500%", "-1900%", "-20000%", "-22000%", "-25000%", "-28000%", "-987600%", "10%", "12%", "15%", "18%", "50%", "53%", "56%", "58%", "90%", "94%", "96%", "97%", "-10%", "-12%", "-15%", "-18%", "-50%", "-53%", "-56%", "-58%", "-90%", "-94%", "-96%", "-97%", "340%", "456%", "432%", "457%", "432%", "-340%", "-456%", "-432%", "-457%", "-432%"]);
		check_numeric_format("%0", numbers,
				["%0", "%100", "%1100", "%1500", "%1900", "%20000", "%22000", "%25000", "%28000", "%123400", "-%100", "-%1100", "-%1500", "-%1900", "-%20000", "-%22000", "-%25000", "-%28000", "-%987600", "%10", "%12", "%15", "%18", "%50", "%53", "%56", "%58", "%90", "%94", "%96", "%97", "-%10", "-%12", "-%15", "-%18", "-%50", "-%53", "-%56", "-%58", "-%90", "-%94", "-%96", "-%97", "%340", "%456", "%432", "%457", "%432", "-%340", "-%456", "-%432", "-%457", "-%432"]);
		check_numeric_format("%0%", numbers,
				["%0%", "%10000%", "%110000%", "%150000%", "%190000%", "%2000000%", "%2200000%", "%2500000%", "%2800000%", "%12340000%", "-%10000%", "-%110000%", "-%150000%", "-%190000%", "-%2000000%", "-%2200000%", "-%2500000%", "-%2800000%", "-%98760000%", "%1000%", "%1200%", "%1510%", "%1810%", "%5000%", "%5300%", "%5550%", "%5750%", "%9000%", "%9400%", "%9590%", "%9690%", "-%1000%", "-%1200%", "-%1510%", "-%1810%", "-%5000%", "-%5300%", "-%5550%", "-%5750%", "-%9000%", "-%9400%", "-%9590%", "-%9690%", "%34000%", "%45600%", "%43200%", "%45670%", "%43210%", "-%34000%", "-%45600%", "-%43200%", "-%45670%", "-%43210%"]);
		check_numeric_format("0\\a\\a", numbers,
				["0aa", "1aa", "11aa", "15aa", "19aa", "200aa", "220aa", "250aa", "280aa", "1234aa", "-1aa", "-11aa", "-15aa", "-19aa", "-200aa", "-220aa", "-250aa", "-280aa", "-9876aa", "0aa", "0aa", "0aa", "0aa", "1aa", "1aa", "1aa", "1aa", "1aa", "1aa", "1aa", "1aa", "0aa", "0aa", "0aa", "0aa", "-1aa", "-1aa", "-1aa", "-1aa", "-1aa", "-1aa", "-1aa", "-1aa", "3aa", "5aa", "4aa", "5aa", "4aa", "-3aa", "-5aa", "-4aa", "-5aa", "-4aa"]);
		check_numeric_format("\\a\\a0", numbers,
				["aa0", "aa1", "aa11", "aa15", "aa19", "aa200", "aa220", "aa250", "aa280", "aa1234", "-aa1", "-aa11", "-aa15", "-aa19", "-aa200", "-aa220", "-aa250", "-aa280", "-aa9876", "aa0", "aa0", "aa0", "aa0", "aa1", "aa1", "aa1", "aa1", "aa1", "aa1", "aa1", "aa1", "aa0", "aa0", "aa0", "aa0", "-aa1", "-aa1", "-aa1", "-aa1", "-aa1", "-aa1", "-aa1", "-aa1", "aa3", "aa5", "aa4", "aa5", "aa4", "-aa3", "-aa5", "-aa4", "-aa5", "-aa4"]);
		check_numeric_format("\\a\\a0\\a\\a", numbers,
				["aa0aa", "aa1aa", "aa11aa", "aa15aa", "aa19aa", "aa200aa", "aa220aa", "aa250aa", "aa280aa", "aa1234aa", "-aa1aa", "-aa11aa", "-aa15aa", "-aa19aa", "-aa200aa", "-aa220aa", "-aa250aa", "-aa280aa", "-aa9876aa", "aa0aa", "aa0aa", "aa0aa", "aa0aa", "aa1aa", "aa1aa", "aa1aa", "aa1aa", "aa1aa", "aa1aa", "aa1aa", "aa1aa", "aa0aa", "aa0aa", "aa0aa", "aa0aa", "-aa1aa", "-aa1aa", "-aa1aa", "-aa1aa", "-aa1aa", "-aa1aa", "-aa1aa", "-aa1aa", "aa3aa", "aa5aa", "aa4aa", "aa5aa", "aa4aa", "-aa3aa", "-aa5aa", "-aa4aa", "-aa5aa", "-aa4aa"]);
		check_numeric_format("0\"%\"", numbers,
				["0%", "1%", "11%", "15%", "19%", "200%", "220%", "250%", "280%", "1234%", "-1%", "-11%", "-15%", "-19%", "-200%", "-220%", "-250%", "-280%", "-9876%", "0%", "0%", "0%", "0%", "1%", "1%", "1%", "1%", "1%", "1%", "1%", "1%", "0%", "0%", "0%", "0%", "-1%", "-1%", "-1%", "-1%", "-1%", "-1%", "-1%", "-1%", "3%", "5%", "4%", "5%", "4%", "-3%", "-5%", "-4%", "-5%", "-4%"]);
		check_numeric_format("\"%\"0", numbers,
				["%0", "%1", "%11", "%15", "%19", "%200", "%220", "%250", "%280", "%1234", "-%1", "-%11", "-%15", "-%19", "-%200", "-%220", "-%250", "-%280", "-%9876", "%0", "%0", "%0", "%0", "%1", "%1", "%1", "%1", "%1", "%1", "%1", "%1", "%0", "%0", "%0", "%0", "-%1", "-%1", "-%1", "-%1", "-%1", "-%1", "-%1", "-%1", "%3", "%5", "%4", "%5", "%4", "-%3", "-%5", "-%4", "-%5", "-%4"]);
		check_numeric_format("\"%\"0\"%\"", numbers,
				["%0%", "%1%", "%11%", "%15%", "%19%", "%200%", "%220%", "%250%", "%280%", "%1234%", "-%1%", "-%11%", "-%15%", "-%19%", "-%200%", "-%220%", "-%250%", "-%280%", "-%9876%", "%0%", "%0%", "%0%", "%0%", "%1%", "%1%", "%1%", "%1%", "%1%", "%1%", "%1%", "%1%", "%0%", "%0%", "%0%", "%0%", "-%1%", "-%1%", "-%1%", "-%1%", "-%1%", "-%1%", "-%1%", "-%1%", "%3%", "%5%", "%4%", "%5%", "%4%", "-%3%", "-%5%", "-%4%", "-%5%", "-%4%"]);
		check_numeric_format("0%0.", numbers,
				["0%0.", "10%0.", "110%0.", "150%0.", "190%0.", "2000%0.", "2200%0.", "2500%0.", "2800%0.", "12340%0.", "-10%0.", "-110%0.", "-150%0.", "-190%0.", "-2000%0.", "-2200%0.", "-2500%0.", "-2800%0.", "-98760%0.", "1%0.", "1%2.", "1%5.", "1%8.", "5%0.", "5%3.", "5%6.", "5%8.", "9%0.", "9%4.", "9%6.", "9%7.", "-1%0.", "-1%2.", "-1%5.", "-1%8.", "-5%0.", "-5%3.", "-5%6.", "-5%8.", "-9%0.", "-9%4.", "-9%6.", "-9%7.", "34%0.", "45%6.", "43%2.", "45%7.", "43%2.", "-34%0.", "-45%6.", "-43%2.", "-45%7.", "-43%2."]);
		check_numeric_format("%00.", numbers,
				["%00.", "%100.", "%1100.", "%1500.", "%1900.", "%20000.", "%22000.", "%25000.", "%28000.", "%123400.", "-%100.", "-%1100.", "-%1500.", "-%1900.", "-%20000.", "-%22000.", "-%25000.", "-%28000.", "-%987600.", "%10.", "%12.", "%15.", "%18.", "%50.", "%53.", "%56.", "%58.", "%90.", "%94.", "%96.", "%97.", "-%10.", "-%12.", "-%15.", "-%18.", "-%50.", "-%53.", "-%56.", "-%58.", "-%90.", "-%94.", "-%96.", "-%97.", "%340.", "%456.", "%432.", "%457.", "%432.", "-%340.", "-%456.", "-%432.", "-%457.", "-%432."]);
		check_numeric_format("%0%0.", numbers,
				["%0%0.", "%1000%0.", "%11000%0.", "%15000%0.", "%19000%0.", "%200000%0.", "%220000%0.", "%250000%0.", "%280000%0.", "%1234000%0.", "-%1000%0.", "-%11000%0.", "-%15000%0.", "-%19000%0.", "-%200000%0.", "-%220000%0.", "-%250000%0.", "-%280000%0.", "-%9876000%0.", "%100%0.", "%120%0.", "%151%0.", "%181%0.", "%500%0.", "%530%0.", "%555%0.", "%575%0.", "%900%0.", "%940%0.", "%959%0.", "%969%0.", "-%100%0.", "-%120%0.", "-%151%0.", "-%181%0.", "-%500%0.", "-%530%0.", "-%555%0.", "-%575%0.", "-%900%0.", "-%940%0.", "-%959%0.", "-%969%0.", "%3400%0.", "%4560%0.", "%4320%0.", "%4567%0.", "%4321%0.", "-%3400%0.", "-%4560%0.", "-%4320%0.", "-%4567%0.", "-%4321%0."]);
		check_numeric_format("0\\a\\a0.", numbers,
				["0aa0.", "0aa1.", "1aa1.", "1aa5.", "1aa9.", "20aa0.", "22aa0.", "25aa0.", "28aa0.", "123aa4.", "-0aa1.", "-1aa1.", "-1aa5.", "-1aa9.", "-20aa0.", "-22aa0.", "-25aa0.", "-28aa0.", "-987aa6.", "0aa0.", "0aa0.", "0aa0.", "0aa0.", "0aa1.", "0aa1.", "0aa1.", "0aa1.", "0aa1.", "0aa1.", "0aa1.", "0aa1.", "0aa0.", "0aa0.", "0aa0.", "0aa0.", "-0aa1.", "-0aa1.", "-0aa1.", "-0aa1.", "-0aa1.", "-0aa1.", "-0aa1.", "-0aa1.", "0aa3.", "0aa5.", "0aa4.", "0aa5.", "0aa4.", "-0aa3.", "-0aa5.", "-0aa4.", "-0aa5.", "-0aa4."]);
		check_numeric_format("\\a\\a00.", numbers,
				["aa00.", "aa01.", "aa11.", "aa15.", "aa19.", "aa200.", "aa220.", "aa250.", "aa280.", "aa1234.", "-aa01.", "-aa11.", "-aa15.", "-aa19.", "-aa200.", "-aa220.", "-aa250.", "-aa280.", "-aa9876.", "aa00.", "aa00.", "aa00.", "aa00.", "aa01.", "aa01.", "aa01.", "aa01.", "aa01.", "aa01.", "aa01.", "aa01.", "aa00.", "aa00.", "aa00.", "aa00.", "-aa01.", "-aa01.", "-aa01.", "-aa01.", "-aa01.", "-aa01.", "-aa01.", "-aa01.", "aa03.", "aa05.", "aa04.", "aa05.", "aa04.", "-aa03.", "-aa05.", "-aa04.", "-aa05.", "-aa04."]);
		check_numeric_format("\\a\\a0\\a\\a0.", numbers,
				["aa0aa0.", "aa0aa1.", "aa1aa1.", "aa1aa5.", "aa1aa9.", "aa20aa0.", "aa22aa0.", "aa25aa0.", "aa28aa0.", "aa123aa4.", "-aa0aa1.", "-aa1aa1.", "-aa1aa5.", "-aa1aa9.", "-aa20aa0.", "-aa22aa0.", "-aa25aa0.", "-aa28aa0.", "-aa987aa6.", "aa0aa0.", "aa0aa0.", "aa0aa0.", "aa0aa0.", "aa0aa1.", "aa0aa1.", "aa0aa1.", "aa0aa1.", "aa0aa1.", "aa0aa1.", "aa0aa1.", "aa0aa1.", "aa0aa0.", "aa0aa0.", "aa0aa0.", "aa0aa0.", "-aa0aa1.", "-aa0aa1.", "-aa0aa1.", "-aa0aa1.", "-aa0aa1.", "-aa0aa1.", "-aa0aa1.", "-aa0aa1.", "aa0aa3.", "aa0aa5.", "aa0aa4.", "aa0aa5.", "aa0aa4.", "-aa0aa3.", "-aa0aa5.", "-aa0aa4.", "-aa0aa5.", "-aa0aa4."]);
		check_numeric_format("0\"%\"0.", numbers,
				["0%0.", "0%1.", "1%1.", "1%5.", "1%9.", "20%0.", "22%0.", "25%0.", "28%0.", "123%4.", "-0%1.", "-1%1.", "-1%5.", "-1%9.", "-20%0.", "-22%0.", "-25%0.", "-28%0.", "-987%6.", "0%0.", "0%0.", "0%0.", "0%0.", "0%1.", "0%1.", "0%1.", "0%1.", "0%1.", "0%1.", "0%1.", "0%1.", "0%0.", "0%0.", "0%0.", "0%0.", "-0%1.", "-0%1.", "-0%1.", "-0%1.", "-0%1.", "-0%1.", "-0%1.", "-0%1.", "0%3.", "0%5.", "0%4.", "0%5.", "0%4.", "-0%3.", "-0%5.", "-0%4.", "-0%5.", "-0%4."]);
		check_numeric_format("\"%\"00.", numbers,
				["%00.", "%01.", "%11.", "%15.", "%19.", "%200.", "%220.", "%250.", "%280.", "%1234.", "-%01.", "-%11.", "-%15.", "-%19.", "-%200.", "-%220.", "-%250.", "-%280.", "-%9876.", "%00.", "%00.", "%00.", "%00.", "%01.", "%01.", "%01.", "%01.", "%01.", "%01.", "%01.", "%01.", "%00.", "%00.", "%00.", "%00.", "-%01.", "-%01.", "-%01.", "-%01.", "-%01.", "-%01.", "-%01.", "-%01.", "%03.", "%05.", "%04.", "%05.", "%04.", "-%03.", "-%05.", "-%04.", "-%05.", "-%04."]);
		check_numeric_format("\"%\"0\"%\"0.", numbers,
				["%0%0.", "%0%1.", "%1%1.", "%1%5.", "%1%9.", "%20%0.", "%22%0.", "%25%0.", "%28%0.", "%123%4.", "-%0%1.", "-%1%1.", "-%1%5.", "-%1%9.", "-%20%0.", "-%22%0.", "-%25%0.", "-%28%0.", "-%987%6.", "%0%0.", "%0%0.", "%0%0.", "%0%0.", "%0%1.", "%0%1.", "%0%1.", "%0%1.", "%0%1.", "%0%1.", "%0%1.", "%0%1.", "%0%0.", "%0%0.", "%0%0.", "%0%0.", "-%0%1.", "-%0%1.", "-%0%1.", "-%0%1.", "-%0%1.", "-%0%1.", "-%0%1.", "-%0%1.", "%0%3.", "%0%5.", "%0%4.", "%0%5.", "%0%4.", "-%0%3.", "-%0%5.", "-%0%4.", "-%0%5.", "-%0%4."]);
		check_numeric_format(".0%", numbers,
				[".0%", "100.0%", "1100.0%", "1500.0%", "1900.0%", "20000.0%", "22000.0%", "25000.0%", "28000.0%", "123400.0%", "-100.0%", "-1100.0%", "-1500.0%", "-1900.0%", "-20000.0%", "-22000.0%", "-25000.0%", "-28000.0%", "-987600.0%", "10.0%", "12.0%", "15.1%", "18.1%", "50.0%", "53.0%", "55.5%", "57.5%", "90.0%", "94.0%", "95.9%", "96.9%", "-10.0%", "-12.0%", "-15.1%", "-18.1%", "-50.0%", "-53.0%", "-55.5%", "-57.5%", "-90.0%", "-94.0%", "-95.9%", "-96.9%", "340.0%", "456.0%", "432.0%", "456.7%", "432.1%", "-340.0%", "-456.0%", "-432.0%", "-456.7%", "-432.1%"]);
		check_numeric_format(".%0", numbers,
				[".%0", "100.%0", "1100.%0", "1500.%0", "1900.%0", "20000.%0", "22000.%0", "25000.%0", "28000.%0", "123400.%0", "-100.%0", "-1100.%0", "-1500.%0", "-1900.%0", "-20000.%0", "-22000.%0", "-25000.%0", "-28000.%0", "-987600.%0", "10.%0", "12.%0", "15.%1", "18.%1", "50.%0", "53.%0", "55.%5", "57.%5", "90.%0", "94.%0", "95.%9", "96.%9", "-10.%0", "-12.%0", "-15.%1", "-18.%1", "-50.%0", "-53.%0", "-55.%5", "-57.%5", "-90.%0", "-94.%0", "-95.%9", "-96.%9", "340.%0", "456.%0", "432.%0", "456.%7", "432.%1", "-340.%0", "-456.%0", "-432.%0", "-456.%7", "-432.%1"]);
		check_numeric_format(".%0%", numbers,
				[".%0%", "10000.%0%", "110000.%0%", "150000.%0%", "190000.%0%", "2000000.%0%", "2200000.%0%", "2500000.%0%", "2800000.%0%", "12340000.%0%", "-10000.%0%", "-110000.%0%", "-150000.%0%", "-190000.%0%", "-2000000.%0%", "-2200000.%0%", "-2500000.%0%", "-2800000.%0%", "############", "1000.%0%", "1200.%0%", "1510.%0%", "1810.%0%", "5000.%0%", "5300.%0%", "5550.%0%", "5750.%0%", "9000.%0%", "9400.%0%", "9590.%0%", "9690.%0%", "-1000.%0%", "-1200.%0%", "-1510.%0%", "-1810.%0%", "-5000.%0%", "-5300.%0%", "-5550.%0%", "-5750.%0%", "-9000.%0%", "-9400.%0%", "-9590.%0%", "-9690.%0%", "34000.%0%", "45600.%0%", "43200.%0%", "45670.%0%", "43210.%0%", "-34000.%0%", "-45600.%0%", "-43200.%0%", "-45670.%0%", "-43210.%0%"]);
		check_numeric_format(".0\\a\\a", numbers,
				[".0aa", "1.0aa", "11.0aa", "15.0aa", "19.0aa", "200.0aa", "220.0aa", "250.0aa", "280.0aa", "1234.0aa", "-1.0aa", "-11.0aa", "-15.0aa", "-19.0aa", "-200.0aa", "-220.0aa", "-250.0aa", "-280.0aa", "-9876.0aa", ".1aa", ".1aa", ".2aa", ".2aa", ".5aa", ".5aa", ".6aa", ".6aa", ".9aa", ".9aa", "1.0aa", "1.0aa", "-.1aa", "-.1aa", "-.2aa", "-.2aa", "-.5aa", "-.5aa", "-.6aa", "-.6aa", "-.9aa", "-.9aa", "-1.0aa", "-1.0aa", "3.4aa", "4.6aa", "4.3aa", "4.6aa", "4.3aa", "-3.4aa", "-4.6aa", "-4.3aa", "-4.6aa", "-4.3aa"]);
		check_numeric_format(".\\a\\a0", numbers,
				[".aa0", "1.aa0", "11.aa0", "15.aa0", "19.aa0", "200.aa0", "220.aa0", "250.aa0", "280.aa0", "1234.aa0", "-1.aa0", "-11.aa0", "-15.aa0", "-19.aa0", "-200.aa0", "-220.aa0", "-250.aa0", "-280.aa0", "-9876.aa0", ".aa1", ".aa1", ".aa2", ".aa2", ".aa5", ".aa5", ".aa6", ".aa6", ".aa9", ".aa9", "1.aa0", "1.aa0", "-.aa1", "-.aa1", "-.aa2", "-.aa2", "-.aa5", "-.aa5", "-.aa6", "-.aa6", "-.aa9", "-.aa9", "-1.aa0", "-1.aa0", "3.aa4", "4.aa6", "4.aa3", "4.aa6", "4.aa3", "-3.aa4", "-4.aa6", "-4.aa3", "-4.aa6", "-4.aa3"]);
		check_numeric_format(".\\a\\a0\\a\\a", numbers,
				[".aa0aa", "1.aa0aa", "11.aa0aa", "15.aa0aa", "19.aa0aa", "200.aa0aa", "220.aa0aa", "250.aa0aa", "280.aa0aa", "1234.aa0aa", "-1.aa0aa", "-11.aa0aa", "-15.aa0aa", "-19.aa0aa", "-200.aa0aa", "-220.aa0aa", "-250.aa0aa", "-280.aa0aa", "-9876.aa0aa", ".aa1aa", ".aa1aa", ".aa2aa", ".aa2aa", ".aa5aa", ".aa5aa", ".aa6aa", ".aa6aa", ".aa9aa", ".aa9aa", "1.aa0aa", "1.aa0aa", "-.aa1aa", "-.aa1aa", "-.aa2aa", "-.aa2aa", "-.aa5aa", "-.aa5aa", "-.aa6aa", "-.aa6aa", "-.aa9aa", "-.aa9aa", "-1.aa0aa", "-1.aa0aa", "3.aa4aa", "4.aa6aa", "4.aa3aa", "4.aa6aa", "4.aa3aa", "-3.aa4aa", "-4.aa6aa", "-4.aa3aa", "-4.aa6aa", "-4.aa3aa"]);
		check_numeric_format(".0\"%\"", numbers,
				[".0%", "1.0%", "11.0%", "15.0%", "19.0%", "200.0%", "220.0%", "250.0%", "280.0%", "1234.0%", "-1.0%", "-11.0%", "-15.0%", "-19.0%", "-200.0%", "-220.0%", "-250.0%", "-280.0%", "-9876.0%", ".1%", ".1%", ".2%", ".2%", ".5%", ".5%", ".6%", ".6%", ".9%", ".9%", "1.0%", "1.0%", "-.1%", "-.1%", "-.2%", "-.2%", "-.5%", "-.5%", "-.6%", "-.6%", "-.9%", "-.9%", "-1.0%", "-1.0%", "3.4%", "4.6%", "4.3%", "4.6%", "4.3%", "-3.4%", "-4.6%", "-4.3%", "-4.6%", "-4.3%"]);
		check_numeric_format(".\"%\"0", numbers,
				[".%0", "1.%0", "11.%0", "15.%0", "19.%0", "200.%0", "220.%0", "250.%0", "280.%0", "1234.%0", "-1.%0", "-11.%0", "-15.%0", "-19.%0", "-200.%0", "-220.%0", "-250.%0", "-280.%0", "-9876.%0", ".%1", ".%1", ".%2", ".%2", ".%5", ".%5", ".%6", ".%6", ".%9", ".%9", "1.%0", "1.%0", "-.%1", "-.%1", "-.%2", "-.%2", "-.%5", "-.%5", "-.%6", "-.%6", "-.%9", "-.%9", "-1.%0", "-1.%0", "3.%4", "4.%6", "4.%3", "4.%6", "4.%3", "-3.%4", "-4.%6", "-4.%3", "-4.%6", "-4.%3"]);
		check_numeric_format(".\"%\"0\"%\"", numbers,
				[".%0%", "1.%0%", "11.%0%", "15.%0%", "19.%0%", "200.%0%", "220.%0%", "250.%0%", "280.%0%", "1234.%0%", "-1.%0%", "-11.%0%", "-15.%0%", "-19.%0%", "-200.%0%", "-220.%0%", "-250.%0%", "-280.%0%", "-9876.%0%", ".%1%", ".%1%", ".%2%", ".%2%", ".%5%", ".%5%", ".%6%", ".%6%", ".%9%", ".%9%", "1.%0%", "1.%0%", "-.%1%", "-.%1%", "-.%2%", "-.%2%", "-.%5%", "-.%5%", "-.%6%", "-.%6%", "-.%9%", "-.%9%", "-1.%0%", "-1.%0%", "3.%4%", "4.%6%", "4.%3%", "4.%6%", "4.%3%", "-3.%4%", "-4.%6%", "-4.%3%", "-4.%6%", "-4.%3%"]);
		check_numeric_format("0%0.0", numbers,
				["0%0.0", "10%0.0", "110%0.0", "150%0.0", "190%0.0", "2000%0.0", "2200%0.0", "2500%0.0", "2800%0.0", "12340%0.0", "-10%0.0", "-110%0.0", "-150%0.0", "-190%0.0", "-2000%0.0", "-2200%0.0", "-2500%0.0", "-2800%0.0", "-98760%0.0", "1%0.0", "1%2.0", "1%5.1", "1%8.1", "5%0.0", "5%3.0", "5%5.5", "5%7.5", "9%0.0", "9%4.0", "9%5.9", "9%6.9", "-1%0.0", "-1%2.0", "-1%5.1", "-1%8.1", "-5%0.0", "-5%3.0", "-5%5.5", "-5%7.5", "-9%0.0", "-9%4.0", "-9%5.9", "-9%6.9", "34%0.0", "45%6.0", "43%2.0", "45%6.7", "43%2.1", "-34%0.0", "-45%6.0", "-43%2.0", "-45%6.7", "-43%2.1"]);
		check_numeric_format("%00.0", numbers,
				["%00.0", "%100.0", "%1100.0", "%1500.0", "%1900.0", "%20000.0", "%22000.0", "%25000.0", "%28000.0", "%123400.0", "-%100.0", "-%1100.0", "-%1500.0", "-%1900.0", "-%20000.0", "-%22000.0", "-%25000.0", "-%28000.0", "-%987600.0", "%10.0", "%12.0", "%15.1", "%18.1", "%50.0", "%53.0", "%55.5", "%57.5", "%90.0", "%94.0", "%95.9", "%96.9", "-%10.0", "-%12.0", "-%15.1", "-%18.1", "-%50.0", "-%53.0", "-%55.5", "-%57.5", "-%90.0", "-%94.0", "-%95.9", "-%96.9", "%340.0", "%456.0", "%432.0", "%456.7", "%432.1", "-%340.0", "-%456.0", "-%432.0", "-%456.7", "-%432.1"]);
		check_numeric_format("%0%0.0", numbers,
				["%0%0.0", "%1000%0.0", "%11000%0.0", "%15000%0.0", "%19000%0.0", "%200000%0.0", "%220000%0.0", "%250000%0.0", "%280000%0.0", "%1234000%0.0", "-%1000%0.0", "-%11000%0.0", "-%15000%0.0", "-%19000%0.0", "-%200000%0.0", "-%220000%0.0", "-%250000%0.0", "-%280000%0.0", "############", "%100%0.0", "%120%0.0", "%151%0.0", "%181%0.0", "%500%0.0", "%530%0.0", "%555%0.0", "%575%0.0", "%900%0.0", "%940%0.0", "%959%0.0", "%969%0.0", "-%100%0.0", "-%120%0.0", "-%151%0.0", "-%181%0.0", "-%500%0.0", "-%530%0.0", "-%555%0.0", "-%575%0.0", "-%900%0.0", "-%940%0.0", "-%959%0.0", "-%969%0.0", "%3400%0.0", "%4560%0.0", "%4320%0.0", "%4567%0.0", "%4321%0.0", "-%3400%0.0", "-%4560%0.0", "-%4320%0.0", "-%4567%0.0", "-%4321%0.0"]);
		check_numeric_format("0.0%", numbers,
				["0.0%", "100.0%", "1100.0%", "1500.0%", "1900.0%", "20000.0%", "22000.0%", "25000.0%", "28000.0%", "123400.0%", "-100.0%", "-1100.0%", "-1500.0%", "-1900.0%", "-20000.0%", "-22000.0%", "-25000.0%", "-28000.0%", "-987600.0%", "10.0%", "12.0%", "15.1%", "18.1%", "50.0%", "53.0%", "55.5%", "57.5%", "90.0%", "94.0%", "95.9%", "96.9%", "-10.0%", "-12.0%", "-15.1%", "-18.1%", "-50.0%", "-53.0%", "-55.5%", "-57.5%", "-90.0%", "-94.0%", "-95.9%", "-96.9%", "340.0%", "456.0%", "432.0%", "456.7%", "432.1%", "-340.0%", "-456.0%", "-432.0%", "-456.7%", "-432.1%"]);
		check_numeric_format("0.%0", numbers,
				["0.%0", "100.%0", "1100.%0", "1500.%0", "1900.%0", "20000.%0", "22000.%0", "25000.%0", "28000.%0", "123400.%0", "-100.%0", "-1100.%0", "-1500.%0", "-1900.%0", "-20000.%0", "-22000.%0", "-25000.%0", "-28000.%0", "-987600.%0", "10.%0", "12.%0", "15.%1", "18.%1", "50.%0", "53.%0", "55.%5", "57.%5", "90.%0", "94.%0", "95.%9", "96.%9", "-10.%0", "-12.%0", "-15.%1", "-18.%1", "-50.%0", "-53.%0", "-55.%5", "-57.%5", "-90.%0", "-94.%0", "-95.%9", "-96.%9", "340.%0", "456.%0", "432.%0", "456.%7", "432.%1", "-340.%0", "-456.%0", "-432.%0", "-456.%7", "-432.%1"]);
		check_numeric_format("0.%0%", numbers,
				["0.%0%", "10000.%0%", "110000.%0%", "150000.%0%", "190000.%0%", "2000000.%0%", "2200000.%0%", "2500000.%0%", "2800000.%0%", "12340000.%0%", "-10000.%0%", "-110000.%0%", "-150000.%0%", "-190000.%0%", "-2000000.%0%", "-2200000.%0%", "-2500000.%0%", "-2800000.%0%", "############", "1000.%0%", "1200.%0%", "1510.%0%", "1810.%0%", "5000.%0%", "5300.%0%", "5550.%0%", "5750.%0%", "9000.%0%", "9400.%0%", "9590.%0%", "9690.%0%", "-1000.%0%", "-1200.%0%", "-1510.%0%", "-1810.%0%", "-5000.%0%", "-5300.%0%", "-5550.%0%", "-5750.%0%", "-9000.%0%", "-9400.%0%", "-9590.%0%", "-9690.%0%", "34000.%0%", "45600.%0%", "43200.%0%", "45670.%0%", "43210.%0%", "-34000.%0%", "-45600.%0%", "-43200.%0%", "-45670.%0%", "-43210.%0%"]);
		check_numeric_format("0\\a\\a0.0", numbers,
				["0aa0.0", "0aa1.0", "1aa1.0", "1aa5.0", "1aa9.0", "20aa0.0", "22aa0.0", "25aa0.0", "28aa0.0", "123aa4.0", "-0aa1.0", "-1aa1.0", "-1aa5.0", "-1aa9.0", "-20aa0.0", "-22aa0.0", "-25aa0.0", "-28aa0.0", "-987aa6.0", "0aa0.1", "0aa0.1", "0aa0.2", "0aa0.2", "0aa0.5", "0aa0.5", "0aa0.6", "0aa0.6", "0aa0.9", "0aa0.9", "0aa1.0", "0aa1.0", "-0aa0.1", "-0aa0.1", "-0aa0.2", "-0aa0.2", "-0aa0.5", "-0aa0.5", "-0aa0.6", "-0aa0.6", "-0aa0.9", "-0aa0.9", "-0aa1.0", "-0aa1.0", "0aa3.4", "0aa4.6", "0aa4.3", "0aa4.6", "0aa4.3", "-0aa3.4", "-0aa4.6", "-0aa4.3", "-0aa4.6", "-0aa4.3"]);
		check_numeric_format("\\a\\a00.0", numbers,
				["aa00.0", "aa01.0", "aa11.0", "aa15.0", "aa19.0", "aa200.0", "aa220.0", "aa250.0", "aa280.0", "aa1234.0", "-aa01.0", "-aa11.0", "-aa15.0", "-aa19.0", "-aa200.0", "-aa220.0", "-aa250.0", "-aa280.0", "-aa9876.0", "aa00.1", "aa00.1", "aa00.2", "aa00.2", "aa00.5", "aa00.5", "aa00.6", "aa00.6", "aa00.9", "aa00.9", "aa01.0", "aa01.0", "-aa00.1", "-aa00.1", "-aa00.2", "-aa00.2", "-aa00.5", "-aa00.5", "-aa00.6", "-aa00.6", "-aa00.9", "-aa00.9", "-aa01.0", "-aa01.0", "aa03.4", "aa04.6", "aa04.3", "aa04.6", "aa04.3", "-aa03.4", "-aa04.6", "-aa04.3", "-aa04.6", "-aa04.3"]);
		check_numeric_format("\\a\\a0\\a\\a0.0", numbers,
				["aa0aa0.0", "aa0aa1.0", "aa1aa1.0", "aa1aa5.0", "aa1aa9.0", "aa20aa0.0", "aa22aa0.0", "aa25aa0.0", "aa28aa0.0", "aa123aa4.0", "-aa0aa1.0", "-aa1aa1.0", "-aa1aa5.0", "-aa1aa9.0", "-aa20aa0.0", "-aa22aa0.0", "-aa25aa0.0", "-aa28aa0.0", "-aa987aa6.0", "aa0aa0.1", "aa0aa0.1", "aa0aa0.2", "aa0aa0.2", "aa0aa0.5", "aa0aa0.5", "aa0aa0.6", "aa0aa0.6", "aa0aa0.9", "aa0aa0.9", "aa0aa1.0", "aa0aa1.0", "-aa0aa0.1", "-aa0aa0.1", "-aa0aa0.2", "-aa0aa0.2", "-aa0aa0.5", "-aa0aa0.5", "-aa0aa0.6", "-aa0aa0.6", "-aa0aa0.9", "-aa0aa0.9", "-aa0aa1.0", "-aa0aa1.0", "aa0aa3.4", "aa0aa4.6", "aa0aa4.3", "aa0aa4.6", "aa0aa4.3", "-aa0aa3.4", "-aa0aa4.6", "-aa0aa4.3", "-aa0aa4.6", "-aa0aa4.3"]);
		check_numeric_format("0.0\\a\\a", numbers,
				["0.0aa", "1.0aa", "11.0aa", "15.0aa", "19.0aa", "200.0aa", "220.0aa", "250.0aa", "280.0aa", "1234.0aa", "-1.0aa", "-11.0aa", "-15.0aa", "-19.0aa", "-200.0aa", "-220.0aa", "-250.0aa", "-280.0aa", "-9876.0aa", "0.1aa", "0.1aa", "0.2aa", "0.2aa", "0.5aa", "0.5aa", "0.6aa", "0.6aa", "0.9aa", "0.9aa", "1.0aa", "1.0aa", "-0.1aa", "-0.1aa", "-0.2aa", "-0.2aa", "-0.5aa", "-0.5aa", "-0.6aa", "-0.6aa", "-0.9aa", "-0.9aa", "-1.0aa", "-1.0aa", "3.4aa", "4.6aa", "4.3aa", "4.6aa", "4.3aa", "-3.4aa", "-4.6aa", "-4.3aa", "-4.6aa", "-4.3aa"]);
		check_numeric_format("0.\\a\\a0", numbers,
				["0.aa0", "1.aa0", "11.aa0", "15.aa0", "19.aa0", "200.aa0", "220.aa0", "250.aa0", "280.aa0", "1234.aa0", "-1.aa0", "-11.aa0", "-15.aa0", "-19.aa0", "-200.aa0", "-220.aa0", "-250.aa0", "-280.aa0", "-9876.aa0", "0.aa1", "0.aa1", "0.aa2", "0.aa2", "0.aa5", "0.aa5", "0.aa6", "0.aa6", "0.aa9", "0.aa9", "1.aa0", "1.aa0", "-0.aa1", "-0.aa1", "-0.aa2", "-0.aa2", "-0.aa5", "-0.aa5", "-0.aa6", "-0.aa6", "-0.aa9", "-0.aa9", "-1.aa0", "-1.aa0", "3.aa4", "4.aa6", "4.aa3", "4.aa6", "4.aa3", "-3.aa4", "-4.aa6", "-4.aa3", "-4.aa6", "-4.aa3"]);
		check_numeric_format("0.\\a\\a0\\a\\a", numbers,
				["0.aa0aa", "1.aa0aa", "11.aa0aa", "15.aa0aa", "19.aa0aa", "200.aa0aa", "220.aa0aa", "250.aa0aa", "280.aa0aa", "1234.aa0aa", "-1.aa0aa", "-11.aa0aa", "-15.aa0aa", "-19.aa0aa", "-200.aa0aa", "-220.aa0aa", "-250.aa0aa", "-280.aa0aa", "-9876.aa0aa", "0.aa1aa", "0.aa1aa", "0.aa2aa", "0.aa2aa", "0.aa5aa", "0.aa5aa", "0.aa6aa", "0.aa6aa", "0.aa9aa", "0.aa9aa", "1.aa0aa", "1.aa0aa", "-0.aa1aa", "-0.aa1aa", "-0.aa2aa", "-0.aa2aa", "-0.aa5aa", "-0.aa5aa", "-0.aa6aa", "-0.aa6aa", "-0.aa9aa", "-0.aa9aa", "-1.aa0aa", "-1.aa0aa", "3.aa4aa", "4.aa6aa", "4.aa3aa", "4.aa6aa", "4.aa3aa", "-3.aa4aa", "-4.aa6aa", "-4.aa3aa", "-4.aa6aa", "-4.aa3aa"]);
		check_numeric_format("0\"%\"0.0", numbers,
				["0%0.0", "0%1.0", "1%1.0", "1%5.0", "1%9.0", "20%0.0", "22%0.0", "25%0.0", "28%0.0", "123%4.0", "-0%1.0", "-1%1.0", "-1%5.0", "-1%9.0", "-20%0.0", "-22%0.0", "-25%0.0", "-28%0.0", "-987%6.0", "0%0.1", "0%0.1", "0%0.2", "0%0.2", "0%0.5", "0%0.5", "0%0.6", "0%0.6", "0%0.9", "0%0.9", "0%1.0", "0%1.0", "-0%0.1", "-0%0.1", "-0%0.2", "-0%0.2", "-0%0.5", "-0%0.5", "-0%0.6", "-0%0.6", "-0%0.9", "-0%0.9", "-0%1.0", "-0%1.0", "0%3.4", "0%4.6", "0%4.3", "0%4.6", "0%4.3", "-0%3.4", "-0%4.6", "-0%4.3", "-0%4.6", "-0%4.3"]);
		check_numeric_format("\"%\"00.0", numbers,
				["%00.0", "%01.0", "%11.0", "%15.0", "%19.0", "%200.0", "%220.0", "%250.0", "%280.0", "%1234.0", "-%01.0", "-%11.0", "-%15.0", "-%19.0", "-%200.0", "-%220.0", "-%250.0", "-%280.0", "-%9876.0", "%00.1", "%00.1", "%00.2", "%00.2", "%00.5", "%00.5", "%00.6", "%00.6", "%00.9", "%00.9", "%01.0", "%01.0", "-%00.1", "-%00.1", "-%00.2", "-%00.2", "-%00.5", "-%00.5", "-%00.6", "-%00.6", "-%00.9", "-%00.9", "-%01.0", "-%01.0", "%03.4", "%04.6", "%04.3", "%04.6", "%04.3", "-%03.4", "-%04.6", "-%04.3", "-%04.6", "-%04.3"]);
		check_numeric_format("\"%\"0\"%\"0.0", numbers,
				["%0%0.0", "%0%1.0", "%1%1.0", "%1%5.0", "%1%9.0", "%20%0.0", "%22%0.0", "%25%0.0", "%28%0.0", "%123%4.0", "-%0%1.0", "-%1%1.0", "-%1%5.0", "-%1%9.0", "-%20%0.0", "-%22%0.0", "-%25%0.0", "-%28%0.0", "-%987%6.0", "%0%0.1", "%0%0.1", "%0%0.2", "%0%0.2", "%0%0.5", "%0%0.5", "%0%0.6", "%0%0.6", "%0%0.9", "%0%0.9", "%0%1.0", "%0%1.0", "-%0%0.1", "-%0%0.1", "-%0%0.2", "-%0%0.2", "-%0%0.5", "-%0%0.5", "-%0%0.6", "-%0%0.6", "-%0%0.9", "-%0%0.9", "-%0%1.0", "-%0%1.0", "%0%3.4", "%0%4.6", "%0%4.3", "%0%4.6", "%0%4.3", "-%0%3.4", "-%0%4.6", "-%0%4.3", "-%0%4.6", "-%0%4.3"]);
		check_numeric_format("0.0\"%\"", numbers,
				["0.0%", "1.0%", "11.0%", "15.0%", "19.0%", "200.0%", "220.0%", "250.0%", "280.0%", "1234.0%", "-1.0%", "-11.0%", "-15.0%", "-19.0%", "-200.0%", "-220.0%", "-250.0%", "-280.0%", "-9876.0%", "0.1%", "0.1%", "0.2%", "0.2%", "0.5%", "0.5%", "0.6%", "0.6%", "0.9%", "0.9%", "1.0%", "1.0%", "-0.1%", "-0.1%", "-0.2%", "-0.2%", "-0.5%", "-0.5%", "-0.6%", "-0.6%", "-0.9%", "-0.9%", "-1.0%", "-1.0%", "3.4%", "4.6%", "4.3%", "4.6%", "4.3%", "-3.4%", "-4.6%", "-4.3%", "-4.6%", "-4.3%"]);
		check_numeric_format("0.\"%\"0", numbers,
				["0.%0", "1.%0", "11.%0", "15.%0", "19.%0", "200.%0", "220.%0", "250.%0", "280.%0", "1234.%0", "-1.%0", "-11.%0", "-15.%0", "-19.%0", "-200.%0", "-220.%0", "-250.%0", "-280.%0", "-9876.%0", "0.%1", "0.%1", "0.%2", "0.%2", "0.%5", "0.%5", "0.%6", "0.%6", "0.%9", "0.%9", "1.%0", "1.%0", "-0.%1", "-0.%1", "-0.%2", "-0.%2", "-0.%5", "-0.%5", "-0.%6", "-0.%6", "-0.%9", "-0.%9", "-1.%0", "-1.%0", "3.%4", "4.%6", "4.%3", "4.%6", "4.%3", "-3.%4", "-4.%6", "-4.%3", "-4.%6", "-4.%3"]);
		check_numeric_format("0.\"%\"0\"%\"", numbers,
				["0.%0%", "1.%0%", "11.%0%", "15.%0%", "19.%0%", "200.%0%", "220.%0%", "250.%0%", "280.%0%", "1234.%0%", "-1.%0%", "-11.%0%", "-15.%0%", "-19.%0%", "-200.%0%", "-220.%0%", "-250.%0%", "-280.%0%", "-9876.%0%", "0.%1%", "0.%1%", "0.%2%", "0.%2%", "0.%5%", "0.%5%", "0.%6%", "0.%6%", "0.%9%", "0.%9%", "1.%0%", "1.%0%", "-0.%1%", "-0.%1%", "-0.%2%", "-0.%2%", "-0.%5%", "-0.%5%", "-0.%6%", "-0.%6%", "-0.%9%", "-0.%9%", "-1.%0%", "-1.%0%", "3.%4%", "4.%6%", "4.%3%", "4.%6%", "4.%3%", "-3.%4%", "-4.%6%", "-4.%3%", "-4.%6%", "-4.%3%"]);
		check_numeric_format("0%0E-0", numbers,
				["0%0E0", "0%1E0", "1%1E0", "1%5E0", "1%9E0", "0%2E2", "0%2E2", "0%3E2", "0%3E2", "1%2E2", "-0%1E0", "-1%1E0", "-1%5E0", "-1%9E0", "-0%2E2", "-0%2E2", "-0%3E2", "-0%3E2", "-9%9E2", "1%0E-2", "1%2E-2", "1%5E-2", "1%8E-2", "5%0E-2", "5%3E-2", "5%6E-2", "5%8E-2", "9%0E-2", "9%4E-2", "9%6E-2", "9%7E-2", "-1%0E-2", "-1%2E-2", "-1%5E-2", "-1%8E-2", "-5%0E-2", "-5%3E-2", "-5%6E-2", "-5%8E-2", "-9%0E-2", "-9%4E-2", "-9%6E-2", "-9%7E-2", "0%3E0", "0%5E0", "0%4E0", "0%5E0", "0%4E0", "-0%3E0", "-0%5E0", "-0%4E0", "-0%5E0", "-0%4E0"]);
		check_numeric_format("%00E-0", numbers,
				["%00E0", "%01E0", "%11E0", "%15E0", "%19E0", "%02E2", "%02E2", "%03E2", "%03E2", "%12E2", "-%01E0", "-%11E0", "-%15E0", "-%19E0", "-%02E2", "-%02E2", "-%03E2", "-%03E2", "-%99E2", "%10E-2", "%12E-2", "%15E-2", "%18E-2", "%50E-2", "%53E-2", "%56E-2", "%58E-2", "%90E-2", "%94E-2", "%96E-2", "%97E-2", "-%10E-2", "-%12E-2", "-%15E-2", "-%18E-2", "-%50E-2", "-%53E-2", "-%56E-2", "-%58E-2", "-%90E-2", "-%94E-2", "-%96E-2", "-%97E-2", "%03E0", "%05E0", "%04E0", "%05E0", "%04E0", "-%03E0", "-%05E0", "-%04E0", "-%05E0", "-%04E0"]);
		check_numeric_format("%0%0E-0", numbers,
				["%0%0E0", "%0%1E0", "%1%1E0", "%1%5E0", "%1%9E0", "%0%2E2", "%0%2E2", "%0%3E2", "%0%3E2", "%1%2E2", "-%0%1E0", "-%1%1E0", "-%1%5E0", "-%1%9E0", "-%0%2E2", "-%0%2E2", "-%0%3E2", "-%0%3E2", "-%9%9E2", "%1%0E-2", "%1%2E-2", "%1%5E-2", "%1%8E-2", "%5%0E-2", "%5%3E-2", "%5%6E-2", "%5%8E-2", "%9%0E-2", "%9%4E-2", "%9%6E-2", "%9%7E-2", "-%1%0E-2", "-%1%2E-2", "-%1%5E-2", "-%1%8E-2", "-%5%0E-2", "-%5%3E-2", "-%5%6E-2", "-%5%8E-2", "-%9%0E-2", "-%9%4E-2", "-%9%6E-2", "-%9%7E-2", "%0%3E0", "%0%5E0", "%0%4E0", "%0%5E0", "%0%4E0", "-%0%3E0", "-%0%5E0", "-%0%4E0", "-%0%5E0", "-%0%4E0"]);
		check_numeric_format("0E-0%", numbers,
				["0E0%", "1E0%", "1E1%", "2E1%", "2E1%", "2E2%", "2E2%", "3E2%", "3E2%", "1E3%", "-1E0%", "-1E1%", "-2E1%", "-2E1%", "-2E2%", "-2E2%", "-3E2%", "-3E2%", "-1E4%", "1E-1%", "1E-1%", "2E-1%", "2E-1%", "5E-1%", "5E-1%", "6E-1%", "6E-1%", "9E-1%", "9E-1%", "1E0%", "1E0%", "-1E-1%", "-1E-1%", "-2E-1%", "-2E-1%", "-5E-1%", "-5E-1%", "-6E-1%", "-6E-1%", "-9E-1%", "-9E-1%", "-1E0%", "-1E0%", "3E0%", "5E0%", "4E0%", "5E0%", "4E0%", "-3E0%", "-5E0%", "-4E0%", "-5E0%", "-4E0%"]);
		check_numeric_format("0\\E%-0", numbers,
				["0E%0", "1E%0", "1E%1", "2E%1", "2E%1", "2E%2", "2E%2", "3E%2", "3E%2", "1E%3", "-1E%0", "-1E%1", "-2E%1", "-2E%1", "-2E%2", "-2E%2", "-3E%2", "-3E%2", "-1E%4", "1E%-1", "1E%-1", "2E%-1", "2E%-1", "5E%-1", "5E%-1", "6E%-1", "6E%-1", "9E%-1", "9E%-1", "1E%0", "1E%0", "-1E%-1", "-1E%-1", "-2E%-1", "-2E%-1", "-5E%-1", "-5E%-1", "-6E%-1", "-6E%-1", "-9E%-1", "-9E%-1", "-1E%0", "-1E%0", "3E%0", "5E%0", "4E%0", "5E%0", "4E%0", "-3E%0", "-5E%0", "-4E%0", "-5E%0", "-4E%0"]);
		check_numeric_format("0\\E%-0%", numbers,
				["0E%0%", "1E%0%", "1E%1%", "2E%1%", "2E%1%", "2E%2%", "2E%2%", "3E%2%", "3E%2%", "1E%3%", "-1E%0%", "-1E%1%", "-2E%1%", "-2E%1%", "-2E%2%", "-2E%2%", "-3E%2%", "-3E%2%", "-1E%4%", "1E%-1%", "1E%-1%", "2E%-1%", "2E%-1%", "5E%-1%", "5E%-1%", "6E%-1%", "6E%-1%", "9E%-1%", "9E%-1%", "1E%0%", "1E%0%", "-1E%-1%", "-1E%-1%", "-2E%-1%", "-2E%-1%", "-5E%-1%", "-5E%-1%", "-6E%-1%", "-6E%-1%", "-9E%-1%", "-9E%-1%", "-1E%0%", "-1E%0%", "3E%0%", "5E%0%", "4E%0%", "5E%0%", "4E%0%", "-3E%0%", "-5E%0%", "-4E%0%", "-5E%0%", "-4E%0%"]);
		check_numeric_format("0\\a\\a0E-0", numbers,
				["0aa0E0", "0aa1E0", "1aa1E0", "1aa5E0", "1aa9E0", "0aa2E2", "0aa2E2", "0aa3E2", "0aa3E2", "1aa2E2", "-0aa1E0", "-1aa1E0", "-1aa5E0", "-1aa9E0", "-0aa2E2", "-0aa2E2", "-0aa3E2", "-0aa3E2", "-9aa9E2", "1aa0E-2", "1aa2E-2", "1aa5E-2", "1aa8E-2", "5aa0E-2", "5aa3E-2", "5aa6E-2", "5aa8E-2", "9aa0E-2", "9aa4E-2", "9aa6E-2", "9aa7E-2", "-1aa0E-2", "-1aa2E-2", "-1aa5E-2", "-1aa8E-2", "-5aa0E-2", "-5aa3E-2", "-5aa6E-2", "-5aa8E-2", "-9aa0E-2", "-9aa4E-2", "-9aa6E-2", "-9aa7E-2", "0aa3E0", "0aa5E0", "0aa4E0", "0aa5E0", "0aa4E0", "-0aa3E0", "-0aa5E0", "-0aa4E0", "-0aa5E0", "-0aa4E0"]);
		check_numeric_format("\\a\\a00E-0", numbers,
				["aa00E0", "aa01E0", "aa11E0", "aa15E0", "aa19E0", "aa02E2", "aa02E2", "aa03E2", "aa03E2", "aa12E2", "-aa01E0", "-aa11E0", "-aa15E0", "-aa19E0", "-aa02E2", "-aa02E2", "-aa03E2", "-aa03E2", "-aa99E2", "aa10E-2", "aa12E-2", "aa15E-2", "aa18E-2", "aa50E-2", "aa53E-2", "aa56E-2", "aa58E-2", "aa90E-2", "aa94E-2", "aa96E-2", "aa97E-2", "-aa10E-2", "-aa12E-2", "-aa15E-2", "-aa18E-2", "-aa50E-2", "-aa53E-2", "-aa56E-2", "-aa58E-2", "-aa90E-2", "-aa94E-2", "-aa96E-2", "-aa97E-2", "aa03E0", "aa05E0", "aa04E0", "aa05E0", "aa04E0", "-aa03E0", "-aa05E0", "-aa04E0", "-aa05E0", "-aa04E0"]);
		check_numeric_format("\\a\\a0\\a\\a0E-0", numbers,
				["aa0aa0E0", "aa0aa1E0", "aa1aa1E0", "aa1aa5E0", "aa1aa9E0", "aa0aa2E2", "aa0aa2E2", "aa0aa3E2", "aa0aa3E2", "aa1aa2E2", "-aa0aa1E0", "-aa1aa1E0", "-aa1aa5E0", "-aa1aa9E0", "-aa0aa2E2", "-aa0aa2E2", "-aa0aa3E2", "-aa0aa3E2", "-aa9aa9E2", "aa1aa0E-2", "aa1aa2E-2", "aa1aa5E-2", "aa1aa8E-2", "aa5aa0E-2", "aa5aa3E-2", "aa5aa6E-2", "aa5aa8E-2", "aa9aa0E-2", "aa9aa4E-2", "aa9aa6E-2", "aa9aa7E-2", "-aa1aa0E-2", "-aa1aa2E-2", "-aa1aa5E-2", "-aa1aa8E-2", "-aa5aa0E-2", "-aa5aa3E-2", "-aa5aa6E-2", "-aa5aa8E-2", "-aa9aa0E-2", "-aa9aa4E-2", "-aa9aa6E-2", "-aa9aa7E-2", "aa0aa3E0", "aa0aa5E0", "aa0aa4E0", "aa0aa5E0", "aa0aa4E0", "-aa0aa3E0", "-aa0aa5E0", "-aa0aa4E0", "-aa0aa5E0", "-aa0aa4E0"]);
		check_numeric_format("0E-0\\a\\a", numbers,
				["0E0aa", "1E0aa", "1E1aa", "2E1aa", "2E1aa", "2E2aa", "2E2aa", "3E2aa", "3E2aa", "1E3aa", "-1E0aa", "-1E1aa", "-2E1aa", "-2E1aa", "-2E2aa", "-2E2aa", "-3E2aa", "-3E2aa", "-1E4aa", "1E-1aa", "1E-1aa", "2E-1aa", "2E-1aa", "5E-1aa", "5E-1aa", "6E-1aa", "6E-1aa", "9E-1aa", "9E-1aa", "1E0aa", "1E0aa", "-1E-1aa", "-1E-1aa", "-2E-1aa", "-2E-1aa", "-5E-1aa", "-5E-1aa", "-6E-1aa", "-6E-1aa", "-9E-1aa", "-9E-1aa", "-1E0aa", "-1E0aa", "3E0aa", "5E0aa", "4E0aa", "5E0aa", "4E0aa", "-3E0aa", "-5E0aa", "-4E0aa", "-5E0aa", "-4E0aa"]);
		check_numeric_format("0\\E\\aa-0", numbers,
				["0Eaa0", "1Eaa0", "1Eaa1", "2Eaa1", "2Eaa1", "2Eaa2", "2Eaa2", "3Eaa2", "3Eaa2", "1Eaa3", "-1Eaa0", "-1Eaa1", "-2Eaa1", "-2Eaa1", "-2Eaa2", "-2Eaa2", "-3Eaa2", "-3Eaa2", "-1Eaa4", "1Eaa-1", "1Eaa-1", "2Eaa-1", "2Eaa-1", "5Eaa-1", "5Eaa-1", "6Eaa-1", "6Eaa-1", "9Eaa-1", "9Eaa-1", "1Eaa0", "1Eaa0", "-1Eaa-1", "-1Eaa-1", "-2Eaa-1", "-2Eaa-1", "-5Eaa-1", "-5Eaa-1", "-6Eaa-1", "-6Eaa-1", "-9Eaa-1", "-9Eaa-1", "-1Eaa0", "-1Eaa0", "3Eaa0", "5Eaa0", "4Eaa0", "5Eaa0", "4Eaa0", "-3Eaa0", "-5Eaa0", "-4Eaa0", "-5Eaa0", "-4Eaa0"]);
		check_numeric_format("0\\E\\aa-0\\a\\a", numbers,
				["0Eaa0aa", "1Eaa0aa", "1Eaa1aa", "2Eaa1aa", "2Eaa1aa", "2Eaa2aa", "2Eaa2aa", "3Eaa2aa", "3Eaa2aa", "1Eaa3aa", "-1Eaa0aa", "-1Eaa1aa", "-2Eaa1aa", "-2Eaa1aa", "-2Eaa2aa", "-2Eaa2aa", "-3Eaa2aa", "-3Eaa2aa", "-1Eaa4aa", "1Eaa-1aa", "1Eaa-1aa", "2Eaa-1aa", "2Eaa-1aa", "5Eaa-1aa", "5Eaa-1aa", "6Eaa-1aa", "6Eaa-1aa", "9Eaa-1aa", "9Eaa-1aa", "1Eaa0aa", "1Eaa0aa", "-1Eaa-1aa", "-1Eaa-1aa", "-2Eaa-1aa", "-2Eaa-1aa", "-5Eaa-1aa", "-5Eaa-1aa", "-6Eaa-1aa", "-6Eaa-1aa", "-9Eaa-1aa", "-9Eaa-1aa", "-1Eaa0aa", "-1Eaa0aa", "3Eaa0aa", "5Eaa0aa", "4Eaa0aa", "5Eaa0aa", "4Eaa0aa", "-3Eaa0aa", "-5Eaa0aa", "-4Eaa0aa", "-5Eaa0aa", "-4Eaa0aa"]);
		check_numeric_format("0\"%\"0E-0", numbers,
				["0%0E0", "0%1E0", "1%1E0", "1%5E0", "1%9E0", "0%2E2", "0%2E2", "0%3E2", "0%3E2", "1%2E2", "-0%1E0", "-1%1E0", "-1%5E0", "-1%9E0", "-0%2E2", "-0%2E2", "-0%3E2", "-0%3E2", "-9%9E2", "1%0E-2", "1%2E-2", "1%5E-2", "1%8E-2", "5%0E-2", "5%3E-2", "5%6E-2", "5%8E-2", "9%0E-2", "9%4E-2", "9%6E-2", "9%7E-2", "-1%0E-2", "-1%2E-2", "-1%5E-2", "-1%8E-2", "-5%0E-2", "-5%3E-2", "-5%6E-2", "-5%8E-2", "-9%0E-2", "-9%4E-2", "-9%6E-2", "-9%7E-2", "0%3E0", "0%5E0", "0%4E0", "0%5E0", "0%4E0", "-0%3E0", "-0%5E0", "-0%4E0", "-0%5E0", "-0%4E0"]);
		check_numeric_format("\"%\"00E-0", numbers,
				["%00E0", "%01E0", "%11E0", "%15E0", "%19E0", "%02E2", "%02E2", "%03E2", "%03E2", "%12E2", "-%01E0", "-%11E0", "-%15E0", "-%19E0", "-%02E2", "-%02E2", "-%03E2", "-%03E2", "-%99E2", "%10E-2", "%12E-2", "%15E-2", "%18E-2", "%50E-2", "%53E-2", "%56E-2", "%58E-2", "%90E-2", "%94E-2", "%96E-2", "%97E-2", "-%10E-2", "-%12E-2", "-%15E-2", "-%18E-2", "-%50E-2", "-%53E-2", "-%56E-2", "-%58E-2", "-%90E-2", "-%94E-2", "-%96E-2", "-%97E-2", "%03E0", "%05E0", "%04E0", "%05E0", "%04E0", "-%03E0", "-%05E0", "-%04E0", "-%05E0", "-%04E0"]);
		check_numeric_format("\"%\"0\"%\"0E-0", numbers,
				["%0%0E0", "%0%1E0", "%1%1E0", "%1%5E0", "%1%9E0", "%0%2E2", "%0%2E2", "%0%3E2", "%0%3E2", "%1%2E2", "-%0%1E0", "-%1%1E0", "-%1%5E0", "-%1%9E0", "-%0%2E2", "-%0%2E2", "-%0%3E2", "-%0%3E2", "-%9%9E2", "%1%0E-2", "%1%2E-2", "%1%5E-2", "%1%8E-2", "%5%0E-2", "%5%3E-2", "%5%6E-2", "%5%8E-2", "%9%0E-2", "%9%4E-2", "%9%6E-2", "%9%7E-2", "-%1%0E-2", "-%1%2E-2", "-%1%5E-2", "-%1%8E-2", "-%5%0E-2", "-%5%3E-2", "-%5%6E-2", "-%5%8E-2", "-%9%0E-2", "-%9%4E-2", "-%9%6E-2", "-%9%7E-2", "%0%3E0", "%0%5E0", "%0%4E0", "%0%5E0", "%0%4E0", "-%0%3E0", "-%0%5E0", "-%0%4E0", "-%0%5E0", "-%0%4E0"]);
		check_numeric_format("0E-0\"%\"", numbers,
				["0E0%", "1E0%", "1E1%", "2E1%", "2E1%", "2E2%", "2E2%", "3E2%", "3E2%", "1E3%", "-1E0%", "-1E1%", "-2E1%", "-2E1%", "-2E2%", "-2E2%", "-3E2%", "-3E2%", "-1E4%", "1E-1%", "1E-1%", "2E-1%", "2E-1%", "5E-1%", "5E-1%", "6E-1%", "6E-1%", "9E-1%", "9E-1%", "1E0%", "1E0%", "-1E-1%", "-1E-1%", "-2E-1%", "-2E-1%", "-5E-1%", "-5E-1%", "-6E-1%", "-6E-1%", "-9E-1%", "-9E-1%", "-1E0%", "-1E0%", "3E0%", "5E0%", "4E0%", "5E0%", "4E0%", "-3E0%", "-5E0%", "-4E0%", "-5E0%", "-4E0%"]);
		check_numeric_format("0\\E\"%\"-0", numbers,
				["0E%0", "1E%0", "1E%1", "2E%1", "2E%1", "2E%2", "2E%2", "3E%2", "3E%2", "1E%3", "-1E%0", "-1E%1", "-2E%1", "-2E%1", "-2E%2", "-2E%2", "-3E%2", "-3E%2", "-1E%4", "1E%-1", "1E%-1", "2E%-1", "2E%-1", "5E%-1", "5E%-1", "6E%-1", "6E%-1", "9E%-1", "9E%-1", "1E%0", "1E%0", "-1E%-1", "-1E%-1", "-2E%-1", "-2E%-1", "-5E%-1", "-5E%-1", "-6E%-1", "-6E%-1", "-9E%-1", "-9E%-1", "-1E%0", "-1E%0", "3E%0", "5E%0", "4E%0", "5E%0", "4E%0", "-3E%0", "-5E%0", "-4E%0", "-5E%0", "-4E%0"]);
		check_numeric_format("0\\E\"%\"-0\"%\"", numbers,
				["0E%0%", "1E%0%", "1E%1%", "2E%1%", "2E%1%", "2E%2%", "2E%2%", "3E%2%", "3E%2%", "1E%3%", "-1E%0%", "-1E%1%", "-2E%1%", "-2E%1%", "-2E%2%", "-2E%2%", "-3E%2%", "-3E%2%", "-1E%4%", "1E%-1%", "1E%-1%", "2E%-1%", "2E%-1%", "5E%-1%", "5E%-1%", "6E%-1%", "6E%-1%", "9E%-1%", "9E%-1%", "1E%0%", "1E%0%", "-1E%-1%", "-1E%-1%", "-2E%-1%", "-2E%-1%", "-5E%-1%", "-5E%-1%", "-6E%-1%", "-6E%-1%", "-9E%-1%", "-9E%-1%", "-1E%0%", "-1E%0%", "3E%0%", "5E%0%", "4E%0%", "5E%0%", "4E%0%", "-3E%0%", "-5E%0%", "-4E%0%", "-5E%0%", "-4E%0%"]);
		check_numeric_format("0%0E+0", numbers,
				["0%0E+0", "0%1E+0", "1%1E+0", "1%5E+0", "1%9E+0", "0%2E+2", "0%2E+2", "0%3E+2", "0%3E+2", "1%2E+2", "-0%1E+0", "-1%1E+0", "-1%5E+0", "-1%9E+0", "-0%2E+2", "-0%2E+2", "-0%3E+2", "-0%3E+2", "-9%9E+2", "1%0E-2", "1%2E-2", "1%5E-2", "1%8E-2", "5%0E-2", "5%3E-2", "5%6E-2", "5%8E-2", "9%0E-2", "9%4E-2", "9%6E-2", "9%7E-2", "-1%0E-2", "-1%2E-2", "-1%5E-2", "-1%8E-2", "-5%0E-2", "-5%3E-2", "-5%6E-2", "-5%8E-2", "-9%0E-2", "-9%4E-2", "-9%6E-2", "-9%7E-2", "0%3E+0", "0%5E+0", "0%4E+0", "0%5E+0", "0%4E+0", "-0%3E+0", "-0%5E+0", "-0%4E+0", "-0%5E+0", "-0%4E+0"]);
		check_numeric_format("%00E+0", numbers,
				["%00E+0", "%01E+0", "%11E+0", "%15E+0", "%19E+0", "%02E+2", "%02E+2", "%03E+2", "%03E+2", "%12E+2", "-%01E+0", "-%11E+0", "-%15E+0", "-%19E+0", "-%02E+2", "-%02E+2", "-%03E+2", "-%03E+2", "-%99E+2", "%10E-2", "%12E-2", "%15E-2", "%18E-2", "%50E-2", "%53E-2", "%56E-2", "%58E-2", "%90E-2", "%94E-2", "%96E-2", "%97E-2", "-%10E-2", "-%12E-2", "-%15E-2", "-%18E-2", "-%50E-2", "-%53E-2", "-%56E-2", "-%58E-2", "-%90E-2", "-%94E-2", "-%96E-2", "-%97E-2", "%03E+0", "%05E+0", "%04E+0", "%05E+0", "%04E+0", "-%03E+0", "-%05E+0", "-%04E+0", "-%05E+0", "-%04E+0"]);
		check_numeric_format("%0%0E+0", numbers,
				["%0%0E+0", "%0%1E+0", "%1%1E+0", "%1%5E+0", "%1%9E+0", "%0%2E+2", "%0%2E+2", "%0%3E+2", "%0%3E+2", "%1%2E+2", "-%0%1E+0", "-%1%1E+0", "-%1%5E+0", "-%1%9E+0", "-%0%2E+2", "-%0%2E+2", "-%0%3E+2", "-%0%3E+2", "-%9%9E+2", "%1%0E-2", "%1%2E-2", "%1%5E-2", "%1%8E-2", "%5%0E-2", "%5%3E-2", "%5%6E-2", "%5%8E-2", "%9%0E-2", "%9%4E-2", "%9%6E-2", "%9%7E-2", "-%1%0E-2", "-%1%2E-2", "-%1%5E-2", "-%1%8E-2", "-%5%0E-2", "-%5%3E-2", "-%5%6E-2", "-%5%8E-2", "-%9%0E-2", "-%9%4E-2", "-%9%6E-2", "-%9%7E-2", "%0%3E+0", "%0%5E+0", "%0%4E+0", "%0%5E+0", "%0%4E+0", "-%0%3E+0", "-%0%5E+0", "-%0%4E+0", "-%0%5E+0", "-%0%4E+0"]);
		check_numeric_format("0E+0%", numbers,
				["0E+0%", "1E+0%", "1E+1%", "2E+1%", "2E+1%", "2E+2%", "2E+2%", "3E+2%", "3E+2%", "1E+3%", "-1E+0%", "-1E+1%", "-2E+1%", "-2E+1%", "-2E+2%", "-2E+2%", "-3E+2%", "-3E+2%", "-1E+4%", "1E-1%", "1E-1%", "2E-1%", "2E-1%", "5E-1%", "5E-1%", "6E-1%", "6E-1%", "9E-1%", "9E-1%", "1E+0%", "1E+0%", "-1E-1%", "-1E-1%", "-2E-1%", "-2E-1%", "-5E-1%", "-5E-1%", "-6E-1%", "-6E-1%", "-9E-1%", "-9E-1%", "-1E+0%", "-1E+0%", "3E+0%", "5E+0%", "4E+0%", "5E+0%", "4E+0%", "-3E+0%", "-5E+0%", "-4E+0%", "-5E+0%", "-4E+0%"]);
		check_numeric_format("0\\E%+0", numbers,
				["0E%+0", "1E%+0", "1E%+1", "2E%+1", "2E%+1", "2E%+2", "2E%+2", "3E%+2", "3E%+2", "1E%+3", "-1E%+0", "-1E%+1", "-2E%+1", "-2E%+1", "-2E%+2", "-2E%+2", "-3E%+2", "-3E%+2", "-1E%+4", "1E%-1", "1E%-1", "2E%-1", "2E%-1", "5E%-1", "5E%-1", "6E%-1", "6E%-1", "9E%-1", "9E%-1", "1E%+0", "1E%+0", "-1E%-1", "-1E%-1", "-2E%-1", "-2E%-1", "-5E%-1", "-5E%-1", "-6E%-1", "-6E%-1", "-9E%-1", "-9E%-1", "-1E%+0", "-1E%+0", "3E%+0", "5E%+0", "4E%+0", "5E%+0", "4E%+0", "-3E%+0", "-5E%+0", "-4E%+0", "-5E%+0", "-4E%+0"]);
		check_numeric_format("0\\E%+0%", numbers,
				["0E%+0%", "1E%+0%", "1E%+1%", "2E%+1%", "2E%+1%", "2E%+2%", "2E%+2%", "3E%+2%", "3E%+2%", "1E%+3%", "-1E%+0%", "-1E%+1%", "-2E%+1%", "-2E%+1%", "-2E%+2%", "-2E%+2%", "-3E%+2%", "-3E%+2%", "-1E%+4%", "1E%-1%", "1E%-1%", "2E%-1%", "2E%-1%", "5E%-1%", "5E%-1%", "6E%-1%", "6E%-1%", "9E%-1%", "9E%-1%", "1E%+0%", "1E%+0%", "-1E%-1%", "-1E%-1%", "-2E%-1%", "-2E%-1%", "-5E%-1%", "-5E%-1%", "-6E%-1%", "-6E%-1%", "-9E%-1%", "-9E%-1%", "-1E%+0%", "-1E%+0%", "3E%+0%", "5E%+0%", "4E%+0%", "5E%+0%", "4E%+0%", "-3E%+0%", "-5E%+0%", "-4E%+0%", "-5E%+0%", "-4E%+0%"]);
		check_numeric_format("0\\a\\a0E+0", numbers,
				["0aa0E+0", "0aa1E+0", "1aa1E+0", "1aa5E+0", "1aa9E+0", "0aa2E+2", "0aa2E+2", "0aa3E+2", "0aa3E+2", "1aa2E+2", "-0aa1E+0", "-1aa1E+0", "-1aa5E+0", "-1aa9E+0", "-0aa2E+2", "-0aa2E+2", "-0aa3E+2", "-0aa3E+2", "-9aa9E+2", "1aa0E-2", "1aa2E-2", "1aa5E-2", "1aa8E-2", "5aa0E-2", "5aa3E-2", "5aa6E-2", "5aa8E-2", "9aa0E-2", "9aa4E-2", "9aa6E-2", "9aa7E-2", "-1aa0E-2", "-1aa2E-2", "-1aa5E-2", "-1aa8E-2", "-5aa0E-2", "-5aa3E-2", "-5aa6E-2", "-5aa8E-2", "-9aa0E-2", "-9aa4E-2", "-9aa6E-2", "-9aa7E-2", "0aa3E+0", "0aa5E+0", "0aa4E+0", "0aa5E+0", "0aa4E+0", "-0aa3E+0", "-0aa5E+0", "-0aa4E+0", "-0aa5E+0", "-0aa4E+0"]);
		check_numeric_format("\\a\\a00E+0", numbers,
				["aa00E+0", "aa01E+0", "aa11E+0", "aa15E+0", "aa19E+0", "aa02E+2", "aa02E+2", "aa03E+2", "aa03E+2", "aa12E+2", "-aa01E+0", "-aa11E+0", "-aa15E+0", "-aa19E+0", "-aa02E+2", "-aa02E+2", "-aa03E+2", "-aa03E+2", "-aa99E+2", "aa10E-2", "aa12E-2", "aa15E-2", "aa18E-2", "aa50E-2", "aa53E-2", "aa56E-2", "aa58E-2", "aa90E-2", "aa94E-2", "aa96E-2", "aa97E-2", "-aa10E-2", "-aa12E-2", "-aa15E-2", "-aa18E-2", "-aa50E-2", "-aa53E-2", "-aa56E-2", "-aa58E-2", "-aa90E-2", "-aa94E-2", "-aa96E-2", "-aa97E-2", "aa03E+0", "aa05E+0", "aa04E+0", "aa05E+0", "aa04E+0", "-aa03E+0", "-aa05E+0", "-aa04E+0", "-aa05E+0", "-aa04E+0"]);
		check_numeric_format("\\a\\a0\\a\\a0E+0", numbers,
				["aa0aa0E+0", "aa0aa1E+0", "aa1aa1E+0", "aa1aa5E+0", "aa1aa9E+0", "aa0aa2E+2", "aa0aa2E+2", "aa0aa3E+2", "aa0aa3E+2", "aa1aa2E+2", "-aa0aa1E+0", "-aa1aa1E+0", "-aa1aa5E+0", "-aa1aa9E+0", "-aa0aa2E+2", "-aa0aa2E+2", "-aa0aa3E+2", "-aa0aa3E+2", "-aa9aa9E+2", "aa1aa0E-2", "aa1aa2E-2", "aa1aa5E-2", "aa1aa8E-2", "aa5aa0E-2", "aa5aa3E-2", "aa5aa6E-2", "aa5aa8E-2", "aa9aa0E-2", "aa9aa4E-2", "aa9aa6E-2", "aa9aa7E-2", "-aa1aa0E-2", "-aa1aa2E-2", "-aa1aa5E-2", "-aa1aa8E-2", "-aa5aa0E-2", "-aa5aa3E-2", "-aa5aa6E-2", "-aa5aa8E-2", "-aa9aa0E-2", "-aa9aa4E-2", "-aa9aa6E-2", "-aa9aa7E-2", "aa0aa3E+0", "aa0aa5E+0", "aa0aa4E+0", "aa0aa5E+0", "aa0aa4E+0", "-aa0aa3E+0", "-aa0aa5E+0", "-aa0aa4E+0", "-aa0aa5E+0", "-aa0aa4E+0"]);
		check_numeric_format("0E+0\\a\\a", numbers,
				["0E+0aa", "1E+0aa", "1E+1aa", "2E+1aa", "2E+1aa", "2E+2aa", "2E+2aa", "3E+2aa", "3E+2aa", "1E+3aa", "-1E+0aa", "-1E+1aa", "-2E+1aa", "-2E+1aa", "-2E+2aa", "-2E+2aa", "-3E+2aa", "-3E+2aa", "-1E+4aa", "1E-1aa", "1E-1aa", "2E-1aa", "2E-1aa", "5E-1aa", "5E-1aa", "6E-1aa", "6E-1aa", "9E-1aa", "9E-1aa", "1E+0aa", "1E+0aa", "-1E-1aa", "-1E-1aa", "-2E-1aa", "-2E-1aa", "-5E-1aa", "-5E-1aa", "-6E-1aa", "-6E-1aa", "-9E-1aa", "-9E-1aa", "-1E+0aa", "-1E+0aa", "3E+0aa", "5E+0aa", "4E+0aa", "5E+0aa", "4E+0aa", "-3E+0aa", "-5E+0aa", "-4E+0aa", "-5E+0aa", "-4E+0aa"]);
		check_numeric_format("0\\E\\aa+0", numbers,
				["0Eaa+0", "1Eaa+0", "1Eaa+1", "2Eaa+1", "2Eaa+1", "2Eaa+2", "2Eaa+2", "3Eaa+2", "3Eaa+2", "1Eaa+3", "-1Eaa+0", "-1Eaa+1", "-2Eaa+1", "-2Eaa+1", "-2Eaa+2", "-2Eaa+2", "-3Eaa+2", "-3Eaa+2", "-1Eaa+4", "1Eaa-1", "1Eaa-1", "2Eaa-1", "2Eaa-1", "5Eaa-1", "5Eaa-1", "6Eaa-1", "6Eaa-1", "9Eaa-1", "9Eaa-1", "1Eaa+0", "1Eaa+0", "-1Eaa-1", "-1Eaa-1", "-2Eaa-1", "-2Eaa-1", "-5Eaa-1", "-5Eaa-1", "-6Eaa-1", "-6Eaa-1", "-9Eaa-1", "-9Eaa-1", "-1Eaa+0", "-1Eaa+0", "3Eaa+0", "5Eaa+0", "4Eaa+0", "5Eaa+0", "4Eaa+0", "-3Eaa+0", "-5Eaa+0", "-4Eaa+0", "-5Eaa+0", "-4Eaa+0"]);
		check_numeric_format("0\\E\\aa+0\\a\\a", numbers,
				["0Eaa+0aa", "1Eaa+0aa", "1Eaa+1aa", "2Eaa+1aa", "2Eaa+1aa", "2Eaa+2aa", "2Eaa+2aa", "3Eaa+2aa", "3Eaa+2aa", "1Eaa+3aa", "-1Eaa+0aa", "-1Eaa+1aa", "-2Eaa+1aa", "-2Eaa+1aa", "-2Eaa+2aa", "-2Eaa+2aa", "-3Eaa+2aa", "-3Eaa+2aa", "-1Eaa+4aa", "1Eaa-1aa", "1Eaa-1aa", "2Eaa-1aa", "2Eaa-1aa", "5Eaa-1aa", "5Eaa-1aa", "6Eaa-1aa", "6Eaa-1aa", "9Eaa-1aa", "9Eaa-1aa", "1Eaa+0aa", "1Eaa+0aa", "-1Eaa-1aa", "-1Eaa-1aa", "-2Eaa-1aa", "-2Eaa-1aa", "-5Eaa-1aa", "-5Eaa-1aa", "-6Eaa-1aa", "-6Eaa-1aa", "-9Eaa-1aa", "-9Eaa-1aa", "-1Eaa+0aa", "-1Eaa+0aa", "3Eaa+0aa", "5Eaa+0aa", "4Eaa+0aa", "5Eaa+0aa", "4Eaa+0aa", "-3Eaa+0aa", "-5Eaa+0aa", "-4Eaa+0aa", "-5Eaa+0aa", "-4Eaa+0aa"]);
		check_numeric_format("0\"%\"0E+0", numbers,
				["0%0E+0", "0%1E+0", "1%1E+0", "1%5E+0", "1%9E+0", "0%2E+2", "0%2E+2", "0%3E+2", "0%3E+2", "1%2E+2", "-0%1E+0", "-1%1E+0", "-1%5E+0", "-1%9E+0", "-0%2E+2", "-0%2E+2", "-0%3E+2", "-0%3E+2", "-9%9E+2", "1%0E-2", "1%2E-2", "1%5E-2", "1%8E-2", "5%0E-2", "5%3E-2", "5%6E-2", "5%8E-2", "9%0E-2", "9%4E-2", "9%6E-2", "9%7E-2", "-1%0E-2", "-1%2E-2", "-1%5E-2", "-1%8E-2", "-5%0E-2", "-5%3E-2", "-5%6E-2", "-5%8E-2", "-9%0E-2", "-9%4E-2", "-9%6E-2", "-9%7E-2", "0%3E+0", "0%5E+0", "0%4E+0", "0%5E+0", "0%4E+0", "-0%3E+0", "-0%5E+0", "-0%4E+0", "-0%5E+0", "-0%4E+0"]);
		check_numeric_format("\"%\"00E+0", numbers,
				["%00E+0", "%01E+0", "%11E+0", "%15E+0", "%19E+0", "%02E+2", "%02E+2", "%03E+2", "%03E+2", "%12E+2", "-%01E+0", "-%11E+0", "-%15E+0", "-%19E+0", "-%02E+2", "-%02E+2", "-%03E+2", "-%03E+2", "-%99E+2", "%10E-2", "%12E-2", "%15E-2", "%18E-2", "%50E-2", "%53E-2", "%56E-2", "%58E-2", "%90E-2", "%94E-2", "%96E-2", "%97E-2", "-%10E-2", "-%12E-2", "-%15E-2", "-%18E-2", "-%50E-2", "-%53E-2", "-%56E-2", "-%58E-2", "-%90E-2", "-%94E-2", "-%96E-2", "-%97E-2", "%03E+0", "%05E+0", "%04E+0", "%05E+0", "%04E+0", "-%03E+0", "-%05E+0", "-%04E+0", "-%05E+0", "-%04E+0"]);
		check_numeric_format("\"%\"0\"%\"0E+0", numbers,
				["%0%0E+0", "%0%1E+0", "%1%1E+0", "%1%5E+0", "%1%9E+0", "%0%2E+2", "%0%2E+2", "%0%3E+2", "%0%3E+2", "%1%2E+2", "-%0%1E+0", "-%1%1E+0", "-%1%5E+0", "-%1%9E+0", "-%0%2E+2", "-%0%2E+2", "-%0%3E+2", "-%0%3E+2", "-%9%9E+2", "%1%0E-2", "%1%2E-2", "%1%5E-2", "%1%8E-2", "%5%0E-2", "%5%3E-2", "%5%6E-2", "%5%8E-2", "%9%0E-2", "%9%4E-2", "%9%6E-2", "%9%7E-2", "-%1%0E-2", "-%1%2E-2", "-%1%5E-2", "-%1%8E-2", "-%5%0E-2", "-%5%3E-2", "-%5%6E-2", "-%5%8E-2", "-%9%0E-2", "-%9%4E-2", "-%9%6E-2", "-%9%7E-2", "%0%3E+0", "%0%5E+0", "%0%4E+0", "%0%5E+0", "%0%4E+0", "-%0%3E+0", "-%0%5E+0", "-%0%4E+0", "-%0%5E+0", "-%0%4E+0"]);
		check_numeric_format("0E+0\"%\"", numbers,
				["0E+0%", "1E+0%", "1E+1%", "2E+1%", "2E+1%", "2E+2%", "2E+2%", "3E+2%", "3E+2%", "1E+3%", "-1E+0%", "-1E+1%", "-2E+1%", "-2E+1%", "-2E+2%", "-2E+2%", "-3E+2%", "-3E+2%", "-1E+4%", "1E-1%", "1E-1%", "2E-1%", "2E-1%", "5E-1%", "5E-1%", "6E-1%", "6E-1%", "9E-1%", "9E-1%", "1E+0%", "1E+0%", "-1E-1%", "-1E-1%", "-2E-1%", "-2E-1%", "-5E-1%", "-5E-1%", "-6E-1%", "-6E-1%", "-9E-1%", "-9E-1%", "-1E+0%", "-1E+0%", "3E+0%", "5E+0%", "4E+0%", "5E+0%", "4E+0%", "-3E+0%", "-5E+0%", "-4E+0%", "-5E+0%", "-4E+0%"]);
		check_numeric_format("0\\E\"%\"+0", numbers,
				["0E%+0", "1E%+0", "1E%+1", "2E%+1", "2E%+1", "2E%+2", "2E%+2", "3E%+2", "3E%+2", "1E%+3", "-1E%+0", "-1E%+1", "-2E%+1", "-2E%+1", "-2E%+2", "-2E%+2", "-3E%+2", "-3E%+2", "-1E%+4", "1E%-1", "1E%-1", "2E%-1", "2E%-1", "5E%-1", "5E%-1", "6E%-1", "6E%-1", "9E%-1", "9E%-1", "1E%+0", "1E%+0", "-1E%-1", "-1E%-1", "-2E%-1", "-2E%-1", "-5E%-1", "-5E%-1", "-6E%-1", "-6E%-1", "-9E%-1", "-9E%-1", "-1E%+0", "-1E%+0", "3E%+0", "5E%+0", "4E%+0", "5E%+0", "4E%+0", "-3E%+0", "-5E%+0", "-4E%+0", "-5E%+0", "-4E%+0"]);
		check_numeric_format("0\\E\"%\"+0\"%\"", numbers,
				["0E%+0%", "1E%+0%", "1E%+1%", "2E%+1%", "2E%+1%", "2E%+2%", "2E%+2%", "3E%+2%", "3E%+2%", "1E%+3%", "-1E%+0%", "-1E%+1%", "-2E%+1%", "-2E%+1%", "-2E%+2%", "-2E%+2%", "-3E%+2%", "-3E%+2%", "-1E%+4%", "1E%-1%", "1E%-1%", "2E%-1%", "2E%-1%", "5E%-1%", "5E%-1%", "6E%-1%", "6E%-1%", "9E%-1%", "9E%-1%", "1E%+0%", "1E%+0%", "-1E%-1%", "-1E%-1%", "-2E%-1%", "-2E%-1%", "-5E%-1%", "-5E%-1%", "-6E%-1%", "-6E%-1%", "-9E%-1%", "-9E%-1%", "-1E%+0%", "-1E%+0%", "3E%+0%", "5E%+0%", "4E%+0%", "5E%+0%", "4E%+0%", "-3E%+0%", "-5E%+0%", "-4E%+0%", "-5E%+0%", "-4E%+0%"]);
		check_numeric_format(".0%0E-0", numbers,
				[".0%0E0", ".1%0E1", ".1%1E2", ".1%5E2", ".1%9E2", ".2%0E3", ".2%2E3", ".2%5E3", ".2%8E3", ".1%2E4", "-.1%0E1", "-.1%1E2", "-.1%5E2", "-.1%9E2", "-.2%0E3", "-.2%2E3", "-.2%5E3", "-.2%8E3", "-.9%9E4", ".1%0E0", ".1%2E0", ".1%5E0", ".1%8E0", ".5%0E0", ".5%3E0", ".5%6E0", ".5%8E0", ".9%0E0", ".9%4E0", ".9%6E0", ".9%7E0", "-.1%0E0", "-.1%2E0", "-.1%5E0", "-.1%8E0", "-.5%0E0", "-.5%3E0", "-.5%6E0", "-.5%8E0", "-.9%0E0", "-.9%4E0", "-.9%6E0", "-.9%7E0", ".3%4E1", ".4%6E1", ".4%3E1", ".4%6E1", ".4%3E1", "-.3%4E1", "-.4%6E1", "-.4%3E1", "-.4%6E1", "-.4%3E1"]);
		check_numeric_format(".%00E-0", numbers,
				[".%00E0", ".%10E1", ".%11E2", ".%15E2", ".%19E2", ".%20E3", ".%22E3", ".%25E3", ".%28E3", ".%12E4", "-.%10E1", "-.%11E2", "-.%15E2", "-.%19E2", "-.%20E3", "-.%22E3", "-.%25E3", "-.%28E3", "-.%99E4", ".%10E0", ".%12E0", ".%15E0", ".%18E0", ".%50E0", ".%53E0", ".%56E0", ".%58E0", ".%90E0", ".%94E0", ".%96E0", ".%97E0", "-.%10E0", "-.%12E0", "-.%15E0", "-.%18E0", "-.%50E0", "-.%53E0", "-.%56E0", "-.%58E0", "-.%90E0", "-.%94E0", "-.%96E0", "-.%97E0", ".%34E1", ".%46E1", ".%43E1", ".%46E1", ".%43E1", "-.%34E1", "-.%46E1", "-.%43E1", "-.%46E1", "-.%43E1"]);
		check_numeric_format(".%0%0E-0", numbers,
				[".%0%0E0", ".%1%0E1", ".%1%1E2", ".%1%5E2", ".%1%9E2", ".%2%0E3", ".%2%2E3", ".%2%5E3", ".%2%8E3", ".%1%2E4", "-.%1%0E1", "-.%1%1E2", "-.%1%5E2", "-.%1%9E2", "-.%2%0E3", "-.%2%2E3", "-.%2%5E3", "-.%2%8E3", "-.%9%9E4", ".%1%0E0", ".%1%2E0", ".%1%5E0", ".%1%8E0", ".%5%0E0", ".%5%3E0", ".%5%6E0", ".%5%8E0", ".%9%0E0", ".%9%4E0", ".%9%6E0", ".%9%7E0", "-.%1%0E0", "-.%1%2E0", "-.%1%5E0", "-.%1%8E0", "-.%5%0E0", "-.%5%3E0", "-.%5%6E0", "-.%5%8E0", "-.%9%0E0", "-.%9%4E0", "-.%9%6E0", "-.%9%7E0", ".%3%4E1", ".%4%6E1", ".%4%3E1", ".%4%6E1", ".%4%3E1", "-.%3%4E1", "-.%4%6E1", "-.%4%3E1", "-.%4%6E1", "-.%4%3E1"]);
		check_numeric_format(".0E-0%", numbers,
				[".0E0%", ".1E1%", ".1E2%", ".2E2%", ".2E2%", ".2E3%", ".2E3%", ".3E3%", ".3E3%", ".1E4%", "-.1E1%", "-.1E2%", "-.2E2%", "-.2E2%", "-.2E3%", "-.2E3%", "-.3E3%", "-.3E3%", "-.1E5%", ".1E0%", ".1E0%", ".2E0%", ".2E0%", ".5E0%", ".5E0%", ".6E0%", ".6E0%", ".9E0%", ".9E0%", ".1E1%", ".1E1%", "-.1E0%", "-.1E0%", "-.2E0%", "-.2E0%", "-.5E0%", "-.5E0%", "-.6E0%", "-.6E0%", "-.9E0%", "-.9E0%", "-.1E1%", "-.1E1%", ".3E1%", ".5E1%", ".4E1%", ".5E1%", ".4E1%", "-.3E1%", "-.5E1%", "-.4E1%", "-.5E1%", "-.4E1%"]);
		check_numeric_format(".0\\E%-0", numbers,
				[".0E%0", ".1E%1", ".1E%2", ".2E%2", ".2E%2", ".2E%3", ".2E%3", ".3E%3", ".3E%3", ".1E%4", "-.1E%1", "-.1E%2", "-.2E%2", "-.2E%2", "-.2E%3", "-.2E%3", "-.3E%3", "-.3E%3", "-.1E%5", ".1E%0", ".1E%0", ".2E%0", ".2E%0", ".5E%0", ".5E%0", ".6E%0", ".6E%0", ".9E%0", ".9E%0", ".1E%1", ".1E%1", "-.1E%0", "-.1E%0", "-.2E%0", "-.2E%0", "-.5E%0", "-.5E%0", "-.6E%0", "-.6E%0", "-.9E%0", "-.9E%0", "-.1E%1", "-.1E%1", ".3E%1", ".5E%1", ".4E%1", ".5E%1", ".4E%1", "-.3E%1", "-.5E%1", "-.4E%1", "-.5E%1", "-.4E%1"]);
		check_numeric_format(".0\\E%-0%", numbers,
				[".0E%0%", ".1E%1%", ".1E%2%", ".2E%2%", ".2E%2%", ".2E%3%", ".2E%3%", ".3E%3%", ".3E%3%", ".1E%4%", "-.1E%1%", "-.1E%2%", "-.2E%2%", "-.2E%2%", "-.2E%3%", "-.2E%3%", "-.3E%3%", "-.3E%3%", "-.1E%5%", ".1E%0%", ".1E%0%", ".2E%0%", ".2E%0%", ".5E%0%", ".5E%0%", ".6E%0%", ".6E%0%", ".9E%0%", ".9E%0%", ".1E%1%", ".1E%1%", "-.1E%0%", "-.1E%0%", "-.2E%0%", "-.2E%0%", "-.5E%0%", "-.5E%0%", "-.6E%0%", "-.6E%0%", "-.9E%0%", "-.9E%0%", "-.1E%1%", "-.1E%1%", ".3E%1%", ".5E%1%", ".4E%1%", ".5E%1%", ".4E%1%", "-.3E%1%", "-.5E%1%", "-.4E%1%", "-.5E%1%", "-.4E%1%"]);
		check_numeric_format(".0\\a\\a0E-0", numbers,
				[".0aa0E0", ".1aa0E1", ".1aa1E2", ".1aa5E2", ".1aa9E2", ".2aa0E3", ".2aa2E3", ".2aa5E3", ".2aa8E3", ".1aa2E4", "-.1aa0E1", "-.1aa1E2", "-.1aa5E2", "-.1aa9E2", "-.2aa0E3", "-.2aa2E3", "-.2aa5E3", "-.2aa8E3", "-.9aa9E4", ".1aa0E0", ".1aa2E0", ".1aa5E0", ".1aa8E0", ".5aa0E0", ".5aa3E0", ".5aa6E0", ".5aa8E0", ".9aa0E0", ".9aa4E0", ".9aa6E0", ".9aa7E0", "-.1aa0E0", "-.1aa2E0", "-.1aa5E0", "-.1aa8E0", "-.5aa0E0", "-.5aa3E0", "-.5aa6E0", "-.5aa8E0", "-.9aa0E0", "-.9aa4E0", "-.9aa6E0", "-.9aa7E0", ".3aa4E1", ".4aa6E1", ".4aa3E1", ".4aa6E1", ".4aa3E1", "-.3aa4E1", "-.4aa6E1", "-.4aa3E1", "-.4aa6E1", "-.4aa3E1"]);
		check_numeric_format(".\\a\\a00E-0", numbers,
				[".aa00E0", ".aa10E1", ".aa11E2", ".aa15E2", ".aa19E2", ".aa20E3", ".aa22E3", ".aa25E3", ".aa28E3", ".aa12E4", "-.aa10E1", "-.aa11E2", "-.aa15E2", "-.aa19E2", "-.aa20E3", "-.aa22E3", "-.aa25E3", "-.aa28E3", "-.aa99E4", ".aa10E0", ".aa12E0", ".aa15E0", ".aa18E0", ".aa50E0", ".aa53E0", ".aa56E0", ".aa58E0", ".aa90E0", ".aa94E0", ".aa96E0", ".aa97E0", "-.aa10E0", "-.aa12E0", "-.aa15E0", "-.aa18E0", "-.aa50E0", "-.aa53E0", "-.aa56E0", "-.aa58E0", "-.aa90E0", "-.aa94E0", "-.aa96E0", "-.aa97E0", ".aa34E1", ".aa46E1", ".aa43E1", ".aa46E1", ".aa43E1", "-.aa34E1", "-.aa46E1", "-.aa43E1", "-.aa46E1", "-.aa43E1"]);
		check_numeric_format(".\\a\\a0\\a\\a0E-0", numbers,
				[".aa0aa0E0", ".aa1aa0E1", ".aa1aa1E2", ".aa1aa5E2", ".aa1aa9E2", ".aa2aa0E3", ".aa2aa2E3", ".aa2aa5E3", ".aa2aa8E3", ".aa1aa2E4", "-.aa1aa0E1", "-.aa1aa1E2", "-.aa1aa5E2", "-.aa1aa9E2", "-.aa2aa0E3", "-.aa2aa2E3", "-.aa2aa5E3", "-.aa2aa8E3", "-.aa9aa9E4", ".aa1aa0E0", ".aa1aa2E0", ".aa1aa5E0", ".aa1aa8E0", ".aa5aa0E0", ".aa5aa3E0", ".aa5aa6E0", ".aa5aa8E0", ".aa9aa0E0", ".aa9aa4E0", ".aa9aa6E0", ".aa9aa7E0", "-.aa1aa0E0", "-.aa1aa2E0", "-.aa1aa5E0", "-.aa1aa8E0", "-.aa5aa0E0", "-.aa5aa3E0", "-.aa5aa6E0", "-.aa5aa8E0", "-.aa9aa0E0", "-.aa9aa4E0", "-.aa9aa6E0", "-.aa9aa7E0", ".aa3aa4E1", ".aa4aa6E1", ".aa4aa3E1", ".aa4aa6E1", ".aa4aa3E1", "-.aa3aa4E1", "-.aa4aa6E1", "-.aa4aa3E1", "-.aa4aa6E1", "-.aa4aa3E1"]);
		check_numeric_format(".0E-0\\a\\a", numbers,
				[".0E0aa", ".1E1aa", ".1E2aa", ".2E2aa", ".2E2aa", ".2E3aa", ".2E3aa", ".3E3aa", ".3E3aa", ".1E4aa", "-.1E1aa", "-.1E2aa", "-.2E2aa", "-.2E2aa", "-.2E3aa", "-.2E3aa", "-.3E3aa", "-.3E3aa", "-.1E5aa", ".1E0aa", ".1E0aa", ".2E0aa", ".2E0aa", ".5E0aa", ".5E0aa", ".6E0aa", ".6E0aa", ".9E0aa", ".9E0aa", ".1E1aa", ".1E1aa", "-.1E0aa", "-.1E0aa", "-.2E0aa", "-.2E0aa", "-.5E0aa", "-.5E0aa", "-.6E0aa", "-.6E0aa", "-.9E0aa", "-.9E0aa", "-.1E1aa", "-.1E1aa", ".3E1aa", ".5E1aa", ".4E1aa", ".5E1aa", ".4E1aa", "-.3E1aa", "-.5E1aa", "-.4E1aa", "-.5E1aa", "-.4E1aa"]);
		check_numeric_format(".0\\E\\aa-0", numbers,
				[".0Eaa0", ".1Eaa1", ".1Eaa2", ".2Eaa2", ".2Eaa2", ".2Eaa3", ".2Eaa3", ".3Eaa3", ".3Eaa3", ".1Eaa4", "-.1Eaa1", "-.1Eaa2", "-.2Eaa2", "-.2Eaa2", "-.2Eaa3", "-.2Eaa3", "-.3Eaa3", "-.3Eaa3", "-.1Eaa5", ".1Eaa0", ".1Eaa0", ".2Eaa0", ".2Eaa0", ".5Eaa0", ".5Eaa0", ".6Eaa0", ".6Eaa0", ".9Eaa0", ".9Eaa0", ".1Eaa1", ".1Eaa1", "-.1Eaa0", "-.1Eaa0", "-.2Eaa0", "-.2Eaa0", "-.5Eaa0", "-.5Eaa0", "-.6Eaa0", "-.6Eaa0", "-.9Eaa0", "-.9Eaa0", "-.1Eaa1", "-.1Eaa1", ".3Eaa1", ".5Eaa1", ".4Eaa1", ".5Eaa1", ".4Eaa1", "-.3Eaa1", "-.5Eaa1", "-.4Eaa1", "-.5Eaa1", "-.4Eaa1"]);
		check_numeric_format(".0\\E\\aa-0\\a\\a", numbers,
				[".0Eaa0aa", ".1Eaa1aa", ".1Eaa2aa", ".2Eaa2aa", ".2Eaa2aa", ".2Eaa3aa", ".2Eaa3aa", ".3Eaa3aa", ".3Eaa3aa", ".1Eaa4aa", "-.1Eaa1aa", "-.1Eaa2aa", "-.2Eaa2aa", "-.2Eaa2aa", "-.2Eaa3aa", "-.2Eaa3aa", "-.3Eaa3aa", "-.3Eaa3aa", "-.1Eaa5aa", ".1Eaa0aa", ".1Eaa0aa", ".2Eaa0aa", ".2Eaa0aa", ".5Eaa0aa", ".5Eaa0aa", ".6Eaa0aa", ".6Eaa0aa", ".9Eaa0aa", ".9Eaa0aa", ".1Eaa1aa", ".1Eaa1aa", "-.1Eaa0aa", "-.1Eaa0aa", "-.2Eaa0aa", "-.2Eaa0aa", "-.5Eaa0aa", "-.5Eaa0aa", "-.6Eaa0aa", "-.6Eaa0aa", "-.9Eaa0aa", "-.9Eaa0aa", "-.1Eaa1aa", "-.1Eaa1aa", ".3Eaa1aa", ".5Eaa1aa", ".4Eaa1aa", ".5Eaa1aa", ".4Eaa1aa", "-.3Eaa1aa", "-.5Eaa1aa", "-.4Eaa1aa", "-.5Eaa1aa", "-.4Eaa1aa"]);
		check_numeric_format(".0\"%\"0E-0", numbers,
				[".0%0E0", ".1%0E1", ".1%1E2", ".1%5E2", ".1%9E2", ".2%0E3", ".2%2E3", ".2%5E3", ".2%8E3", ".1%2E4", "-.1%0E1", "-.1%1E2", "-.1%5E2", "-.1%9E2", "-.2%0E3", "-.2%2E3", "-.2%5E3", "-.2%8E3", "-.9%9E4", ".1%0E0", ".1%2E0", ".1%5E0", ".1%8E0", ".5%0E0", ".5%3E0", ".5%6E0", ".5%8E0", ".9%0E0", ".9%4E0", ".9%6E0", ".9%7E0", "-.1%0E0", "-.1%2E0", "-.1%5E0", "-.1%8E0", "-.5%0E0", "-.5%3E0", "-.5%6E0", "-.5%8E0", "-.9%0E0", "-.9%4E0", "-.9%6E0", "-.9%7E0", ".3%4E1", ".4%6E1", ".4%3E1", ".4%6E1", ".4%3E1", "-.3%4E1", "-.4%6E1", "-.4%3E1", "-.4%6E1", "-.4%3E1"]);
		check_numeric_format(".\"%\"00E-0", numbers,
				[".%00E0", ".%10E1", ".%11E2", ".%15E2", ".%19E2", ".%20E3", ".%22E3", ".%25E3", ".%28E3", ".%12E4", "-.%10E1", "-.%11E2", "-.%15E2", "-.%19E2", "-.%20E3", "-.%22E3", "-.%25E3", "-.%28E3", "-.%99E4", ".%10E0", ".%12E0", ".%15E0", ".%18E0", ".%50E0", ".%53E0", ".%56E0", ".%58E0", ".%90E0", ".%94E0", ".%96E0", ".%97E0", "-.%10E0", "-.%12E0", "-.%15E0", "-.%18E0", "-.%50E0", "-.%53E0", "-.%56E0", "-.%58E0", "-.%90E0", "-.%94E0", "-.%96E0", "-.%97E0", ".%34E1", ".%46E1", ".%43E1", ".%46E1", ".%43E1", "-.%34E1", "-.%46E1", "-.%43E1", "-.%46E1", "-.%43E1"]);
		check_numeric_format(".\"%\"0\"%\"0E-0", numbers,
				[".%0%0E0", ".%1%0E1", ".%1%1E2", ".%1%5E2", ".%1%9E2", ".%2%0E3", ".%2%2E3", ".%2%5E3", ".%2%8E3", ".%1%2E4", "-.%1%0E1", "-.%1%1E2", "-.%1%5E2", "-.%1%9E2", "-.%2%0E3", "-.%2%2E3", "-.%2%5E3", "-.%2%8E3", "-.%9%9E4", ".%1%0E0", ".%1%2E0", ".%1%5E0", ".%1%8E0", ".%5%0E0", ".%5%3E0", ".%5%6E0", ".%5%8E0", ".%9%0E0", ".%9%4E0", ".%9%6E0", ".%9%7E0", "-.%1%0E0", "-.%1%2E0", "-.%1%5E0", "-.%1%8E0", "-.%5%0E0", "-.%5%3E0", "-.%5%6E0", "-.%5%8E0", "-.%9%0E0", "-.%9%4E0", "-.%9%6E0", "-.%9%7E0", ".%3%4E1", ".%4%6E1", ".%4%3E1", ".%4%6E1", ".%4%3E1", "-.%3%4E1", "-.%4%6E1", "-.%4%3E1", "-.%4%6E1", "-.%4%3E1"]);
		check_numeric_format(".0E-0\"%\"", numbers,
				[".0E0%", ".1E1%", ".1E2%", ".2E2%", ".2E2%", ".2E3%", ".2E3%", ".3E3%", ".3E3%", ".1E4%", "-.1E1%", "-.1E2%", "-.2E2%", "-.2E2%", "-.2E3%", "-.2E3%", "-.3E3%", "-.3E3%", "-.1E5%", ".1E0%", ".1E0%", ".2E0%", ".2E0%", ".5E0%", ".5E0%", ".6E0%", ".6E0%", ".9E0%", ".9E0%", ".1E1%", ".1E1%", "-.1E0%", "-.1E0%", "-.2E0%", "-.2E0%", "-.5E0%", "-.5E0%", "-.6E0%", "-.6E0%", "-.9E0%", "-.9E0%", "-.1E1%", "-.1E1%", ".3E1%", ".5E1%", ".4E1%", ".5E1%", ".4E1%", "-.3E1%", "-.5E1%", "-.4E1%", "-.5E1%", "-.4E1%"]);
		check_numeric_format(".0\\E\"%\"-0", numbers,
				[".0E%0", ".1E%1", ".1E%2", ".2E%2", ".2E%2", ".2E%3", ".2E%3", ".3E%3", ".3E%3", ".1E%4", "-.1E%1", "-.1E%2", "-.2E%2", "-.2E%2", "-.2E%3", "-.2E%3", "-.3E%3", "-.3E%3", "-.1E%5", ".1E%0", ".1E%0", ".2E%0", ".2E%0", ".5E%0", ".5E%0", ".6E%0", ".6E%0", ".9E%0", ".9E%0", ".1E%1", ".1E%1", "-.1E%0", "-.1E%0", "-.2E%0", "-.2E%0", "-.5E%0", "-.5E%0", "-.6E%0", "-.6E%0", "-.9E%0", "-.9E%0", "-.1E%1", "-.1E%1", ".3E%1", ".5E%1", ".4E%1", ".5E%1", ".4E%1", "-.3E%1", "-.5E%1", "-.4E%1", "-.5E%1", "-.4E%1"]);
		check_numeric_format(".0\\E\"%\"-0\"%\"", numbers,
				[".0E%0%", ".1E%1%", ".1E%2%", ".2E%2%", ".2E%2%", ".2E%3%", ".2E%3%", ".3E%3%", ".3E%3%", ".1E%4%", "-.1E%1%", "-.1E%2%", "-.2E%2%", "-.2E%2%", "-.2E%3%", "-.2E%3%", "-.3E%3%", "-.3E%3%", "-.1E%5%", ".1E%0%", ".1E%0%", ".2E%0%", ".2E%0%", ".5E%0%", ".5E%0%", ".6E%0%", ".6E%0%", ".9E%0%", ".9E%0%", ".1E%1%", ".1E%1%", "-.1E%0%", "-.1E%0%", "-.2E%0%", "-.2E%0%", "-.5E%0%", "-.5E%0%", "-.6E%0%", "-.6E%0%", "-.9E%0%", "-.9E%0%", "-.1E%1%", "-.1E%1%", ".3E%1%", ".5E%1%", ".4E%1%", ".5E%1%", ".4E%1%", "-.3E%1%", "-.5E%1%", "-.4E%1%", "-.5E%1%", "-.4E%1%"]);
		check_numeric_format(".0%0E+0", numbers,
				[".0%0E+0", ".1%0E+1", ".1%1E+2", ".1%5E+2", ".1%9E+2", ".2%0E+3", ".2%2E+3", ".2%5E+3", ".2%8E+3", ".1%2E+4", "-.1%0E+1", "-.1%1E+2", "-.1%5E+2", "-.1%9E+2", "-.2%0E+3", "-.2%2E+3", "-.2%5E+3", "-.2%8E+3", "-.9%9E+4", ".1%0E+0", ".1%2E+0", ".1%5E+0", ".1%8E+0", ".5%0E+0", ".5%3E+0", ".5%6E+0", ".5%8E+0", ".9%0E+0", ".9%4E+0", ".9%6E+0", ".9%7E+0", "-.1%0E+0", "-.1%2E+0", "-.1%5E+0", "-.1%8E+0", "-.5%0E+0", "-.5%3E+0", "-.5%6E+0", "-.5%8E+0", "-.9%0E+0", "-.9%4E+0", "-.9%6E+0", "-.9%7E+0", ".3%4E+1", ".4%6E+1", ".4%3E+1", ".4%6E+1", ".4%3E+1", "-.3%4E+1", "-.4%6E+1", "-.4%3E+1", "-.4%6E+1", "-.4%3E+1"]);
		check_numeric_format(".%00E+0", numbers,
				[".%00E+0", ".%10E+1", ".%11E+2", ".%15E+2", ".%19E+2", ".%20E+3", ".%22E+3", ".%25E+3", ".%28E+3", ".%12E+4", "-.%10E+1", "-.%11E+2", "-.%15E+2", "-.%19E+2", "-.%20E+3", "-.%22E+3", "-.%25E+3", "-.%28E+3", "-.%99E+4", ".%10E+0", ".%12E+0", ".%15E+0", ".%18E+0", ".%50E+0", ".%53E+0", ".%56E+0", ".%58E+0", ".%90E+0", ".%94E+0", ".%96E+0", ".%97E+0", "-.%10E+0", "-.%12E+0", "-.%15E+0", "-.%18E+0", "-.%50E+0", "-.%53E+0", "-.%56E+0", "-.%58E+0", "-.%90E+0", "-.%94E+0", "-.%96E+0", "-.%97E+0", ".%34E+1", ".%46E+1", ".%43E+1", ".%46E+1", ".%43E+1", "-.%34E+1", "-.%46E+1", "-.%43E+1", "-.%46E+1", "-.%43E+1"]);
		check_numeric_format(".%0%0E+0", numbers,
				[".%0%0E+0", ".%1%0E+1", ".%1%1E+2", ".%1%5E+2", ".%1%9E+2", ".%2%0E+3", ".%2%2E+3", ".%2%5E+3", ".%2%8E+3", ".%1%2E+4", "-.%1%0E+1", "-.%1%1E+2", "-.%1%5E+2", "-.%1%9E+2", "-.%2%0E+3", "-.%2%2E+3", "-.%2%5E+3", "-.%2%8E+3", "-.%9%9E+4", ".%1%0E+0", ".%1%2E+0", ".%1%5E+0", ".%1%8E+0", ".%5%0E+0", ".%5%3E+0", ".%5%6E+0", ".%5%8E+0", ".%9%0E+0", ".%9%4E+0", ".%9%6E+0", ".%9%7E+0", "-.%1%0E+0", "-.%1%2E+0", "-.%1%5E+0", "-.%1%8E+0", "-.%5%0E+0", "-.%5%3E+0", "-.%5%6E+0", "-.%5%8E+0", "-.%9%0E+0", "-.%9%4E+0", "-.%9%6E+0", "-.%9%7E+0", ".%3%4E+1", ".%4%6E+1", ".%4%3E+1", ".%4%6E+1", ".%4%3E+1", "-.%3%4E+1", "-.%4%6E+1", "-.%4%3E+1", "-.%4%6E+1", "-.%4%3E+1"]);
		check_numeric_format(".0E+0%", numbers,
				[".0E+0%", ".1E+1%", ".1E+2%", ".2E+2%", ".2E+2%", ".2E+3%", ".2E+3%", ".3E+3%", ".3E+3%", ".1E+4%", "-.1E+1%", "-.1E+2%", "-.2E+2%", "-.2E+2%", "-.2E+3%", "-.2E+3%", "-.3E+3%", "-.3E+3%", "-.1E+5%", ".1E+0%", ".1E+0%", ".2E+0%", ".2E+0%", ".5E+0%", ".5E+0%", ".6E+0%", ".6E+0%", ".9E+0%", ".9E+0%", ".1E+1%", ".1E+1%", "-.1E+0%", "-.1E+0%", "-.2E+0%", "-.2E+0%", "-.5E+0%", "-.5E+0%", "-.6E+0%", "-.6E+0%", "-.9E+0%", "-.9E+0%", "-.1E+1%", "-.1E+1%", ".3E+1%", ".5E+1%", ".4E+1%", ".5E+1%", ".4E+1%", "-.3E+1%", "-.5E+1%", "-.4E+1%", "-.5E+1%", "-.4E+1%"]);
		check_numeric_format(".0\\E%+0", numbers,
				[".0E%+0", ".1E%+1", ".1E%+2", ".2E%+2", ".2E%+2", ".2E%+3", ".2E%+3", ".3E%+3", ".3E%+3", ".1E%+4", "-.1E%+1", "-.1E%+2", "-.2E%+2", "-.2E%+2", "-.2E%+3", "-.2E%+3", "-.3E%+3", "-.3E%+3", "-.1E%+5", ".1E%+0", ".1E%+0", ".2E%+0", ".2E%+0", ".5E%+0", ".5E%+0", ".6E%+0", ".6E%+0", ".9E%+0", ".9E%+0", ".1E%+1", ".1E%+1", "-.1E%+0", "-.1E%+0", "-.2E%+0", "-.2E%+0", "-.5E%+0", "-.5E%+0", "-.6E%+0", "-.6E%+0", "-.9E%+0", "-.9E%+0", "-.1E%+1", "-.1E%+1", ".3E%+1", ".5E%+1", ".4E%+1", ".5E%+1", ".4E%+1", "-.3E%+1", "-.5E%+1", "-.4E%+1", "-.5E%+1", "-.4E%+1"]);
		check_numeric_format(".0\\E%+0%", numbers,
				[".0E%+0%", ".1E%+1%", ".1E%+2%", ".2E%+2%", ".2E%+2%", ".2E%+3%", ".2E%+3%", ".3E%+3%", ".3E%+3%", ".1E%+4%", "-.1E%+1%", "-.1E%+2%", "-.2E%+2%", "-.2E%+2%", "-.2E%+3%", "-.2E%+3%", "-.3E%+3%", "-.3E%+3%", "-.1E%+5%", ".1E%+0%", ".1E%+0%", ".2E%+0%", ".2E%+0%", ".5E%+0%", ".5E%+0%", ".6E%+0%", ".6E%+0%", ".9E%+0%", ".9E%+0%", ".1E%+1%", ".1E%+1%", "-.1E%+0%", "-.1E%+0%", "-.2E%+0%", "-.2E%+0%", "-.5E%+0%", "-.5E%+0%", "-.6E%+0%", "-.6E%+0%", "-.9E%+0%", "-.9E%+0%", "-.1E%+1%", "-.1E%+1%", ".3E%+1%", ".5E%+1%", ".4E%+1%", ".5E%+1%", ".4E%+1%", "-.3E%+1%", "-.5E%+1%", "-.4E%+1%", "-.5E%+1%", "-.4E%+1%"]);
		check_numeric_format(".0\\a\\a0E+0", numbers,
				[".0aa0E+0", ".1aa0E+1", ".1aa1E+2", ".1aa5E+2", ".1aa9E+2", ".2aa0E+3", ".2aa2E+3", ".2aa5E+3", ".2aa8E+3", ".1aa2E+4", "-.1aa0E+1", "-.1aa1E+2", "-.1aa5E+2", "-.1aa9E+2", "-.2aa0E+3", "-.2aa2E+3", "-.2aa5E+3", "-.2aa8E+3", "-.9aa9E+4", ".1aa0E+0", ".1aa2E+0", ".1aa5E+0", ".1aa8E+0", ".5aa0E+0", ".5aa3E+0", ".5aa6E+0", ".5aa8E+0", ".9aa0E+0", ".9aa4E+0", ".9aa6E+0", ".9aa7E+0", "-.1aa0E+0", "-.1aa2E+0", "-.1aa5E+0", "-.1aa8E+0", "-.5aa0E+0", "-.5aa3E+0", "-.5aa6E+0", "-.5aa8E+0", "-.9aa0E+0", "-.9aa4E+0", "-.9aa6E+0", "-.9aa7E+0", ".3aa4E+1", ".4aa6E+1", ".4aa3E+1", ".4aa6E+1", ".4aa3E+1", "-.3aa4E+1", "-.4aa6E+1", "-.4aa3E+1", "-.4aa6E+1", "-.4aa3E+1"]);
		check_numeric_format(".\\a\\a00E+0", numbers,
				[".aa00E+0", ".aa10E+1", ".aa11E+2", ".aa15E+2", ".aa19E+2", ".aa20E+3", ".aa22E+3", ".aa25E+3", ".aa28E+3", ".aa12E+4", "-.aa10E+1", "-.aa11E+2", "-.aa15E+2", "-.aa19E+2", "-.aa20E+3", "-.aa22E+3", "-.aa25E+3", "-.aa28E+3", "-.aa99E+4", ".aa10E+0", ".aa12E+0", ".aa15E+0", ".aa18E+0", ".aa50E+0", ".aa53E+0", ".aa56E+0", ".aa58E+0", ".aa90E+0", ".aa94E+0", ".aa96E+0", ".aa97E+0", "-.aa10E+0", "-.aa12E+0", "-.aa15E+0", "-.aa18E+0", "-.aa50E+0", "-.aa53E+0", "-.aa56E+0", "-.aa58E+0", "-.aa90E+0", "-.aa94E+0", "-.aa96E+0", "-.aa97E+0", ".aa34E+1", ".aa46E+1", ".aa43E+1", ".aa46E+1", ".aa43E+1", "-.aa34E+1", "-.aa46E+1", "-.aa43E+1", "-.aa46E+1", "-.aa43E+1"]);
		check_numeric_format(".\\a\\a0\\a\\a0E+0", numbers,
				[".aa0aa0E+0", ".aa1aa0E+1", ".aa1aa1E+2", ".aa1aa5E+2", ".aa1aa9E+2", ".aa2aa0E+3", ".aa2aa2E+3", ".aa2aa5E+3", ".aa2aa8E+3", ".aa1aa2E+4", "-.aa1aa0E+1", "-.aa1aa1E+2", "-.aa1aa5E+2", "-.aa1aa9E+2", "-.aa2aa0E+3", "-.aa2aa2E+3", "-.aa2aa5E+3", "-.aa2aa8E+3", "-.aa9aa9E+4", ".aa1aa0E+0", ".aa1aa2E+0", ".aa1aa5E+0", ".aa1aa8E+0", ".aa5aa0E+0", ".aa5aa3E+0", ".aa5aa6E+0", ".aa5aa8E+0", ".aa9aa0E+0", ".aa9aa4E+0", ".aa9aa6E+0", ".aa9aa7E+0", "-.aa1aa0E+0", "-.aa1aa2E+0", "-.aa1aa5E+0", "-.aa1aa8E+0", "-.aa5aa0E+0", "-.aa5aa3E+0", "-.aa5aa6E+0", "-.aa5aa8E+0", "-.aa9aa0E+0", "-.aa9aa4E+0", "-.aa9aa6E+0", "-.aa9aa7E+0", ".aa3aa4E+1", ".aa4aa6E+1", ".aa4aa3E+1", ".aa4aa6E+1", ".aa4aa3E+1", "-.aa3aa4E+1", "-.aa4aa6E+1", "-.aa4aa3E+1", "-.aa4aa6E+1", "-.aa4aa3E+1"]);
		check_numeric_format(".0E+0\\a\\a", numbers,
				[".0E+0aa", ".1E+1aa", ".1E+2aa", ".2E+2aa", ".2E+2aa", ".2E+3aa", ".2E+3aa", ".3E+3aa", ".3E+3aa", ".1E+4aa", "-.1E+1aa", "-.1E+2aa", "-.2E+2aa", "-.2E+2aa", "-.2E+3aa", "-.2E+3aa", "-.3E+3aa", "-.3E+3aa", "-.1E+5aa", ".1E+0aa", ".1E+0aa", ".2E+0aa", ".2E+0aa", ".5E+0aa", ".5E+0aa", ".6E+0aa", ".6E+0aa", ".9E+0aa", ".9E+0aa", ".1E+1aa", ".1E+1aa", "-.1E+0aa", "-.1E+0aa", "-.2E+0aa", "-.2E+0aa", "-.5E+0aa", "-.5E+0aa", "-.6E+0aa", "-.6E+0aa", "-.9E+0aa", "-.9E+0aa", "-.1E+1aa", "-.1E+1aa", ".3E+1aa", ".5E+1aa", ".4E+1aa", ".5E+1aa", ".4E+1aa", "-.3E+1aa", "-.5E+1aa", "-.4E+1aa", "-.5E+1aa", "-.4E+1aa"]);
		check_numeric_format(".0\\E\\aa+0", numbers,
				[".0Eaa+0", ".1Eaa+1", ".1Eaa+2", ".2Eaa+2", ".2Eaa+2", ".2Eaa+3", ".2Eaa+3", ".3Eaa+3", ".3Eaa+3", ".1Eaa+4", "-.1Eaa+1", "-.1Eaa+2", "-.2Eaa+2", "-.2Eaa+2", "-.2Eaa+3", "-.2Eaa+3", "-.3Eaa+3", "-.3Eaa+3", "-.1Eaa+5", ".1Eaa+0", ".1Eaa+0", ".2Eaa+0", ".2Eaa+0", ".5Eaa+0", ".5Eaa+0", ".6Eaa+0", ".6Eaa+0", ".9Eaa+0", ".9Eaa+0", ".1Eaa+1", ".1Eaa+1", "-.1Eaa+0", "-.1Eaa+0", "-.2Eaa+0", "-.2Eaa+0", "-.5Eaa+0", "-.5Eaa+0", "-.6Eaa+0", "-.6Eaa+0", "-.9Eaa+0", "-.9Eaa+0", "-.1Eaa+1", "-.1Eaa+1", ".3Eaa+1", ".5Eaa+1", ".4Eaa+1", ".5Eaa+1", ".4Eaa+1", "-.3Eaa+1", "-.5Eaa+1", "-.4Eaa+1", "-.5Eaa+1", "-.4Eaa+1"]);
		check_numeric_format(".0\\E\\aa+0\\a\\a", numbers,
				[".0Eaa+0aa", ".1Eaa+1aa", ".1Eaa+2aa", ".2Eaa+2aa", ".2Eaa+2aa", ".2Eaa+3aa", ".2Eaa+3aa", ".3Eaa+3aa", ".3Eaa+3aa", ".1Eaa+4aa", "-.1Eaa+1aa", "-.1Eaa+2aa", "-.2Eaa+2aa", "-.2Eaa+2aa", "-.2Eaa+3aa", "-.2Eaa+3aa", "-.3Eaa+3aa", "-.3Eaa+3aa", "-.1Eaa+5aa", ".1Eaa+0aa", ".1Eaa+0aa", ".2Eaa+0aa", ".2Eaa+0aa", ".5Eaa+0aa", ".5Eaa+0aa", ".6Eaa+0aa", ".6Eaa+0aa", ".9Eaa+0aa", ".9Eaa+0aa", ".1Eaa+1aa", ".1Eaa+1aa", "-.1Eaa+0aa", "-.1Eaa+0aa", "-.2Eaa+0aa", "-.2Eaa+0aa", "-.5Eaa+0aa", "-.5Eaa+0aa", "-.6Eaa+0aa", "-.6Eaa+0aa", "-.9Eaa+0aa", "-.9Eaa+0aa", "-.1Eaa+1aa", "-.1Eaa+1aa", ".3Eaa+1aa", ".5Eaa+1aa", ".4Eaa+1aa", ".5Eaa+1aa", ".4Eaa+1aa", "-.3Eaa+1aa", "-.5Eaa+1aa", "-.4Eaa+1aa", "-.5Eaa+1aa", "-.4Eaa+1aa"]);
		check_numeric_format(".0\"%\"0E+0", numbers,
				[".0%0E+0", ".1%0E+1", ".1%1E+2", ".1%5E+2", ".1%9E+2", ".2%0E+3", ".2%2E+3", ".2%5E+3", ".2%8E+3", ".1%2E+4", "-.1%0E+1", "-.1%1E+2", "-.1%5E+2", "-.1%9E+2", "-.2%0E+3", "-.2%2E+3", "-.2%5E+3", "-.2%8E+3", "-.9%9E+4", ".1%0E+0", ".1%2E+0", ".1%5E+0", ".1%8E+0", ".5%0E+0", ".5%3E+0", ".5%6E+0", ".5%8E+0", ".9%0E+0", ".9%4E+0", ".9%6E+0", ".9%7E+0", "-.1%0E+0", "-.1%2E+0", "-.1%5E+0", "-.1%8E+0", "-.5%0E+0", "-.5%3E+0", "-.5%6E+0", "-.5%8E+0", "-.9%0E+0", "-.9%4E+0", "-.9%6E+0", "-.9%7E+0", ".3%4E+1", ".4%6E+1", ".4%3E+1", ".4%6E+1", ".4%3E+1", "-.3%4E+1", "-.4%6E+1", "-.4%3E+1", "-.4%6E+1", "-.4%3E+1"]);
		check_numeric_format(".\"%\"00E+0", numbers,
				[".%00E+0", ".%10E+1", ".%11E+2", ".%15E+2", ".%19E+2", ".%20E+3", ".%22E+3", ".%25E+3", ".%28E+3", ".%12E+4", "-.%10E+1", "-.%11E+2", "-.%15E+2", "-.%19E+2", "-.%20E+3", "-.%22E+3", "-.%25E+3", "-.%28E+3", "-.%99E+4", ".%10E+0", ".%12E+0", ".%15E+0", ".%18E+0", ".%50E+0", ".%53E+0", ".%56E+0", ".%58E+0", ".%90E+0", ".%94E+0", ".%96E+0", ".%97E+0", "-.%10E+0", "-.%12E+0", "-.%15E+0", "-.%18E+0", "-.%50E+0", "-.%53E+0", "-.%56E+0", "-.%58E+0", "-.%90E+0", "-.%94E+0", "-.%96E+0", "-.%97E+0", ".%34E+1", ".%46E+1", ".%43E+1", ".%46E+1", ".%43E+1", "-.%34E+1", "-.%46E+1", "-.%43E+1", "-.%46E+1", "-.%43E+1"]);
		check_numeric_format(".\"%\"0\"%\"0E+0", numbers,
				[".%0%0E+0", ".%1%0E+1", ".%1%1E+2", ".%1%5E+2", ".%1%9E+2", ".%2%0E+3", ".%2%2E+3", ".%2%5E+3", ".%2%8E+3", ".%1%2E+4", "-.%1%0E+1", "-.%1%1E+2", "-.%1%5E+2", "-.%1%9E+2", "-.%2%0E+3", "-.%2%2E+3", "-.%2%5E+3", "-.%2%8E+3", "-.%9%9E+4", ".%1%0E+0", ".%1%2E+0", ".%1%5E+0", ".%1%8E+0", ".%5%0E+0", ".%5%3E+0", ".%5%6E+0", ".%5%8E+0", ".%9%0E+0", ".%9%4E+0", ".%9%6E+0", ".%9%7E+0", "-.%1%0E+0", "-.%1%2E+0", "-.%1%5E+0", "-.%1%8E+0", "-.%5%0E+0", "-.%5%3E+0", "-.%5%6E+0", "-.%5%8E+0", "-.%9%0E+0", "-.%9%4E+0", "-.%9%6E+0", "-.%9%7E+0", ".%3%4E+1", ".%4%6E+1", ".%4%3E+1", ".%4%6E+1", ".%4%3E+1", "-.%3%4E+1", "-.%4%6E+1", "-.%4%3E+1", "-.%4%6E+1", "-.%4%3E+1"]);
		check_numeric_format(".0E+0\"%\"", numbers,
				[".0E+0%", ".1E+1%", ".1E+2%", ".2E+2%", ".2E+2%", ".2E+3%", ".2E+3%", ".3E+3%", ".3E+3%", ".1E+4%", "-.1E+1%", "-.1E+2%", "-.2E+2%", "-.2E+2%", "-.2E+3%", "-.2E+3%", "-.3E+3%", "-.3E+3%", "-.1E+5%", ".1E+0%", ".1E+0%", ".2E+0%", ".2E+0%", ".5E+0%", ".5E+0%", ".6E+0%", ".6E+0%", ".9E+0%", ".9E+0%", ".1E+1%", ".1E+1%", "-.1E+0%", "-.1E+0%", "-.2E+0%", "-.2E+0%", "-.5E+0%", "-.5E+0%", "-.6E+0%", "-.6E+0%", "-.9E+0%", "-.9E+0%", "-.1E+1%", "-.1E+1%", ".3E+1%", ".5E+1%", ".4E+1%", ".5E+1%", ".4E+1%", "-.3E+1%", "-.5E+1%", "-.4E+1%", "-.5E+1%", "-.4E+1%"]);
		check_numeric_format(".0\\E\"%\"+0", numbers,
				[".0E%+0", ".1E%+1", ".1E%+2", ".2E%+2", ".2E%+2", ".2E%+3", ".2E%+3", ".3E%+3", ".3E%+3", ".1E%+4", "-.1E%+1", "-.1E%+2", "-.2E%+2", "-.2E%+2", "-.2E%+3", "-.2E%+3", "-.3E%+3", "-.3E%+3", "-.1E%+5", ".1E%+0", ".1E%+0", ".2E%+0", ".2E%+0", ".5E%+0", ".5E%+0", ".6E%+0", ".6E%+0", ".9E%+0", ".9E%+0", ".1E%+1", ".1E%+1", "-.1E%+0", "-.1E%+0", "-.2E%+0", "-.2E%+0", "-.5E%+0", "-.5E%+0", "-.6E%+0", "-.6E%+0", "-.9E%+0", "-.9E%+0", "-.1E%+1", "-.1E%+1", ".3E%+1", ".5E%+1", ".4E%+1", ".5E%+1", ".4E%+1", "-.3E%+1", "-.5E%+1", "-.4E%+1", "-.5E%+1", "-.4E%+1"]);
		check_numeric_format(".0\\E\"%\"+0\"%\"", numbers,
				[".0E%+0%", ".1E%+1%", ".1E%+2%", ".2E%+2%", ".2E%+2%", ".2E%+3%", ".2E%+3%", ".3E%+3%", ".3E%+3%", ".1E%+4%", "-.1E%+1%", "-.1E%+2%", "-.2E%+2%", "-.2E%+2%", "-.2E%+3%", "-.2E%+3%", "-.3E%+3%", "-.3E%+3%", "-.1E%+5%", ".1E%+0%", ".1E%+0%", ".2E%+0%", ".2E%+0%", ".5E%+0%", ".5E%+0%", ".6E%+0%", ".6E%+0%", ".9E%+0%", ".9E%+0%", ".1E%+1%", ".1E%+1%", "-.1E%+0%", "-.1E%+0%", "-.2E%+0%", "-.2E%+0%", "-.5E%+0%", "-.5E%+0%", "-.6E%+0%", "-.6E%+0%", "-.9E%+0%", "-.9E%+0%", "-.1E%+1%", "-.1E%+1%", ".3E%+1%", ".5E%+1%", ".4E%+1%", ".5E%+1%", ".4E%+1%", "-.3E%+1%", "-.5E%+1%", "-.4E%+1%", "-.5E%+1%", "-.4E%+1%"]);
		check_numeric_format("0%0.0E-0", numbers,
				["0%0.0E0", "0%1.0E0", "1%1.0E0", "1%5.0E0", "1%9.0E0", "0%2.0E2", "0%2.2E2", "0%2.5E2", "0%2.8E2", "1%2.3E2", "-0%1.0E0", "-1%1.0E0", "-1%5.0E0", "-1%9.0E0", "-0%2.0E2", "-0%2.2E2", "-0%2.5E2", "-0%2.8E2", "-9%8.8E2", "1%0.0E-2", "1%2.0E-2", "1%5.1E-2", "1%8.1E-2", "5%0.0E-2", "5%3.0E-2", "5%5.5E-2", "5%7.5E-2", "9%0.0E-2", "9%4.0E-2", "9%5.9E-2", "9%6.9E-2", "-1%0.0E-2", "-1%2.0E-2", "-1%5.1E-2", "-1%8.1E-2", "-5%0.0E-2", "-5%3.0E-2", "-5%5.5E-2", "-5%7.5E-2", "-9%0.0E-2", "-9%4.0E-2", "-9%5.9E-2", "-9%6.9E-2", "0%3.4E0", "0%4.6E0", "0%4.3E0", "0%4.6E0", "0%4.3E0", "-0%3.4E0", "-0%4.6E0", "-0%4.3E0", "-0%4.6E0", "-0%4.3E0"]);
		check_numeric_format("%00.0E-0", numbers,
				["%00.0E0", "%01.0E0", "%11.0E0", "%15.0E0", "%19.0E0", "%02.0E2", "%02.2E2", "%02.5E2", "%02.8E2", "%12.3E2", "-%01.0E0", "-%11.0E0", "-%15.0E0", "-%19.0E0", "-%02.0E2", "-%02.2E2", "-%02.5E2", "-%02.8E2", "-%98.8E2", "%10.0E-2", "%12.0E-2", "%15.1E-2", "%18.1E-2", "%50.0E-2", "%53.0E-2", "%55.5E-2", "%57.5E-2", "%90.0E-2", "%94.0E-2", "%95.9E-2", "%96.9E-2", "-%10.0E-2", "-%12.0E-2", "-%15.1E-2", "-%18.1E-2", "-%50.0E-2", "-%53.0E-2", "-%55.5E-2", "-%57.5E-2", "-%90.0E-2", "-%94.0E-2", "-%95.9E-2", "-%96.9E-2", "%03.4E0", "%04.6E0", "%04.3E0", "%04.6E0", "%04.3E0", "-%03.4E0", "-%04.6E0", "-%04.3E0", "-%04.6E0", "-%04.3E0"]);
		check_numeric_format("%0%0.0E-0", numbers,
				["%0%0.0E0", "%0%1.0E0", "%1%1.0E0", "%1%5.0E0", "%1%9.0E0", "%0%2.0E2", "%0%2.2E2", "%0%2.5E2", "%0%2.8E2", "%1%2.3E2", "-%0%1.0E0", "-%1%1.0E0", "-%1%5.0E0", "-%1%9.0E0", "-%0%2.0E2", "-%0%2.2E2", "-%0%2.5E2", "-%0%2.8E2", "-%9%8.8E2", "%1%0.0E-2", "%1%2.0E-2", "%1%5.1E-2", "%1%8.1E-2", "%5%0.0E-2", "%5%3.0E-2", "%5%5.5E-2", "%5%7.5E-2", "%9%0.0E-2", "%9%4.0E-2", "%9%5.9E-2", "%9%6.9E-2", "-%1%0.0E-2", "-%1%2.0E-2", "-%1%5.1E-2", "-%1%8.1E-2", "-%5%0.0E-2", "-%5%3.0E-2", "-%5%5.5E-2", "-%5%7.5E-2", "-%9%0.0E-2", "-%9%4.0E-2", "-%9%5.9E-2", "-%9%6.9E-2", "%0%3.4E0", "%0%4.6E0", "%0%4.3E0", "%0%4.6E0", "%0%4.3E0", "-%0%3.4E0", "-%0%4.6E0", "-%0%4.3E0", "-%0%4.6E0", "-%0%4.3E0"]);
		check_numeric_format("0.0%0E-0", numbers,
				["0.0%0E0", "1.0%0E0", "1.1%0E1", "1.5%0E1", "1.9%0E1", "2.0%0E2", "2.2%0E2", "2.5%0E2", "2.8%0E2", "1.2%3E3", "-1.0%0E0", "-1.1%0E1", "-1.5%0E1", "-1.9%0E1", "-2.0%0E2", "-2.2%0E2", "-2.5%0E2", "-2.8%0E2", "-9.8%8E3", "1.0%0E-1", "1.2%0E-1", "1.5%1E-1", "1.8%1E-1", "5.0%0E-1", "5.3%0E-1", "5.5%5E-1", "5.7%5E-1", "9.0%0E-1", "9.4%0E-1", "9.5%9E-1", "9.6%9E-1", "-1.0%0E-1", "-1.2%0E-1", "-1.5%1E-1", "-1.8%1E-1", "-5.0%0E-1", "-5.3%0E-1", "-5.5%5E-1", "-5.7%5E-1", "-9.0%0E-1", "-9.4%0E-1", "-9.5%9E-1", "-9.6%9E-1", "3.4%0E0", "4.5%6E0", "4.3%2E0", "4.5%7E0", "4.3%2E0", "-3.4%0E0", "-4.5%6E0", "-4.3%2E0", "-4.5%7E0", "-4.3%2E0"]);
		check_numeric_format("0.%00E-0", numbers,
				["0.%00E0", "1.%00E0", "1.%10E1", "1.%50E1", "1.%90E1", "2.%00E2", "2.%20E2", "2.%50E2", "2.%80E2", "1.%23E3", "-1.%00E0", "-1.%10E1", "-1.%50E1", "-1.%90E1", "-2.%00E2", "-2.%20E2", "-2.%50E2", "-2.%80E2", "-9.%88E3", "1.%00E-1", "1.%20E-1", "1.%51E-1", "1.%81E-1", "5.%00E-1", "5.%30E-1", "5.%55E-1", "5.%75E-1", "9.%00E-1", "9.%40E-1", "9.%59E-1", "9.%69E-1", "-1.%00E-1", "-1.%20E-1", "-1.%51E-1", "-1.%81E-1", "-5.%00E-1", "-5.%30E-1", "-5.%55E-1", "-5.%75E-1", "-9.%00E-1", "-9.%40E-1", "-9.%59E-1", "-9.%69E-1", "3.%40E0", "4.%56E0", "4.%32E0", "4.%57E0", "4.%32E0", "-3.%40E0", "-4.%56E0", "-4.%32E0", "-4.%57E0", "-4.%32E0"]);
		check_numeric_format("0.%0%0E-0", numbers,
				["0.%0%0E0", "1.%0%0E0", "1.%1%0E1", "1.%5%0E1", "1.%9%0E1", "2.%0%0E2", "2.%2%0E2", "2.%5%0E2", "2.%8%0E2", "1.%2%3E3", "-1.%0%0E0", "-1.%1%0E1", "-1.%5%0E1", "-1.%9%0E1", "-2.%0%0E2", "-2.%2%0E2", "-2.%5%0E2", "-2.%8%0E2", "-9.%8%8E3", "1.%0%0E-1", "1.%2%0E-1", "1.%5%1E-1", "1.%8%1E-1", "5.%0%0E-1", "5.%3%0E-1", "5.%5%5E-1", "5.%7%5E-1", "9.%0%0E-1", "9.%4%0E-1", "9.%5%9E-1", "9.%6%9E-1", "-1.%0%0E-1", "-1.%2%0E-1", "-1.%5%1E-1", "-1.%8%1E-1", "-5.%0%0E-1", "-5.%3%0E-1", "-5.%5%5E-1", "-5.%7%5E-1", "-9.%0%0E-1", "-9.%4%0E-1", "-9.%5%9E-1", "-9.%6%9E-1", "3.%4%0E0", "4.%5%6E0", "4.%3%2E0", "4.%5%7E0", "4.%3%2E0", "-3.%4%0E0", "-4.%5%6E0", "-4.%3%2E0", "-4.%5%7E0", "-4.%3%2E0"]);
		check_numeric_format("0.0E-0%", numbers,
				["0.0E0%", "1.0E0%", "1.1E1%", "1.5E1%", "1.9E1%", "2.0E2%", "2.2E2%", "2.5E2%", "2.8E2%", "1.2E3%", "-1.0E0%", "-1.1E1%", "-1.5E1%", "-1.9E1%", "-2.0E2%", "-2.2E2%", "-2.5E2%", "-2.8E2%", "-9.9E3%", "1.0E-1%", "1.2E-1%", "1.5E-1%", "1.8E-1%", "5.0E-1%", "5.3E-1%", "5.6E-1%", "5.8E-1%", "9.0E-1%", "9.4E-1%", "9.6E-1%", "9.7E-1%", "-1.0E-1%", "-1.2E-1%", "-1.5E-1%", "-1.8E-1%", "-5.0E-1%", "-5.3E-1%", "-5.6E-1%", "-5.8E-1%", "-9.0E-1%", "-9.4E-1%", "-9.6E-1%", "-9.7E-1%", "3.4E0%", "4.6E0%", "4.3E0%", "4.6E0%", "4.3E0%", "-3.4E0%", "-4.6E0%", "-4.3E0%", "-4.6E0%", "-4.3E0%"]);
		check_numeric_format("0.0\\E%-0", numbers,
				["0.0E%0", "1.0E%0", "1.1E%1", "1.5E%1", "1.9E%1", "2.0E%2", "2.2E%2", "2.5E%2", "2.8E%2", "1.2E%3", "-1.0E%0", "-1.1E%1", "-1.5E%1", "-1.9E%1", "-2.0E%2", "-2.2E%2", "-2.5E%2", "-2.8E%2", "-9.9E%3", "1.0E%-1", "1.2E%-1", "1.5E%-1", "1.8E%-1", "5.0E%-1", "5.3E%-1", "5.6E%-1", "5.8E%-1", "9.0E%-1", "9.4E%-1", "9.6E%-1", "9.7E%-1", "-1.0E%-1", "-1.2E%-1", "-1.5E%-1", "-1.8E%-1", "-5.0E%-1", "-5.3E%-1", "-5.6E%-1", "-5.8E%-1", "-9.0E%-1", "-9.4E%-1", "-9.6E%-1", "-9.7E%-1", "3.4E%0", "4.6E%0", "4.3E%0", "4.6E%0", "4.3E%0", "-3.4E%0", "-4.6E%0", "-4.3E%0", "-4.6E%0", "-4.3E%0"]);
		check_numeric_format("0.0\\E%-0%", numbers,
				["0.0E%0%", "1.0E%0%", "1.1E%1%", "1.5E%1%", "1.9E%1%", "2.0E%2%", "2.2E%2%", "2.5E%2%", "2.8E%2%", "1.2E%3%", "-1.0E%0%", "-1.1E%1%", "-1.5E%1%", "-1.9E%1%", "-2.0E%2%", "-2.2E%2%", "-2.5E%2%", "-2.8E%2%", "-9.9E%3%", "1.0E%-1%", "1.2E%-1%", "1.5E%-1%", "1.8E%-1%", "5.0E%-1%", "5.3E%-1%", "5.6E%-1%", "5.8E%-1%", "9.0E%-1%", "9.4E%-1%", "9.6E%-1%", "9.7E%-1%", "-1.0E%-1%", "-1.2E%-1%", "-1.5E%-1%", "-1.8E%-1%", "-5.0E%-1%", "-5.3E%-1%", "-5.6E%-1%", "-5.8E%-1%", "-9.0E%-1%", "-9.4E%-1%", "-9.6E%-1%", "-9.7E%-1%", "3.4E%0%", "4.6E%0%", "4.3E%0%", "4.6E%0%", "4.3E%0%", "-3.4E%0%", "-4.6E%0%", "-4.3E%0%", "-4.6E%0%", "-4.3E%0%"]);
		check_numeric_format("0\\a\\a0.0E-0", numbers,
				["0aa0.0E0", "0aa1.0E0", "1aa1.0E0", "1aa5.0E0", "1aa9.0E0", "0aa2.0E2", "0aa2.2E2", "0aa2.5E2", "0aa2.8E2", "1aa2.3E2", "-0aa1.0E0", "-1aa1.0E0", "-1aa5.0E0", "-1aa9.0E0", "-0aa2.0E2", "-0aa2.2E2", "-0aa2.5E2", "-0aa2.8E2", "-9aa8.8E2", "1aa0.0E-2", "1aa2.0E-2", "1aa5.1E-2", "1aa8.1E-2", "5aa0.0E-2", "5aa3.0E-2", "5aa5.5E-2", "5aa7.5E-2", "9aa0.0E-2", "9aa4.0E-2", "9aa5.9E-2", "9aa6.9E-2", "-1aa0.0E-2", "-1aa2.0E-2", "-1aa5.1E-2", "-1aa8.1E-2", "-5aa0.0E-2", "-5aa3.0E-2", "-5aa5.5E-2", "-5aa7.5E-2", "-9aa0.0E-2", "-9aa4.0E-2", "-9aa5.9E-2", "-9aa6.9E-2", "0aa3.4E0", "0aa4.6E0", "0aa4.3E0", "0aa4.6E0", "0aa4.3E0", "-0aa3.4E0", "-0aa4.6E0", "-0aa4.3E0", "-0aa4.6E0", "-0aa4.3E0"]);
		check_numeric_format("\\a\\a00.0E-0", numbers,
				["aa00.0E0", "aa01.0E0", "aa11.0E0", "aa15.0E0", "aa19.0E0", "aa02.0E2", "aa02.2E2", "aa02.5E2", "aa02.8E2", "aa12.3E2", "-aa01.0E0", "-aa11.0E0", "-aa15.0E0", "-aa19.0E0", "-aa02.0E2", "-aa02.2E2", "-aa02.5E2", "-aa02.8E2", "-aa98.8E2", "aa10.0E-2", "aa12.0E-2", "aa15.1E-2", "aa18.1E-2", "aa50.0E-2", "aa53.0E-2", "aa55.5E-2", "aa57.5E-2", "aa90.0E-2", "aa94.0E-2", "aa95.9E-2", "aa96.9E-2", "-aa10.0E-2", "-aa12.0E-2", "-aa15.1E-2", "-aa18.1E-2", "-aa50.0E-2", "-aa53.0E-2", "-aa55.5E-2", "-aa57.5E-2", "-aa90.0E-2", "-aa94.0E-2", "-aa95.9E-2", "-aa96.9E-2", "aa03.4E0", "aa04.6E0", "aa04.3E0", "aa04.6E0", "aa04.3E0", "-aa03.4E0", "-aa04.6E0", "-aa04.3E0", "-aa04.6E0", "-aa04.3E0"]);
		check_numeric_format("\\a\\a0\\a\\a0.0E-0", numbers,
				["aa0aa0.0E0", "aa0aa1.0E0", "aa1aa1.0E0", "aa1aa5.0E0", "aa1aa9.0E0", "aa0aa2.0E2", "aa0aa2.2E2", "aa0aa2.5E2", "aa0aa2.8E2", "aa1aa2.3E2", "-aa0aa1.0E0", "-aa1aa1.0E0", "-aa1aa5.0E0", "-aa1aa9.0E0", "-aa0aa2.0E2", "-aa0aa2.2E2", "-aa0aa2.5E2", "-aa0aa2.8E2", "-aa9aa8.8E2", "aa1aa0.0E-2", "aa1aa2.0E-2", "aa1aa5.1E-2", "aa1aa8.1E-2", "aa5aa0.0E-2", "aa5aa3.0E-2", "aa5aa5.5E-2", "aa5aa7.5E-2", "aa9aa0.0E-2", "aa9aa4.0E-2", "aa9aa5.9E-2", "aa9aa6.9E-2", "-aa1aa0.0E-2", "-aa1aa2.0E-2", "-aa1aa5.1E-2", "-aa1aa8.1E-2", "-aa5aa0.0E-2", "-aa5aa3.0E-2", "-aa5aa5.5E-2", "-aa5aa7.5E-2", "-aa9aa0.0E-2", "-aa9aa4.0E-2", "-aa9aa5.9E-2", "-aa9aa6.9E-2", "aa0aa3.4E0", "aa0aa4.6E0", "aa0aa4.3E0", "aa0aa4.6E0", "aa0aa4.3E0", "-aa0aa3.4E0", "-aa0aa4.6E0", "-aa0aa4.3E0", "-aa0aa4.6E0", "-aa0aa4.3E0"]);
		check_numeric_format("0.0\\a\\a0E-0", numbers,
				["0.0aa0E0", "1.0aa0E0", "1.1aa0E1", "1.5aa0E1", "1.9aa0E1", "2.0aa0E2", "2.2aa0E2", "2.5aa0E2", "2.8aa0E2", "1.2aa3E3", "-1.0aa0E0", "-1.1aa0E1", "-1.5aa0E1", "-1.9aa0E1", "-2.0aa0E2", "-2.2aa0E2", "-2.5aa0E2", "-2.8aa0E2", "-9.8aa8E3", "1.0aa0E-1", "1.2aa0E-1", "1.5aa1E-1", "1.8aa1E-1", "5.0aa0E-1", "5.3aa0E-1", "5.5aa5E-1", "5.7aa5E-1", "9.0aa0E-1", "9.4aa0E-1", "9.5aa9E-1", "9.6aa9E-1", "-1.0aa0E-1", "-1.2aa0E-1", "-1.5aa1E-1", "-1.8aa1E-1", "-5.0aa0E-1", "-5.3aa0E-1", "-5.5aa5E-1", "-5.7aa5E-1", "-9.0aa0E-1", "-9.4aa0E-1", "-9.5aa9E-1", "-9.6aa9E-1", "3.4aa0E0", "4.5aa6E0", "4.3aa2E0", "4.5aa7E0", "4.3aa2E0", "-3.4aa0E0", "-4.5aa6E0", "-4.3aa2E0", "-4.5aa7E0", "-4.3aa2E0"]);
		check_numeric_format("0.\\a\\a00E-0", numbers,
				["0.aa00E0", "1.aa00E0", "1.aa10E1", "1.aa50E1", "1.aa90E1", "2.aa00E2", "2.aa20E2", "2.aa50E2", "2.aa80E2", "1.aa23E3", "-1.aa00E0", "-1.aa10E1", "-1.aa50E1", "-1.aa90E1", "-2.aa00E2", "-2.aa20E2", "-2.aa50E2", "-2.aa80E2", "-9.aa88E3", "1.aa00E-1", "1.aa20E-1", "1.aa51E-1", "1.aa81E-1", "5.aa00E-1", "5.aa30E-1", "5.aa55E-1", "5.aa75E-1", "9.aa00E-1", "9.aa40E-1", "9.aa59E-1", "9.aa69E-1", "-1.aa00E-1", "-1.aa20E-1", "-1.aa51E-1", "-1.aa81E-1", "-5.aa00E-1", "-5.aa30E-1", "-5.aa55E-1", "-5.aa75E-1", "-9.aa00E-1", "-9.aa40E-1", "-9.aa59E-1", "-9.aa69E-1", "3.aa40E0", "4.aa56E0", "4.aa32E0", "4.aa57E0", "4.aa32E0", "-3.aa40E0", "-4.aa56E0", "-4.aa32E0", "-4.aa57E0", "-4.aa32E0"]);
		check_numeric_format("0.\\a\\a0\\a\\a0E-0", numbers,
				["0.aa0aa0E0", "1.aa0aa0E0", "1.aa1aa0E1", "1.aa5aa0E1", "1.aa9aa0E1", "2.aa0aa0E2", "2.aa2aa0E2", "2.aa5aa0E2", "2.aa8aa0E2", "1.aa2aa3E3", "-1.aa0aa0E0", "-1.aa1aa0E1", "-1.aa5aa0E1", "-1.aa9aa0E1", "-2.aa0aa0E2", "-2.aa2aa0E2", "-2.aa5aa0E2", "-2.aa8aa0E2", "-9.aa8aa8E3", "1.aa0aa0E-1", "1.aa2aa0E-1", "1.aa5aa1E-1", "1.aa8aa1E-1", "5.aa0aa0E-1", "5.aa3aa0E-1", "5.aa5aa5E-1", "5.aa7aa5E-1", "9.aa0aa0E-1", "9.aa4aa0E-1", "9.aa5aa9E-1", "9.aa6aa9E-1", "-1.aa0aa0E-1", "-1.aa2aa0E-1", "-1.aa5aa1E-1", "-1.aa8aa1E-1", "-5.aa0aa0E-1", "-5.aa3aa0E-1", "-5.aa5aa5E-1", "-5.aa7aa5E-1", "-9.aa0aa0E-1", "-9.aa4aa0E-1", "-9.aa5aa9E-1", "-9.aa6aa9E-1", "3.aa4aa0E0", "4.aa5aa6E0", "4.aa3aa2E0", "4.aa5aa7E0", "4.aa3aa2E0", "-3.aa4aa0E0", "-4.aa5aa6E0", "-4.aa3aa2E0", "-4.aa5aa7E0", "-4.aa3aa2E0"]);
		check_numeric_format("0.0E-0\\a\\a", numbers,
				["0.0E0aa", "1.0E0aa", "1.1E1aa", "1.5E1aa", "1.9E1aa", "2.0E2aa", "2.2E2aa", "2.5E2aa", "2.8E2aa", "1.2E3aa", "-1.0E0aa", "-1.1E1aa", "-1.5E1aa", "-1.9E1aa", "-2.0E2aa", "-2.2E2aa", "-2.5E2aa", "-2.8E2aa", "-9.9E3aa", "1.0E-1aa", "1.2E-1aa", "1.5E-1aa", "1.8E-1aa", "5.0E-1aa", "5.3E-1aa", "5.6E-1aa", "5.8E-1aa", "9.0E-1aa", "9.4E-1aa", "9.6E-1aa", "9.7E-1aa", "-1.0E-1aa", "-1.2E-1aa", "-1.5E-1aa", "-1.8E-1aa", "-5.0E-1aa", "-5.3E-1aa", "-5.6E-1aa", "-5.8E-1aa", "-9.0E-1aa", "-9.4E-1aa", "-9.6E-1aa", "-9.7E-1aa", "3.4E0aa", "4.6E0aa", "4.3E0aa", "4.6E0aa", "4.3E0aa", "-3.4E0aa", "-4.6E0aa", "-4.3E0aa", "-4.6E0aa", "-4.3E0aa"]);
		check_numeric_format("0.0\\E\\aa-0", numbers,
				["0.0Eaa0", "1.0Eaa0", "1.1Eaa1", "1.5Eaa1", "1.9Eaa1", "2.0Eaa2", "2.2Eaa2", "2.5Eaa2", "2.8Eaa2", "1.2Eaa3", "-1.0Eaa0", "-1.1Eaa1", "-1.5Eaa1", "-1.9Eaa1", "-2.0Eaa2", "-2.2Eaa2", "-2.5Eaa2", "-2.8Eaa2", "-9.9Eaa3", "1.0Eaa-1", "1.2Eaa-1", "1.5Eaa-1", "1.8Eaa-1", "5.0Eaa-1", "5.3Eaa-1", "5.6Eaa-1", "5.8Eaa-1", "9.0Eaa-1", "9.4Eaa-1", "9.6Eaa-1", "9.7Eaa-1", "-1.0Eaa-1", "-1.2Eaa-1", "-1.5Eaa-1", "-1.8Eaa-1", "-5.0Eaa-1", "-5.3Eaa-1", "-5.6Eaa-1", "-5.8Eaa-1", "-9.0Eaa-1", "-9.4Eaa-1", "-9.6Eaa-1", "-9.7Eaa-1", "3.4Eaa0", "4.6Eaa0", "4.3Eaa0", "4.6Eaa0", "4.3Eaa0", "-3.4Eaa0", "-4.6Eaa0", "-4.3Eaa0", "-4.6Eaa0", "-4.3Eaa0"]);
		check_numeric_format("0.0\\E\\aa-0\\a\\a", numbers,
				["0.0Eaa0aa", "1.0Eaa0aa", "1.1Eaa1aa", "1.5Eaa1aa", "1.9Eaa1aa", "2.0Eaa2aa", "2.2Eaa2aa", "2.5Eaa2aa", "2.8Eaa2aa", "1.2Eaa3aa", "-1.0Eaa0aa", "-1.1Eaa1aa", "-1.5Eaa1aa", "-1.9Eaa1aa", "-2.0Eaa2aa", "-2.2Eaa2aa", "-2.5Eaa2aa", "-2.8Eaa2aa", "-9.9Eaa3aa", "1.0Eaa-1aa", "1.2Eaa-1aa", "1.5Eaa-1aa", "1.8Eaa-1aa", "5.0Eaa-1aa", "5.3Eaa-1aa", "5.6Eaa-1aa", "5.8Eaa-1aa", "9.0Eaa-1aa", "9.4Eaa-1aa", "9.6Eaa-1aa", "9.7Eaa-1aa", "-1.0Eaa-1aa", "-1.2Eaa-1aa", "-1.5Eaa-1aa", "-1.8Eaa-1aa", "-5.0Eaa-1aa", "-5.3Eaa-1aa", "-5.6Eaa-1aa", "-5.8Eaa-1aa", "-9.0Eaa-1aa", "-9.4Eaa-1aa", "-9.6Eaa-1aa", "-9.7Eaa-1aa", "3.4Eaa0aa", "4.6Eaa0aa", "4.3Eaa0aa", "4.6Eaa0aa", "4.3Eaa0aa", "-3.4Eaa0aa", "-4.6Eaa0aa", "-4.3Eaa0aa", "-4.6Eaa0aa", "-4.3Eaa0aa"]);
		check_numeric_format("0\"%\"0.0E-0", numbers,
				["0%0.0E0", "0%1.0E0", "1%1.0E0", "1%5.0E0", "1%9.0E0", "0%2.0E2", "0%2.2E2", "0%2.5E2", "0%2.8E2", "1%2.3E2", "-0%1.0E0", "-1%1.0E0", "-1%5.0E0", "-1%9.0E0", "-0%2.0E2", "-0%2.2E2", "-0%2.5E2", "-0%2.8E2", "-9%8.8E2", "1%0.0E-2", "1%2.0E-2", "1%5.1E-2", "1%8.1E-2", "5%0.0E-2", "5%3.0E-2", "5%5.5E-2", "5%7.5E-2", "9%0.0E-2", "9%4.0E-2", "9%5.9E-2", "9%6.9E-2", "-1%0.0E-2", "-1%2.0E-2", "-1%5.1E-2", "-1%8.1E-2", "-5%0.0E-2", "-5%3.0E-2", "-5%5.5E-2", "-5%7.5E-2", "-9%0.0E-2", "-9%4.0E-2", "-9%5.9E-2", "-9%6.9E-2", "0%3.4E0", "0%4.6E0", "0%4.3E0", "0%4.6E0", "0%4.3E0", "-0%3.4E0", "-0%4.6E0", "-0%4.3E0", "-0%4.6E0", "-0%4.3E0"]);
		check_numeric_format("\"%\"00.0E-0", numbers,
				["%00.0E0", "%01.0E0", "%11.0E0", "%15.0E0", "%19.0E0", "%02.0E2", "%02.2E2", "%02.5E2", "%02.8E2", "%12.3E2", "-%01.0E0", "-%11.0E0", "-%15.0E0", "-%19.0E0", "-%02.0E2", "-%02.2E2", "-%02.5E2", "-%02.8E2", "-%98.8E2", "%10.0E-2", "%12.0E-2", "%15.1E-2", "%18.1E-2", "%50.0E-2", "%53.0E-2", "%55.5E-2", "%57.5E-2", "%90.0E-2", "%94.0E-2", "%95.9E-2", "%96.9E-2", "-%10.0E-2", "-%12.0E-2", "-%15.1E-2", "-%18.1E-2", "-%50.0E-2", "-%53.0E-2", "-%55.5E-2", "-%57.5E-2", "-%90.0E-2", "-%94.0E-2", "-%95.9E-2", "-%96.9E-2", "%03.4E0", "%04.6E0", "%04.3E0", "%04.6E0", "%04.3E0", "-%03.4E0", "-%04.6E0", "-%04.3E0", "-%04.6E0", "-%04.3E0"]);
		check_numeric_format("\"%\"0\"%\"0.0E-0", numbers,
				["%0%0.0E0", "%0%1.0E0", "%1%1.0E0", "%1%5.0E0", "%1%9.0E0", "%0%2.0E2", "%0%2.2E2", "%0%2.5E2", "%0%2.8E2", "%1%2.3E2", "-%0%1.0E0", "-%1%1.0E0", "-%1%5.0E0", "-%1%9.0E0", "-%0%2.0E2", "-%0%2.2E2", "-%0%2.5E2", "-%0%2.8E2", "-%9%8.8E2", "%1%0.0E-2", "%1%2.0E-2", "%1%5.1E-2", "%1%8.1E-2", "%5%0.0E-2", "%5%3.0E-2", "%5%5.5E-2", "%5%7.5E-2", "%9%0.0E-2", "%9%4.0E-2", "%9%5.9E-2", "%9%6.9E-2", "-%1%0.0E-2", "-%1%2.0E-2", "-%1%5.1E-2", "-%1%8.1E-2", "-%5%0.0E-2", "-%5%3.0E-2", "-%5%5.5E-2", "-%5%7.5E-2", "-%9%0.0E-2", "-%9%4.0E-2", "-%9%5.9E-2", "-%9%6.9E-2", "%0%3.4E0", "%0%4.6E0", "%0%4.3E0", "%0%4.6E0", "%0%4.3E0", "-%0%3.4E0", "-%0%4.6E0", "-%0%4.3E0", "-%0%4.6E0", "-%0%4.3E0"]);
		check_numeric_format("0.0\"%\"0E-0", numbers,
				["0.0%0E0", "1.0%0E0", "1.1%0E1", "1.5%0E1", "1.9%0E1", "2.0%0E2", "2.2%0E2", "2.5%0E2", "2.8%0E2", "1.2%3E3", "-1.0%0E0", "-1.1%0E1", "-1.5%0E1", "-1.9%0E1", "-2.0%0E2", "-2.2%0E2", "-2.5%0E2", "-2.8%0E2", "-9.8%8E3", "1.0%0E-1", "1.2%0E-1", "1.5%1E-1", "1.8%1E-1", "5.0%0E-1", "5.3%0E-1", "5.5%5E-1", "5.7%5E-1", "9.0%0E-1", "9.4%0E-1", "9.5%9E-1", "9.6%9E-1", "-1.0%0E-1", "-1.2%0E-1", "-1.5%1E-1", "-1.8%1E-1", "-5.0%0E-1", "-5.3%0E-1", "-5.5%5E-1", "-5.7%5E-1", "-9.0%0E-1", "-9.4%0E-1", "-9.5%9E-1", "-9.6%9E-1", "3.4%0E0", "4.5%6E0", "4.3%2E0", "4.5%7E0", "4.3%2E0", "-3.4%0E0", "-4.5%6E0", "-4.3%2E0", "-4.5%7E0", "-4.3%2E0"]);
		check_numeric_format("0.\"%\"00E-0", numbers,
				["0.%00E0", "1.%00E0", "1.%10E1", "1.%50E1", "1.%90E1", "2.%00E2", "2.%20E2", "2.%50E2", "2.%80E2", "1.%23E3", "-1.%00E0", "-1.%10E1", "-1.%50E1", "-1.%90E1", "-2.%00E2", "-2.%20E2", "-2.%50E2", "-2.%80E2", "-9.%88E3", "1.%00E-1", "1.%20E-1", "1.%51E-1", "1.%81E-1", "5.%00E-1", "5.%30E-1", "5.%55E-1", "5.%75E-1", "9.%00E-1", "9.%40E-1", "9.%59E-1", "9.%69E-1", "-1.%00E-1", "-1.%20E-1", "-1.%51E-1", "-1.%81E-1", "-5.%00E-1", "-5.%30E-1", "-5.%55E-1", "-5.%75E-1", "-9.%00E-1", "-9.%40E-1", "-9.%59E-1", "-9.%69E-1", "3.%40E0", "4.%56E0", "4.%32E0", "4.%57E0", "4.%32E0", "-3.%40E0", "-4.%56E0", "-4.%32E0", "-4.%57E0", "-4.%32E0"]);
		check_numeric_format("0.\"%\"0\"%\"0E-0", numbers,
				["0.%0%0E0", "1.%0%0E0", "1.%1%0E1", "1.%5%0E1", "1.%9%0E1", "2.%0%0E2", "2.%2%0E2", "2.%5%0E2", "2.%8%0E2", "1.%2%3E3", "-1.%0%0E0", "-1.%1%0E1", "-1.%5%0E1", "-1.%9%0E1", "-2.%0%0E2", "-2.%2%0E2", "-2.%5%0E2", "-2.%8%0E2", "-9.%8%8E3", "1.%0%0E-1", "1.%2%0E-1", "1.%5%1E-1", "1.%8%1E-1", "5.%0%0E-1", "5.%3%0E-1", "5.%5%5E-1", "5.%7%5E-1", "9.%0%0E-1", "9.%4%0E-1", "9.%5%9E-1", "9.%6%9E-1", "-1.%0%0E-1", "-1.%2%0E-1", "-1.%5%1E-1", "-1.%8%1E-1", "-5.%0%0E-1", "-5.%3%0E-1", "-5.%5%5E-1", "-5.%7%5E-1", "-9.%0%0E-1", "-9.%4%0E-1", "-9.%5%9E-1", "-9.%6%9E-1", "3.%4%0E0", "4.%5%6E0", "4.%3%2E0", "4.%5%7E0", "4.%3%2E0", "-3.%4%0E0", "-4.%5%6E0", "-4.%3%2E0", "-4.%5%7E0", "-4.%3%2E0"]);
		check_numeric_format("0.0E-0\"%\"", numbers,
				["0.0E0%", "1.0E0%", "1.1E1%", "1.5E1%", "1.9E1%", "2.0E2%", "2.2E2%", "2.5E2%", "2.8E2%", "1.2E3%", "-1.0E0%", "-1.1E1%", "-1.5E1%", "-1.9E1%", "-2.0E2%", "-2.2E2%", "-2.5E2%", "-2.8E2%", "-9.9E3%", "1.0E-1%", "1.2E-1%", "1.5E-1%", "1.8E-1%", "5.0E-1%", "5.3E-1%", "5.6E-1%", "5.8E-1%", "9.0E-1%", "9.4E-1%", "9.6E-1%", "9.7E-1%", "-1.0E-1%", "-1.2E-1%", "-1.5E-1%", "-1.8E-1%", "-5.0E-1%", "-5.3E-1%", "-5.6E-1%", "-5.8E-1%", "-9.0E-1%", "-9.4E-1%", "-9.6E-1%", "-9.7E-1%", "3.4E0%", "4.6E0%", "4.3E0%", "4.6E0%", "4.3E0%", "-3.4E0%", "-4.6E0%", "-4.3E0%", "-4.6E0%", "-4.3E0%"]);
		check_numeric_format("0.0\\E\"%\"-0", numbers,
				["0.0E%0", "1.0E%0", "1.1E%1", "1.5E%1", "1.9E%1", "2.0E%2", "2.2E%2", "2.5E%2", "2.8E%2", "1.2E%3", "-1.0E%0", "-1.1E%1", "-1.5E%1", "-1.9E%1", "-2.0E%2", "-2.2E%2", "-2.5E%2", "-2.8E%2", "-9.9E%3", "1.0E%-1", "1.2E%-1", "1.5E%-1", "1.8E%-1", "5.0E%-1", "5.3E%-1", "5.6E%-1", "5.8E%-1", "9.0E%-1", "9.4E%-1", "9.6E%-1", "9.7E%-1", "-1.0E%-1", "-1.2E%-1", "-1.5E%-1", "-1.8E%-1", "-5.0E%-1", "-5.3E%-1", "-5.6E%-1", "-5.8E%-1", "-9.0E%-1", "-9.4E%-1", "-9.6E%-1", "-9.7E%-1", "3.4E%0", "4.6E%0", "4.3E%0", "4.6E%0", "4.3E%0", "-3.4E%0", "-4.6E%0", "-4.3E%0", "-4.6E%0", "-4.3E%0"]);
		check_numeric_format("0.0\\E\"%\"-0\"%\"", numbers,
				["0.0E%0%", "1.0E%0%", "1.1E%1%", "1.5E%1%", "1.9E%1%", "2.0E%2%", "2.2E%2%", "2.5E%2%", "2.8E%2%", "1.2E%3%", "-1.0E%0%", "-1.1E%1%", "-1.5E%1%", "-1.9E%1%", "-2.0E%2%", "-2.2E%2%", "-2.5E%2%", "-2.8E%2%", "-9.9E%3%", "1.0E%-1%", "1.2E%-1%", "1.5E%-1%", "1.8E%-1%", "5.0E%-1%", "5.3E%-1%", "5.6E%-1%", "5.8E%-1%", "9.0E%-1%", "9.4E%-1%", "9.6E%-1%", "9.7E%-1%", "-1.0E%-1%", "-1.2E%-1%", "-1.5E%-1%", "-1.8E%-1%", "-5.0E%-1%", "-5.3E%-1%", "-5.6E%-1%", "-5.8E%-1%", "-9.0E%-1%", "-9.4E%-1%", "-9.6E%-1%", "-9.7E%-1%", "3.4E%0%", "4.6E%0%", "4.3E%0%", "4.6E%0%", "4.3E%0%", "-3.4E%0%", "-4.6E%0%", "-4.3E%0%", "-4.6E%0%", "-4.3E%0%"]);
		check_numeric_format("0%0.0E+0", numbers,
				["0%0.0E+0", "0%1.0E+0", "1%1.0E+0", "1%5.0E+0", "1%9.0E+0", "0%2.0E+2", "0%2.2E+2", "0%2.5E+2", "0%2.8E+2", "1%2.3E+2", "-0%1.0E+0", "-1%1.0E+0", "-1%5.0E+0", "-1%9.0E+0", "-0%2.0E+2", "-0%2.2E+2", "-0%2.5E+2", "-0%2.8E+2", "-9%8.8E+2", "1%0.0E-2", "1%2.0E-2", "1%5.1E-2", "1%8.1E-2", "5%0.0E-2", "5%3.0E-2", "5%5.5E-2", "5%7.5E-2", "9%0.0E-2", "9%4.0E-2", "9%5.9E-2", "9%6.9E-2", "-1%0.0E-2", "-1%2.0E-2", "-1%5.1E-2", "-1%8.1E-2", "-5%0.0E-2", "-5%3.0E-2", "-5%5.5E-2", "-5%7.5E-2", "-9%0.0E-2", "-9%4.0E-2", "-9%5.9E-2", "-9%6.9E-2", "0%3.4E+0", "0%4.6E+0", "0%4.3E+0", "0%4.6E+0", "0%4.3E+0", "-0%3.4E+0", "-0%4.6E+0", "-0%4.3E+0", "-0%4.6E+0", "-0%4.3E+0"]);
		check_numeric_format("%00.0E+0", numbers,
				["%00.0E+0", "%01.0E+0", "%11.0E+0", "%15.0E+0", "%19.0E+0", "%02.0E+2", "%02.2E+2", "%02.5E+2", "%02.8E+2", "%12.3E+2", "-%01.0E+0", "-%11.0E+0", "-%15.0E+0", "-%19.0E+0", "-%02.0E+2", "-%02.2E+2", "-%02.5E+2", "-%02.8E+2", "-%98.8E+2", "%10.0E-2", "%12.0E-2", "%15.1E-2", "%18.1E-2", "%50.0E-2", "%53.0E-2", "%55.5E-2", "%57.5E-2", "%90.0E-2", "%94.0E-2", "%95.9E-2", "%96.9E-2", "-%10.0E-2", "-%12.0E-2", "-%15.1E-2", "-%18.1E-2", "-%50.0E-2", "-%53.0E-2", "-%55.5E-2", "-%57.5E-2", "-%90.0E-2", "-%94.0E-2", "-%95.9E-2", "-%96.9E-2", "%03.4E+0", "%04.6E+0", "%04.3E+0", "%04.6E+0", "%04.3E+0", "-%03.4E+0", "-%04.6E+0", "-%04.3E+0", "-%04.6E+0", "-%04.3E+0"]);
		check_numeric_format("%0%0.0E+0", numbers,
				["%0%0.0E+0", "%0%1.0E+0", "%1%1.0E+0", "%1%5.0E+0", "%1%9.0E+0", "%0%2.0E+2", "%0%2.2E+2", "%0%2.5E+2", "%0%2.8E+2", "%1%2.3E+2", "-%0%1.0E+0", "-%1%1.0E+0", "-%1%5.0E+0", "-%1%9.0E+0", "-%0%2.0E+2", "-%0%2.2E+2", "-%0%2.5E+2", "-%0%2.8E+2", "-%9%8.8E+2", "%1%0.0E-2", "%1%2.0E-2", "%1%5.1E-2", "%1%8.1E-2", "%5%0.0E-2", "%5%3.0E-2", "%5%5.5E-2", "%5%7.5E-2", "%9%0.0E-2", "%9%4.0E-2", "%9%5.9E-2", "%9%6.9E-2", "-%1%0.0E-2", "-%1%2.0E-2", "-%1%5.1E-2", "-%1%8.1E-2", "-%5%0.0E-2", "-%5%3.0E-2", "-%5%5.5E-2", "-%5%7.5E-2", "-%9%0.0E-2", "-%9%4.0E-2", "-%9%5.9E-2", "-%9%6.9E-2", "%0%3.4E+0", "%0%4.6E+0", "%0%4.3E+0", "%0%4.6E+0", "%0%4.3E+0", "-%0%3.4E+0", "-%0%4.6E+0", "-%0%4.3E+0", "-%0%4.6E+0", "-%0%4.3E+0"]);
		check_numeric_format("0.0%0E+0", numbers,
				["0.0%0E+0", "1.0%0E+0", "1.1%0E+1", "1.5%0E+1", "1.9%0E+1", "2.0%0E+2", "2.2%0E+2", "2.5%0E+2", "2.8%0E+2", "1.2%3E+3", "-1.0%0E+0", "-1.1%0E+1", "-1.5%0E+1", "-1.9%0E+1", "-2.0%0E+2", "-2.2%0E+2", "-2.5%0E+2", "-2.8%0E+2", "-9.8%8E+3", "1.0%0E-1", "1.2%0E-1", "1.5%1E-1", "1.8%1E-1", "5.0%0E-1", "5.3%0E-1", "5.5%5E-1", "5.7%5E-1", "9.0%0E-1", "9.4%0E-1", "9.5%9E-1", "9.6%9E-1", "-1.0%0E-1", "-1.2%0E-1", "-1.5%1E-1", "-1.8%1E-1", "-5.0%0E-1", "-5.3%0E-1", "-5.5%5E-1", "-5.7%5E-1", "-9.0%0E-1", "-9.4%0E-1", "-9.5%9E-1", "-9.6%9E-1", "3.4%0E+0", "4.5%6E+0", "4.3%2E+0", "4.5%7E+0", "4.3%2E+0", "-3.4%0E+0", "-4.5%6E+0", "-4.3%2E+0", "-4.5%7E+0", "-4.3%2E+0"]);
		check_numeric_format("0.%00E+0", numbers,
				["0.%00E+0", "1.%00E+0", "1.%10E+1", "1.%50E+1", "1.%90E+1", "2.%00E+2", "2.%20E+2", "2.%50E+2", "2.%80E+2", "1.%23E+3", "-1.%00E+0", "-1.%10E+1", "-1.%50E+1", "-1.%90E+1", "-2.%00E+2", "-2.%20E+2", "-2.%50E+2", "-2.%80E+2", "-9.%88E+3", "1.%00E-1", "1.%20E-1", "1.%51E-1", "1.%81E-1", "5.%00E-1", "5.%30E-1", "5.%55E-1", "5.%75E-1", "9.%00E-1", "9.%40E-1", "9.%59E-1", "9.%69E-1", "-1.%00E-1", "-1.%20E-1", "-1.%51E-1", "-1.%81E-1", "-5.%00E-1", "-5.%30E-1", "-5.%55E-1", "-5.%75E-1", "-9.%00E-1", "-9.%40E-1", "-9.%59E-1", "-9.%69E-1", "3.%40E+0", "4.%56E+0", "4.%32E+0", "4.%57E+0", "4.%32E+0", "-3.%40E+0", "-4.%56E+0", "-4.%32E+0", "-4.%57E+0", "-4.%32E+0"]);
		check_numeric_format("0.%0%0E+0", numbers,
				["0.%0%0E+0", "1.%0%0E+0", "1.%1%0E+1", "1.%5%0E+1", "1.%9%0E+1", "2.%0%0E+2", "2.%2%0E+2", "2.%5%0E+2", "2.%8%0E+2", "1.%2%3E+3", "-1.%0%0E+0", "-1.%1%0E+1", "-1.%5%0E+1", "-1.%9%0E+1", "-2.%0%0E+2", "-2.%2%0E+2", "-2.%5%0E+2", "-2.%8%0E+2", "-9.%8%8E+3", "1.%0%0E-1", "1.%2%0E-1", "1.%5%1E-1", "1.%8%1E-1", "5.%0%0E-1", "5.%3%0E-1", "5.%5%5E-1", "5.%7%5E-1", "9.%0%0E-1", "9.%4%0E-1", "9.%5%9E-1", "9.%6%9E-1", "-1.%0%0E-1", "-1.%2%0E-1", "-1.%5%1E-1", "-1.%8%1E-1", "-5.%0%0E-1", "-5.%3%0E-1", "-5.%5%5E-1", "-5.%7%5E-1", "-9.%0%0E-1", "-9.%4%0E-1", "-9.%5%9E-1", "-9.%6%9E-1", "3.%4%0E+0", "4.%5%6E+0", "4.%3%2E+0", "4.%5%7E+0", "4.%3%2E+0", "-3.%4%0E+0", "-4.%5%6E+0", "-4.%3%2E+0", "-4.%5%7E+0", "-4.%3%2E+0"]);
		check_numeric_format("0.0E+0%", numbers,
				["0.0E+0%", "1.0E+0%", "1.1E+1%", "1.5E+1%", "1.9E+1%", "2.0E+2%", "2.2E+2%", "2.5E+2%", "2.8E+2%", "1.2E+3%", "-1.0E+0%", "-1.1E+1%", "-1.5E+1%", "-1.9E+1%", "-2.0E+2%", "-2.2E+2%", "-2.5E+2%", "-2.8E+2%", "-9.9E+3%", "1.0E-1%", "1.2E-1%", "1.5E-1%", "1.8E-1%", "5.0E-1%", "5.3E-1%", "5.6E-1%", "5.8E-1%", "9.0E-1%", "9.4E-1%", "9.6E-1%", "9.7E-1%", "-1.0E-1%", "-1.2E-1%", "-1.5E-1%", "-1.8E-1%", "-5.0E-1%", "-5.3E-1%", "-5.6E-1%", "-5.8E-1%", "-9.0E-1%", "-9.4E-1%", "-9.6E-1%", "-9.7E-1%", "3.4E+0%", "4.6E+0%", "4.3E+0%", "4.6E+0%", "4.3E+0%", "-3.4E+0%", "-4.6E+0%", "-4.3E+0%", "-4.6E+0%", "-4.3E+0%"]);
		check_numeric_format("0.0\\E%+0", numbers,
				["0.0E%+0", "1.0E%+0", "1.1E%+1", "1.5E%+1", "1.9E%+1", "2.0E%+2", "2.2E%+2", "2.5E%+2", "2.8E%+2", "1.2E%+3", "-1.0E%+0", "-1.1E%+1", "-1.5E%+1", "-1.9E%+1", "-2.0E%+2", "-2.2E%+2", "-2.5E%+2", "-2.8E%+2", "-9.9E%+3", "1.0E%-1", "1.2E%-1", "1.5E%-1", "1.8E%-1", "5.0E%-1", "5.3E%-1", "5.6E%-1", "5.8E%-1", "9.0E%-1", "9.4E%-1", "9.6E%-1", "9.7E%-1", "-1.0E%-1", "-1.2E%-1", "-1.5E%-1", "-1.8E%-1", "-5.0E%-1", "-5.3E%-1", "-5.6E%-1", "-5.8E%-1", "-9.0E%-1", "-9.4E%-1", "-9.6E%-1", "-9.7E%-1", "3.4E%+0", "4.6E%+0", "4.3E%+0", "4.6E%+0", "4.3E%+0", "-3.4E%+0", "-4.6E%+0", "-4.3E%+0", "-4.6E%+0", "-4.3E%+0"]);
		check_numeric_format("0.0\\E%+0%", numbers,
				["0.0E%+0%", "1.0E%+0%", "1.1E%+1%", "1.5E%+1%", "1.9E%+1%", "2.0E%+2%", "2.2E%+2%", "2.5E%+2%", "2.8E%+2%", "1.2E%+3%", "-1.0E%+0%", "-1.1E%+1%", "-1.5E%+1%", "-1.9E%+1%", "-2.0E%+2%", "-2.2E%+2%", "-2.5E%+2%", "-2.8E%+2%", "-9.9E%+3%", "1.0E%-1%", "1.2E%-1%", "1.5E%-1%", "1.8E%-1%", "5.0E%-1%", "5.3E%-1%", "5.6E%-1%", "5.8E%-1%", "9.0E%-1%", "9.4E%-1%", "9.6E%-1%", "9.7E%-1%", "-1.0E%-1%", "-1.2E%-1%", "-1.5E%-1%", "-1.8E%-1%", "-5.0E%-1%", "-5.3E%-1%", "-5.6E%-1%", "-5.8E%-1%", "-9.0E%-1%", "-9.4E%-1%", "-9.6E%-1%", "-9.7E%-1%", "3.4E%+0%", "4.6E%+0%", "4.3E%+0%", "4.6E%+0%", "4.3E%+0%", "-3.4E%+0%", "-4.6E%+0%", "-4.3E%+0%", "-4.6E%+0%", "-4.3E%+0%"]);
		check_numeric_format("0\\a\\a0.0E+0", numbers,
				["0aa0.0E+0", "0aa1.0E+0", "1aa1.0E+0", "1aa5.0E+0", "1aa9.0E+0", "0aa2.0E+2", "0aa2.2E+2", "0aa2.5E+2", "0aa2.8E+2", "1aa2.3E+2", "-0aa1.0E+0", "-1aa1.0E+0", "-1aa5.0E+0", "-1aa9.0E+0", "-0aa2.0E+2", "-0aa2.2E+2", "-0aa2.5E+2", "-0aa2.8E+2", "-9aa8.8E+2", "1aa0.0E-2", "1aa2.0E-2", "1aa5.1E-2", "1aa8.1E-2", "5aa0.0E-2", "5aa3.0E-2", "5aa5.5E-2", "5aa7.5E-2", "9aa0.0E-2", "9aa4.0E-2", "9aa5.9E-2", "9aa6.9E-2", "-1aa0.0E-2", "-1aa2.0E-2", "-1aa5.1E-2", "-1aa8.1E-2", "-5aa0.0E-2", "-5aa3.0E-2", "-5aa5.5E-2", "-5aa7.5E-2", "-9aa0.0E-2", "-9aa4.0E-2", "-9aa5.9E-2", "-9aa6.9E-2", "0aa3.4E+0", "0aa4.6E+0", "0aa4.3E+0", "0aa4.6E+0", "0aa4.3E+0", "-0aa3.4E+0", "-0aa4.6E+0", "-0aa4.3E+0", "-0aa4.6E+0", "-0aa4.3E+0"]);
		check_numeric_format("\\a\\a00.0E+0", numbers,
				["aa00.0E+0", "aa01.0E+0", "aa11.0E+0", "aa15.0E+0", "aa19.0E+0", "aa02.0E+2", "aa02.2E+2", "aa02.5E+2", "aa02.8E+2", "aa12.3E+2", "-aa01.0E+0", "-aa11.0E+0", "-aa15.0E+0", "-aa19.0E+0", "-aa02.0E+2", "-aa02.2E+2", "-aa02.5E+2", "-aa02.8E+2", "-aa98.8E+2", "aa10.0E-2", "aa12.0E-2", "aa15.1E-2", "aa18.1E-2", "aa50.0E-2", "aa53.0E-2", "aa55.5E-2", "aa57.5E-2", "aa90.0E-2", "aa94.0E-2", "aa95.9E-2", "aa96.9E-2", "-aa10.0E-2", "-aa12.0E-2", "-aa15.1E-2", "-aa18.1E-2", "-aa50.0E-2", "-aa53.0E-2", "-aa55.5E-2", "-aa57.5E-2", "-aa90.0E-2", "-aa94.0E-2", "-aa95.9E-2", "-aa96.9E-2", "aa03.4E+0", "aa04.6E+0", "aa04.3E+0", "aa04.6E+0", "aa04.3E+0", "-aa03.4E+0", "-aa04.6E+0", "-aa04.3E+0", "-aa04.6E+0", "-aa04.3E+0"]);
		check_numeric_format("\\a\\a0\\a\\a0.0E+0", numbers,
				["aa0aa0.0E+0", "aa0aa1.0E+0", "aa1aa1.0E+0", "aa1aa5.0E+0", "aa1aa9.0E+0", "aa0aa2.0E+2", "aa0aa2.2E+2", "aa0aa2.5E+2", "aa0aa2.8E+2", "aa1aa2.3E+2", "-aa0aa1.0E+0", "-aa1aa1.0E+0", "-aa1aa5.0E+0", "-aa1aa9.0E+0", "-aa0aa2.0E+2", "-aa0aa2.2E+2", "-aa0aa2.5E+2", "-aa0aa2.8E+2", "-aa9aa8.8E+2", "aa1aa0.0E-2", "aa1aa2.0E-2", "aa1aa5.1E-2", "aa1aa8.1E-2", "aa5aa0.0E-2", "aa5aa3.0E-2", "aa5aa5.5E-2", "aa5aa7.5E-2", "aa9aa0.0E-2", "aa9aa4.0E-2", "aa9aa5.9E-2", "aa9aa6.9E-2", "-aa1aa0.0E-2", "-aa1aa2.0E-2", "-aa1aa5.1E-2", "-aa1aa8.1E-2", "-aa5aa0.0E-2", "-aa5aa3.0E-2", "-aa5aa5.5E-2", "-aa5aa7.5E-2", "-aa9aa0.0E-2", "-aa9aa4.0E-2", "-aa9aa5.9E-2", "-aa9aa6.9E-2", "aa0aa3.4E+0", "aa0aa4.6E+0", "aa0aa4.3E+0", "aa0aa4.6E+0", "aa0aa4.3E+0", "-aa0aa3.4E+0", "-aa0aa4.6E+0", "-aa0aa4.3E+0", "-aa0aa4.6E+0", "-aa0aa4.3E+0"]);
		check_numeric_format("0.0\\a\\a0E+0", numbers,
				["0.0aa0E+0", "1.0aa0E+0", "1.1aa0E+1", "1.5aa0E+1", "1.9aa0E+1", "2.0aa0E+2", "2.2aa0E+2", "2.5aa0E+2", "2.8aa0E+2", "1.2aa3E+3", "-1.0aa0E+0", "-1.1aa0E+1", "-1.5aa0E+1", "-1.9aa0E+1", "-2.0aa0E+2", "-2.2aa0E+2", "-2.5aa0E+2", "-2.8aa0E+2", "-9.8aa8E+3", "1.0aa0E-1", "1.2aa0E-1", "1.5aa1E-1", "1.8aa1E-1", "5.0aa0E-1", "5.3aa0E-1", "5.5aa5E-1", "5.7aa5E-1", "9.0aa0E-1", "9.4aa0E-1", "9.5aa9E-1", "9.6aa9E-1", "-1.0aa0E-1", "-1.2aa0E-1", "-1.5aa1E-1", "-1.8aa1E-1", "-5.0aa0E-1", "-5.3aa0E-1", "-5.5aa5E-1", "-5.7aa5E-1", "-9.0aa0E-1", "-9.4aa0E-1", "-9.5aa9E-1", "-9.6aa9E-1", "3.4aa0E+0", "4.5aa6E+0", "4.3aa2E+0", "4.5aa7E+0", "4.3aa2E+0", "-3.4aa0E+0", "-4.5aa6E+0", "-4.3aa2E+0", "-4.5aa7E+0", "-4.3aa2E+0"]);
		check_numeric_format("0.\\a\\a00E+0", numbers,
				["0.aa00E+0", "1.aa00E+0", "1.aa10E+1", "1.aa50E+1", "1.aa90E+1", "2.aa00E+2", "2.aa20E+2", "2.aa50E+2", "2.aa80E+2", "1.aa23E+3", "-1.aa00E+0", "-1.aa10E+1", "-1.aa50E+1", "-1.aa90E+1", "-2.aa00E+2", "-2.aa20E+2", "-2.aa50E+2", "-2.aa80E+2", "-9.aa88E+3", "1.aa00E-1", "1.aa20E-1", "1.aa51E-1", "1.aa81E-1", "5.aa00E-1", "5.aa30E-1", "5.aa55E-1", "5.aa75E-1", "9.aa00E-1", "9.aa40E-1", "9.aa59E-1", "9.aa69E-1", "-1.aa00E-1", "-1.aa20E-1", "-1.aa51E-1", "-1.aa81E-1", "-5.aa00E-1", "-5.aa30E-1", "-5.aa55E-1", "-5.aa75E-1", "-9.aa00E-1", "-9.aa40E-1", "-9.aa59E-1", "-9.aa69E-1", "3.aa40E+0", "4.aa56E+0", "4.aa32E+0", "4.aa57E+0", "4.aa32E+0", "-3.aa40E+0", "-4.aa56E+0", "-4.aa32E+0", "-4.aa57E+0", "-4.aa32E+0"]);
		check_numeric_format("0.\\a\\a0\\a\\a0E+0", numbers,
				["0.aa0aa0E+0", "1.aa0aa0E+0", "1.aa1aa0E+1", "1.aa5aa0E+1", "1.aa9aa0E+1", "2.aa0aa0E+2", "2.aa2aa0E+2", "2.aa5aa0E+2", "2.aa8aa0E+2", "1.aa2aa3E+3", "-1.aa0aa0E+0", "-1.aa1aa0E+1", "-1.aa5aa0E+1", "-1.aa9aa0E+1", "-2.aa0aa0E+2", "-2.aa2aa0E+2", "-2.aa5aa0E+2", "-2.aa8aa0E+2", "-9.aa8aa8E+3", "1.aa0aa0E-1", "1.aa2aa0E-1", "1.aa5aa1E-1", "1.aa8aa1E-1", "5.aa0aa0E-1", "5.aa3aa0E-1", "5.aa5aa5E-1", "5.aa7aa5E-1", "9.aa0aa0E-1", "9.aa4aa0E-1", "9.aa5aa9E-1", "9.aa6aa9E-1", "-1.aa0aa0E-1", "-1.aa2aa0E-1", "-1.aa5aa1E-1", "-1.aa8aa1E-1", "-5.aa0aa0E-1", "-5.aa3aa0E-1", "-5.aa5aa5E-1", "-5.aa7aa5E-1", "-9.aa0aa0E-1", "-9.aa4aa0E-1", "-9.aa5aa9E-1", "-9.aa6aa9E-1", "3.aa4aa0E+0", "4.aa5aa6E+0", "4.aa3aa2E+0", "4.aa5aa7E+0", "4.aa3aa2E+0", "-3.aa4aa0E+0", "-4.aa5aa6E+0", "-4.aa3aa2E+0", "-4.aa5aa7E+0", "-4.aa3aa2E+0"]);
		check_numeric_format("0.0E+0\\a\\a", numbers,
				["0.0E+0aa", "1.0E+0aa", "1.1E+1aa", "1.5E+1aa", "1.9E+1aa", "2.0E+2aa", "2.2E+2aa", "2.5E+2aa", "2.8E+2aa", "1.2E+3aa", "-1.0E+0aa", "-1.1E+1aa", "-1.5E+1aa", "-1.9E+1aa", "-2.0E+2aa", "-2.2E+2aa", "-2.5E+2aa", "-2.8E+2aa", "-9.9E+3aa", "1.0E-1aa", "1.2E-1aa", "1.5E-1aa", "1.8E-1aa", "5.0E-1aa", "5.3E-1aa", "5.6E-1aa", "5.8E-1aa", "9.0E-1aa", "9.4E-1aa", "9.6E-1aa", "9.7E-1aa", "-1.0E-1aa", "-1.2E-1aa", "-1.5E-1aa", "-1.8E-1aa", "-5.0E-1aa", "-5.3E-1aa", "-5.6E-1aa", "-5.8E-1aa", "-9.0E-1aa", "-9.4E-1aa", "-9.6E-1aa", "-9.7E-1aa", "3.4E+0aa", "4.6E+0aa", "4.3E+0aa", "4.6E+0aa", "4.3E+0aa", "-3.4E+0aa", "-4.6E+0aa", "-4.3E+0aa", "-4.6E+0aa", "-4.3E+0aa"]);
		check_numeric_format("0.0\\E\\aa+0", numbers,
				["0.0Eaa+0", "1.0Eaa+0", "1.1Eaa+1", "1.5Eaa+1", "1.9Eaa+1", "2.0Eaa+2", "2.2Eaa+2", "2.5Eaa+2", "2.8Eaa+2", "1.2Eaa+3", "-1.0Eaa+0", "-1.1Eaa+1", "-1.5Eaa+1", "-1.9Eaa+1", "-2.0Eaa+2", "-2.2Eaa+2", "-2.5Eaa+2", "-2.8Eaa+2", "-9.9Eaa+3", "1.0Eaa-1", "1.2Eaa-1", "1.5Eaa-1", "1.8Eaa-1", "5.0Eaa-1", "5.3Eaa-1", "5.6Eaa-1", "5.8Eaa-1", "9.0Eaa-1", "9.4Eaa-1", "9.6Eaa-1", "9.7Eaa-1", "-1.0Eaa-1", "-1.2Eaa-1", "-1.5Eaa-1", "-1.8Eaa-1", "-5.0Eaa-1", "-5.3Eaa-1", "-5.6Eaa-1", "-5.8Eaa-1", "-9.0Eaa-1", "-9.4Eaa-1", "-9.6Eaa-1", "-9.7Eaa-1", "3.4Eaa+0", "4.6Eaa+0", "4.3Eaa+0", "4.6Eaa+0", "4.3Eaa+0", "-3.4Eaa+0", "-4.6Eaa+0", "-4.3Eaa+0", "-4.6Eaa+0", "-4.3Eaa+0"]);
		check_numeric_format("0.0\\E\\aa+0\\a\\a", numbers,
				["0.0Eaa+0aa", "1.0Eaa+0aa", "1.1Eaa+1aa", "1.5Eaa+1aa", "1.9Eaa+1aa", "2.0Eaa+2aa", "2.2Eaa+2aa", "2.5Eaa+2aa", "2.8Eaa+2aa", "1.2Eaa+3aa", "-1.0Eaa+0aa", "-1.1Eaa+1aa", "-1.5Eaa+1aa", "-1.9Eaa+1aa", "-2.0Eaa+2aa", "-2.2Eaa+2aa", "-2.5Eaa+2aa", "-2.8Eaa+2aa", "-9.9Eaa+3aa", "1.0Eaa-1aa", "1.2Eaa-1aa", "1.5Eaa-1aa", "1.8Eaa-1aa", "5.0Eaa-1aa", "5.3Eaa-1aa", "5.6Eaa-1aa", "5.8Eaa-1aa", "9.0Eaa-1aa", "9.4Eaa-1aa", "9.6Eaa-1aa", "9.7Eaa-1aa", "-1.0Eaa-1aa", "-1.2Eaa-1aa", "-1.5Eaa-1aa", "-1.8Eaa-1aa", "-5.0Eaa-1aa", "-5.3Eaa-1aa", "-5.6Eaa-1aa", "-5.8Eaa-1aa", "-9.0Eaa-1aa", "-9.4Eaa-1aa", "-9.6Eaa-1aa", "-9.7Eaa-1aa", "3.4Eaa+0aa", "4.6Eaa+0aa", "4.3Eaa+0aa", "4.6Eaa+0aa", "4.3Eaa+0aa", "-3.4Eaa+0aa", "-4.6Eaa+0aa", "-4.3Eaa+0aa", "-4.6Eaa+0aa", "-4.3Eaa+0aa"]);
		check_numeric_format("0\"%\"0.0E+0", numbers,
				["0%0.0E+0", "0%1.0E+0", "1%1.0E+0", "1%5.0E+0", "1%9.0E+0", "0%2.0E+2", "0%2.2E+2", "0%2.5E+2", "0%2.8E+2", "1%2.3E+2", "-0%1.0E+0", "-1%1.0E+0", "-1%5.0E+0", "-1%9.0E+0", "-0%2.0E+2", "-0%2.2E+2", "-0%2.5E+2", "-0%2.8E+2", "-9%8.8E+2", "1%0.0E-2", "1%2.0E-2", "1%5.1E-2", "1%8.1E-2", "5%0.0E-2", "5%3.0E-2", "5%5.5E-2", "5%7.5E-2", "9%0.0E-2", "9%4.0E-2", "9%5.9E-2", "9%6.9E-2", "-1%0.0E-2", "-1%2.0E-2", "-1%5.1E-2", "-1%8.1E-2", "-5%0.0E-2", "-5%3.0E-2", "-5%5.5E-2", "-5%7.5E-2", "-9%0.0E-2", "-9%4.0E-2", "-9%5.9E-2", "-9%6.9E-2", "0%3.4E+0", "0%4.6E+0", "0%4.3E+0", "0%4.6E+0", "0%4.3E+0", "-0%3.4E+0", "-0%4.6E+0", "-0%4.3E+0", "-0%4.6E+0", "-0%4.3E+0"]);
		check_numeric_format("\"%\"00.0E+0", numbers,
				["%00.0E+0", "%01.0E+0", "%11.0E+0", "%15.0E+0", "%19.0E+0", "%02.0E+2", "%02.2E+2", "%02.5E+2", "%02.8E+2", "%12.3E+2", "-%01.0E+0", "-%11.0E+0", "-%15.0E+0", "-%19.0E+0", "-%02.0E+2", "-%02.2E+2", "-%02.5E+2", "-%02.8E+2", "-%98.8E+2", "%10.0E-2", "%12.0E-2", "%15.1E-2", "%18.1E-2", "%50.0E-2", "%53.0E-2", "%55.5E-2", "%57.5E-2", "%90.0E-2", "%94.0E-2", "%95.9E-2", "%96.9E-2", "-%10.0E-2", "-%12.0E-2", "-%15.1E-2", "-%18.1E-2", "-%50.0E-2", "-%53.0E-2", "-%55.5E-2", "-%57.5E-2", "-%90.0E-2", "-%94.0E-2", "-%95.9E-2", "-%96.9E-2", "%03.4E+0", "%04.6E+0", "%04.3E+0", "%04.6E+0", "%04.3E+0", "-%03.4E+0", "-%04.6E+0", "-%04.3E+0", "-%04.6E+0", "-%04.3E+0"]);
		check_numeric_format("\"%\"0\"%\"0.0E+0", numbers,
				["%0%0.0E+0", "%0%1.0E+0", "%1%1.0E+0", "%1%5.0E+0", "%1%9.0E+0", "%0%2.0E+2", "%0%2.2E+2", "%0%2.5E+2", "%0%2.8E+2", "%1%2.3E+2", "-%0%1.0E+0", "-%1%1.0E+0", "-%1%5.0E+0", "-%1%9.0E+0", "-%0%2.0E+2", "-%0%2.2E+2", "-%0%2.5E+2", "-%0%2.8E+2", "-%9%8.8E+2", "%1%0.0E-2", "%1%2.0E-2", "%1%5.1E-2", "%1%8.1E-2", "%5%0.0E-2", "%5%3.0E-2", "%5%5.5E-2", "%5%7.5E-2", "%9%0.0E-2", "%9%4.0E-2", "%9%5.9E-2", "%9%6.9E-2", "-%1%0.0E-2", "-%1%2.0E-2", "-%1%5.1E-2", "-%1%8.1E-2", "-%5%0.0E-2", "-%5%3.0E-2", "-%5%5.5E-2", "-%5%7.5E-2", "-%9%0.0E-2", "-%9%4.0E-2", "-%9%5.9E-2", "-%9%6.9E-2", "%0%3.4E+0", "%0%4.6E+0", "%0%4.3E+0", "%0%4.6E+0", "%0%4.3E+0", "-%0%3.4E+0", "-%0%4.6E+0", "-%0%4.3E+0", "-%0%4.6E+0", "-%0%4.3E+0"]);
		check_numeric_format("0.0\"%\"0E+0", numbers,
				["0.0%0E+0", "1.0%0E+0", "1.1%0E+1", "1.5%0E+1", "1.9%0E+1", "2.0%0E+2", "2.2%0E+2", "2.5%0E+2", "2.8%0E+2", "1.2%3E+3", "-1.0%0E+0", "-1.1%0E+1", "-1.5%0E+1", "-1.9%0E+1", "-2.0%0E+2", "-2.2%0E+2", "-2.5%0E+2", "-2.8%0E+2", "-9.8%8E+3", "1.0%0E-1", "1.2%0E-1", "1.5%1E-1", "1.8%1E-1", "5.0%0E-1", "5.3%0E-1", "5.5%5E-1", "5.7%5E-1", "9.0%0E-1", "9.4%0E-1", "9.5%9E-1", "9.6%9E-1", "-1.0%0E-1", "-1.2%0E-1", "-1.5%1E-1", "-1.8%1E-1", "-5.0%0E-1", "-5.3%0E-1", "-5.5%5E-1", "-5.7%5E-1", "-9.0%0E-1", "-9.4%0E-1", "-9.5%9E-1", "-9.6%9E-1", "3.4%0E+0", "4.5%6E+0", "4.3%2E+0", "4.5%7E+0", "4.3%2E+0", "-3.4%0E+0", "-4.5%6E+0", "-4.3%2E+0", "-4.5%7E+0", "-4.3%2E+0"]);
		check_numeric_format("0.\"%\"00E+0", numbers,
				["0.%00E+0", "1.%00E+0", "1.%10E+1", "1.%50E+1", "1.%90E+1", "2.%00E+2", "2.%20E+2", "2.%50E+2", "2.%80E+2", "1.%23E+3", "-1.%00E+0", "-1.%10E+1", "-1.%50E+1", "-1.%90E+1", "-2.%00E+2", "-2.%20E+2", "-2.%50E+2", "-2.%80E+2", "-9.%88E+3", "1.%00E-1", "1.%20E-1", "1.%51E-1", "1.%81E-1", "5.%00E-1", "5.%30E-1", "5.%55E-1", "5.%75E-1", "9.%00E-1", "9.%40E-1", "9.%59E-1", "9.%69E-1", "-1.%00E-1", "-1.%20E-1", "-1.%51E-1", "-1.%81E-1", "-5.%00E-1", "-5.%30E-1", "-5.%55E-1", "-5.%75E-1", "-9.%00E-1", "-9.%40E-1", "-9.%59E-1", "-9.%69E-1", "3.%40E+0", "4.%56E+0", "4.%32E+0", "4.%57E+0", "4.%32E+0", "-3.%40E+0", "-4.%56E+0", "-4.%32E+0", "-4.%57E+0", "-4.%32E+0"]);
		check_numeric_format("0.\"%\"0\"%\"0E+0", numbers,
				["0.%0%0E+0", "1.%0%0E+0", "1.%1%0E+1", "1.%5%0E+1", "1.%9%0E+1", "2.%0%0E+2", "2.%2%0E+2", "2.%5%0E+2", "2.%8%0E+2", "1.%2%3E+3", "-1.%0%0E+0", "-1.%1%0E+1", "-1.%5%0E+1", "-1.%9%0E+1", "-2.%0%0E+2", "-2.%2%0E+2", "-2.%5%0E+2", "-2.%8%0E+2", "-9.%8%8E+3", "1.%0%0E-1", "1.%2%0E-1", "1.%5%1E-1", "1.%8%1E-1", "5.%0%0E-1", "5.%3%0E-1", "5.%5%5E-1", "5.%7%5E-1", "9.%0%0E-1", "9.%4%0E-1", "9.%5%9E-1", "9.%6%9E-1", "-1.%0%0E-1", "-1.%2%0E-1", "-1.%5%1E-1", "-1.%8%1E-1", "-5.%0%0E-1", "-5.%3%0E-1", "-5.%5%5E-1", "-5.%7%5E-1", "-9.%0%0E-1", "-9.%4%0E-1", "-9.%5%9E-1", "-9.%6%9E-1", "3.%4%0E+0", "4.%5%6E+0", "4.%3%2E+0", "4.%5%7E+0", "4.%3%2E+0", "-3.%4%0E+0", "-4.%5%6E+0", "-4.%3%2E+0", "-4.%5%7E+0", "-4.%3%2E+0"]);
		check_numeric_format("0.0E+0\"%\"", numbers,
				["0.0E+0%", "1.0E+0%", "1.1E+1%", "1.5E+1%", "1.9E+1%", "2.0E+2%", "2.2E+2%", "2.5E+2%", "2.8E+2%", "1.2E+3%", "-1.0E+0%", "-1.1E+1%", "-1.5E+1%", "-1.9E+1%", "-2.0E+2%", "-2.2E+2%", "-2.5E+2%", "-2.8E+2%", "-9.9E+3%", "1.0E-1%", "1.2E-1%", "1.5E-1%", "1.8E-1%", "5.0E-1%", "5.3E-1%", "5.6E-1%", "5.8E-1%", "9.0E-1%", "9.4E-1%", "9.6E-1%", "9.7E-1%", "-1.0E-1%", "-1.2E-1%", "-1.5E-1%", "-1.8E-1%", "-5.0E-1%", "-5.3E-1%", "-5.6E-1%", "-5.8E-1%", "-9.0E-1%", "-9.4E-1%", "-9.6E-1%", "-9.7E-1%", "3.4E+0%", "4.6E+0%", "4.3E+0%", "4.6E+0%", "4.3E+0%", "-3.4E+0%", "-4.6E+0%", "-4.3E+0%", "-4.6E+0%", "-4.3E+0%"]);
		check_numeric_format("0.0\\E\"%\"+0", numbers,
				["0.0E%+0", "1.0E%+0", "1.1E%+1", "1.5E%+1", "1.9E%+1", "2.0E%+2", "2.2E%+2", "2.5E%+2", "2.8E%+2", "1.2E%+3", "-1.0E%+0", "-1.1E%+1", "-1.5E%+1", "-1.9E%+1", "-2.0E%+2", "-2.2E%+2", "-2.5E%+2", "-2.8E%+2", "-9.9E%+3", "1.0E%-1", "1.2E%-1", "1.5E%-1", "1.8E%-1", "5.0E%-1", "5.3E%-1", "5.6E%-1", "5.8E%-1", "9.0E%-1", "9.4E%-1", "9.6E%-1", "9.7E%-1", "-1.0E%-1", "-1.2E%-1", "-1.5E%-1", "-1.8E%-1", "-5.0E%-1", "-5.3E%-1", "-5.6E%-1", "-5.8E%-1", "-9.0E%-1", "-9.4E%-1", "-9.6E%-1", "-9.7E%-1", "3.4E%+0", "4.6E%+0", "4.3E%+0", "4.6E%+0", "4.3E%+0", "-3.4E%+0", "-4.6E%+0", "-4.3E%+0", "-4.6E%+0", "-4.3E%+0"]);
		check_numeric_format("0.0\\E\"%\"+0\"%\"", numbers,
				["0.0E%+0%", "1.0E%+0%", "1.1E%+1%", "1.5E%+1%", "1.9E%+1%", "2.0E%+2%", "2.2E%+2%", "2.5E%+2%", "2.8E%+2%", "1.2E%+3%", "-1.0E%+0%", "-1.1E%+1%", "-1.5E%+1%", "-1.9E%+1%", "-2.0E%+2%", "-2.2E%+2%", "-2.5E%+2%", "-2.8E%+2%", "-9.9E%+3%", "1.0E%-1%", "1.2E%-1%", "1.5E%-1%", "1.8E%-1%", "5.0E%-1%", "5.3E%-1%", "5.6E%-1%", "5.8E%-1%", "9.0E%-1%", "9.4E%-1%", "9.6E%-1%", "9.7E%-1%", "-1.0E%-1%", "-1.2E%-1%", "-1.5E%-1%", "-1.8E%-1%", "-5.0E%-1%", "-5.3E%-1%", "-5.6E%-1%", "-5.8E%-1%", "-9.0E%-1%", "-9.4E%-1%", "-9.6E%-1%", "-9.7E%-1%", "3.4E%+0%", "4.6E%+0%", "4.3E%+0%", "4.6E%+0%", "4.3E%+0%", "-3.4E%+0%", "-4.6E%+0%", "-4.3E%+0%", "-4.6E%+0%", "-4.3E%+0%"]);
	});

	test("format4", function test_format4() {
		var numbers = [0, 1, 11, 15, 19, 200, 220, 250, 280, 1234, -1, -11, -15, -19, -200, -220, -250, -280, -9876, 0.1, 0.12, 0.151, 0.181, 0.5, 0.53, 0.555, 0.575, 0.9, 0.94, 0.959, 0.969, -0.1, -0.12, -0.151, -0.181, -0.5, -0.53, -0.555, -0.575, -0.9, -0.94, -0.959, -0.969, 3.4, 4.56, 4.32, 4.567, 4.321, -3.4, -4.56, -4.32, -4.567, -4.321];

		check_numeric_format("0\\.", numbers,
				["0.", "1.", "11.", "15.", "19.", "200.", "220.", "250.", "280.", "1234.", "-1.", "-11.", "-15.", "-19.", "-200.", "-220.", "-250.", "-280.", "-9876.", "0.", "0.", "0.", "0.", "1.", "1.", "1.", "1.", "1.", "1.", "1.", "1.", "0.", "0.", "0.", "0.", "-1.", "-1.", "-1.", "-1.", "-1.", "-1.", "-1.", "-1.", "3.", "5.", "4.", "5.", "4.", "-3.", "-5.", "-4.", "-5.", "-4."]);
		check_numeric_format("\\.0", numbers,
				[".0", ".1", ".11", ".15", ".19", ".200", ".220", ".250", ".280", ".1234", "-.1", "-.11", "-.15", "-.19", "-.200", "-.220", "-.250", "-.280", "-.9876", ".0", ".0", ".0", ".0", ".1", ".1", ".1", ".1", ".1", ".1", ".1", ".1", ".0", ".0", ".0", ".0", "-.1", "-.1", "-.1", "-.1", "-.1", "-.1", "-.1", "-.1", ".3", ".5", ".4", ".5", ".4", "-.3", "-.5", "-.4", "-.5", "-.4"]);
		check_numeric_format("\\.0\\.", numbers,
				[".0.", ".1.", ".11.", ".15.", ".19.", ".200.", ".220.", ".250.", ".280.", ".1234.", "-.1.", "-.11.", "-.15.", "-.19.", "-.200.", "-.220.", "-.250.", "-.280.", "-.9876.", ".0.", ".0.", ".0.", ".0.", ".1.", ".1.", ".1.", ".1.", ".1.", ".1.", ".1.", ".1.", ".0.", ".0.", ".0.", ".0.", "-.1.", "-.1.", "-.1.", "-.1.", "-.1.", "-.1.", "-.1.", "-.1.", ".3.", ".5.", ".4.", ".5.", ".4.", "-.3.", "-.5.", "-.4.", "-.5.", "-.4."]);
		check_numeric_format("0\\E", numbers,
				["0E", "1E", "11E", "15E", "19E", "200E", "220E", "250E", "280E", "1234E", "-1E", "-11E", "-15E", "-19E", "-200E", "-220E", "-250E", "-280E", "-9876E", "0E", "0E", "0E", "0E", "1E", "1E", "1E", "1E", "1E", "1E", "1E", "1E", "0E", "0E", "0E", "0E", "-1E", "-1E", "-1E", "-1E", "-1E", "-1E", "-1E", "-1E", "3E", "5E", "4E", "5E", "4E", "-3E", "-5E", "-4E", "-5E", "-4E"]);
		check_numeric_format("\\E0", numbers,
				["E0", "E1", "E11", "E15", "E19", "E200", "E220", "E250", "E280", "E1234", "-E1", "-E11", "-E15", "-E19", "-E200", "-E220", "-E250", "-E280", "-E9876", "E0", "E0", "E0", "E0", "E1", "E1", "E1", "E1", "E1", "E1", "E1", "E1", "E0", "E0", "E0", "E0", "-E1", "-E1", "-E1", "-E1", "-E1", "-E1", "-E1", "-E1", "E3", "E5", "E4", "E5", "E4", "-E3", "-E5", "-E4", "-E5", "-E4"]);
		check_numeric_format("\\E0\\E", numbers,
				["E0E", "E1E", "E11E", "E15E", "E19E", "E200E", "E220E", "E250E", "E280E", "E1234E", "-E1E", "-E11E", "-E15E", "-E19E", "-E200E", "-E220E", "-E250E", "-E280E", "-E9876E", "E0E", "E0E", "E0E", "E0E", "E1E", "E1E", "E1E", "E1E", "E1E", "E1E", "E1E", "E1E", "E0E", "E0E", "E0E", "E0E", "-E1E", "-E1E", "-E1E", "-E1E", "-E1E", "-E1E", "-E1E", "-E1E", "E3E", "E5E", "E4E", "E5E", "E4E", "-E3E", "-E5E", "-E4E", "-E5E", "-E4E"]);
		check_numeric_format("0\"E+\"", numbers,
				["0E+", "1E+", "11E+", "15E+", "19E+", "200E+", "220E+", "250E+", "280E+", "1234E+", "-1E+", "-11E+", "-15E+", "-19E+", "-200E+", "-220E+", "-250E+", "-280E+", "-9876E+", "0E+", "0E+", "0E+", "0E+", "1E+", "1E+", "1E+", "1E+", "1E+", "1E+", "1E+", "1E+", "0E+", "0E+", "0E+", "0E+", "-1E+", "-1E+", "-1E+", "-1E+", "-1E+", "-1E+", "-1E+", "-1E+", "3E+", "5E+", "4E+", "5E+", "4E+", "-3E+", "-5E+", "-4E+", "-5E+", "-4E+"]);
		check_numeric_format("\"E+\"0", numbers,
				["E+0", "E+1", "E+11", "E+15", "E+19", "E+200", "E+220", "E+250", "E+280", "E+1234", "-E+1", "-E+11", "-E+15", "-E+19", "-E+200", "-E+220", "-E+250", "-E+280", "-E+9876", "E+0", "E+0", "E+0", "E+0", "E+1", "E+1", "E+1", "E+1", "E+1", "E+1", "E+1", "E+1", "E+0", "E+0", "E+0", "E+0", "-E+1", "-E+1", "-E+1", "-E+1", "-E+1", "-E+1", "-E+1", "-E+1", "E+3", "E+5", "E+4", "E+5", "E+4", "-E+3", "-E+5", "-E+4", "-E+5", "-E+4"]);
		check_numeric_format("\"E+\"0\"E+\"", numbers,
				["E+0E+", "E+1E+", "E+11E+", "E+15E+", "E+19E+", "E+200E+", "E+220E+", "E+250E+", "E+280E+", "E+1234E+", "-E+1E+", "-E+11E+", "-E+15E+", "-E+19E+", "-E+200E+", "-E+220E+", "-E+250E+", "-E+280E+", "-E+9876E+", "E+0E+", "E+0E+", "E+0E+", "E+0E+", "E+1E+", "E+1E+", "E+1E+", "E+1E+", "E+1E+", "E+1E+", "E+1E+", "E+1E+", "E+0E+", "E+0E+", "E+0E+", "E+0E+", "-E+1E+", "-E+1E+", "-E+1E+", "-E+1E+", "-E+1E+", "-E+1E+", "-E+1E+", "-E+1E+", "E+3E+", "E+5E+", "E+4E+", "E+5E+", "E+4E+", "-E+3E+", "-E+5E+", "-E+4E+", "-E+5E+", "-E+4E+"]);
		check_numeric_format("0\\.0.", numbers,
				["0.0.", "0.1.", "1.1.", "1.5.", "1.9.", "20.0.", "22.0.", "25.0.", "28.0.", "123.4.", "-0.1.", "-1.1.", "-1.5.", "-1.9.", "-20.0.", "-22.0.", "-25.0.", "-28.0.", "-987.6.", "0.0.", "0.0.", "0.0.", "0.0.", "0.1.", "0.1.", "0.1.", "0.1.", "0.1.", "0.1.", "0.1.", "0.1.", "0.0.", "0.0.", "0.0.", "0.0.", "-0.1.", "-0.1.", "-0.1.", "-0.1.", "-0.1.", "-0.1.", "-0.1.", "-0.1.", "0.3.", "0.5.", "0.4.", "0.5.", "0.4.", "-0.3.", "-0.5.", "-0.4.", "-0.5.", "-0.4."]);
		check_numeric_format("\\.00.", numbers,
				[".00.", ".01.", ".11.", ".15.", ".19.", ".200.", ".220.", ".250.", ".280.", ".1234.", "-.01.", "-.11.", "-.15.", "-.19.", "-.200.", "-.220.", "-.250.", "-.280.", "-.9876.", ".00.", ".00.", ".00.", ".00.", ".01.", ".01.", ".01.", ".01.", ".01.", ".01.", ".01.", ".01.", ".00.", ".00.", ".00.", ".00.", "-.01.", "-.01.", "-.01.", "-.01.", "-.01.", "-.01.", "-.01.", "-.01.", ".03.", ".05.", ".04.", ".05.", ".04.", "-.03.", "-.05.", "-.04.", "-.05.", "-.04."]);
		check_numeric_format("\\.0\\.0.", numbers,
				[".0.0.", ".0.1.", ".1.1.", ".1.5.", ".1.9.", ".20.0.", ".22.0.", ".25.0.", ".28.0.", ".123.4.", "-.0.1.", "-.1.1.", "-.1.5.", "-.1.9.", "-.20.0.", "-.22.0.", "-.25.0.", "-.28.0.", "-.987.6.", ".0.0.", ".0.0.", ".0.0.", ".0.0.", ".0.1.", ".0.1.", ".0.1.", ".0.1.", ".0.1.", ".0.1.", ".0.1.", ".0.1.", ".0.0.", ".0.0.", ".0.0.", ".0.0.", "-.0.1.", "-.0.1.", "-.0.1.", "-.0.1.", "-.0.1.", "-.0.1.", "-.0.1.", "-.0.1.", ".0.3.", ".0.5.", ".0.4.", ".0.5.", ".0.4.", "-.0.3.", "-.0.5.", "-.0.4.", "-.0.5.", "-.0.4."]);
		check_numeric_format("0\\E0.", numbers,
				["0E0.", "0E1.", "1E1.", "1E5.", "1E9.", "20E0.", "22E0.", "25E0.", "28E0.", "123E4.", "-0E1.", "-1E1.", "-1E5.", "-1E9.", "-20E0.", "-22E0.", "-25E0.", "-28E0.", "-987E6.", "0E0.", "0E0.", "0E0.", "0E0.", "0E1.", "0E1.", "0E1.", "0E1.", "0E1.", "0E1.", "0E1.", "0E1.", "0E0.", "0E0.", "0E0.", "0E0.", "-0E1.", "-0E1.", "-0E1.", "-0E1.", "-0E1.", "-0E1.", "-0E1.", "-0E1.", "0E3.", "0E5.", "0E4.", "0E5.", "0E4.", "-0E3.", "-0E5.", "-0E4.", "-0E5.", "-0E4."]);
		check_numeric_format("\\E00.", numbers,
				["E00.", "E01.", "E11.", "E15.", "E19.", "E200.", "E220.", "E250.", "E280.", "E1234.", "-E01.", "-E11.", "-E15.", "-E19.", "-E200.", "-E220.", "-E250.", "-E280.", "-E9876.", "E00.", "E00.", "E00.", "E00.", "E01.", "E01.", "E01.", "E01.", "E01.", "E01.", "E01.", "E01.", "E00.", "E00.", "E00.", "E00.", "-E01.", "-E01.", "-E01.", "-E01.", "-E01.", "-E01.", "-E01.", "-E01.", "E03.", "E05.", "E04.", "E05.", "E04.", "-E03.", "-E05.", "-E04.", "-E05.", "-E04."]);
		check_numeric_format("\\E0\\E0.", numbers,
				["E0E0.", "E0E1.", "E1E1.", "E1E5.", "E1E9.", "E20E0.", "E22E0.", "E25E0.", "E28E0.", "E123E4.", "-E0E1.", "-E1E1.", "-E1E5.", "-E1E9.", "-E20E0.", "-E22E0.", "-E25E0.", "-E28E0.", "-E987E6.", "E0E0.", "E0E0.", "E0E0.", "E0E0.", "E0E1.", "E0E1.", "E0E1.", "E0E1.", "E0E1.", "E0E1.", "E0E1.", "E0E1.", "E0E0.", "E0E0.", "E0E0.", "E0E0.", "-E0E1.", "-E0E1.", "-E0E1.", "-E0E1.", "-E0E1.", "-E0E1.", "-E0E1.", "-E0E1.", "E0E3.", "E0E5.", "E0E4.", "E0E5.", "E0E4.", "-E0E3.", "-E0E5.", "-E0E4.", "-E0E5.", "-E0E4."]);
		check_numeric_format("0\"E+\"0.", numbers,
				["0E+0.", "0E+1.", "1E+1.", "1E+5.", "1E+9.", "20E+0.", "22E+0.", "25E+0.", "28E+0.", "123E+4.", "-0E+1.", "-1E+1.", "-1E+5.", "-1E+9.", "-20E+0.", "-22E+0.", "-25E+0.", "-28E+0.", "-987E+6.", "0E+0.", "0E+0.", "0E+0.", "0E+0.", "0E+1.", "0E+1.", "0E+1.", "0E+1.", "0E+1.", "0E+1.", "0E+1.", "0E+1.", "0E+0.", "0E+0.", "0E+0.", "0E+0.", "-0E+1.", "-0E+1.", "-0E+1.", "-0E+1.", "-0E+1.", "-0E+1.", "-0E+1.", "-0E+1.", "0E+3.", "0E+5.", "0E+4.", "0E+5.", "0E+4.", "-0E+3.", "-0E+5.", "-0E+4.", "-0E+5.", "-0E+4."]);
		check_numeric_format("\"E+\"00.", numbers,
				["E+00.", "E+01.", "E+11.", "E+15.", "E+19.", "E+200.", "E+220.", "E+250.", "E+280.", "E+1234.", "-E+01.", "-E+11.", "-E+15.", "-E+19.", "-E+200.", "-E+220.", "-E+250.", "-E+280.", "-E+9876.", "E+00.", "E+00.", "E+00.", "E+00.", "E+01.", "E+01.", "E+01.", "E+01.", "E+01.", "E+01.", "E+01.", "E+01.", "E+00.", "E+00.", "E+00.", "E+00.", "-E+01.", "-E+01.", "-E+01.", "-E+01.", "-E+01.", "-E+01.", "-E+01.", "-E+01.", "E+03.", "E+05.", "E+04.", "E+05.", "E+04.", "-E+03.", "-E+05.", "-E+04.", "-E+05.", "-E+04."]);
		check_numeric_format("\"E+\"0\"E+\"0.", numbers,
				["E+0E+0.", "E+0E+1.", "E+1E+1.", "E+1E+5.", "E+1E+9.", "E+20E+0.", "E+22E+0.", "E+25E+0.", "E+28E+0.", "E+123E+4.", "-E+0E+1.", "-E+1E+1.", "-E+1E+5.", "-E+1E+9.", "-E+20E+0.", "-E+22E+0.", "-E+25E+0.", "-E+28E+0.", "-E+987E+6.", "E+0E+0.", "E+0E+0.", "E+0E+0.", "E+0E+0.", "E+0E+1.", "E+0E+1.", "E+0E+1.", "E+0E+1.", "E+0E+1.", "E+0E+1.", "E+0E+1.", "E+0E+1.", "E+0E+0.", "E+0E+0.", "E+0E+0.", "E+0E+0.", "-E+0E+1.", "-E+0E+1.", "-E+0E+1.", "-E+0E+1.", "-E+0E+1.", "-E+0E+1.", "-E+0E+1.", "-E+0E+1.", "E+0E+3.", "E+0E+5.", "E+0E+4.", "E+0E+5.", "E+0E+4.", "-E+0E+3.", "-E+0E+5.", "-E+0E+4.", "-E+0E+5.", "-E+0E+4."]);
		check_numeric_format(".0\\.", numbers,
				[".0.", "1.0.", "11.0.", "15.0.", "19.0.", "200.0.", "220.0.", "250.0.", "280.0.", "1234.0.", "-1.0.", "-11.0.", "-15.0.", "-19.0.", "-200.0.", "-220.0.", "-250.0.", "-280.0.", "-9876.0.", ".1.", ".1.", ".2.", ".2.", ".5.", ".5.", ".6.", ".6.", ".9.", ".9.", "1.0.", "1.0.", "-.1.", "-.1.", "-.2.", "-.2.", "-.5.", "-.5.", "-.6.", "-.6.", "-.9.", "-.9.", "-1.0.", "-1.0.", "3.4.", "4.6.", "4.3.", "4.6.", "4.3.", "-3.4.", "-4.6.", "-4.3.", "-4.6.", "-4.3."]);
		check_numeric_format(".\\.0", numbers,
				["..0", "1..0", "11..0", "15..0", "19..0", "200..0", "220..0", "250..0", "280..0", "1234..0", "-1..0", "-11..0", "-15..0", "-19..0", "-200..0", "-220..0", "-250..0", "-280..0", "-9876..0", "..1", "..1", "..2", "..2", "..5", "..5", "..6", "..6", "..9", "..9", "1..0", "1..0", "-..1", "-..1", "-..2", "-..2", "-..5", "-..5", "-..6", "-..6", "-..9", "-..9", "-1..0", "-1..0", "3..4", "4..6", "4..3", "4..6", "4..3", "-3..4", "-4..6", "-4..3", "-4..6", "-4..3"]);
		check_numeric_format(".\\.0\\.", numbers,
				["..0.", "1..0.", "11..0.", "15..0.", "19..0.", "200..0.", "220..0.", "250..0.", "280..0.", "1234..0.", "-1..0.", "-11..0.", "-15..0.", "-19..0.", "-200..0.", "-220..0.", "-250..0.", "-280..0.", "-9876..0.", "..1.", "..1.", "..2.", "..2.", "..5.", "..5.", "..6.", "..6.", "..9.", "..9.", "1..0.", "1..0.", "-..1.", "-..1.", "-..2.", "-..2.", "-..5.", "-..5.", "-..6.", "-..6.", "-..9.", "-..9.", "-1..0.", "-1..0.", "3..4.", "4..6.", "4..3.", "4..6.", "4..3.", "-3..4.", "-4..6.", "-4..3.", "-4..6.", "-4..3."]);
		check_numeric_format(".0\\E", numbers,
				[".0E", "1.0E", "11.0E", "15.0E", "19.0E", "200.0E", "220.0E", "250.0E", "280.0E", "1234.0E", "-1.0E", "-11.0E", "-15.0E", "-19.0E", "-200.0E", "-220.0E", "-250.0E", "-280.0E", "-9876.0E", ".1E", ".1E", ".2E", ".2E", ".5E", ".5E", ".6E", ".6E", ".9E", ".9E", "1.0E", "1.0E", "-.1E", "-.1E", "-.2E", "-.2E", "-.5E", "-.5E", "-.6E", "-.6E", "-.9E", "-.9E", "-1.0E", "-1.0E", "3.4E", "4.6E", "4.3E", "4.6E", "4.3E", "-3.4E", "-4.6E", "-4.3E", "-4.6E", "-4.3E"]);
		check_numeric_format(".\\E0", numbers,
				[".E0", "1.E0", "11.E0", "15.E0", "19.E0", "200.E0", "220.E0", "250.E0", "280.E0", "1234.E0", "-1.E0", "-11.E0", "-15.E0", "-19.E0", "-200.E0", "-220.E0", "-250.E0", "-280.E0", "-9876.E0", ".E1", ".E1", ".E2", ".E2", ".E5", ".E5", ".E6", ".E6", ".E9", ".E9", "1.E0", "1.E0", "-.E1", "-.E1", "-.E2", "-.E2", "-.E5", "-.E5", "-.E6", "-.E6", "-.E9", "-.E9", "-1.E0", "-1.E0", "3.E4", "4.E6", "4.E3", "4.E6", "4.E3", "-3.E4", "-4.E6", "-4.E3", "-4.E6", "-4.E3"]);
		check_numeric_format(".\\E0\\E", numbers,
				[".E0E", "1.E0E", "11.E0E", "15.E0E", "19.E0E", "200.E0E", "220.E0E", "250.E0E", "280.E0E", "1234.E0E", "-1.E0E", "-11.E0E", "-15.E0E", "-19.E0E", "-200.E0E", "-220.E0E", "-250.E0E", "-280.E0E", "-9876.E0E", ".E1E", ".E1E", ".E2E", ".E2E", ".E5E", ".E5E", ".E6E", ".E6E", ".E9E", ".E9E", "1.E0E", "1.E0E", "-.E1E", "-.E1E", "-.E2E", "-.E2E", "-.E5E", "-.E5E", "-.E6E", "-.E6E", "-.E9E", "-.E9E", "-1.E0E", "-1.E0E", "3.E4E", "4.E6E", "4.E3E", "4.E6E", "4.E3E", "-3.E4E", "-4.E6E", "-4.E3E", "-4.E6E", "-4.E3E"]);
		check_numeric_format(".0\"E+\"", numbers,
				[".0E+", "1.0E+", "11.0E+", "15.0E+", "19.0E+", "200.0E+", "220.0E+", "250.0E+", "280.0E+", "1234.0E+", "-1.0E+", "-11.0E+", "-15.0E+", "-19.0E+", "-200.0E+", "-220.0E+", "-250.0E+", "-280.0E+", "-9876.0E+", ".1E+", ".1E+", ".2E+", ".2E+", ".5E+", ".5E+", ".6E+", ".6E+", ".9E+", ".9E+", "1.0E+", "1.0E+", "-.1E+", "-.1E+", "-.2E+", "-.2E+", "-.5E+", "-.5E+", "-.6E+", "-.6E+", "-.9E+", "-.9E+", "-1.0E+", "-1.0E+", "3.4E+", "4.6E+", "4.3E+", "4.6E+", "4.3E+", "-3.4E+", "-4.6E+", "-4.3E+", "-4.6E+", "-4.3E+"]);
		check_numeric_format(".\"E+\"0", numbers,
				[".E+0", "1.E+0", "11.E+0", "15.E+0", "19.E+0", "200.E+0", "220.E+0", "250.E+0", "280.E+0", "1234.E+0", "-1.E+0", "-11.E+0", "-15.E+0", "-19.E+0", "-200.E+0", "-220.E+0", "-250.E+0", "-280.E+0", "-9876.E+0", ".E+1", ".E+1", ".E+2", ".E+2", ".E+5", ".E+5", ".E+6", ".E+6", ".E+9", ".E+9", "1.E+0", "1.E+0", "-.E+1", "-.E+1", "-.E+2", "-.E+2", "-.E+5", "-.E+5", "-.E+6", "-.E+6", "-.E+9", "-.E+9", "-1.E+0", "-1.E+0", "3.E+4", "4.E+6", "4.E+3", "4.E+6", "4.E+3", "-3.E+4", "-4.E+6", "-4.E+3", "-4.E+6", "-4.E+3"]);
		check_numeric_format(".\"E+\"0\"E+\"", numbers,
				[".E+0E+", "1.E+0E+", "11.E+0E+", "15.E+0E+", "19.E+0E+", "200.E+0E+", "220.E+0E+", "250.E+0E+", "280.E+0E+", "1234.E+0E+", "-1.E+0E+", "-11.E+0E+", "-15.E+0E+", "-19.E+0E+", "-200.E+0E+", "-220.E+0E+", "-250.E+0E+", "-280.E+0E+", "-9876.E+0E+", ".E+1E+", ".E+1E+", ".E+2E+", ".E+2E+", ".E+5E+", ".E+5E+", ".E+6E+", ".E+6E+", ".E+9E+", ".E+9E+", "1.E+0E+", "1.E+0E+", "-.E+1E+", "-.E+1E+", "-.E+2E+", "-.E+2E+", "-.E+5E+", "-.E+5E+", "-.E+6E+", "-.E+6E+", "-.E+9E+", "-.E+9E+", "-1.E+0E+", "-1.E+0E+", "3.E+4E+", "4.E+6E+", "4.E+3E+", "4.E+6E+", "4.E+3E+", "-3.E+4E+", "-4.E+6E+", "-4.E+3E+", "-4.E+6E+", "-4.E+3E+"]);
		check_numeric_format("0\\.0.0", numbers,
				["0.0.0", "0.1.0", "1.1.0", "1.5.0", "1.9.0", "20.0.0", "22.0.0", "25.0.0", "28.0.0", "123.4.0", "-0.1.0", "-1.1.0", "-1.5.0", "-1.9.0", "-20.0.0", "-22.0.0", "-25.0.0", "-28.0.0", "-987.6.0", "0.0.1", "0.0.1", "0.0.2", "0.0.2", "0.0.5", "0.0.5", "0.0.6", "0.0.6", "0.0.9", "0.0.9", "0.1.0", "0.1.0", "-0.0.1", "-0.0.1", "-0.0.2", "-0.0.2", "-0.0.5", "-0.0.5", "-0.0.6", "-0.0.6", "-0.0.9", "-0.0.9", "-0.1.0", "-0.1.0", "0.3.4", "0.4.6", "0.4.3", "0.4.6", "0.4.3", "-0.3.4", "-0.4.6", "-0.4.3", "-0.4.6", "-0.4.3"]);
		check_numeric_format("\\.00.0", numbers,
				[".00.0", ".01.0", ".11.0", ".15.0", ".19.0", ".200.0", ".220.0", ".250.0", ".280.0", ".1234.0", "-.01.0", "-.11.0", "-.15.0", "-.19.0", "-.200.0", "-.220.0", "-.250.0", "-.280.0", "-.9876.0", ".00.1", ".00.1", ".00.2", ".00.2", ".00.5", ".00.5", ".00.6", ".00.6", ".00.9", ".00.9", ".01.0", ".01.0", "-.00.1", "-.00.1", "-.00.2", "-.00.2", "-.00.5", "-.00.5", "-.00.6", "-.00.6", "-.00.9", "-.00.9", "-.01.0", "-.01.0", ".03.4", ".04.6", ".04.3", ".04.6", ".04.3", "-.03.4", "-.04.6", "-.04.3", "-.04.6", "-.04.3"]);
		check_numeric_format("\\.0\\.0.0", numbers,
				[".0.0.0", ".0.1.0", ".1.1.0", ".1.5.0", ".1.9.0", ".20.0.0", ".22.0.0", ".25.0.0", ".28.0.0", ".123.4.0", "-.0.1.0", "-.1.1.0", "-.1.5.0", "-.1.9.0", "-.20.0.0", "-.22.0.0", "-.25.0.0", "-.28.0.0", "-.987.6.0", ".0.0.1", ".0.0.1", ".0.0.2", ".0.0.2", ".0.0.5", ".0.0.5", ".0.0.6", ".0.0.6", ".0.0.9", ".0.0.9", ".0.1.0", ".0.1.0", "-.0.0.1", "-.0.0.1", "-.0.0.2", "-.0.0.2", "-.0.0.5", "-.0.0.5", "-.0.0.6", "-.0.0.6", "-.0.0.9", "-.0.0.9", "-.0.1.0", "-.0.1.0", ".0.3.4", ".0.4.6", ".0.4.3", ".0.4.6", ".0.4.3", "-.0.3.4", "-.0.4.6", "-.0.4.3", "-.0.4.6", "-.0.4.3"]);
		check_numeric_format("0.0\\.", numbers,
				["0.0.", "1.0.", "11.0.", "15.0.", "19.0.", "200.0.", "220.0.", "250.0.", "280.0.", "1234.0.", "-1.0.", "-11.0.", "-15.0.", "-19.0.", "-200.0.", "-220.0.", "-250.0.", "-280.0.", "-9876.0.", "0.1.", "0.1.", "0.2.", "0.2.", "0.5.", "0.5.", "0.6.", "0.6.", "0.9.", "0.9.", "1.0.", "1.0.", "-0.1.", "-0.1.", "-0.2.", "-0.2.", "-0.5.", "-0.5.", "-0.6.", "-0.6.", "-0.9.", "-0.9.", "-1.0.", "-1.0.", "3.4.", "4.6.", "4.3.", "4.6.", "4.3.", "-3.4.", "-4.6.", "-4.3.", "-4.6.", "-4.3."]);
		check_numeric_format("0.\\.0", numbers,
				["0..0", "1..0", "11..0", "15..0", "19..0", "200..0", "220..0", "250..0", "280..0", "1234..0", "-1..0", "-11..0", "-15..0", "-19..0", "-200..0", "-220..0", "-250..0", "-280..0", "-9876..0", "0..1", "0..1", "0..2", "0..2", "0..5", "0..5", "0..6", "0..6", "0..9", "0..9", "1..0", "1..0", "-0..1", "-0..1", "-0..2", "-0..2", "-0..5", "-0..5", "-0..6", "-0..6", "-0..9", "-0..9", "-1..0", "-1..0", "3..4", "4..6", "4..3", "4..6", "4..3", "-3..4", "-4..6", "-4..3", "-4..6", "-4..3"]);
		check_numeric_format("0.\\.0\\.", numbers,
				["0..0.", "1..0.", "11..0.", "15..0.", "19..0.", "200..0.", "220..0.", "250..0.", "280..0.", "1234..0.", "-1..0.", "-11..0.", "-15..0.", "-19..0.", "-200..0.", "-220..0.", "-250..0.", "-280..0.", "-9876..0.", "0..1.", "0..1.", "0..2.", "0..2.", "0..5.", "0..5.", "0..6.", "0..6.", "0..9.", "0..9.", "1..0.", "1..0.", "-0..1.", "-0..1.", "-0..2.", "-0..2.", "-0..5.", "-0..5.", "-0..6.", "-0..6.", "-0..9.", "-0..9.", "-1..0.", "-1..0.", "3..4.", "4..6.", "4..3.", "4..6.", "4..3.", "-3..4.", "-4..6.", "-4..3.", "-4..6.", "-4..3."]);
		check_numeric_format("0\\E0.0", numbers,
				["0E0.0", "0E1.0", "1E1.0", "1E5.0", "1E9.0", "20E0.0", "22E0.0", "25E0.0", "28E0.0", "123E4.0", "-0E1.0", "-1E1.0", "-1E5.0", "-1E9.0", "-20E0.0", "-22E0.0", "-25E0.0", "-28E0.0", "-987E6.0", "0E0.1", "0E0.1", "0E0.2", "0E0.2", "0E0.5", "0E0.5", "0E0.6", "0E0.6", "0E0.9", "0E0.9", "0E1.0", "0E1.0", "-0E0.1", "-0E0.1", "-0E0.2", "-0E0.2", "-0E0.5", "-0E0.5", "-0E0.6", "-0E0.6", "-0E0.9", "-0E0.9", "-0E1.0", "-0E1.0", "0E3.4", "0E4.6", "0E4.3", "0E4.6", "0E4.3", "-0E3.4", "-0E4.6", "-0E4.3", "-0E4.6", "-0E4.3"]);
		check_numeric_format("\\E00.0", numbers,
				["E00.0", "E01.0", "E11.0", "E15.0", "E19.0", "E200.0", "E220.0", "E250.0", "E280.0", "E1234.0", "-E01.0", "-E11.0", "-E15.0", "-E19.0", "-E200.0", "-E220.0", "-E250.0", "-E280.0", "-E9876.0", "E00.1", "E00.1", "E00.2", "E00.2", "E00.5", "E00.5", "E00.6", "E00.6", "E00.9", "E00.9", "E01.0", "E01.0", "-E00.1", "-E00.1", "-E00.2", "-E00.2", "-E00.5", "-E00.5", "-E00.6", "-E00.6", "-E00.9", "-E00.9", "-E01.0", "-E01.0", "E03.4", "E04.6", "E04.3", "E04.6", "E04.3", "-E03.4", "-E04.6", "-E04.3", "-E04.6", "-E04.3"]);
		check_numeric_format("\\E0\\E0.0", numbers,
				["E0E0.0", "E0E1.0", "E1E1.0", "E1E5.0", "E1E9.0", "E20E0.0", "E22E0.0", "E25E0.0", "E28E0.0", "E123E4.0", "-E0E1.0", "-E1E1.0", "-E1E5.0", "-E1E9.0", "-E20E0.0", "-E22E0.0", "-E25E0.0", "-E28E0.0", "-E987E6.0", "E0E0.1", "E0E0.1", "E0E0.2", "E0E0.2", "E0E0.5", "E0E0.5", "E0E0.6", "E0E0.6", "E0E0.9", "E0E0.9", "E0E1.0", "E0E1.0", "-E0E0.1", "-E0E0.1", "-E0E0.2", "-E0E0.2", "-E0E0.5", "-E0E0.5", "-E0E0.6", "-E0E0.6", "-E0E0.9", "-E0E0.9", "-E0E1.0", "-E0E1.0", "E0E3.4", "E0E4.6", "E0E4.3", "E0E4.6", "E0E4.3", "-E0E3.4", "-E0E4.6", "-E0E4.3", "-E0E4.6", "-E0E4.3"]);
		check_numeric_format("0.0\\E", numbers,
				["0.0E", "1.0E", "11.0E", "15.0E", "19.0E", "200.0E", "220.0E", "250.0E", "280.0E", "1234.0E", "-1.0E", "-11.0E", "-15.0E", "-19.0E", "-200.0E", "-220.0E", "-250.0E", "-280.0E", "-9876.0E", "0.1E", "0.1E", "0.2E", "0.2E", "0.5E", "0.5E", "0.6E", "0.6E", "0.9E", "0.9E", "1.0E", "1.0E", "-0.1E", "-0.1E", "-0.2E", "-0.2E", "-0.5E", "-0.5E", "-0.6E", "-0.6E", "-0.9E", "-0.9E", "-1.0E", "-1.0E", "3.4E", "4.6E", "4.3E", "4.6E", "4.3E", "-3.4E", "-4.6E", "-4.3E", "-4.6E", "-4.3E"]);
		check_numeric_format("0.\\E0", numbers,
				["0.E0", "1.E0", "11.E0", "15.E0", "19.E0", "200.E0", "220.E0", "250.E0", "280.E0", "1234.E0", "-1.E0", "-11.E0", "-15.E0", "-19.E0", "-200.E0", "-220.E0", "-250.E0", "-280.E0", "-9876.E0", "0.E1", "0.E1", "0.E2", "0.E2", "0.E5", "0.E5", "0.E6", "0.E6", "0.E9", "0.E9", "1.E0", "1.E0", "-0.E1", "-0.E1", "-0.E2", "-0.E2", "-0.E5", "-0.E5", "-0.E6", "-0.E6", "-0.E9", "-0.E9", "-1.E0", "-1.E0", "3.E4", "4.E6", "4.E3", "4.E6", "4.E3", "-3.E4", "-4.E6", "-4.E3", "-4.E6", "-4.E3"]);
		check_numeric_format("0.\\E0\\E", numbers,
				["0.E0E", "1.E0E", "11.E0E", "15.E0E", "19.E0E", "200.E0E", "220.E0E", "250.E0E", "280.E0E", "1234.E0E", "-1.E0E", "-11.E0E", "-15.E0E", "-19.E0E", "-200.E0E", "-220.E0E", "-250.E0E", "-280.E0E", "-9876.E0E", "0.E1E", "0.E1E", "0.E2E", "0.E2E", "0.E5E", "0.E5E", "0.E6E", "0.E6E", "0.E9E", "0.E9E", "1.E0E", "1.E0E", "-0.E1E", "-0.E1E", "-0.E2E", "-0.E2E", "-0.E5E", "-0.E5E", "-0.E6E", "-0.E6E", "-0.E9E", "-0.E9E", "-1.E0E", "-1.E0E", "3.E4E", "4.E6E", "4.E3E", "4.E6E", "4.E3E", "-3.E4E", "-4.E6E", "-4.E3E", "-4.E6E", "-4.E3E"]);
		check_numeric_format("0\"E+\"0.0", numbers,
				["0E+0.0", "0E+1.0", "1E+1.0", "1E+5.0", "1E+9.0", "20E+0.0", "22E+0.0", "25E+0.0", "28E+0.0", "123E+4.0", "-0E+1.0", "-1E+1.0", "-1E+5.0", "-1E+9.0", "-20E+0.0", "-22E+0.0", "-25E+0.0", "-28E+0.0", "-987E+6.0", "0E+0.1", "0E+0.1", "0E+0.2", "0E+0.2", "0E+0.5", "0E+0.5", "0E+0.6", "0E+0.6", "0E+0.9", "0E+0.9", "0E+1.0", "0E+1.0", "-0E+0.1", "-0E+0.1", "-0E+0.2", "-0E+0.2", "-0E+0.5", "-0E+0.5", "-0E+0.6", "-0E+0.6", "-0E+0.9", "-0E+0.9", "-0E+1.0", "-0E+1.0", "0E+3.4", "0E+4.6", "0E+4.3", "0E+4.6", "0E+4.3", "-0E+3.4", "-0E+4.6", "-0E+4.3", "-0E+4.6", "-0E+4.3"]);
		check_numeric_format("\"E+\"00.0", numbers,
				["E+00.0", "E+01.0", "E+11.0", "E+15.0", "E+19.0", "E+200.0", "E+220.0", "E+250.0", "E+280.0", "E+1234.0", "-E+01.0", "-E+11.0", "-E+15.0", "-E+19.0", "-E+200.0", "-E+220.0", "-E+250.0", "-E+280.0", "-E+9876.0", "E+00.1", "E+00.1", "E+00.2", "E+00.2", "E+00.5", "E+00.5", "E+00.6", "E+00.6", "E+00.9", "E+00.9", "E+01.0", "E+01.0", "-E+00.1", "-E+00.1", "-E+00.2", "-E+00.2", "-E+00.5", "-E+00.5", "-E+00.6", "-E+00.6", "-E+00.9", "-E+00.9", "-E+01.0", "-E+01.0", "E+03.4", "E+04.6", "E+04.3", "E+04.6", "E+04.3", "-E+03.4", "-E+04.6", "-E+04.3", "-E+04.6", "-E+04.3"]);
		check_numeric_format("\"E+\"0\"E+\"0.0", numbers,
				["E+0E+0.0", "E+0E+1.0", "E+1E+1.0", "E+1E+5.0", "E+1E+9.0", "E+20E+0.0", "E+22E+0.0", "E+25E+0.0", "E+28E+0.0", "E+123E+4.0", "-E+0E+1.0", "-E+1E+1.0", "-E+1E+5.0", "-E+1E+9.0", "-E+20E+0.0", "-E+22E+0.0", "-E+25E+0.0", "-E+28E+0.0", "-E+987E+6.0", "E+0E+0.1", "E+0E+0.1", "E+0E+0.2", "E+0E+0.2", "E+0E+0.5", "E+0E+0.5", "E+0E+0.6", "E+0E+0.6", "E+0E+0.9", "E+0E+0.9", "E+0E+1.0", "E+0E+1.0", "-E+0E+0.1", "-E+0E+0.1", "-E+0E+0.2", "-E+0E+0.2", "-E+0E+0.5", "-E+0E+0.5", "-E+0E+0.6", "-E+0E+0.6", "-E+0E+0.9", "-E+0E+0.9", "-E+0E+1.0", "-E+0E+1.0", "E+0E+3.4", "E+0E+4.6", "E+0E+4.3", "E+0E+4.6", "E+0E+4.3", "-E+0E+3.4", "-E+0E+4.6", "-E+0E+4.3", "-E+0E+4.6", "-E+0E+4.3"]);
		check_numeric_format("0.0\"E+\"", numbers,
				["0.0E+", "1.0E+", "11.0E+", "15.0E+", "19.0E+", "200.0E+", "220.0E+", "250.0E+", "280.0E+", "1234.0E+", "-1.0E+", "-11.0E+", "-15.0E+", "-19.0E+", "-200.0E+", "-220.0E+", "-250.0E+", "-280.0E+", "-9876.0E+", "0.1E+", "0.1E+", "0.2E+", "0.2E+", "0.5E+", "0.5E+", "0.6E+", "0.6E+", "0.9E+", "0.9E+", "1.0E+", "1.0E+", "-0.1E+", "-0.1E+", "-0.2E+", "-0.2E+", "-0.5E+", "-0.5E+", "-0.6E+", "-0.6E+", "-0.9E+", "-0.9E+", "-1.0E+", "-1.0E+", "3.4E+", "4.6E+", "4.3E+", "4.6E+", "4.3E+", "-3.4E+", "-4.6E+", "-4.3E+", "-4.6E+", "-4.3E+"]);
		check_numeric_format("0.\"E+\"0", numbers,
				["0.E+0", "1.E+0", "11.E+0", "15.E+0", "19.E+0", "200.E+0", "220.E+0", "250.E+0", "280.E+0", "1234.E+0", "-1.E+0", "-11.E+0", "-15.E+0", "-19.E+0", "-200.E+0", "-220.E+0", "-250.E+0", "-280.E+0", "-9876.E+0", "0.E+1", "0.E+1", "0.E+2", "0.E+2", "0.E+5", "0.E+5", "0.E+6", "0.E+6", "0.E+9", "0.E+9", "1.E+0", "1.E+0", "-0.E+1", "-0.E+1", "-0.E+2", "-0.E+2", "-0.E+5", "-0.E+5", "-0.E+6", "-0.E+6", "-0.E+9", "-0.E+9", "-1.E+0", "-1.E+0", "3.E+4", "4.E+6", "4.E+3", "4.E+6", "4.E+3", "-3.E+4", "-4.E+6", "-4.E+3", "-4.E+6", "-4.E+3"]);
		check_numeric_format("0.\"E+\"0\"E+\"", numbers,
				["0.E+0E+", "1.E+0E+", "11.E+0E+", "15.E+0E+", "19.E+0E+", "200.E+0E+", "220.E+0E+", "250.E+0E+", "280.E+0E+", "1234.E+0E+", "-1.E+0E+", "-11.E+0E+", "-15.E+0E+", "-19.E+0E+", "-200.E+0E+", "-220.E+0E+", "-250.E+0E+", "-280.E+0E+", "-9876.E+0E+", "0.E+1E+", "0.E+1E+", "0.E+2E+", "0.E+2E+", "0.E+5E+", "0.E+5E+", "0.E+6E+", "0.E+6E+", "0.E+9E+", "0.E+9E+", "1.E+0E+", "1.E+0E+", "-0.E+1E+", "-0.E+1E+", "-0.E+2E+", "-0.E+2E+", "-0.E+5E+", "-0.E+5E+", "-0.E+6E+", "-0.E+6E+", "-0.E+9E+", "-0.E+9E+", "-1.E+0E+", "-1.E+0E+", "3.E+4E+", "4.E+6E+", "4.E+3E+", "4.E+6E+", "4.E+3E+", "-3.E+4E+", "-4.E+6E+", "-4.E+3E+", "-4.E+6E+", "-4.E+3E+"]);
		check_numeric_format("0\\.0E-0", numbers,
				["0.0E0", "0.1E0", "1.1E0", "1.5E0", "1.9E0", "0.2E2", "0.2E2", "0.3E2", "0.3E2", "1.2E2", "-0.1E0", "-1.1E0", "-1.5E0", "-1.9E0", "-0.2E2", "-0.2E2", "-0.3E2", "-0.3E2", "-9.9E2", "1.0E-2", "1.2E-2", "1.5E-2", "1.8E-2", "5.0E-2", "5.3E-2", "5.6E-2", "5.8E-2", "9.0E-2", "9.4E-2", "9.6E-2", "9.7E-2", "-1.0E-2", "-1.2E-2", "-1.5E-2", "-1.8E-2", "-5.0E-2", "-5.3E-2", "-5.6E-2", "-5.8E-2", "-9.0E-2", "-9.4E-2", "-9.6E-2", "-9.7E-2", "0.3E0", "0.5E0", "0.4E0", "0.5E0", "0.4E0", "-0.3E0", "-0.5E0", "-0.4E0", "-0.5E0", "-0.4E0"]);
		check_numeric_format("\\.00E-0", numbers,
				[".00E0", ".01E0", ".11E0", ".15E0", ".19E0", ".02E2", ".02E2", ".03E2", ".03E2", ".12E2", "-.01E0", "-.11E0", "-.15E0", "-.19E0", "-.02E2", "-.02E2", "-.03E2", "-.03E2", "-.99E2", ".10E-2", ".12E-2", ".15E-2", ".18E-2", ".50E-2", ".53E-2", ".56E-2", ".58E-2", ".90E-2", ".94E-2", ".96E-2", ".97E-2", "-.10E-2", "-.12E-2", "-.15E-2", "-.18E-2", "-.50E-2", "-.53E-2", "-.56E-2", "-.58E-2", "-.90E-2", "-.94E-2", "-.96E-2", "-.97E-2", ".03E0", ".05E0", ".04E0", ".05E0", ".04E0", "-.03E0", "-.05E0", "-.04E0", "-.05E0", "-.04E0"]);
		check_numeric_format("\\.0\\.0E-0", numbers,
				[".0.0E0", ".0.1E0", ".1.1E0", ".1.5E0", ".1.9E0", ".0.2E2", ".0.2E2", ".0.3E2", ".0.3E2", ".1.2E2", "-.0.1E0", "-.1.1E0", "-.1.5E0", "-.1.9E0", "-.0.2E2", "-.0.2E2", "-.0.3E2", "-.0.3E2", "-.9.9E2", ".1.0E-2", ".1.2E-2", ".1.5E-2", ".1.8E-2", ".5.0E-2", ".5.3E-2", ".5.6E-2", ".5.8E-2", ".9.0E-2", ".9.4E-2", ".9.6E-2", ".9.7E-2", "-.1.0E-2", "-.1.2E-2", "-.1.5E-2", "-.1.8E-2", "-.5.0E-2", "-.5.3E-2", "-.5.6E-2", "-.5.8E-2", "-.9.0E-2", "-.9.4E-2", "-.9.6E-2", "-.9.7E-2", ".0.3E0", ".0.5E0", ".0.4E0", ".0.5E0", ".0.4E0", "-.0.3E0", "-.0.5E0", "-.0.4E0", "-.0.5E0", "-.0.4E0"]);
		check_numeric_format("0E-0\\.", numbers,
				["0E0.", "1E0.", "1E1.", "2E1.", "2E1.", "2E2.", "2E2.", "3E2.", "3E2.", "1E3.", "-1E0.", "-1E1.", "-2E1.", "-2E1.", "-2E2.", "-2E2.", "-3E2.", "-3E2.", "-1E4.", "1E-1.", "1E-1.", "2E-1.", "2E-1.", "5E-1.", "5E-1.", "6E-1.", "6E-1.", "9E-1.", "9E-1.", "1E0.", "1E0.", "-1E-1.", "-1E-1.", "-2E-1.", "-2E-1.", "-5E-1.", "-5E-1.", "-6E-1.", "-6E-1.", "-9E-1.", "-9E-1.", "-1E0.", "-1E0.", "3E0.", "5E0.", "4E0.", "5E0.", "4E0.", "-3E0.", "-5E0.", "-4E0.", "-5E0.", "-4E0."]);
		check_numeric_format("0\\E.-0", numbers,
				["0E.0", "1E.0", "1E.1", "2E.1", "2E.1", "2E.2", "2E.2", "3E.2", "3E.2", "1E.3", "-1E.0", "-1E.1", "-2E.1", "-2E.1", "-2E.2", "-2E.2", "-3E.2", "-3E.2", "-1E.4", "1E.-1", "1E.-1", "2E.-1", "2E.-1", "5E.-1", "5E.-1", "6E.-1", "6E.-1", "9E.-1", "9E.-1", "1E.0", "1E.0", "-1E.-1", "-1E.-1", "-2E.-1", "-2E.-1", "-5E.-1", "-5E.-1", "-6E.-1", "-6E.-1", "-9E.-1", "-9E.-1", "-1E.0", "-1E.0", "3E.0", "5E.0", "4E.0", "5E.0", "4E.0", "-3E.0", "-5E.0", "-4E.0", "-5E.0", "-4E.0"]);
		check_numeric_format("0\\E.-0\\.", numbers,
				["0E.0.", "1E.0.", "1E.1.", "2E.1.", "2E.1.", "2E.2.", "2E.2.", "3E.2.", "3E.2.", "1E.3.", "-1E.0.", "-1E.1.", "-2E.1.", "-2E.1.", "-2E.2.", "-2E.2.", "-3E.2.", "-3E.2.", "-1E.4.", "1E.-1.", "1E.-1.", "2E.-1.", "2E.-1.", "5E.-1.", "5E.-1.", "6E.-1.", "6E.-1.", "9E.-1.", "9E.-1.", "1E.0.", "1E.0.", "-1E.-1.", "-1E.-1.", "-2E.-1.", "-2E.-1.", "-5E.-1.", "-5E.-1.", "-6E.-1.", "-6E.-1.", "-9E.-1.", "-9E.-1.", "-1E.0.", "-1E.0.", "3E.0.", "5E.0.", "4E.0.", "5E.0.", "4E.0.", "-3E.0.", "-5E.0.", "-4E.0.", "-5E.0.", "-4E.0."]);
		check_numeric_format("0\\E0E-0", numbers,
				["0E0E0", "0E1E0", "1E1E0", "1E5E0", "1E9E0", "0E2E2", "0E2E2", "0E3E2", "0E3E2", "1E2E2", "-0E1E0", "-1E1E0", "-1E5E0", "-1E9E0", "-0E2E2", "-0E2E2", "-0E3E2", "-0E3E2", "-9E9E2", "1E0E-2", "1E2E-2", "1E5E-2", "1E8E-2", "5E0E-2", "5E3E-2", "5E6E-2", "5E8E-2", "9E0E-2", "9E4E-2", "9E6E-2", "9E7E-2", "-1E0E-2", "-1E2E-2", "-1E5E-2", "-1E8E-2", "-5E0E-2", "-5E3E-2", "-5E6E-2", "-5E8E-2", "-9E0E-2", "-9E4E-2", "-9E6E-2", "-9E7E-2", "0E3E0", "0E5E0", "0E4E0", "0E5E0", "0E4E0", "-0E3E0", "-0E5E0", "-0E4E0", "-0E5E0", "-0E4E0"]);
		check_numeric_format("\\E00E-0", numbers,
				["E00E0", "E01E0", "E11E0", "E15E0", "E19E0", "E02E2", "E02E2", "E03E2", "E03E2", "E12E2", "-E01E0", "-E11E0", "-E15E0", "-E19E0", "-E02E2", "-E02E2", "-E03E2", "-E03E2", "-E99E2", "E10E-2", "E12E-2", "E15E-2", "E18E-2", "E50E-2", "E53E-2", "E56E-2", "E58E-2", "E90E-2", "E94E-2", "E96E-2", "E97E-2", "-E10E-2", "-E12E-2", "-E15E-2", "-E18E-2", "-E50E-2", "-E53E-2", "-E56E-2", "-E58E-2", "-E90E-2", "-E94E-2", "-E96E-2", "-E97E-2", "E03E0", "E05E0", "E04E0", "E05E0", "E04E0", "-E03E0", "-E05E0", "-E04E0", "-E05E0", "-E04E0"]);
		check_numeric_format("\\E0\\E0E-0", numbers,
				["E0E0E0", "E0E1E0", "E1E1E0", "E1E5E0", "E1E9E0", "E0E2E2", "E0E2E2", "E0E3E2", "E0E3E2", "E1E2E2", "-E0E1E0", "-E1E1E0", "-E1E5E0", "-E1E9E0", "-E0E2E2", "-E0E2E2", "-E0E3E2", "-E0E3E2", "-E9E9E2", "E1E0E-2", "E1E2E-2", "E1E5E-2", "E1E8E-2", "E5E0E-2", "E5E3E-2", "E5E6E-2", "E5E8E-2", "E9E0E-2", "E9E4E-2", "E9E6E-2", "E9E7E-2", "-E1E0E-2", "-E1E2E-2", "-E1E5E-2", "-E1E8E-2", "-E5E0E-2", "-E5E3E-2", "-E5E6E-2", "-E5E8E-2", "-E9E0E-2", "-E9E4E-2", "-E9E6E-2", "-E9E7E-2", "E0E3E0", "E0E5E0", "E0E4E0", "E0E5E0", "E0E4E0", "-E0E3E0", "-E0E5E0", "-E0E4E0", "-E0E5E0", "-E0E4E0"]);
		check_numeric_format("0E-0\\E", numbers,
				["0E0E", "1E0E", "1E1E", "2E1E", "2E1E", "2E2E", "2E2E", "3E2E", "3E2E", "1E3E", "-1E0E", "-1E1E", "-2E1E", "-2E1E", "-2E2E", "-2E2E", "-3E2E", "-3E2E", "-1E4E", "1E-1E", "1E-1E", "2E-1E", "2E-1E", "5E-1E", "5E-1E", "6E-1E", "6E-1E", "9E-1E", "9E-1E", "1E0E", "1E0E", "-1E-1E", "-1E-1E", "-2E-1E", "-2E-1E", "-5E-1E", "-5E-1E", "-6E-1E", "-6E-1E", "-9E-1E", "-9E-1E", "-1E0E", "-1E0E", "3E0E", "5E0E", "4E0E", "5E0E", "4E0E", "-3E0E", "-5E0E", "-4E0E", "-5E0E", "-4E0E"]);
		check_numeric_format("0\\EE-0", numbers,
				["0EE0", "1EE0", "1EE1", "2EE1", "2EE1", "2EE2", "2EE2", "3EE2", "3EE2", "1EE3", "-1EE0", "-1EE1", "-2EE1", "-2EE1", "-2EE2", "-2EE2", "-3EE2", "-3EE2", "-1EE4", "1EE-1", "1EE-1", "2EE-1", "2EE-1", "5EE-1", "5EE-1", "6EE-1", "6EE-1", "9EE-1", "9EE-1", "1EE0", "1EE0", "-1EE-1", "-1EE-1", "-2EE-1", "-2EE-1", "-5EE-1", "-5EE-1", "-6EE-1", "-6EE-1", "-9EE-1", "-9EE-1", "-1EE0", "-1EE0", "3EE0", "5EE0", "4EE0", "5EE0", "4EE0", "-3EE0", "-5EE0", "-4EE0", "-5EE0", "-4EE0"]);
		check_numeric_format("0\\EE-0\\E", numbers,
				["0EE0E", "1EE0E", "1EE1E", "2EE1E", "2EE1E", "2EE2E", "2EE2E", "3EE2E", "3EE2E", "1EE3E", "-1EE0E", "-1EE1E", "-2EE1E", "-2EE1E", "-2EE2E", "-2EE2E", "-3EE2E", "-3EE2E", "-1EE4E", "1EE-1E", "1EE-1E", "2EE-1E", "2EE-1E", "5EE-1E", "5EE-1E", "6EE-1E", "6EE-1E", "9EE-1E", "9EE-1E", "1EE0E", "1EE0E", "-1EE-1E", "-1EE-1E", "-2EE-1E", "-2EE-1E", "-5EE-1E", "-5EE-1E", "-6EE-1E", "-6EE-1E", "-9EE-1E", "-9EE-1E", "-1EE0E", "-1EE0E", "3EE0E", "5EE0E", "4EE0E", "5EE0E", "4EE0E", "-3EE0E", "-5EE0E", "-4EE0E", "-5EE0E", "-4EE0E"]);
		check_numeric_format("0\"E+\"0E-0", numbers,
				["0E+0E0", "0E+1E0", "1E+1E0", "1E+5E0", "1E+9E0", "0E+2E2", "0E+2E2", "0E+3E2", "0E+3E2", "1E+2E2", "-0E+1E0", "-1E+1E0", "-1E+5E0", "-1E+9E0", "-0E+2E2", "-0E+2E2", "-0E+3E2", "-0E+3E2", "-9E+9E2", "1E+0E-2", "1E+2E-2", "1E+5E-2", "1E+8E-2", "5E+0E-2", "5E+3E-2", "5E+6E-2", "5E+8E-2", "9E+0E-2", "9E+4E-2", "9E+6E-2", "9E+7E-2", "-1E+0E-2", "-1E+2E-2", "-1E+5E-2", "-1E+8E-2", "-5E+0E-2", "-5E+3E-2", "-5E+6E-2", "-5E+8E-2", "-9E+0E-2", "-9E+4E-2", "-9E+6E-2", "-9E+7E-2", "0E+3E0", "0E+5E0", "0E+4E0", "0E+5E0", "0E+4E0", "-0E+3E0", "-0E+5E0", "-0E+4E0", "-0E+5E0", "-0E+4E0"]);
		check_numeric_format("\"E+\"00E-0", numbers,
				["E+00E0", "E+01E0", "E+11E0", "E+15E0", "E+19E0", "E+02E2", "E+02E2", "E+03E2", "E+03E2", "E+12E2", "-E+01E0", "-E+11E0", "-E+15E0", "-E+19E0", "-E+02E2", "-E+02E2", "-E+03E2", "-E+03E2", "-E+99E2", "E+10E-2", "E+12E-2", "E+15E-2", "E+18E-2", "E+50E-2", "E+53E-2", "E+56E-2", "E+58E-2", "E+90E-2", "E+94E-2", "E+96E-2", "E+97E-2", "-E+10E-2", "-E+12E-2", "-E+15E-2", "-E+18E-2", "-E+50E-2", "-E+53E-2", "-E+56E-2", "-E+58E-2", "-E+90E-2", "-E+94E-2", "-E+96E-2", "-E+97E-2", "E+03E0", "E+05E0", "E+04E0", "E+05E0", "E+04E0", "-E+03E0", "-E+05E0", "-E+04E0", "-E+05E0", "-E+04E0"]);
		check_numeric_format("\"E+\"0\"E+\"0E-0", numbers,
				["E+0E+0E0", "E+0E+1E0", "E+1E+1E0", "E+1E+5E0", "E+1E+9E0", "E+0E+2E2", "E+0E+2E2", "E+0E+3E2", "E+0E+3E2", "E+1E+2E2", "-E+0E+1E0", "-E+1E+1E0", "-E+1E+5E0", "-E+1E+9E0", "-E+0E+2E2", "-E+0E+2E2", "-E+0E+3E2", "-E+0E+3E2", "-E+9E+9E2", "E+1E+0E-2", "E+1E+2E-2", "E+1E+5E-2", "E+1E+8E-2", "E+5E+0E-2", "E+5E+3E-2", "E+5E+6E-2", "E+5E+8E-2", "E+9E+0E-2", "E+9E+4E-2", "E+9E+6E-2", "E+9E+7E-2", "-E+1E+0E-2", "-E+1E+2E-2", "-E+1E+5E-2", "-E+1E+8E-2", "-E+5E+0E-2", "-E+5E+3E-2", "-E+5E+6E-2", "-E+5E+8E-2", "-E+9E+0E-2", "-E+9E+4E-2", "-E+9E+6E-2", "-E+9E+7E-2", "E+0E+3E0", "E+0E+5E0", "E+0E+4E0", "E+0E+5E0", "E+0E+4E0", "-E+0E+3E0", "-E+0E+5E0", "-E+0E+4E0", "-E+0E+5E0", "-E+0E+4E0"]);
		check_numeric_format("0E-0\"E+\"", numbers,
				["0E0E+", "1E0E+", "1E1E+", "2E1E+", "2E1E+", "2E2E+", "2E2E+", "3E2E+", "3E2E+", "1E3E+", "-1E0E+", "-1E1E+", "-2E1E+", "-2E1E+", "-2E2E+", "-2E2E+", "-3E2E+", "-3E2E+", "-1E4E+", "1E-1E+", "1E-1E+", "2E-1E+", "2E-1E+", "5E-1E+", "5E-1E+", "6E-1E+", "6E-1E+", "9E-1E+", "9E-1E+", "1E0E+", "1E0E+", "-1E-1E+", "-1E-1E+", "-2E-1E+", "-2E-1E+", "-5E-1E+", "-5E-1E+", "-6E-1E+", "-6E-1E+", "-9E-1E+", "-9E-1E+", "-1E0E+", "-1E0E+", "3E0E+", "5E0E+", "4E0E+", "5E0E+", "4E0E+", "-3E0E+", "-5E0E+", "-4E0E+", "-5E0E+", "-4E0E+"]);
		check_numeric_format("0\\E\"E+\"-0", numbers,
				["0EE+0", "1EE+0", "1EE+1", "2EE+1", "2EE+1", "2EE+2", "2EE+2", "3EE+2", "3EE+2", "1EE+3", "-1EE+0", "-1EE+1", "-2EE+1", "-2EE+1", "-2EE+2", "-2EE+2", "-3EE+2", "-3EE+2", "-1EE+4", "1EE+-1", "1EE+-1", "2EE+-1", "2EE+-1", "5EE+-1", "5EE+-1", "6EE+-1", "6EE+-1", "9EE+-1", "9EE+-1", "1EE+0", "1EE+0", "-1EE+-1", "-1EE+-1", "-2EE+-1", "-2EE+-1", "-5EE+-1", "-5EE+-1", "-6EE+-1", "-6EE+-1", "-9EE+-1", "-9EE+-1", "-1EE+0", "-1EE+0", "3EE+0", "5EE+0", "4EE+0", "5EE+0", "4EE+0", "-3EE+0", "-5EE+0", "-4EE+0", "-5EE+0", "-4EE+0"]);
		check_numeric_format("0\\E\"E+\"-0\"E+\"", numbers,
				["0EE+0E+", "1EE+0E+", "1EE+1E+", "2EE+1E+", "2EE+1E+", "2EE+2E+", "2EE+2E+", "3EE+2E+", "3EE+2E+", "1EE+3E+", "-1EE+0E+", "-1EE+1E+", "-2EE+1E+", "-2EE+1E+", "-2EE+2E+", "-2EE+2E+", "-3EE+2E+", "-3EE+2E+", "-1EE+4E+", "1EE+-1E+", "1EE+-1E+", "2EE+-1E+", "2EE+-1E+", "5EE+-1E+", "5EE+-1E+", "6EE+-1E+", "6EE+-1E+", "9EE+-1E+", "9EE+-1E+", "1EE+0E+", "1EE+0E+", "-1EE+-1E+", "-1EE+-1E+", "-2EE+-1E+", "-2EE+-1E+", "-5EE+-1E+", "-5EE+-1E+", "-6EE+-1E+", "-6EE+-1E+", "-9EE+-1E+", "-9EE+-1E+", "-1EE+0E+", "-1EE+0E+", "3EE+0E+", "5EE+0E+", "4EE+0E+", "5EE+0E+", "4EE+0E+", "-3EE+0E+", "-5EE+0E+", "-4EE+0E+", "-5EE+0E+", "-4EE+0E+"]);
		check_numeric_format("0\\.0E+0", numbers,
				["0.0E+0", "0.1E+0", "1.1E+0", "1.5E+0", "1.9E+0", "0.2E+2", "0.2E+2", "0.3E+2", "0.3E+2", "1.2E+2", "-0.1E+0", "-1.1E+0", "-1.5E+0", "-1.9E+0", "-0.2E+2", "-0.2E+2", "-0.3E+2", "-0.3E+2", "-9.9E+2", "1.0E-2", "1.2E-2", "1.5E-2", "1.8E-2", "5.0E-2", "5.3E-2", "5.6E-2", "5.8E-2", "9.0E-2", "9.4E-2", "9.6E-2", "9.7E-2", "-1.0E-2", "-1.2E-2", "-1.5E-2", "-1.8E-2", "-5.0E-2", "-5.3E-2", "-5.6E-2", "-5.8E-2", "-9.0E-2", "-9.4E-2", "-9.6E-2", "-9.7E-2", "0.3E+0", "0.5E+0", "0.4E+0", "0.5E+0", "0.4E+0", "-0.3E+0", "-0.5E+0", "-0.4E+0", "-0.5E+0", "-0.4E+0"]);
		check_numeric_format("\\.00E+0", numbers,
				[".00E+0", ".01E+0", ".11E+0", ".15E+0", ".19E+0", ".02E+2", ".02E+2", ".03E+2", ".03E+2", ".12E+2", "-.01E+0", "-.11E+0", "-.15E+0", "-.19E+0", "-.02E+2", "-.02E+2", "-.03E+2", "-.03E+2", "-.99E+2", ".10E-2", ".12E-2", ".15E-2", ".18E-2", ".50E-2", ".53E-2", ".56E-2", ".58E-2", ".90E-2", ".94E-2", ".96E-2", ".97E-2", "-.10E-2", "-.12E-2", "-.15E-2", "-.18E-2", "-.50E-2", "-.53E-2", "-.56E-2", "-.58E-2", "-.90E-2", "-.94E-2", "-.96E-2", "-.97E-2", ".03E+0", ".05E+0", ".04E+0", ".05E+0", ".04E+0", "-.03E+0", "-.05E+0", "-.04E+0", "-.05E+0", "-.04E+0"]);
		check_numeric_format("\\.0\\.0E+0", numbers,
				[".0.0E+0", ".0.1E+0", ".1.1E+0", ".1.5E+0", ".1.9E+0", ".0.2E+2", ".0.2E+2", ".0.3E+2", ".0.3E+2", ".1.2E+2", "-.0.1E+0", "-.1.1E+0", "-.1.5E+0", "-.1.9E+0", "-.0.2E+2", "-.0.2E+2", "-.0.3E+2", "-.0.3E+2", "-.9.9E+2", ".1.0E-2", ".1.2E-2", ".1.5E-2", ".1.8E-2", ".5.0E-2", ".5.3E-2", ".5.6E-2", ".5.8E-2", ".9.0E-2", ".9.4E-2", ".9.6E-2", ".9.7E-2", "-.1.0E-2", "-.1.2E-2", "-.1.5E-2", "-.1.8E-2", "-.5.0E-2", "-.5.3E-2", "-.5.6E-2", "-.5.8E-2", "-.9.0E-2", "-.9.4E-2", "-.9.6E-2", "-.9.7E-2", ".0.3E+0", ".0.5E+0", ".0.4E+0", ".0.5E+0", ".0.4E+0", "-.0.3E+0", "-.0.5E+0", "-.0.4E+0", "-.0.5E+0", "-.0.4E+0"]);
		check_numeric_format("0E+0\\.", numbers,
				["0E+0.", "1E+0.", "1E+1.", "2E+1.", "2E+1.", "2E+2.", "2E+2.", "3E+2.", "3E+2.", "1E+3.", "-1E+0.", "-1E+1.", "-2E+1.", "-2E+1.", "-2E+2.", "-2E+2.", "-3E+2.", "-3E+2.", "-1E+4.", "1E-1.", "1E-1.", "2E-1.", "2E-1.", "5E-1.", "5E-1.", "6E-1.", "6E-1.", "9E-1.", "9E-1.", "1E+0.", "1E+0.", "-1E-1.", "-1E-1.", "-2E-1.", "-2E-1.", "-5E-1.", "-5E-1.", "-6E-1.", "-6E-1.", "-9E-1.", "-9E-1.", "-1E+0.", "-1E+0.", "3E+0.", "5E+0.", "4E+0.", "5E+0.", "4E+0.", "-3E+0.", "-5E+0.", "-4E+0.", "-5E+0.", "-4E+0."]);
		check_numeric_format("0\\E.+0", numbers,
				["0E.+0", "1E.+0", "1E.+1", "2E.+1", "2E.+1", "2E.+2", "2E.+2", "3E.+2", "3E.+2", "1E.+3", "-1E.+0", "-1E.+1", "-2E.+1", "-2E.+1", "-2E.+2", "-2E.+2", "-3E.+2", "-3E.+2", "-1E.+4", "1E.-1", "1E.-1", "2E.-1", "2E.-1", "5E.-1", "5E.-1", "6E.-1", "6E.-1", "9E.-1", "9E.-1", "1E.+0", "1E.+0", "-1E.-1", "-1E.-1", "-2E.-1", "-2E.-1", "-5E.-1", "-5E.-1", "-6E.-1", "-6E.-1", "-9E.-1", "-9E.-1", "-1E.+0", "-1E.+0", "3E.+0", "5E.+0", "4E.+0", "5E.+0", "4E.+0", "-3E.+0", "-5E.+0", "-4E.+0", "-5E.+0", "-4E.+0"]);
		check_numeric_format("0\\E.+0\\.", numbers,
				["0E.+0.", "1E.+0.", "1E.+1.", "2E.+1.", "2E.+1.", "2E.+2.", "2E.+2.", "3E.+2.", "3E.+2.", "1E.+3.", "-1E.+0.", "-1E.+1.", "-2E.+1.", "-2E.+1.", "-2E.+2.", "-2E.+2.", "-3E.+2.", "-3E.+2.", "-1E.+4.", "1E.-1.", "1E.-1.", "2E.-1.", "2E.-1.", "5E.-1.", "5E.-1.", "6E.-1.", "6E.-1.", "9E.-1.", "9E.-1.", "1E.+0.", "1E.+0.", "-1E.-1.", "-1E.-1.", "-2E.-1.", "-2E.-1.", "-5E.-1.", "-5E.-1.", "-6E.-1.", "-6E.-1.", "-9E.-1.", "-9E.-1.", "-1E.+0.", "-1E.+0.", "3E.+0.", "5E.+0.", "4E.+0.", "5E.+0.", "4E.+0.", "-3E.+0.", "-5E.+0.", "-4E.+0.", "-5E.+0.", "-4E.+0."]);
		check_numeric_format("0\\E0E+0", numbers,
				["0E0E+0", "0E1E+0", "1E1E+0", "1E5E+0", "1E9E+0", "0E2E+2", "0E2E+2", "0E3E+2", "0E3E+2", "1E2E+2", "-0E1E+0", "-1E1E+0", "-1E5E+0", "-1E9E+0", "-0E2E+2", "-0E2E+2", "-0E3E+2", "-0E3E+2", "-9E9E+2", "1E0E-2", "1E2E-2", "1E5E-2", "1E8E-2", "5E0E-2", "5E3E-2", "5E6E-2", "5E8E-2", "9E0E-2", "9E4E-2", "9E6E-2", "9E7E-2", "-1E0E-2", "-1E2E-2", "-1E5E-2", "-1E8E-2", "-5E0E-2", "-5E3E-2", "-5E6E-2", "-5E8E-2", "-9E0E-2", "-9E4E-2", "-9E6E-2", "-9E7E-2", "0E3E+0", "0E5E+0", "0E4E+0", "0E5E+0", "0E4E+0", "-0E3E+0", "-0E5E+0", "-0E4E+0", "-0E5E+0", "-0E4E+0"]);
		check_numeric_format("\\E00E+0", numbers,
				["E00E+0", "E01E+0", "E11E+0", "E15E+0", "E19E+0", "E02E+2", "E02E+2", "E03E+2", "E03E+2", "E12E+2", "-E01E+0", "-E11E+0", "-E15E+0", "-E19E+0", "-E02E+2", "-E02E+2", "-E03E+2", "-E03E+2", "-E99E+2", "E10E-2", "E12E-2", "E15E-2", "E18E-2", "E50E-2", "E53E-2", "E56E-2", "E58E-2", "E90E-2", "E94E-2", "E96E-2", "E97E-2", "-E10E-2", "-E12E-2", "-E15E-2", "-E18E-2", "-E50E-2", "-E53E-2", "-E56E-2", "-E58E-2", "-E90E-2", "-E94E-2", "-E96E-2", "-E97E-2", "E03E+0", "E05E+0", "E04E+0", "E05E+0", "E04E+0", "-E03E+0", "-E05E+0", "-E04E+0", "-E05E+0", "-E04E+0"]);
		check_numeric_format("\\E0\\E0E+0", numbers,
				["E0E0E+0", "E0E1E+0", "E1E1E+0", "E1E5E+0", "E1E9E+0", "E0E2E+2", "E0E2E+2", "E0E3E+2", "E0E3E+2", "E1E2E+2", "-E0E1E+0", "-E1E1E+0", "-E1E5E+0", "-E1E9E+0", "-E0E2E+2", "-E0E2E+2", "-E0E3E+2", "-E0E3E+2", "-E9E9E+2", "E1E0E-2", "E1E2E-2", "E1E5E-2", "E1E8E-2", "E5E0E-2", "E5E3E-2", "E5E6E-2", "E5E8E-2", "E9E0E-2", "E9E4E-2", "E9E6E-2", "E9E7E-2", "-E1E0E-2", "-E1E2E-2", "-E1E5E-2", "-E1E8E-2", "-E5E0E-2", "-E5E3E-2", "-E5E6E-2", "-E5E8E-2", "-E9E0E-2", "-E9E4E-2", "-E9E6E-2", "-E9E7E-2", "E0E3E+0", "E0E5E+0", "E0E4E+0", "E0E5E+0", "E0E4E+0", "-E0E3E+0", "-E0E5E+0", "-E0E4E+0", "-E0E5E+0", "-E0E4E+0"]);
		check_numeric_format("0E+0\\E", numbers,
				["0E+0E", "1E+0E", "1E+1E", "2E+1E", "2E+1E", "2E+2E", "2E+2E", "3E+2E", "3E+2E", "1E+3E", "-1E+0E", "-1E+1E", "-2E+1E", "-2E+1E", "-2E+2E", "-2E+2E", "-3E+2E", "-3E+2E", "-1E+4E", "1E-1E", "1E-1E", "2E-1E", "2E-1E", "5E-1E", "5E-1E", "6E-1E", "6E-1E", "9E-1E", "9E-1E", "1E+0E", "1E+0E", "-1E-1E", "-1E-1E", "-2E-1E", "-2E-1E", "-5E-1E", "-5E-1E", "-6E-1E", "-6E-1E", "-9E-1E", "-9E-1E", "-1E+0E", "-1E+0E", "3E+0E", "5E+0E", "4E+0E", "5E+0E", "4E+0E", "-3E+0E", "-5E+0E", "-4E+0E", "-5E+0E", "-4E+0E"]);
		check_numeric_format("0\\EE+0", numbers,
				["0EE+0", "1EE+0", "1EE+1", "2EE+1", "2EE+1", "2EE+2", "2EE+2", "3EE+2", "3EE+2", "1EE+3", "-1EE+0", "-1EE+1", "-2EE+1", "-2EE+1", "-2EE+2", "-2EE+2", "-3EE+2", "-3EE+2", "-1EE+4", "1EE-1", "1EE-1", "2EE-1", "2EE-1", "5EE-1", "5EE-1", "6EE-1", "6EE-1", "9EE-1", "9EE-1", "1EE+0", "1EE+0", "-1EE-1", "-1EE-1", "-2EE-1", "-2EE-1", "-5EE-1", "-5EE-1", "-6EE-1", "-6EE-1", "-9EE-1", "-9EE-1", "-1EE+0", "-1EE+0", "3EE+0", "5EE+0", "4EE+0", "5EE+0", "4EE+0", "-3EE+0", "-5EE+0", "-4EE+0", "-5EE+0", "-4EE+0"]);
		check_numeric_format("0\\EE+0\\E", numbers,
				["0EE+0E", "1EE+0E", "1EE+1E", "2EE+1E", "2EE+1E", "2EE+2E", "2EE+2E", "3EE+2E", "3EE+2E", "1EE+3E", "-1EE+0E", "-1EE+1E", "-2EE+1E", "-2EE+1E", "-2EE+2E", "-2EE+2E", "-3EE+2E", "-3EE+2E", "-1EE+4E", "1EE-1E", "1EE-1E", "2EE-1E", "2EE-1E", "5EE-1E", "5EE-1E", "6EE-1E", "6EE-1E", "9EE-1E", "9EE-1E", "1EE+0E", "1EE+0E", "-1EE-1E", "-1EE-1E", "-2EE-1E", "-2EE-1E", "-5EE-1E", "-5EE-1E", "-6EE-1E", "-6EE-1E", "-9EE-1E", "-9EE-1E", "-1EE+0E", "-1EE+0E", "3EE+0E", "5EE+0E", "4EE+0E", "5EE+0E", "4EE+0E", "-3EE+0E", "-5EE+0E", "-4EE+0E", "-5EE+0E", "-4EE+0E"]);
		check_numeric_format("0\"E+\"0E+0", numbers,
				["0E+0E+0", "0E+1E+0", "1E+1E+0", "1E+5E+0", "1E+9E+0", "0E+2E+2", "0E+2E+2", "0E+3E+2", "0E+3E+2", "1E+2E+2", "-0E+1E+0", "-1E+1E+0", "-1E+5E+0", "-1E+9E+0", "-0E+2E+2", "-0E+2E+2", "-0E+3E+2", "-0E+3E+2", "-9E+9E+2", "1E+0E-2", "1E+2E-2", "1E+5E-2", "1E+8E-2", "5E+0E-2", "5E+3E-2", "5E+6E-2", "5E+8E-2", "9E+0E-2", "9E+4E-2", "9E+6E-2", "9E+7E-2", "-1E+0E-2", "-1E+2E-2", "-1E+5E-2", "-1E+8E-2", "-5E+0E-2", "-5E+3E-2", "-5E+6E-2", "-5E+8E-2", "-9E+0E-2", "-9E+4E-2", "-9E+6E-2", "-9E+7E-2", "0E+3E+0", "0E+5E+0", "0E+4E+0", "0E+5E+0", "0E+4E+0", "-0E+3E+0", "-0E+5E+0", "-0E+4E+0", "-0E+5E+0", "-0E+4E+0"]);
		check_numeric_format("\"E+\"00E+0", numbers,
				["E+00E+0", "E+01E+0", "E+11E+0", "E+15E+0", "E+19E+0", "E+02E+2", "E+02E+2", "E+03E+2", "E+03E+2", "E+12E+2", "-E+01E+0", "-E+11E+0", "-E+15E+0", "-E+19E+0", "-E+02E+2", "-E+02E+2", "-E+03E+2", "-E+03E+2", "-E+99E+2", "E+10E-2", "E+12E-2", "E+15E-2", "E+18E-2", "E+50E-2", "E+53E-2", "E+56E-2", "E+58E-2", "E+90E-2", "E+94E-2", "E+96E-2", "E+97E-2", "-E+10E-2", "-E+12E-2", "-E+15E-2", "-E+18E-2", "-E+50E-2", "-E+53E-2", "-E+56E-2", "-E+58E-2", "-E+90E-2", "-E+94E-2", "-E+96E-2", "-E+97E-2", "E+03E+0", "E+05E+0", "E+04E+0", "E+05E+0", "E+04E+0", "-E+03E+0", "-E+05E+0", "-E+04E+0", "-E+05E+0", "-E+04E+0"]);
		check_numeric_format("\"E+\"0\"E+\"0E+0", numbers,
				["E+0E+0E+0", "E+0E+1E+0", "E+1E+1E+0", "E+1E+5E+0", "E+1E+9E+0", "E+0E+2E+2", "E+0E+2E+2", "E+0E+3E+2", "E+0E+3E+2", "E+1E+2E+2", "-E+0E+1E+0", "-E+1E+1E+0", "-E+1E+5E+0", "-E+1E+9E+0", "-E+0E+2E+2", "-E+0E+2E+2", "-E+0E+3E+2", "-E+0E+3E+2", "-E+9E+9E+2", "E+1E+0E-2", "E+1E+2E-2", "E+1E+5E-2", "E+1E+8E-2", "E+5E+0E-2", "E+5E+3E-2", "E+5E+6E-2", "E+5E+8E-2", "E+9E+0E-2", "E+9E+4E-2", "E+9E+6E-2", "E+9E+7E-2", "-E+1E+0E-2", "-E+1E+2E-2", "-E+1E+5E-2", "-E+1E+8E-2", "-E+5E+0E-2", "-E+5E+3E-2", "-E+5E+6E-2", "-E+5E+8E-2", "-E+9E+0E-2", "-E+9E+4E-2", "-E+9E+6E-2", "-E+9E+7E-2", "E+0E+3E+0", "E+0E+5E+0", "E+0E+4E+0", "E+0E+5E+0", "E+0E+4E+0", "-E+0E+3E+0", "-E+0E+5E+0", "-E+0E+4E+0", "-E+0E+5E+0", "-E+0E+4E+0"]);
		check_numeric_format("0E+0\"E+\"", numbers,
				["0E+0E+", "1E+0E+", "1E+1E+", "2E+1E+", "2E+1E+", "2E+2E+", "2E+2E+", "3E+2E+", "3E+2E+", "1E+3E+", "-1E+0E+", "-1E+1E+", "-2E+1E+", "-2E+1E+", "-2E+2E+", "-2E+2E+", "-3E+2E+", "-3E+2E+", "-1E+4E+", "1E-1E+", "1E-1E+", "2E-1E+", "2E-1E+", "5E-1E+", "5E-1E+", "6E-1E+", "6E-1E+", "9E-1E+", "9E-1E+", "1E+0E+", "1E+0E+", "-1E-1E+", "-1E-1E+", "-2E-1E+", "-2E-1E+", "-5E-1E+", "-5E-1E+", "-6E-1E+", "-6E-1E+", "-9E-1E+", "-9E-1E+", "-1E+0E+", "-1E+0E+", "3E+0E+", "5E+0E+", "4E+0E+", "5E+0E+", "4E+0E+", "-3E+0E+", "-5E+0E+", "-4E+0E+", "-5E+0E+", "-4E+0E+"]);
		check_numeric_format("0\\E\"E+\"+0", numbers,
				["0EE++0", "1EE++0", "1EE++1", "2EE++1", "2EE++1", "2EE++2", "2EE++2", "3EE++2", "3EE++2", "1EE++3", "-1EE++0", "-1EE++1", "-2EE++1", "-2EE++1", "-2EE++2", "-2EE++2", "-3EE++2", "-3EE++2", "-1EE++4", "1EE+-1", "1EE+-1", "2EE+-1", "2EE+-1", "5EE+-1", "5EE+-1", "6EE+-1", "6EE+-1", "9EE+-1", "9EE+-1", "1EE++0", "1EE++0", "-1EE+-1", "-1EE+-1", "-2EE+-1", "-2EE+-1", "-5EE+-1", "-5EE+-1", "-6EE+-1", "-6EE+-1", "-9EE+-1", "-9EE+-1", "-1EE++0", "-1EE++0", "3EE++0", "5EE++0", "4EE++0", "5EE++0", "4EE++0", "-3EE++0", "-5EE++0", "-4EE++0", "-5EE++0", "-4EE++0"]);
		check_numeric_format("0\\E\"E+\"+0\"E+\"", numbers,
				["0EE++0E+", "1EE++0E+", "1EE++1E+", "2EE++1E+", "2EE++1E+", "2EE++2E+", "2EE++2E+", "3EE++2E+", "3EE++2E+", "1EE++3E+", "-1EE++0E+", "-1EE++1E+", "-2EE++1E+", "-2EE++1E+", "-2EE++2E+", "-2EE++2E+", "-3EE++2E+", "-3EE++2E+", "-1EE++4E+", "1EE+-1E+", "1EE+-1E+", "2EE+-1E+", "2EE+-1E+", "5EE+-1E+", "5EE+-1E+", "6EE+-1E+", "6EE+-1E+", "9EE+-1E+", "9EE+-1E+", "1EE++0E+", "1EE++0E+", "-1EE+-1E+", "-1EE+-1E+", "-2EE+-1E+", "-2EE+-1E+", "-5EE+-1E+", "-5EE+-1E+", "-6EE+-1E+", "-6EE+-1E+", "-9EE+-1E+", "-9EE+-1E+", "-1EE++0E+", "-1EE++0E+", "3EE++0E+", "5EE++0E+", "4EE++0E+", "5EE++0E+", "4EE++0E+", "-3EE++0E+", "-5EE++0E+", "-4EE++0E+", "-5EE++0E+", "-4EE++0E+"]);
		check_numeric_format(".0\\.0E-0", numbers,
				[".0.0E0", ".1.0E1", ".1.1E2", ".1.5E2", ".1.9E2", ".2.0E3", ".2.2E3", ".2.5E3", ".2.8E3", ".1.2E4", "-.1.0E1", "-.1.1E2", "-.1.5E2", "-.1.9E2", "-.2.0E3", "-.2.2E3", "-.2.5E3", "-.2.8E3", "-.9.9E4", ".1.0E0", ".1.2E0", ".1.5E0", ".1.8E0", ".5.0E0", ".5.3E0", ".5.6E0", ".5.8E0", ".9.0E0", ".9.4E0", ".9.6E0", ".9.7E0", "-.1.0E0", "-.1.2E0", "-.1.5E0", "-.1.8E0", "-.5.0E0", "-.5.3E0", "-.5.6E0", "-.5.8E0", "-.9.0E0", "-.9.4E0", "-.9.6E0", "-.9.7E0", ".3.4E1", ".4.6E1", ".4.3E1", ".4.6E1", ".4.3E1", "-.3.4E1", "-.4.6E1", "-.4.3E1", "-.4.6E1", "-.4.3E1"]);
		check_numeric_format(".\\.00E-0", numbers,
				["..00E0", "..10E1", "..11E2", "..15E2", "..19E2", "..20E3", "..22E3", "..25E3", "..28E3", "..12E4", "-..10E1", "-..11E2", "-..15E2", "-..19E2", "-..20E3", "-..22E3", "-..25E3", "-..28E3", "-..99E4", "..10E0", "..12E0", "..15E0", "..18E0", "..50E0", "..53E0", "..56E0", "..58E0", "..90E0", "..94E0", "..96E0", "..97E0", "-..10E0", "-..12E0", "-..15E0", "-..18E0", "-..50E0", "-..53E0", "-..56E0", "-..58E0", "-..90E0", "-..94E0", "-..96E0", "-..97E0", "..34E1", "..46E1", "..43E1", "..46E1", "..43E1", "-..34E1", "-..46E1", "-..43E1", "-..46E1", "-..43E1"]);
		check_numeric_format(".\\.0\\.0E-0", numbers,
				["..0.0E0", "..1.0E1", "..1.1E2", "..1.5E2", "..1.9E2", "..2.0E3", "..2.2E3", "..2.5E3", "..2.8E3", "..1.2E4", "-..1.0E1", "-..1.1E2", "-..1.5E2", "-..1.9E2", "-..2.0E3", "-..2.2E3", "-..2.5E3", "-..2.8E3", "-..9.9E4", "..1.0E0", "..1.2E0", "..1.5E0", "..1.8E0", "..5.0E0", "..5.3E0", "..5.6E0", "..5.8E0", "..9.0E0", "..9.4E0", "..9.6E0", "..9.7E0", "-..1.0E0", "-..1.2E0", "-..1.5E0", "-..1.8E0", "-..5.0E0", "-..5.3E0", "-..5.6E0", "-..5.8E0", "-..9.0E0", "-..9.4E0", "-..9.6E0", "-..9.7E0", "..3.4E1", "..4.6E1", "..4.3E1", "..4.6E1", "..4.3E1", "-..3.4E1", "-..4.6E1", "-..4.3E1", "-..4.6E1", "-..4.3E1"]);
		check_numeric_format(".0E-0\\.", numbers,
				[".0E0.", ".1E1.", ".1E2.", ".2E2.", ".2E2.", ".2E3.", ".2E3.", ".3E3.", ".3E3.", ".1E4.", "-.1E1.", "-.1E2.", "-.2E2.", "-.2E2.", "-.2E3.", "-.2E3.", "-.3E3.", "-.3E3.", "-.1E5.", ".1E0.", ".1E0.", ".2E0.", ".2E0.", ".5E0.", ".5E0.", ".6E0.", ".6E0.", ".9E0.", ".9E0.", ".1E1.", ".1E1.", "-.1E0.", "-.1E0.", "-.2E0.", "-.2E0.", "-.5E0.", "-.5E0.", "-.6E0.", "-.6E0.", "-.9E0.", "-.9E0.", "-.1E1.", "-.1E1.", ".3E1.", ".5E1.", ".4E1.", ".5E1.", ".4E1.", "-.3E1.", "-.5E1.", "-.4E1.", "-.5E1.", "-.4E1."]);
		check_numeric_format(".0\\E.-0", numbers,
				[".0E.0", ".1E.1", ".1E.2", ".2E.2", ".2E.2", ".2E.3", ".2E.3", ".3E.3", ".3E.3", ".1E.4", "-.1E.1", "-.1E.2", "-.2E.2", "-.2E.2", "-.2E.3", "-.2E.3", "-.3E.3", "-.3E.3", "-.1E.5", ".1E.0", ".1E.0", ".2E.0", ".2E.0", ".5E.0", ".5E.0", ".6E.0", ".6E.0", ".9E.0", ".9E.0", ".1E.1", ".1E.1", "-.1E.0", "-.1E.0", "-.2E.0", "-.2E.0", "-.5E.0", "-.5E.0", "-.6E.0", "-.6E.0", "-.9E.0", "-.9E.0", "-.1E.1", "-.1E.1", ".3E.1", ".5E.1", ".4E.1", ".5E.1", ".4E.1", "-.3E.1", "-.5E.1", "-.4E.1", "-.5E.1", "-.4E.1"]);
		check_numeric_format(".0\\E.-0\\.", numbers,
				[".0E.0.", ".1E.1.", ".1E.2.", ".2E.2.", ".2E.2.", ".2E.3.", ".2E.3.", ".3E.3.", ".3E.3.", ".1E.4.", "-.1E.1.", "-.1E.2.", "-.2E.2.", "-.2E.2.", "-.2E.3.", "-.2E.3.", "-.3E.3.", "-.3E.3.", "-.1E.5.", ".1E.0.", ".1E.0.", ".2E.0.", ".2E.0.", ".5E.0.", ".5E.0.", ".6E.0.", ".6E.0.", ".9E.0.", ".9E.0.", ".1E.1.", ".1E.1.", "-.1E.0.", "-.1E.0.", "-.2E.0.", "-.2E.0.", "-.5E.0.", "-.5E.0.", "-.6E.0.", "-.6E.0.", "-.9E.0.", "-.9E.0.", "-.1E.1.", "-.1E.1.", ".3E.1.", ".5E.1.", ".4E.1.", ".5E.1.", ".4E.1.", "-.3E.1.", "-.5E.1.", "-.4E.1.", "-.5E.1.", "-.4E.1."]);
		check_numeric_format(".0\\E0E-0", numbers,
				[".0E0E0", ".1E0E1", ".1E1E2", ".1E5E2", ".1E9E2", ".2E0E3", ".2E2E3", ".2E5E3", ".2E8E3", ".1E2E4", "-.1E0E1", "-.1E1E2", "-.1E5E2", "-.1E9E2", "-.2E0E3", "-.2E2E3", "-.2E5E3", "-.2E8E3", "-.9E9E4", ".1E0E0", ".1E2E0", ".1E5E0", ".1E8E0", ".5E0E0", ".5E3E0", ".5E6E0", ".5E8E0", ".9E0E0", ".9E4E0", ".9E6E0", ".9E7E0", "-.1E0E0", "-.1E2E0", "-.1E5E0", "-.1E8E0", "-.5E0E0", "-.5E3E0", "-.5E6E0", "-.5E8E0", "-.9E0E0", "-.9E4E0", "-.9E6E0", "-.9E7E0", ".3E4E1", ".4E6E1", ".4E3E1", ".4E6E1", ".4E3E1", "-.3E4E1", "-.4E6E1", "-.4E3E1", "-.4E6E1", "-.4E3E1"]);
		check_numeric_format(".\\E00E-0", numbers,
				[".E00E0", ".E10E1", ".E11E2", ".E15E2", ".E19E2", ".E20E3", ".E22E3", ".E25E3", ".E28E3", ".E12E4", "-.E10E1", "-.E11E2", "-.E15E2", "-.E19E2", "-.E20E3", "-.E22E3", "-.E25E3", "-.E28E3", "-.E99E4", ".E10E0", ".E12E0", ".E15E0", ".E18E0", ".E50E0", ".E53E0", ".E56E0", ".E58E0", ".E90E0", ".E94E0", ".E96E0", ".E97E0", "-.E10E0", "-.E12E0", "-.E15E0", "-.E18E0", "-.E50E0", "-.E53E0", "-.E56E0", "-.E58E0", "-.E90E0", "-.E94E0", "-.E96E0", "-.E97E0", ".E34E1", ".E46E1", ".E43E1", ".E46E1", ".E43E1", "-.E34E1", "-.E46E1", "-.E43E1", "-.E46E1", "-.E43E1"]);
		check_numeric_format(".\\E0\\E0E-0", numbers,
				[".E0E0E0", ".E1E0E1", ".E1E1E2", ".E1E5E2", ".E1E9E2", ".E2E0E3", ".E2E2E3", ".E2E5E3", ".E2E8E3", ".E1E2E4", "-.E1E0E1", "-.E1E1E2", "-.E1E5E2", "-.E1E9E2", "-.E2E0E3", "-.E2E2E3", "-.E2E5E3", "-.E2E8E3", "-.E9E9E4", ".E1E0E0", ".E1E2E0", ".E1E5E0", ".E1E8E0", ".E5E0E0", ".E5E3E0", ".E5E6E0", ".E5E8E0", ".E9E0E0", ".E9E4E0", ".E9E6E0", ".E9E7E0", "-.E1E0E0", "-.E1E2E0", "-.E1E5E0", "-.E1E8E0", "-.E5E0E0", "-.E5E3E0", "-.E5E6E0", "-.E5E8E0", "-.E9E0E0", "-.E9E4E0", "-.E9E6E0", "-.E9E7E0", ".E3E4E1", ".E4E6E1", ".E4E3E1", ".E4E6E1", ".E4E3E1", "-.E3E4E1", "-.E4E6E1", "-.E4E3E1", "-.E4E6E1", "-.E4E3E1"]);
		check_numeric_format(".0E-0\\E", numbers,
				[".0E0E", ".1E1E", ".1E2E", ".2E2E", ".2E2E", ".2E3E", ".2E3E", ".3E3E", ".3E3E", ".1E4E", "-.1E1E", "-.1E2E", "-.2E2E", "-.2E2E", "-.2E3E", "-.2E3E", "-.3E3E", "-.3E3E", "-.1E5E", ".1E0E", ".1E0E", ".2E0E", ".2E0E", ".5E0E", ".5E0E", ".6E0E", ".6E0E", ".9E0E", ".9E0E", ".1E1E", ".1E1E", "-.1E0E", "-.1E0E", "-.2E0E", "-.2E0E", "-.5E0E", "-.5E0E", "-.6E0E", "-.6E0E", "-.9E0E", "-.9E0E", "-.1E1E", "-.1E1E", ".3E1E", ".5E1E", ".4E1E", ".5E1E", ".4E1E", "-.3E1E", "-.5E1E", "-.4E1E", "-.5E1E", "-.4E1E"]);
		check_numeric_format(".0\\EE-0", numbers,
				[".0EE0", ".1EE1", ".1EE2", ".2EE2", ".2EE2", ".2EE3", ".2EE3", ".3EE3", ".3EE3", ".1EE4", "-.1EE1", "-.1EE2", "-.2EE2", "-.2EE2", "-.2EE3", "-.2EE3", "-.3EE3", "-.3EE3", "-.1EE5", ".1EE0", ".1EE0", ".2EE0", ".2EE0", ".5EE0", ".5EE0", ".6EE0", ".6EE0", ".9EE0", ".9EE0", ".1EE1", ".1EE1", "-.1EE0", "-.1EE0", "-.2EE0", "-.2EE0", "-.5EE0", "-.5EE0", "-.6EE0", "-.6EE0", "-.9EE0", "-.9EE0", "-.1EE1", "-.1EE1", ".3EE1", ".5EE1", ".4EE1", ".5EE1", ".4EE1", "-.3EE1", "-.5EE1", "-.4EE1", "-.5EE1", "-.4EE1"]);
		check_numeric_format(".0\\EE-0\\E", numbers,
				[".0EE0E", ".1EE1E", ".1EE2E", ".2EE2E", ".2EE2E", ".2EE3E", ".2EE3E", ".3EE3E", ".3EE3E", ".1EE4E", "-.1EE1E", "-.1EE2E", "-.2EE2E", "-.2EE2E", "-.2EE3E", "-.2EE3E", "-.3EE3E", "-.3EE3E", "-.1EE5E", ".1EE0E", ".1EE0E", ".2EE0E", ".2EE0E", ".5EE0E", ".5EE0E", ".6EE0E", ".6EE0E", ".9EE0E", ".9EE0E", ".1EE1E", ".1EE1E", "-.1EE0E", "-.1EE0E", "-.2EE0E", "-.2EE0E", "-.5EE0E", "-.5EE0E", "-.6EE0E", "-.6EE0E", "-.9EE0E", "-.9EE0E", "-.1EE1E", "-.1EE1E", ".3EE1E", ".5EE1E", ".4EE1E", ".5EE1E", ".4EE1E", "-.3EE1E", "-.5EE1E", "-.4EE1E", "-.5EE1E", "-.4EE1E"]);
		check_numeric_format(".0\"E+\"0E-0", numbers,
				[".0E+0E0", ".1E+0E1", ".1E+1E2", ".1E+5E2", ".1E+9E2", ".2E+0E3", ".2E+2E3", ".2E+5E3", ".2E+8E3", ".1E+2E4", "-.1E+0E1", "-.1E+1E2", "-.1E+5E2", "-.1E+9E2", "-.2E+0E3", "-.2E+2E3", "-.2E+5E3", "-.2E+8E3", "-.9E+9E4", ".1E+0E0", ".1E+2E0", ".1E+5E0", ".1E+8E0", ".5E+0E0", ".5E+3E0", ".5E+6E0", ".5E+8E0", ".9E+0E0", ".9E+4E0", ".9E+6E0", ".9E+7E0", "-.1E+0E0", "-.1E+2E0", "-.1E+5E0", "-.1E+8E0", "-.5E+0E0", "-.5E+3E0", "-.5E+6E0", "-.5E+8E0", "-.9E+0E0", "-.9E+4E0", "-.9E+6E0", "-.9E+7E0", ".3E+4E1", ".4E+6E1", ".4E+3E1", ".4E+6E1", ".4E+3E1", "-.3E+4E1", "-.4E+6E1", "-.4E+3E1", "-.4E+6E1", "-.4E+3E1"]);
		check_numeric_format(".\"E+\"00E-0", numbers,
				[".E+00E0", ".E+10E1", ".E+11E2", ".E+15E2", ".E+19E2", ".E+20E3", ".E+22E3", ".E+25E3", ".E+28E3", ".E+12E4", "-.E+10E1", "-.E+11E2", "-.E+15E2", "-.E+19E2", "-.E+20E3", "-.E+22E3", "-.E+25E3", "-.E+28E3", "-.E+99E4", ".E+10E0", ".E+12E0", ".E+15E0", ".E+18E0", ".E+50E0", ".E+53E0", ".E+56E0", ".E+58E0", ".E+90E0", ".E+94E0", ".E+96E0", ".E+97E0", "-.E+10E0", "-.E+12E0", "-.E+15E0", "-.E+18E0", "-.E+50E0", "-.E+53E0", "-.E+56E0", "-.E+58E0", "-.E+90E0", "-.E+94E0", "-.E+96E0", "-.E+97E0", ".E+34E1", ".E+46E1", ".E+43E1", ".E+46E1", ".E+43E1", "-.E+34E1", "-.E+46E1", "-.E+43E1", "-.E+46E1", "-.E+43E1"]);
		check_numeric_format(".\"E+\"0\"E+\"0E-0", numbers,
				[".E+0E+0E0", ".E+1E+0E1", ".E+1E+1E2", ".E+1E+5E2", ".E+1E+9E2", ".E+2E+0E3", ".E+2E+2E3", ".E+2E+5E3", ".E+2E+8E3", ".E+1E+2E4", "-.E+1E+0E1", "-.E+1E+1E2", "-.E+1E+5E2", "-.E+1E+9E2", "-.E+2E+0E3", "-.E+2E+2E3", "-.E+2E+5E3", "-.E+2E+8E3", "-.E+9E+9E4", ".E+1E+0E0", ".E+1E+2E0", ".E+1E+5E0", ".E+1E+8E0", ".E+5E+0E0", ".E+5E+3E0", ".E+5E+6E0", ".E+5E+8E0", ".E+9E+0E0", ".E+9E+4E0", ".E+9E+6E0", ".E+9E+7E0", "-.E+1E+0E0", "-.E+1E+2E0", "-.E+1E+5E0", "-.E+1E+8E0", "-.E+5E+0E0", "-.E+5E+3E0", "-.E+5E+6E0", "-.E+5E+8E0", "-.E+9E+0E0", "-.E+9E+4E0", "-.E+9E+6E0", "-.E+9E+7E0", ".E+3E+4E1", ".E+4E+6E1", ".E+4E+3E1", ".E+4E+6E1", ".E+4E+3E1", "-.E+3E+4E1", "-.E+4E+6E1", "-.E+4E+3E1", "-.E+4E+6E1", "-.E+4E+3E1"]);
		check_numeric_format(".0E-0\"E+\"", numbers,
				[".0E0E+", ".1E1E+", ".1E2E+", ".2E2E+", ".2E2E+", ".2E3E+", ".2E3E+", ".3E3E+", ".3E3E+", ".1E4E+", "-.1E1E+", "-.1E2E+", "-.2E2E+", "-.2E2E+", "-.2E3E+", "-.2E3E+", "-.3E3E+", "-.3E3E+", "-.1E5E+", ".1E0E+", ".1E0E+", ".2E0E+", ".2E0E+", ".5E0E+", ".5E0E+", ".6E0E+", ".6E0E+", ".9E0E+", ".9E0E+", ".1E1E+", ".1E1E+", "-.1E0E+", "-.1E0E+", "-.2E0E+", "-.2E0E+", "-.5E0E+", "-.5E0E+", "-.6E0E+", "-.6E0E+", "-.9E0E+", "-.9E0E+", "-.1E1E+", "-.1E1E+", ".3E1E+", ".5E1E+", ".4E1E+", ".5E1E+", ".4E1E+", "-.3E1E+", "-.5E1E+", "-.4E1E+", "-.5E1E+", "-.4E1E+"]);
		check_numeric_format(".0\\E\"E+\"-0", numbers,
				[".0EE+0", ".1EE+1", ".1EE+2", ".2EE+2", ".2EE+2", ".2EE+3", ".2EE+3", ".3EE+3", ".3EE+3", ".1EE+4", "-.1EE+1", "-.1EE+2", "-.2EE+2", "-.2EE+2", "-.2EE+3", "-.2EE+3", "-.3EE+3", "-.3EE+3", "-.1EE+5", ".1EE+0", ".1EE+0", ".2EE+0", ".2EE+0", ".5EE+0", ".5EE+0", ".6EE+0", ".6EE+0", ".9EE+0", ".9EE+0", ".1EE+1", ".1EE+1", "-.1EE+0", "-.1EE+0", "-.2EE+0", "-.2EE+0", "-.5EE+0", "-.5EE+0", "-.6EE+0", "-.6EE+0", "-.9EE+0", "-.9EE+0", "-.1EE+1", "-.1EE+1", ".3EE+1", ".5EE+1", ".4EE+1", ".5EE+1", ".4EE+1", "-.3EE+1", "-.5EE+1", "-.4EE+1", "-.5EE+1", "-.4EE+1"]);
		check_numeric_format(".0\\E\"E+\"-0\"E+\"", numbers,
				[".0EE+0E+", ".1EE+1E+", ".1EE+2E+", ".2EE+2E+", ".2EE+2E+", ".2EE+3E+", ".2EE+3E+", ".3EE+3E+", ".3EE+3E+", ".1EE+4E+", "-.1EE+1E+", "-.1EE+2E+", "-.2EE+2E+", "-.2EE+2E+", "-.2EE+3E+", "-.2EE+3E+", "-.3EE+3E+", "-.3EE+3E+", "-.1EE+5E+", ".1EE+0E+", ".1EE+0E+", ".2EE+0E+", ".2EE+0E+", ".5EE+0E+", ".5EE+0E+", ".6EE+0E+", ".6EE+0E+", ".9EE+0E+", ".9EE+0E+", ".1EE+1E+", ".1EE+1E+", "-.1EE+0E+", "-.1EE+0E+", "-.2EE+0E+", "-.2EE+0E+", "-.5EE+0E+", "-.5EE+0E+", "-.6EE+0E+", "-.6EE+0E+", "-.9EE+0E+", "-.9EE+0E+", "-.1EE+1E+", "-.1EE+1E+", ".3EE+1E+", ".5EE+1E+", ".4EE+1E+", ".5EE+1E+", ".4EE+1E+", "-.3EE+1E+", "-.5EE+1E+", "-.4EE+1E+", "-.5EE+1E+", "-.4EE+1E+"]);
		check_numeric_format(".0\\.0E+0", numbers,
				[".0.0E+0", ".1.0E+1", ".1.1E+2", ".1.5E+2", ".1.9E+2", ".2.0E+3", ".2.2E+3", ".2.5E+3", ".2.8E+3", ".1.2E+4", "-.1.0E+1", "-.1.1E+2", "-.1.5E+2", "-.1.9E+2", "-.2.0E+3", "-.2.2E+3", "-.2.5E+3", "-.2.8E+3", "-.9.9E+4", ".1.0E+0", ".1.2E+0", ".1.5E+0", ".1.8E+0", ".5.0E+0", ".5.3E+0", ".5.6E+0", ".5.8E+0", ".9.0E+0", ".9.4E+0", ".9.6E+0", ".9.7E+0", "-.1.0E+0", "-.1.2E+0", "-.1.5E+0", "-.1.8E+0", "-.5.0E+0", "-.5.3E+0", "-.5.6E+0", "-.5.8E+0", "-.9.0E+0", "-.9.4E+0", "-.9.6E+0", "-.9.7E+0", ".3.4E+1", ".4.6E+1", ".4.3E+1", ".4.6E+1", ".4.3E+1", "-.3.4E+1", "-.4.6E+1", "-.4.3E+1", "-.4.6E+1", "-.4.3E+1"]);
		check_numeric_format(".\\.00E+0", numbers,
				["..00E+0", "..10E+1", "..11E+2", "..15E+2", "..19E+2", "..20E+3", "..22E+3", "..25E+3", "..28E+3", "..12E+4", "-..10E+1", "-..11E+2", "-..15E+2", "-..19E+2", "-..20E+3", "-..22E+3", "-..25E+3", "-..28E+3", "-..99E+4", "..10E+0", "..12E+0", "..15E+0", "..18E+0", "..50E+0", "..53E+0", "..56E+0", "..58E+0", "..90E+0", "..94E+0", "..96E+0", "..97E+0", "-..10E+0", "-..12E+0", "-..15E+0", "-..18E+0", "-..50E+0", "-..53E+0", "-..56E+0", "-..58E+0", "-..90E+0", "-..94E+0", "-..96E+0", "-..97E+0", "..34E+1", "..46E+1", "..43E+1", "..46E+1", "..43E+1", "-..34E+1", "-..46E+1", "-..43E+1", "-..46E+1", "-..43E+1"]);
		check_numeric_format(".\\.0\\.0E+0", numbers,
				["..0.0E+0", "..1.0E+1", "..1.1E+2", "..1.5E+2", "..1.9E+2", "..2.0E+3", "..2.2E+3", "..2.5E+3", "..2.8E+3", "..1.2E+4", "-..1.0E+1", "-..1.1E+2", "-..1.5E+2", "-..1.9E+2", "-..2.0E+3", "-..2.2E+3", "-..2.5E+3", "-..2.8E+3", "-..9.9E+4", "..1.0E+0", "..1.2E+0", "..1.5E+0", "..1.8E+0", "..5.0E+0", "..5.3E+0", "..5.6E+0", "..5.8E+0", "..9.0E+0", "..9.4E+0", "..9.6E+0", "..9.7E+0", "-..1.0E+0", "-..1.2E+0", "-..1.5E+0", "-..1.8E+0", "-..5.0E+0", "-..5.3E+0", "-..5.6E+0", "-..5.8E+0", "-..9.0E+0", "-..9.4E+0", "-..9.6E+0", "-..9.7E+0", "..3.4E+1", "..4.6E+1", "..4.3E+1", "..4.6E+1", "..4.3E+1", "-..3.4E+1", "-..4.6E+1", "-..4.3E+1", "-..4.6E+1", "-..4.3E+1"]);
		check_numeric_format(".0E+0\\.", numbers,
				[".0E+0.", ".1E+1.", ".1E+2.", ".2E+2.", ".2E+2.", ".2E+3.", ".2E+3.", ".3E+3.", ".3E+3.", ".1E+4.", "-.1E+1.", "-.1E+2.", "-.2E+2.", "-.2E+2.", "-.2E+3.", "-.2E+3.", "-.3E+3.", "-.3E+3.", "-.1E+5.", ".1E+0.", ".1E+0.", ".2E+0.", ".2E+0.", ".5E+0.", ".5E+0.", ".6E+0.", ".6E+0.", ".9E+0.", ".9E+0.", ".1E+1.", ".1E+1.", "-.1E+0.", "-.1E+0.", "-.2E+0.", "-.2E+0.", "-.5E+0.", "-.5E+0.", "-.6E+0.", "-.6E+0.", "-.9E+0.", "-.9E+0.", "-.1E+1.", "-.1E+1.", ".3E+1.", ".5E+1.", ".4E+1.", ".5E+1.", ".4E+1.", "-.3E+1.", "-.5E+1.", "-.4E+1.", "-.5E+1.", "-.4E+1."]);
		check_numeric_format(".0\\E.+0", numbers,
				[".0E.+0", ".1E.+1", ".1E.+2", ".2E.+2", ".2E.+2", ".2E.+3", ".2E.+3", ".3E.+3", ".3E.+3", ".1E.+4", "-.1E.+1", "-.1E.+2", "-.2E.+2", "-.2E.+2", "-.2E.+3", "-.2E.+3", "-.3E.+3", "-.3E.+3", "-.1E.+5", ".1E.+0", ".1E.+0", ".2E.+0", ".2E.+0", ".5E.+0", ".5E.+0", ".6E.+0", ".6E.+0", ".9E.+0", ".9E.+0", ".1E.+1", ".1E.+1", "-.1E.+0", "-.1E.+0", "-.2E.+0", "-.2E.+0", "-.5E.+0", "-.5E.+0", "-.6E.+0", "-.6E.+0", "-.9E.+0", "-.9E.+0", "-.1E.+1", "-.1E.+1", ".3E.+1", ".5E.+1", ".4E.+1", ".5E.+1", ".4E.+1", "-.3E.+1", "-.5E.+1", "-.4E.+1", "-.5E.+1", "-.4E.+1"]);
		check_numeric_format(".0\\E.+0\\.", numbers,
				[".0E.+0.", ".1E.+1.", ".1E.+2.", ".2E.+2.", ".2E.+2.", ".2E.+3.", ".2E.+3.", ".3E.+3.", ".3E.+3.", ".1E.+4.", "-.1E.+1.", "-.1E.+2.", "-.2E.+2.", "-.2E.+2.", "-.2E.+3.", "-.2E.+3.", "-.3E.+3.", "-.3E.+3.", "-.1E.+5.", ".1E.+0.", ".1E.+0.", ".2E.+0.", ".2E.+0.", ".5E.+0.", ".5E.+0.", ".6E.+0.", ".6E.+0.", ".9E.+0.", ".9E.+0.", ".1E.+1.", ".1E.+1.", "-.1E.+0.", "-.1E.+0.", "-.2E.+0.", "-.2E.+0.", "-.5E.+0.", "-.5E.+0.", "-.6E.+0.", "-.6E.+0.", "-.9E.+0.", "-.9E.+0.", "-.1E.+1.", "-.1E.+1.", ".3E.+1.", ".5E.+1.", ".4E.+1.", ".5E.+1.", ".4E.+1.", "-.3E.+1.", "-.5E.+1.", "-.4E.+1.", "-.5E.+1.", "-.4E.+1."]);
		check_numeric_format(".0\\E0E+0", numbers,
				[".0E0E+0", ".1E0E+1", ".1E1E+2", ".1E5E+2", ".1E9E+2", ".2E0E+3", ".2E2E+3", ".2E5E+3", ".2E8E+3", ".1E2E+4", "-.1E0E+1", "-.1E1E+2", "-.1E5E+2", "-.1E9E+2", "-.2E0E+3", "-.2E2E+3", "-.2E5E+3", "-.2E8E+3", "-.9E9E+4", ".1E0E+0", ".1E2E+0", ".1E5E+0", ".1E8E+0", ".5E0E+0", ".5E3E+0", ".5E6E+0", ".5E8E+0", ".9E0E+0", ".9E4E+0", ".9E6E+0", ".9E7E+0", "-.1E0E+0", "-.1E2E+0", "-.1E5E+0", "-.1E8E+0", "-.5E0E+0", "-.5E3E+0", "-.5E6E+0", "-.5E8E+0", "-.9E0E+0", "-.9E4E+0", "-.9E6E+0", "-.9E7E+0", ".3E4E+1", ".4E6E+1", ".4E3E+1", ".4E6E+1", ".4E3E+1", "-.3E4E+1", "-.4E6E+1", "-.4E3E+1", "-.4E6E+1", "-.4E3E+1"]);
		check_numeric_format(".\\E00E+0", numbers,
				[".E00E+0", ".E10E+1", ".E11E+2", ".E15E+2", ".E19E+2", ".E20E+3", ".E22E+3", ".E25E+3", ".E28E+3", ".E12E+4", "-.E10E+1", "-.E11E+2", "-.E15E+2", "-.E19E+2", "-.E20E+3", "-.E22E+3", "-.E25E+3", "-.E28E+3", "-.E99E+4", ".E10E+0", ".E12E+0", ".E15E+0", ".E18E+0", ".E50E+0", ".E53E+0", ".E56E+0", ".E58E+0", ".E90E+0", ".E94E+0", ".E96E+0", ".E97E+0", "-.E10E+0", "-.E12E+0", "-.E15E+0", "-.E18E+0", "-.E50E+0", "-.E53E+0", "-.E56E+0", "-.E58E+0", "-.E90E+0", "-.E94E+0", "-.E96E+0", "-.E97E+0", ".E34E+1", ".E46E+1", ".E43E+1", ".E46E+1", ".E43E+1", "-.E34E+1", "-.E46E+1", "-.E43E+1", "-.E46E+1", "-.E43E+1"]);
		check_numeric_format(".\\E0\\E0E+0", numbers,
				[".E0E0E+0", ".E1E0E+1", ".E1E1E+2", ".E1E5E+2", ".E1E9E+2", ".E2E0E+3", ".E2E2E+3", ".E2E5E+3", ".E2E8E+3", ".E1E2E+4", "-.E1E0E+1", "-.E1E1E+2", "-.E1E5E+2", "-.E1E9E+2", "-.E2E0E+3", "-.E2E2E+3", "-.E2E5E+3", "-.E2E8E+3", "-.E9E9E+4", ".E1E0E+0", ".E1E2E+0", ".E1E5E+0", ".E1E8E+0", ".E5E0E+0", ".E5E3E+0", ".E5E6E+0", ".E5E8E+0", ".E9E0E+0", ".E9E4E+0", ".E9E6E+0", ".E9E7E+0", "-.E1E0E+0", "-.E1E2E+0", "-.E1E5E+0", "-.E1E8E+0", "-.E5E0E+0", "-.E5E3E+0", "-.E5E6E+0", "-.E5E8E+0", "-.E9E0E+0", "-.E9E4E+0", "-.E9E6E+0", "-.E9E7E+0", ".E3E4E+1", ".E4E6E+1", ".E4E3E+1", ".E4E6E+1", ".E4E3E+1", "-.E3E4E+1", "-.E4E6E+1", "-.E4E3E+1", "-.E4E6E+1", "-.E4E3E+1"]);
		check_numeric_format(".0E+0\\E", numbers,
				[".0E+0E", ".1E+1E", ".1E+2E", ".2E+2E", ".2E+2E", ".2E+3E", ".2E+3E", ".3E+3E", ".3E+3E", ".1E+4E", "-.1E+1E", "-.1E+2E", "-.2E+2E", "-.2E+2E", "-.2E+3E", "-.2E+3E", "-.3E+3E", "-.3E+3E", "-.1E+5E", ".1E+0E", ".1E+0E", ".2E+0E", ".2E+0E", ".5E+0E", ".5E+0E", ".6E+0E", ".6E+0E", ".9E+0E", ".9E+0E", ".1E+1E", ".1E+1E", "-.1E+0E", "-.1E+0E", "-.2E+0E", "-.2E+0E", "-.5E+0E", "-.5E+0E", "-.6E+0E", "-.6E+0E", "-.9E+0E", "-.9E+0E", "-.1E+1E", "-.1E+1E", ".3E+1E", ".5E+1E", ".4E+1E", ".5E+1E", ".4E+1E", "-.3E+1E", "-.5E+1E", "-.4E+1E", "-.5E+1E", "-.4E+1E"]);
		check_numeric_format(".0\\EE+0", numbers,
				[".0EE+0", ".1EE+1", ".1EE+2", ".2EE+2", ".2EE+2", ".2EE+3", ".2EE+3", ".3EE+3", ".3EE+3", ".1EE+4", "-.1EE+1", "-.1EE+2", "-.2EE+2", "-.2EE+2", "-.2EE+3", "-.2EE+3", "-.3EE+3", "-.3EE+3", "-.1EE+5", ".1EE+0", ".1EE+0", ".2EE+0", ".2EE+0", ".5EE+0", ".5EE+0", ".6EE+0", ".6EE+0", ".9EE+0", ".9EE+0", ".1EE+1", ".1EE+1", "-.1EE+0", "-.1EE+0", "-.2EE+0", "-.2EE+0", "-.5EE+0", "-.5EE+0", "-.6EE+0", "-.6EE+0", "-.9EE+0", "-.9EE+0", "-.1EE+1", "-.1EE+1", ".3EE+1", ".5EE+1", ".4EE+1", ".5EE+1", ".4EE+1", "-.3EE+1", "-.5EE+1", "-.4EE+1", "-.5EE+1", "-.4EE+1"]);
		check_numeric_format(".0\\EE+0\\E", numbers,
				[".0EE+0E", ".1EE+1E", ".1EE+2E", ".2EE+2E", ".2EE+2E", ".2EE+3E", ".2EE+3E", ".3EE+3E", ".3EE+3E", ".1EE+4E", "-.1EE+1E", "-.1EE+2E", "-.2EE+2E", "-.2EE+2E", "-.2EE+3E", "-.2EE+3E", "-.3EE+3E", "-.3EE+3E", "-.1EE+5E", ".1EE+0E", ".1EE+0E", ".2EE+0E", ".2EE+0E", ".5EE+0E", ".5EE+0E", ".6EE+0E", ".6EE+0E", ".9EE+0E", ".9EE+0E", ".1EE+1E", ".1EE+1E", "-.1EE+0E", "-.1EE+0E", "-.2EE+0E", "-.2EE+0E", "-.5EE+0E", "-.5EE+0E", "-.6EE+0E", "-.6EE+0E", "-.9EE+0E", "-.9EE+0E", "-.1EE+1E", "-.1EE+1E", ".3EE+1E", ".5EE+1E", ".4EE+1E", ".5EE+1E", ".4EE+1E", "-.3EE+1E", "-.5EE+1E", "-.4EE+1E", "-.5EE+1E", "-.4EE+1E"]);
		check_numeric_format(".0\"E+\"0E+0", numbers,
				[".0E+0E+0", ".1E+0E+1", ".1E+1E+2", ".1E+5E+2", ".1E+9E+2", ".2E+0E+3", ".2E+2E+3", ".2E+5E+3", ".2E+8E+3", ".1E+2E+4", "-.1E+0E+1", "-.1E+1E+2", "-.1E+5E+2", "-.1E+9E+2", "-.2E+0E+3", "-.2E+2E+3", "-.2E+5E+3", "-.2E+8E+3", "-.9E+9E+4", ".1E+0E+0", ".1E+2E+0", ".1E+5E+0", ".1E+8E+0", ".5E+0E+0", ".5E+3E+0", ".5E+6E+0", ".5E+8E+0", ".9E+0E+0", ".9E+4E+0", ".9E+6E+0", ".9E+7E+0", "-.1E+0E+0", "-.1E+2E+0", "-.1E+5E+0", "-.1E+8E+0", "-.5E+0E+0", "-.5E+3E+0", "-.5E+6E+0", "-.5E+8E+0", "-.9E+0E+0", "-.9E+4E+0", "-.9E+6E+0", "-.9E+7E+0", ".3E+4E+1", ".4E+6E+1", ".4E+3E+1", ".4E+6E+1", ".4E+3E+1", "-.3E+4E+1", "-.4E+6E+1", "-.4E+3E+1", "-.4E+6E+1", "-.4E+3E+1"]);
		check_numeric_format(".\"E+\"00E+0", numbers,
				[".E+00E+0", ".E+10E+1", ".E+11E+2", ".E+15E+2", ".E+19E+2", ".E+20E+3", ".E+22E+3", ".E+25E+3", ".E+28E+3", ".E+12E+4", "-.E+10E+1", "-.E+11E+2", "-.E+15E+2", "-.E+19E+2", "-.E+20E+3", "-.E+22E+3", "-.E+25E+3", "-.E+28E+3", "-.E+99E+4", ".E+10E+0", ".E+12E+0", ".E+15E+0", ".E+18E+0", ".E+50E+0", ".E+53E+0", ".E+56E+0", ".E+58E+0", ".E+90E+0", ".E+94E+0", ".E+96E+0", ".E+97E+0", "-.E+10E+0", "-.E+12E+0", "-.E+15E+0", "-.E+18E+0", "-.E+50E+0", "-.E+53E+0", "-.E+56E+0", "-.E+58E+0", "-.E+90E+0", "-.E+94E+0", "-.E+96E+0", "-.E+97E+0", ".E+34E+1", ".E+46E+1", ".E+43E+1", ".E+46E+1", ".E+43E+1", "-.E+34E+1", "-.E+46E+1", "-.E+43E+1", "-.E+46E+1", "-.E+43E+1"]);
		check_numeric_format(".\"E+\"0\"E+\"0E+0", numbers,
				[".E+0E+0E+0", ".E+1E+0E+1", ".E+1E+1E+2", ".E+1E+5E+2", ".E+1E+9E+2", ".E+2E+0E+3", ".E+2E+2E+3", ".E+2E+5E+3", ".E+2E+8E+3", ".E+1E+2E+4", "-.E+1E+0E+1", "-.E+1E+1E+2", "-.E+1E+5E+2", "-.E+1E+9E+2", "-.E+2E+0E+3", "-.E+2E+2E+3", "-.E+2E+5E+3", "-.E+2E+8E+3", "-.E+9E+9E+4", ".E+1E+0E+0", ".E+1E+2E+0", ".E+1E+5E+0", ".E+1E+8E+0", ".E+5E+0E+0", ".E+5E+3E+0", ".E+5E+6E+0", ".E+5E+8E+0", ".E+9E+0E+0", ".E+9E+4E+0", ".E+9E+6E+0", ".E+9E+7E+0", "-.E+1E+0E+0", "-.E+1E+2E+0", "-.E+1E+5E+0", "-.E+1E+8E+0", "-.E+5E+0E+0", "-.E+5E+3E+0", "-.E+5E+6E+0", "-.E+5E+8E+0", "-.E+9E+0E+0", "-.E+9E+4E+0", "-.E+9E+6E+0", "-.E+9E+7E+0", ".E+3E+4E+1", ".E+4E+6E+1", ".E+4E+3E+1", ".E+4E+6E+1", ".E+4E+3E+1", "-.E+3E+4E+1", "-.E+4E+6E+1", "-.E+4E+3E+1", "-.E+4E+6E+1", "-.E+4E+3E+1"]);
		check_numeric_format(".0E+0\"E+\"", numbers,
				[".0E+0E+", ".1E+1E+", ".1E+2E+", ".2E+2E+", ".2E+2E+", ".2E+3E+", ".2E+3E+", ".3E+3E+", ".3E+3E+", ".1E+4E+", "-.1E+1E+", "-.1E+2E+", "-.2E+2E+", "-.2E+2E+", "-.2E+3E+", "-.2E+3E+", "-.3E+3E+", "-.3E+3E+", "-.1E+5E+", ".1E+0E+", ".1E+0E+", ".2E+0E+", ".2E+0E+", ".5E+0E+", ".5E+0E+", ".6E+0E+", ".6E+0E+", ".9E+0E+", ".9E+0E+", ".1E+1E+", ".1E+1E+", "-.1E+0E+", "-.1E+0E+", "-.2E+0E+", "-.2E+0E+", "-.5E+0E+", "-.5E+0E+", "-.6E+0E+", "-.6E+0E+", "-.9E+0E+", "-.9E+0E+", "-.1E+1E+", "-.1E+1E+", ".3E+1E+", ".5E+1E+", ".4E+1E+", ".5E+1E+", ".4E+1E+", "-.3E+1E+", "-.5E+1E+", "-.4E+1E+", "-.5E+1E+", "-.4E+1E+"]);
		check_numeric_format(".0\\E\"E+\"+0", numbers,
				[".0EE++0", ".1EE++1", ".1EE++2", ".2EE++2", ".2EE++2", ".2EE++3", ".2EE++3", ".3EE++3", ".3EE++3", ".1EE++4", "-.1EE++1", "-.1EE++2", "-.2EE++2", "-.2EE++2", "-.2EE++3", "-.2EE++3", "-.3EE++3", "-.3EE++3", "-.1EE++5", ".1EE++0", ".1EE++0", ".2EE++0", ".2EE++0", ".5EE++0", ".5EE++0", ".6EE++0", ".6EE++0", ".9EE++0", ".9EE++0", ".1EE++1", ".1EE++1", "-.1EE++0", "-.1EE++0", "-.2EE++0", "-.2EE++0", "-.5EE++0", "-.5EE++0", "-.6EE++0", "-.6EE++0", "-.9EE++0", "-.9EE++0", "-.1EE++1", "-.1EE++1", ".3EE++1", ".5EE++1", ".4EE++1", ".5EE++1", ".4EE++1", "-.3EE++1", "-.5EE++1", "-.4EE++1", "-.5EE++1", "-.4EE++1"]);
		check_numeric_format(".0\\E\"E+\"+0\"E+\"", numbers,
				[".0EE++0E+", ".1EE++1E+", ".1EE++2E+", ".2EE++2E+", ".2EE++2E+", ".2EE++3E+", ".2EE++3E+", ".3EE++3E+", ".3EE++3E+", ".1EE++4E+", "-.1EE++1E+", "-.1EE++2E+", "-.2EE++2E+", "-.2EE++2E+", "-.2EE++3E+", "-.2EE++3E+", "-.3EE++3E+", "-.3EE++3E+", "-.1EE++5E+", ".1EE++0E+", ".1EE++0E+", ".2EE++0E+", ".2EE++0E+", ".5EE++0E+", ".5EE++0E+", ".6EE++0E+", ".6EE++0E+", ".9EE++0E+", ".9EE++0E+", ".1EE++1E+", ".1EE++1E+", "-.1EE++0E+", "-.1EE++0E+", "-.2EE++0E+", "-.2EE++0E+", "-.5EE++0E+", "-.5EE++0E+", "-.6EE++0E+", "-.6EE++0E+", "-.9EE++0E+", "-.9EE++0E+", "-.1EE++1E+", "-.1EE++1E+", ".3EE++1E+", ".5EE++1E+", ".4EE++1E+", ".5EE++1E+", ".4EE++1E+", "-.3EE++1E+", "-.5EE++1E+", "-.4EE++1E+", "-.5EE++1E+", "-.4EE++1E+"]);
		check_numeric_format("0\\.0.0E-0", numbers,
				["0.0.0E0", "0.1.0E0", "1.1.0E0", "1.5.0E0", "1.9.0E0", "0.2.0E2", "0.2.2E2", "0.2.5E2", "0.2.8E2", "1.2.3E2", "-0.1.0E0", "-1.1.0E0", "-1.5.0E0", "-1.9.0E0", "-0.2.0E2", "-0.2.2E2", "-0.2.5E2", "-0.2.8E2", "-9.8.8E2", "1.0.0E-2", "1.2.0E-2", "1.5.1E-2", "1.8.1E-2", "5.0.0E-2", "5.3.0E-2", "5.5.5E-2", "5.7.5E-2", "9.0.0E-2", "9.4.0E-2", "9.5.9E-2", "9.6.9E-2", "-1.0.0E-2", "-1.2.0E-2", "-1.5.1E-2", "-1.8.1E-2", "-5.0.0E-2", "-5.3.0E-2", "-5.5.5E-2", "-5.7.5E-2", "-9.0.0E-2", "-9.4.0E-2", "-9.5.9E-2", "-9.6.9E-2", "0.3.4E0", "0.4.6E0", "0.4.3E0", "0.4.6E0", "0.4.3E0", "-0.3.4E0", "-0.4.6E0", "-0.4.3E0", "-0.4.6E0", "-0.4.3E0"]);
		check_numeric_format("\\.00.0E-0", numbers,
				[".00.0E0", ".01.0E0", ".11.0E0", ".15.0E0", ".19.0E0", ".02.0E2", ".02.2E2", ".02.5E2", ".02.8E2", ".12.3E2", "-.01.0E0", "-.11.0E0", "-.15.0E0", "-.19.0E0", "-.02.0E2", "-.02.2E2", "-.02.5E2", "-.02.8E2", "-.98.8E2", ".10.0E-2", ".12.0E-2", ".15.1E-2", ".18.1E-2", ".50.0E-2", ".53.0E-2", ".55.5E-2", ".57.5E-2", ".90.0E-2", ".94.0E-2", ".95.9E-2", ".96.9E-2", "-.10.0E-2", "-.12.0E-2", "-.15.1E-2", "-.18.1E-2", "-.50.0E-2", "-.53.0E-2", "-.55.5E-2", "-.57.5E-2", "-.90.0E-2", "-.94.0E-2", "-.95.9E-2", "-.96.9E-2", ".03.4E0", ".04.6E0", ".04.3E0", ".04.6E0", ".04.3E0", "-.03.4E0", "-.04.6E0", "-.04.3E0", "-.04.6E0", "-.04.3E0"]);
		check_numeric_format("\\.0\\.0.0E-0", numbers,
				[".0.0.0E0", ".0.1.0E0", ".1.1.0E0", ".1.5.0E0", ".1.9.0E0", ".0.2.0E2", ".0.2.2E2", ".0.2.5E2", ".0.2.8E2", ".1.2.3E2", "-.0.1.0E0", "-.1.1.0E0", "-.1.5.0E0", "-.1.9.0E0", "-.0.2.0E2", "-.0.2.2E2", "-.0.2.5E2", "-.0.2.8E2", "-.9.8.8E2", ".1.0.0E-2", ".1.2.0E-2", ".1.5.1E-2", ".1.8.1E-2", ".5.0.0E-2", ".5.3.0E-2", ".5.5.5E-2", ".5.7.5E-2", ".9.0.0E-2", ".9.4.0E-2", ".9.5.9E-2", ".9.6.9E-2", "-.1.0.0E-2", "-.1.2.0E-2", "-.1.5.1E-2", "-.1.8.1E-2", "-.5.0.0E-2", "-.5.3.0E-2", "-.5.5.5E-2", "-.5.7.5E-2", "-.9.0.0E-2", "-.9.4.0E-2", "-.9.5.9E-2", "-.9.6.9E-2", ".0.3.4E0", ".0.4.6E0", ".0.4.3E0", ".0.4.6E0", ".0.4.3E0", "-.0.3.4E0", "-.0.4.6E0", "-.0.4.3E0", "-.0.4.6E0", "-.0.4.3E0"]);
		check_numeric_format("0.0\\.0E-0", numbers,
				["0.0.0E0", "1.0.0E0", "1.1.0E1", "1.5.0E1", "1.9.0E1", "2.0.0E2", "2.2.0E2", "2.5.0E2", "2.8.0E2", "1.2.3E3", "-1.0.0E0", "-1.1.0E1", "-1.5.0E1", "-1.9.0E1", "-2.0.0E2", "-2.2.0E2", "-2.5.0E2", "-2.8.0E2", "-9.8.8E3", "1.0.0E-1", "1.2.0E-1", "1.5.1E-1", "1.8.1E-1", "5.0.0E-1", "5.3.0E-1", "5.5.5E-1", "5.7.5E-1", "9.0.0E-1", "9.4.0E-1", "9.5.9E-1", "9.6.9E-1", "-1.0.0E-1", "-1.2.0E-1", "-1.5.1E-1", "-1.8.1E-1", "-5.0.0E-1", "-5.3.0E-1", "-5.5.5E-1", "-5.7.5E-1", "-9.0.0E-1", "-9.4.0E-1", "-9.5.9E-1", "-9.6.9E-1", "3.4.0E0", "4.5.6E0", "4.3.2E0", "4.5.7E0", "4.3.2E0", "-3.4.0E0", "-4.5.6E0", "-4.3.2E0", "-4.5.7E0", "-4.3.2E0"]);
		check_numeric_format("0.\\.00E-0", numbers,
				["0..00E0", "1..00E0", "1..10E1", "1..50E1", "1..90E1", "2..00E2", "2..20E2", "2..50E2", "2..80E2", "1..23E3", "-1..00E0", "-1..10E1", "-1..50E1", "-1..90E1", "-2..00E2", "-2..20E2", "-2..50E2", "-2..80E2", "-9..88E3", "1..00E-1", "1..20E-1", "1..51E-1", "1..81E-1", "5..00E-1", "5..30E-1", "5..55E-1", "5..75E-1", "9..00E-1", "9..40E-1", "9..59E-1", "9..69E-1", "-1..00E-1", "-1..20E-1", "-1..51E-1", "-1..81E-1", "-5..00E-1", "-5..30E-1", "-5..55E-1", "-5..75E-1", "-9..00E-1", "-9..40E-1", "-9..59E-1", "-9..69E-1", "3..40E0", "4..56E0", "4..32E0", "4..57E0", "4..32E0", "-3..40E0", "-4..56E0", "-4..32E0", "-4..57E0", "-4..32E0"]);
		check_numeric_format("0.\\.0\\.0E-0", numbers,
				["0..0.0E0", "1..0.0E0", "1..1.0E1", "1..5.0E1", "1..9.0E1", "2..0.0E2", "2..2.0E2", "2..5.0E2", "2..8.0E2", "1..2.3E3", "-1..0.0E0", "-1..1.0E1", "-1..5.0E1", "-1..9.0E1", "-2..0.0E2", "-2..2.0E2", "-2..5.0E2", "-2..8.0E2", "-9..8.8E3", "1..0.0E-1", "1..2.0E-1", "1..5.1E-1", "1..8.1E-1", "5..0.0E-1", "5..3.0E-1", "5..5.5E-1", "5..7.5E-1", "9..0.0E-1", "9..4.0E-1", "9..5.9E-1", "9..6.9E-1", "-1..0.0E-1", "-1..2.0E-1", "-1..5.1E-1", "-1..8.1E-1", "-5..0.0E-1", "-5..3.0E-1", "-5..5.5E-1", "-5..7.5E-1", "-9..0.0E-1", "-9..4.0E-1", "-9..5.9E-1", "-9..6.9E-1", "3..4.0E0", "4..5.6E0", "4..3.2E0", "4..5.7E0", "4..3.2E0", "-3..4.0E0", "-4..5.6E0", "-4..3.2E0", "-4..5.7E0", "-4..3.2E0"]);
		check_numeric_format("0.0E-0\\.", numbers,
				["0.0E0.", "1.0E0.", "1.1E1.", "1.5E1.", "1.9E1.", "2.0E2.", "2.2E2.", "2.5E2.", "2.8E2.", "1.2E3.", "-1.0E0.", "-1.1E1.", "-1.5E1.", "-1.9E1.", "-2.0E2.", "-2.2E2.", "-2.5E2.", "-2.8E2.", "-9.9E3.", "1.0E-1.", "1.2E-1.", "1.5E-1.", "1.8E-1.", "5.0E-1.", "5.3E-1.", "5.6E-1.", "5.8E-1.", "9.0E-1.", "9.4E-1.", "9.6E-1.", "9.7E-1.", "-1.0E-1.", "-1.2E-1.", "-1.5E-1.", "-1.8E-1.", "-5.0E-1.", "-5.3E-1.", "-5.6E-1.", "-5.8E-1.", "-9.0E-1.", "-9.4E-1.", "-9.6E-1.", "-9.7E-1.", "3.4E0.", "4.6E0.", "4.3E0.", "4.6E0.", "4.3E0.", "-3.4E0.", "-4.6E0.", "-4.3E0.", "-4.6E0.", "-4.3E0."]);
		check_numeric_format("0.0\\E.-0", numbers,
				["0.0E.0", "1.0E.0", "1.1E.1", "1.5E.1", "1.9E.1", "2.0E.2", "2.2E.2", "2.5E.2", "2.8E.2", "1.2E.3", "-1.0E.0", "-1.1E.1", "-1.5E.1", "-1.9E.1", "-2.0E.2", "-2.2E.2", "-2.5E.2", "-2.8E.2", "-9.9E.3", "1.0E.-1", "1.2E.-1", "1.5E.-1", "1.8E.-1", "5.0E.-1", "5.3E.-1", "5.6E.-1", "5.8E.-1", "9.0E.-1", "9.4E.-1", "9.6E.-1", "9.7E.-1", "-1.0E.-1", "-1.2E.-1", "-1.5E.-1", "-1.8E.-1", "-5.0E.-1", "-5.3E.-1", "-5.6E.-1", "-5.8E.-1", "-9.0E.-1", "-9.4E.-1", "-9.6E.-1", "-9.7E.-1", "3.4E.0", "4.6E.0", "4.3E.0", "4.6E.0", "4.3E.0", "-3.4E.0", "-4.6E.0", "-4.3E.0", "-4.6E.0", "-4.3E.0"]);
		check_numeric_format("0.0\\E.-0\\.", numbers,
				["0.0E.0.", "1.0E.0.", "1.1E.1.", "1.5E.1.", "1.9E.1.", "2.0E.2.", "2.2E.2.", "2.5E.2.", "2.8E.2.", "1.2E.3.", "-1.0E.0.", "-1.1E.1.", "-1.5E.1.", "-1.9E.1.", "-2.0E.2.", "-2.2E.2.", "-2.5E.2.", "-2.8E.2.", "-9.9E.3.", "1.0E.-1.", "1.2E.-1.", "1.5E.-1.", "1.8E.-1.", "5.0E.-1.", "5.3E.-1.", "5.6E.-1.", "5.8E.-1.", "9.0E.-1.", "9.4E.-1.", "9.6E.-1.", "9.7E.-1.", "-1.0E.-1.", "-1.2E.-1.", "-1.5E.-1.", "-1.8E.-1.", "-5.0E.-1.", "-5.3E.-1.", "-5.6E.-1.", "-5.8E.-1.", "-9.0E.-1.", "-9.4E.-1.", "-9.6E.-1.", "-9.7E.-1.", "3.4E.0.", "4.6E.0.", "4.3E.0.", "4.6E.0.", "4.3E.0.", "-3.4E.0.", "-4.6E.0.", "-4.3E.0.", "-4.6E.0.", "-4.3E.0."]);
		check_numeric_format("0\\E0.0E-0", numbers,
				["0E0.0E0", "0E1.0E0", "1E1.0E0", "1E5.0E0", "1E9.0E0", "0E2.0E2", "0E2.2E2", "0E2.5E2", "0E2.8E2", "1E2.3E2", "-0E1.0E0", "-1E1.0E0", "-1E5.0E0", "-1E9.0E0", "-0E2.0E2", "-0E2.2E2", "-0E2.5E2", "-0E2.8E2", "-9E8.8E2", "1E0.0E-2", "1E2.0E-2", "1E5.1E-2", "1E8.1E-2", "5E0.0E-2", "5E3.0E-2", "5E5.5E-2", "5E7.5E-2", "9E0.0E-2", "9E4.0E-2", "9E5.9E-2", "9E6.9E-2", "-1E0.0E-2", "-1E2.0E-2", "-1E5.1E-2", "-1E8.1E-2", "-5E0.0E-2", "-5E3.0E-2", "-5E5.5E-2", "-5E7.5E-2", "-9E0.0E-2", "-9E4.0E-2", "-9E5.9E-2", "-9E6.9E-2", "0E3.4E0", "0E4.6E0", "0E4.3E0", "0E4.6E0", "0E4.3E0", "-0E3.4E0", "-0E4.6E0", "-0E4.3E0", "-0E4.6E0", "-0E4.3E0"]);
		check_numeric_format("\\E00.0E-0", numbers,
				["E00.0E0", "E01.0E0", "E11.0E0", "E15.0E0", "E19.0E0", "E02.0E2", "E02.2E2", "E02.5E2", "E02.8E2", "E12.3E2", "-E01.0E0", "-E11.0E0", "-E15.0E0", "-E19.0E0", "-E02.0E2", "-E02.2E2", "-E02.5E2", "-E02.8E2", "-E98.8E2", "E10.0E-2", "E12.0E-2", "E15.1E-2", "E18.1E-2", "E50.0E-2", "E53.0E-2", "E55.5E-2", "E57.5E-2", "E90.0E-2", "E94.0E-2", "E95.9E-2", "E96.9E-2", "-E10.0E-2", "-E12.0E-2", "-E15.1E-2", "-E18.1E-2", "-E50.0E-2", "-E53.0E-2", "-E55.5E-2", "-E57.5E-2", "-E90.0E-2", "-E94.0E-2", "-E95.9E-2", "-E96.9E-2", "E03.4E0", "E04.6E0", "E04.3E0", "E04.6E0", "E04.3E0", "-E03.4E0", "-E04.6E0", "-E04.3E0", "-E04.6E0", "-E04.3E0"]);
		check_numeric_format("\\E0\\E0.0E-0", numbers,
				["E0E0.0E0", "E0E1.0E0", "E1E1.0E0", "E1E5.0E0", "E1E9.0E0", "E0E2.0E2", "E0E2.2E2", "E0E2.5E2", "E0E2.8E2", "E1E2.3E2", "-E0E1.0E0", "-E1E1.0E0", "-E1E5.0E0", "-E1E9.0E0", "-E0E2.0E2", "-E0E2.2E2", "-E0E2.5E2", "-E0E2.8E2", "-E9E8.8E2", "E1E0.0E-2", "E1E2.0E-2", "E1E5.1E-2", "E1E8.1E-2", "E5E0.0E-2", "E5E3.0E-2", "E5E5.5E-2", "E5E7.5E-2", "E9E0.0E-2", "E9E4.0E-2", "E9E5.9E-2", "E9E6.9E-2", "-E1E0.0E-2", "-E1E2.0E-2", "-E1E5.1E-2", "-E1E8.1E-2", "-E5E0.0E-2", "-E5E3.0E-2", "-E5E5.5E-2", "-E5E7.5E-2", "-E9E0.0E-2", "-E9E4.0E-2", "-E9E5.9E-2", "-E9E6.9E-2", "E0E3.4E0", "E0E4.6E0", "E0E4.3E0", "E0E4.6E0", "E0E4.3E0", "-E0E3.4E0", "-E0E4.6E0", "-E0E4.3E0", "-E0E4.6E0", "-E0E4.3E0"]);
		check_numeric_format("0.0\\E0E-0", numbers,
				["0.0E0E0", "1.0E0E0", "1.1E0E1", "1.5E0E1", "1.9E0E1", "2.0E0E2", "2.2E0E2", "2.5E0E2", "2.8E0E2", "1.2E3E3", "-1.0E0E0", "-1.1E0E1", "-1.5E0E1", "-1.9E0E1", "-2.0E0E2", "-2.2E0E2", "-2.5E0E2", "-2.8E0E2", "-9.8E8E3", "1.0E0E-1", "1.2E0E-1", "1.5E1E-1", "1.8E1E-1", "5.0E0E-1", "5.3E0E-1", "5.5E5E-1", "5.7E5E-1", "9.0E0E-1", "9.4E0E-1", "9.5E9E-1", "9.6E9E-1", "-1.0E0E-1", "-1.2E0E-1", "-1.5E1E-1", "-1.8E1E-1", "-5.0E0E-1", "-5.3E0E-1", "-5.5E5E-1", "-5.7E5E-1", "-9.0E0E-1", "-9.4E0E-1", "-9.5E9E-1", "-9.6E9E-1", "3.4E0E0", "4.5E6E0", "4.3E2E0", "4.5E7E0", "4.3E2E0", "-3.4E0E0", "-4.5E6E0", "-4.3E2E0", "-4.5E7E0", "-4.3E2E0"]);
		check_numeric_format("0.\\E00E-0", numbers,
				["0.E00E0", "1.E00E0", "1.E10E1", "1.E50E1", "1.E90E1", "2.E00E2", "2.E20E2", "2.E50E2", "2.E80E2", "1.E23E3", "-1.E00E0", "-1.E10E1", "-1.E50E1", "-1.E90E1", "-2.E00E2", "-2.E20E2", "-2.E50E2", "-2.E80E2", "-9.E88E3", "1.E00E-1", "1.E20E-1", "1.E51E-1", "1.E81E-1", "5.E00E-1", "5.E30E-1", "5.E55E-1", "5.E75E-1", "9.E00E-1", "9.E40E-1", "9.E59E-1", "9.E69E-1", "-1.E00E-1", "-1.E20E-1", "-1.E51E-1", "-1.E81E-1", "-5.E00E-1", "-5.E30E-1", "-5.E55E-1", "-5.E75E-1", "-9.E00E-1", "-9.E40E-1", "-9.E59E-1", "-9.E69E-1", "3.E40E0", "4.E56E0", "4.E32E0", "4.E57E0", "4.E32E0", "-3.E40E0", "-4.E56E0", "-4.E32E0", "-4.E57E0", "-4.E32E0"]);
		check_numeric_format("0.\\E0\\E0E-0", numbers,
				["0.E0E0E0", "1.E0E0E0", "1.E1E0E1", "1.E5E0E1", "1.E9E0E1", "2.E0E0E2", "2.E2E0E2", "2.E5E0E2", "2.E8E0E2", "1.E2E3E3", "-1.E0E0E0", "-1.E1E0E1", "-1.E5E0E1", "-1.E9E0E1", "-2.E0E0E2", "-2.E2E0E2", "-2.E5E0E2", "-2.E8E0E2", "-9.E8E8E3", "1.E0E0E-1", "1.E2E0E-1", "1.E5E1E-1", "1.E8E1E-1", "5.E0E0E-1", "5.E3E0E-1", "5.E5E5E-1", "5.E7E5E-1", "9.E0E0E-1", "9.E4E0E-1", "9.E5E9E-1", "9.E6E9E-1", "-1.E0E0E-1", "-1.E2E0E-1", "-1.E5E1E-1", "-1.E8E1E-1", "-5.E0E0E-1", "-5.E3E0E-1", "-5.E5E5E-1", "-5.E7E5E-1", "-9.E0E0E-1", "-9.E4E0E-1", "-9.E5E9E-1", "-9.E6E9E-1", "3.E4E0E0", "4.E5E6E0", "4.E3E2E0", "4.E5E7E0", "4.E3E2E0", "-3.E4E0E0", "-4.E5E6E0", "-4.E3E2E0", "-4.E5E7E0", "-4.E3E2E0"]);
		check_numeric_format("0.0E-0\\E", numbers,
				["0.0E0E", "1.0E0E", "1.1E1E", "1.5E1E", "1.9E1E", "2.0E2E", "2.2E2E", "2.5E2E", "2.8E2E", "1.2E3E", "-1.0E0E", "-1.1E1E", "-1.5E1E", "-1.9E1E", "-2.0E2E", "-2.2E2E", "-2.5E2E", "-2.8E2E", "-9.9E3E", "1.0E-1E", "1.2E-1E", "1.5E-1E", "1.8E-1E", "5.0E-1E", "5.3E-1E", "5.6E-1E", "5.8E-1E", "9.0E-1E", "9.4E-1E", "9.6E-1E", "9.7E-1E", "-1.0E-1E", "-1.2E-1E", "-1.5E-1E", "-1.8E-1E", "-5.0E-1E", "-5.3E-1E", "-5.6E-1E", "-5.8E-1E", "-9.0E-1E", "-9.4E-1E", "-9.6E-1E", "-9.7E-1E", "3.4E0E", "4.6E0E", "4.3E0E", "4.6E0E", "4.3E0E", "-3.4E0E", "-4.6E0E", "-4.3E0E", "-4.6E0E", "-4.3E0E"]);
		check_numeric_format("0.0\\EE-0", numbers,
				["0.0EE0", "1.0EE0", "1.1EE1", "1.5EE1", "1.9EE1", "2.0EE2", "2.2EE2", "2.5EE2", "2.8EE2", "1.2EE3", "-1.0EE0", "-1.1EE1", "-1.5EE1", "-1.9EE1", "-2.0EE2", "-2.2EE2", "-2.5EE2", "-2.8EE2", "-9.9EE3", "1.0EE-1", "1.2EE-1", "1.5EE-1", "1.8EE-1", "5.0EE-1", "5.3EE-1", "5.6EE-1", "5.8EE-1", "9.0EE-1", "9.4EE-1", "9.6EE-1", "9.7EE-1", "-1.0EE-1", "-1.2EE-1", "-1.5EE-1", "-1.8EE-1", "-5.0EE-1", "-5.3EE-1", "-5.6EE-1", "-5.8EE-1", "-9.0EE-1", "-9.4EE-1", "-9.6EE-1", "-9.7EE-1", "3.4EE0", "4.6EE0", "4.3EE0", "4.6EE0", "4.3EE0", "-3.4EE0", "-4.6EE0", "-4.3EE0", "-4.6EE0", "-4.3EE0"]);
		check_numeric_format("0.0\\EE-0\\E", numbers,
				["0.0EE0E", "1.0EE0E", "1.1EE1E", "1.5EE1E", "1.9EE1E", "2.0EE2E", "2.2EE2E", "2.5EE2E", "2.8EE2E", "1.2EE3E", "-1.0EE0E", "-1.1EE1E", "-1.5EE1E", "-1.9EE1E", "-2.0EE2E", "-2.2EE2E", "-2.5EE2E", "-2.8EE2E", "-9.9EE3E", "1.0EE-1E", "1.2EE-1E", "1.5EE-1E", "1.8EE-1E", "5.0EE-1E", "5.3EE-1E", "5.6EE-1E", "5.8EE-1E", "9.0EE-1E", "9.4EE-1E", "9.6EE-1E", "9.7EE-1E", "-1.0EE-1E", "-1.2EE-1E", "-1.5EE-1E", "-1.8EE-1E", "-5.0EE-1E", "-5.3EE-1E", "-5.6EE-1E", "-5.8EE-1E", "-9.0EE-1E", "-9.4EE-1E", "-9.6EE-1E", "-9.7EE-1E", "3.4EE0E", "4.6EE0E", "4.3EE0E", "4.6EE0E", "4.3EE0E", "-3.4EE0E", "-4.6EE0E", "-4.3EE0E", "-4.6EE0E", "-4.3EE0E"]);
		check_numeric_format("0\"E+\"0.0E-0", numbers,
				["0E+0.0E0", "0E+1.0E0", "1E+1.0E0", "1E+5.0E0", "1E+9.0E0", "0E+2.0E2", "0E+2.2E2", "0E+2.5E2", "0E+2.8E2", "1E+2.3E2", "-0E+1.0E0", "-1E+1.0E0", "-1E+5.0E0", "-1E+9.0E0", "-0E+2.0E2", "-0E+2.2E2", "-0E+2.5E2", "-0E+2.8E2", "-9E+8.8E2", "1E+0.0E-2", "1E+2.0E-2", "1E+5.1E-2", "1E+8.1E-2", "5E+0.0E-2", "5E+3.0E-2", "5E+5.5E-2", "5E+7.5E-2", "9E+0.0E-2", "9E+4.0E-2", "9E+5.9E-2", "9E+6.9E-2", "-1E+0.0E-2", "-1E+2.0E-2", "-1E+5.1E-2", "-1E+8.1E-2", "-5E+0.0E-2", "-5E+3.0E-2", "-5E+5.5E-2", "-5E+7.5E-2", "-9E+0.0E-2", "-9E+4.0E-2", "-9E+5.9E-2", "-9E+6.9E-2", "0E+3.4E0", "0E+4.6E0", "0E+4.3E0", "0E+4.6E0", "0E+4.3E0", "-0E+3.4E0", "-0E+4.6E0", "-0E+4.3E0", "-0E+4.6E0", "-0E+4.3E0"]);
		check_numeric_format("\"E+\"00.0E-0", numbers,
				["E+00.0E0", "E+01.0E0", "E+11.0E0", "E+15.0E0", "E+19.0E0", "E+02.0E2", "E+02.2E2", "E+02.5E2", "E+02.8E2", "E+12.3E2", "-E+01.0E0", "-E+11.0E0", "-E+15.0E0", "-E+19.0E0", "-E+02.0E2", "-E+02.2E2", "-E+02.5E2", "-E+02.8E2", "-E+98.8E2", "E+10.0E-2", "E+12.0E-2", "E+15.1E-2", "E+18.1E-2", "E+50.0E-2", "E+53.0E-2", "E+55.5E-2", "E+57.5E-2", "E+90.0E-2", "E+94.0E-2", "E+95.9E-2", "E+96.9E-2", "-E+10.0E-2", "-E+12.0E-2", "-E+15.1E-2", "-E+18.1E-2", "-E+50.0E-2", "-E+53.0E-2", "-E+55.5E-2", "-E+57.5E-2", "-E+90.0E-2", "-E+94.0E-2", "-E+95.9E-2", "-E+96.9E-2", "E+03.4E0", "E+04.6E0", "E+04.3E0", "E+04.6E0", "E+04.3E0", "-E+03.4E0", "-E+04.6E0", "-E+04.3E0", "-E+04.6E0", "-E+04.3E0"]);
		check_numeric_format("\"E+\"0\"E+\"0.0E-0", numbers,
				["E+0E+0.0E0", "E+0E+1.0E0", "E+1E+1.0E0", "E+1E+5.0E0", "E+1E+9.0E0", "E+0E+2.0E2", "E+0E+2.2E2", "E+0E+2.5E2", "E+0E+2.8E2", "E+1E+2.3E2", "-E+0E+1.0E0", "-E+1E+1.0E0", "-E+1E+5.0E0", "-E+1E+9.0E0", "-E+0E+2.0E2", "-E+0E+2.2E2", "-E+0E+2.5E2", "-E+0E+2.8E2", "-E+9E+8.8E2", "E+1E+0.0E-2", "E+1E+2.0E-2", "E+1E+5.1E-2", "E+1E+8.1E-2", "E+5E+0.0E-2", "E+5E+3.0E-2", "E+5E+5.5E-2", "E+5E+7.5E-2", "E+9E+0.0E-2", "E+9E+4.0E-2", "E+9E+5.9E-2", "E+9E+6.9E-2", "-E+1E+0.0E-2", "-E+1E+2.0E-2", "-E+1E+5.1E-2", "-E+1E+8.1E-2", "-E+5E+0.0E-2", "-E+5E+3.0E-2", "-E+5E+5.5E-2", "-E+5E+7.5E-2", "-E+9E+0.0E-2", "-E+9E+4.0E-2", "-E+9E+5.9E-2", "-E+9E+6.9E-2", "E+0E+3.4E0", "E+0E+4.6E0", "E+0E+4.3E0", "E+0E+4.6E0", "E+0E+4.3E0", "-E+0E+3.4E0", "-E+0E+4.6E0", "-E+0E+4.3E0", "-E+0E+4.6E0", "-E+0E+4.3E0"]);
		check_numeric_format("0.0\"E+\"0E-0", numbers,
				["0.0E+0E0", "1.0E+0E0", "1.1E+0E1", "1.5E+0E1", "1.9E+0E1", "2.0E+0E2", "2.2E+0E2", "2.5E+0E2", "2.8E+0E2", "1.2E+3E3", "-1.0E+0E0", "-1.1E+0E1", "-1.5E+0E1", "-1.9E+0E1", "-2.0E+0E2", "-2.2E+0E2", "-2.5E+0E2", "-2.8E+0E2", "-9.8E+8E3", "1.0E+0E-1", "1.2E+0E-1", "1.5E+1E-1", "1.8E+1E-1", "5.0E+0E-1", "5.3E+0E-1", "5.5E+5E-1", "5.7E+5E-1", "9.0E+0E-1", "9.4E+0E-1", "9.5E+9E-1", "9.6E+9E-1", "-1.0E+0E-1", "-1.2E+0E-1", "-1.5E+1E-1", "-1.8E+1E-1", "-5.0E+0E-1", "-5.3E+0E-1", "-5.5E+5E-1", "-5.7E+5E-1", "-9.0E+0E-1", "-9.4E+0E-1", "-9.5E+9E-1", "-9.6E+9E-1", "3.4E+0E0", "4.5E+6E0", "4.3E+2E0", "4.5E+7E0", "4.3E+2E0", "-3.4E+0E0", "-4.5E+6E0", "-4.3E+2E0", "-4.5E+7E0", "-4.3E+2E0"]);
		check_numeric_format("0.\"E+\"00E-0", numbers,
				["0.E+00E0", "1.E+00E0", "1.E+10E1", "1.E+50E1", "1.E+90E1", "2.E+00E2", "2.E+20E2", "2.E+50E2", "2.E+80E2", "1.E+23E3", "-1.E+00E0", "-1.E+10E1", "-1.E+50E1", "-1.E+90E1", "-2.E+00E2", "-2.E+20E2", "-2.E+50E2", "-2.E+80E2", "-9.E+88E3", "1.E+00E-1", "1.E+20E-1", "1.E+51E-1", "1.E+81E-1", "5.E+00E-1", "5.E+30E-1", "5.E+55E-1", "5.E+75E-1", "9.E+00E-1", "9.E+40E-1", "9.E+59E-1", "9.E+69E-1", "-1.E+00E-1", "-1.E+20E-1", "-1.E+51E-1", "-1.E+81E-1", "-5.E+00E-1", "-5.E+30E-1", "-5.E+55E-1", "-5.E+75E-1", "-9.E+00E-1", "-9.E+40E-1", "-9.E+59E-1", "-9.E+69E-1", "3.E+40E0", "4.E+56E0", "4.E+32E0", "4.E+57E0", "4.E+32E0", "-3.E+40E0", "-4.E+56E0", "-4.E+32E0", "-4.E+57E0", "-4.E+32E0"]);
		check_numeric_format("0.\"E+\"0\"E+\"0E-0", numbers,
				["0.E+0E+0E0", "1.E+0E+0E0", "1.E+1E+0E1", "1.E+5E+0E1", "1.E+9E+0E1", "2.E+0E+0E2", "2.E+2E+0E2", "2.E+5E+0E2", "2.E+8E+0E2", "1.E+2E+3E3", "-1.E+0E+0E0", "-1.E+1E+0E1", "-1.E+5E+0E1", "-1.E+9E+0E1", "-2.E+0E+0E2", "-2.E+2E+0E2", "-2.E+5E+0E2", "-2.E+8E+0E2", "-9.E+8E+8E3", "1.E+0E+0E-1", "1.E+2E+0E-1", "1.E+5E+1E-1", "1.E+8E+1E-1", "5.E+0E+0E-1", "5.E+3E+0E-1", "5.E+5E+5E-1", "5.E+7E+5E-1", "9.E+0E+0E-1", "9.E+4E+0E-1", "9.E+5E+9E-1", "9.E+6E+9E-1", "-1.E+0E+0E-1", "-1.E+2E+0E-1", "-1.E+5E+1E-1", "-1.E+8E+1E-1", "-5.E+0E+0E-1", "-5.E+3E+0E-1", "-5.E+5E+5E-1", "-5.E+7E+5E-1", "-9.E+0E+0E-1", "-9.E+4E+0E-1", "-9.E+5E+9E-1", "-9.E+6E+9E-1", "3.E+4E+0E0", "4.E+5E+6E0", "4.E+3E+2E0", "4.E+5E+7E0", "4.E+3E+2E0", "-3.E+4E+0E0", "-4.E+5E+6E0", "-4.E+3E+2E0", "-4.E+5E+7E0", "-4.E+3E+2E0"]);
		check_numeric_format("0.0E-0\"E+\"", numbers,
				["0.0E0E+", "1.0E0E+", "1.1E1E+", "1.5E1E+", "1.9E1E+", "2.0E2E+", "2.2E2E+", "2.5E2E+", "2.8E2E+", "1.2E3E+", "-1.0E0E+", "-1.1E1E+", "-1.5E1E+", "-1.9E1E+", "-2.0E2E+", "-2.2E2E+", "-2.5E2E+", "-2.8E2E+", "-9.9E3E+", "1.0E-1E+", "1.2E-1E+", "1.5E-1E+", "1.8E-1E+", "5.0E-1E+", "5.3E-1E+", "5.6E-1E+", "5.8E-1E+", "9.0E-1E+", "9.4E-1E+", "9.6E-1E+", "9.7E-1E+", "-1.0E-1E+", "-1.2E-1E+", "-1.5E-1E+", "-1.8E-1E+", "-5.0E-1E+", "-5.3E-1E+", "-5.6E-1E+", "-5.8E-1E+", "-9.0E-1E+", "-9.4E-1E+", "-9.6E-1E+", "-9.7E-1E+", "3.4E0E+", "4.6E0E+", "4.3E0E+", "4.6E0E+", "4.3E0E+", "-3.4E0E+", "-4.6E0E+", "-4.3E0E+", "-4.6E0E+", "-4.3E0E+"]);
		check_numeric_format("0.0\\E\"E+\"-0", numbers,
				["0.0EE+0", "1.0EE+0", "1.1EE+1", "1.5EE+1", "1.9EE+1", "2.0EE+2", "2.2EE+2", "2.5EE+2", "2.8EE+2", "1.2EE+3", "-1.0EE+0", "-1.1EE+1", "-1.5EE+1", "-1.9EE+1", "-2.0EE+2", "-2.2EE+2", "-2.5EE+2", "-2.8EE+2", "-9.9EE+3", "1.0EE+-1", "1.2EE+-1", "1.5EE+-1", "1.8EE+-1", "5.0EE+-1", "5.3EE+-1", "5.6EE+-1", "5.8EE+-1", "9.0EE+-1", "9.4EE+-1", "9.6EE+-1", "9.7EE+-1", "-1.0EE+-1", "-1.2EE+-1", "-1.5EE+-1", "-1.8EE+-1", "-5.0EE+-1", "-5.3EE+-1", "-5.6EE+-1", "-5.8EE+-1", "-9.0EE+-1", "-9.4EE+-1", "-9.6EE+-1", "-9.7EE+-1", "3.4EE+0", "4.6EE+0", "4.3EE+0", "4.6EE+0", "4.3EE+0", "-3.4EE+0", "-4.6EE+0", "-4.3EE+0", "-4.6EE+0", "-4.3EE+0"]);
		check_numeric_format("0.0\\E\"E+\"-0\"E+\"", numbers,
				["0.0EE+0E+", "1.0EE+0E+", "1.1EE+1E+", "1.5EE+1E+", "1.9EE+1E+", "2.0EE+2E+", "2.2EE+2E+", "2.5EE+2E+", "2.8EE+2E+", "1.2EE+3E+", "-1.0EE+0E+", "-1.1EE+1E+", "-1.5EE+1E+", "-1.9EE+1E+", "-2.0EE+2E+", "-2.2EE+2E+", "-2.5EE+2E+", "-2.8EE+2E+", "-9.9EE+3E+", "1.0EE+-1E+", "1.2EE+-1E+", "1.5EE+-1E+", "1.8EE+-1E+", "5.0EE+-1E+", "5.3EE+-1E+", "5.6EE+-1E+", "5.8EE+-1E+", "9.0EE+-1E+", "9.4EE+-1E+", "9.6EE+-1E+", "9.7EE+-1E+", "-1.0EE+-1E+", "-1.2EE+-1E+", "-1.5EE+-1E+", "-1.8EE+-1E+", "-5.0EE+-1E+", "-5.3EE+-1E+", "-5.6EE+-1E+", "-5.8EE+-1E+", "-9.0EE+-1E+", "-9.4EE+-1E+", "-9.6EE+-1E+", "-9.7EE+-1E+", "3.4EE+0E+", "4.6EE+0E+", "4.3EE+0E+", "4.6EE+0E+", "4.3EE+0E+", "-3.4EE+0E+", "-4.6EE+0E+", "-4.3EE+0E+", "-4.6EE+0E+", "-4.3EE+0E+"]);
		check_numeric_format("0\\.0.0E+0", numbers,
				["0.0.0E+0", "0.1.0E+0", "1.1.0E+0", "1.5.0E+0", "1.9.0E+0", "0.2.0E+2", "0.2.2E+2", "0.2.5E+2", "0.2.8E+2", "1.2.3E+2", "-0.1.0E+0", "-1.1.0E+0", "-1.5.0E+0", "-1.9.0E+0", "-0.2.0E+2", "-0.2.2E+2", "-0.2.5E+2", "-0.2.8E+2", "-9.8.8E+2", "1.0.0E-2", "1.2.0E-2", "1.5.1E-2", "1.8.1E-2", "5.0.0E-2", "5.3.0E-2", "5.5.5E-2", "5.7.5E-2", "9.0.0E-2", "9.4.0E-2", "9.5.9E-2", "9.6.9E-2", "-1.0.0E-2", "-1.2.0E-2", "-1.5.1E-2", "-1.8.1E-2", "-5.0.0E-2", "-5.3.0E-2", "-5.5.5E-2", "-5.7.5E-2", "-9.0.0E-2", "-9.4.0E-2", "-9.5.9E-2", "-9.6.9E-2", "0.3.4E+0", "0.4.6E+0", "0.4.3E+0", "0.4.6E+0", "0.4.3E+0", "-0.3.4E+0", "-0.4.6E+0", "-0.4.3E+0", "-0.4.6E+0", "-0.4.3E+0"]);
		check_numeric_format("\\.00.0E+0", numbers,
				[".00.0E+0", ".01.0E+0", ".11.0E+0", ".15.0E+0", ".19.0E+0", ".02.0E+2", ".02.2E+2", ".02.5E+2", ".02.8E+2", ".12.3E+2", "-.01.0E+0", "-.11.0E+0", "-.15.0E+0", "-.19.0E+0", "-.02.0E+2", "-.02.2E+2", "-.02.5E+2", "-.02.8E+2", "-.98.8E+2", ".10.0E-2", ".12.0E-2", ".15.1E-2", ".18.1E-2", ".50.0E-2", ".53.0E-2", ".55.5E-2", ".57.5E-2", ".90.0E-2", ".94.0E-2", ".95.9E-2", ".96.9E-2", "-.10.0E-2", "-.12.0E-2", "-.15.1E-2", "-.18.1E-2", "-.50.0E-2", "-.53.0E-2", "-.55.5E-2", "-.57.5E-2", "-.90.0E-2", "-.94.0E-2", "-.95.9E-2", "-.96.9E-2", ".03.4E+0", ".04.6E+0", ".04.3E+0", ".04.6E+0", ".04.3E+0", "-.03.4E+0", "-.04.6E+0", "-.04.3E+0", "-.04.6E+0", "-.04.3E+0"]);
		check_numeric_format("\\.0\\.0.0E+0", numbers,
				[".0.0.0E+0", ".0.1.0E+0", ".1.1.0E+0", ".1.5.0E+0", ".1.9.0E+0", ".0.2.0E+2", ".0.2.2E+2", ".0.2.5E+2", ".0.2.8E+2", ".1.2.3E+2", "-.0.1.0E+0", "-.1.1.0E+0", "-.1.5.0E+0", "-.1.9.0E+0", "-.0.2.0E+2", "-.0.2.2E+2", "-.0.2.5E+2", "-.0.2.8E+2", "-.9.8.8E+2", ".1.0.0E-2", ".1.2.0E-2", ".1.5.1E-2", ".1.8.1E-2", ".5.0.0E-2", ".5.3.0E-2", ".5.5.5E-2", ".5.7.5E-2", ".9.0.0E-2", ".9.4.0E-2", ".9.5.9E-2", ".9.6.9E-2", "-.1.0.0E-2", "-.1.2.0E-2", "-.1.5.1E-2", "-.1.8.1E-2", "-.5.0.0E-2", "-.5.3.0E-2", "-.5.5.5E-2", "-.5.7.5E-2", "-.9.0.0E-2", "-.9.4.0E-2", "-.9.5.9E-2", "-.9.6.9E-2", ".0.3.4E+0", ".0.4.6E+0", ".0.4.3E+0", ".0.4.6E+0", ".0.4.3E+0", "-.0.3.4E+0", "-.0.4.6E+0", "-.0.4.3E+0", "-.0.4.6E+0", "-.0.4.3E+0"]);
		check_numeric_format("0.0\\.0E+0", numbers,
				["0.0.0E+0", "1.0.0E+0", "1.1.0E+1", "1.5.0E+1", "1.9.0E+1", "2.0.0E+2", "2.2.0E+2", "2.5.0E+2", "2.8.0E+2", "1.2.3E+3", "-1.0.0E+0", "-1.1.0E+1", "-1.5.0E+1", "-1.9.0E+1", "-2.0.0E+2", "-2.2.0E+2", "-2.5.0E+2", "-2.8.0E+2", "-9.8.8E+3", "1.0.0E-1", "1.2.0E-1", "1.5.1E-1", "1.8.1E-1", "5.0.0E-1", "5.3.0E-1", "5.5.5E-1", "5.7.5E-1", "9.0.0E-1", "9.4.0E-1", "9.5.9E-1", "9.6.9E-1", "-1.0.0E-1", "-1.2.0E-1", "-1.5.1E-1", "-1.8.1E-1", "-5.0.0E-1", "-5.3.0E-1", "-5.5.5E-1", "-5.7.5E-1", "-9.0.0E-1", "-9.4.0E-1", "-9.5.9E-1", "-9.6.9E-1", "3.4.0E+0", "4.5.6E+0", "4.3.2E+0", "4.5.7E+0", "4.3.2E+0", "-3.4.0E+0", "-4.5.6E+0", "-4.3.2E+0", "-4.5.7E+0", "-4.3.2E+0"]);
		check_numeric_format("0.\\.00E+0", numbers,
				["0..00E+0", "1..00E+0", "1..10E+1", "1..50E+1", "1..90E+1", "2..00E+2", "2..20E+2", "2..50E+2", "2..80E+2", "1..23E+3", "-1..00E+0", "-1..10E+1", "-1..50E+1", "-1..90E+1", "-2..00E+2", "-2..20E+2", "-2..50E+2", "-2..80E+2", "-9..88E+3", "1..00E-1", "1..20E-1", "1..51E-1", "1..81E-1", "5..00E-1", "5..30E-1", "5..55E-1", "5..75E-1", "9..00E-1", "9..40E-1", "9..59E-1", "9..69E-1", "-1..00E-1", "-1..20E-1", "-1..51E-1", "-1..81E-1", "-5..00E-1", "-5..30E-1", "-5..55E-1", "-5..75E-1", "-9..00E-1", "-9..40E-1", "-9..59E-1", "-9..69E-1", "3..40E+0", "4..56E+0", "4..32E+0", "4..57E+0", "4..32E+0", "-3..40E+0", "-4..56E+0", "-4..32E+0", "-4..57E+0", "-4..32E+0"]);
		check_numeric_format("0.\\.0\\.0E+0", numbers,
				["0..0.0E+0", "1..0.0E+0", "1..1.0E+1", "1..5.0E+1", "1..9.0E+1", "2..0.0E+2", "2..2.0E+2", "2..5.0E+2", "2..8.0E+2", "1..2.3E+3", "-1..0.0E+0", "-1..1.0E+1", "-1..5.0E+1", "-1..9.0E+1", "-2..0.0E+2", "-2..2.0E+2", "-2..5.0E+2", "-2..8.0E+2", "-9..8.8E+3", "1..0.0E-1", "1..2.0E-1", "1..5.1E-1", "1..8.1E-1", "5..0.0E-1", "5..3.0E-1", "5..5.5E-1", "5..7.5E-1", "9..0.0E-1", "9..4.0E-1", "9..5.9E-1", "9..6.9E-1", "-1..0.0E-1", "-1..2.0E-1", "-1..5.1E-1", "-1..8.1E-1", "-5..0.0E-1", "-5..3.0E-1", "-5..5.5E-1", "-5..7.5E-1", "-9..0.0E-1", "-9..4.0E-1", "-9..5.9E-1", "-9..6.9E-1", "3..4.0E+0", "4..5.6E+0", "4..3.2E+0", "4..5.7E+0", "4..3.2E+0", "-3..4.0E+0", "-4..5.6E+0", "-4..3.2E+0", "-4..5.7E+0", "-4..3.2E+0"]);
		check_numeric_format("0.0E+0\\.", numbers,
				["0.0E+0.", "1.0E+0.", "1.1E+1.", "1.5E+1.", "1.9E+1.", "2.0E+2.", "2.2E+2.", "2.5E+2.", "2.8E+2.", "1.2E+3.", "-1.0E+0.", "-1.1E+1.", "-1.5E+1.", "-1.9E+1.", "-2.0E+2.", "-2.2E+2.", "-2.5E+2.", "-2.8E+2.", "-9.9E+3.", "1.0E-1.", "1.2E-1.", "1.5E-1.", "1.8E-1.", "5.0E-1.", "5.3E-1.", "5.6E-1.", "5.8E-1.", "9.0E-1.", "9.4E-1.", "9.6E-1.", "9.7E-1.", "-1.0E-1.", "-1.2E-1.", "-1.5E-1.", "-1.8E-1.", "-5.0E-1.", "-5.3E-1.", "-5.6E-1.", "-5.8E-1.", "-9.0E-1.", "-9.4E-1.", "-9.6E-1.", "-9.7E-1.", "3.4E+0.", "4.6E+0.", "4.3E+0.", "4.6E+0.", "4.3E+0.", "-3.4E+0.", "-4.6E+0.", "-4.3E+0.", "-4.6E+0.", "-4.3E+0."]);
		check_numeric_format("0.0\\E.+0", numbers,
				["0.0E.+0", "1.0E.+0", "1.1E.+1", "1.5E.+1", "1.9E.+1", "2.0E.+2", "2.2E.+2", "2.5E.+2", "2.8E.+2", "1.2E.+3", "-1.0E.+0", "-1.1E.+1", "-1.5E.+1", "-1.9E.+1", "-2.0E.+2", "-2.2E.+2", "-2.5E.+2", "-2.8E.+2", "-9.9E.+3", "1.0E.-1", "1.2E.-1", "1.5E.-1", "1.8E.-1", "5.0E.-1", "5.3E.-1", "5.6E.-1", "5.8E.-1", "9.0E.-1", "9.4E.-1", "9.6E.-1", "9.7E.-1", "-1.0E.-1", "-1.2E.-1", "-1.5E.-1", "-1.8E.-1", "-5.0E.-1", "-5.3E.-1", "-5.6E.-1", "-5.8E.-1", "-9.0E.-1", "-9.4E.-1", "-9.6E.-1", "-9.7E.-1", "3.4E.+0", "4.6E.+0", "4.3E.+0", "4.6E.+0", "4.3E.+0", "-3.4E.+0", "-4.6E.+0", "-4.3E.+0", "-4.6E.+0", "-4.3E.+0"]);
		check_numeric_format("0.0\\E.+0\\.", numbers,
				["0.0E.+0.", "1.0E.+0.", "1.1E.+1.", "1.5E.+1.", "1.9E.+1.", "2.0E.+2.", "2.2E.+2.", "2.5E.+2.", "2.8E.+2.", "1.2E.+3.", "-1.0E.+0.", "-1.1E.+1.", "-1.5E.+1.", "-1.9E.+1.", "-2.0E.+2.", "-2.2E.+2.", "-2.5E.+2.", "-2.8E.+2.", "-9.9E.+3.", "1.0E.-1.", "1.2E.-1.", "1.5E.-1.", "1.8E.-1.", "5.0E.-1.", "5.3E.-1.", "5.6E.-1.", "5.8E.-1.", "9.0E.-1.", "9.4E.-1.", "9.6E.-1.", "9.7E.-1.", "-1.0E.-1.", "-1.2E.-1.", "-1.5E.-1.", "-1.8E.-1.", "-5.0E.-1.", "-5.3E.-1.", "-5.6E.-1.", "-5.8E.-1.", "-9.0E.-1.", "-9.4E.-1.", "-9.6E.-1.", "-9.7E.-1.", "3.4E.+0.", "4.6E.+0.", "4.3E.+0.", "4.6E.+0.", "4.3E.+0.", "-3.4E.+0.", "-4.6E.+0.", "-4.3E.+0.", "-4.6E.+0.", "-4.3E.+0."]);
		check_numeric_format("0\\E0.0E+0", numbers,
				["0E0.0E+0", "0E1.0E+0", "1E1.0E+0", "1E5.0E+0", "1E9.0E+0", "0E2.0E+2", "0E2.2E+2", "0E2.5E+2", "0E2.8E+2", "1E2.3E+2", "-0E1.0E+0", "-1E1.0E+0", "-1E5.0E+0", "-1E9.0E+0", "-0E2.0E+2", "-0E2.2E+2", "-0E2.5E+2", "-0E2.8E+2", "-9E8.8E+2", "1E0.0E-2", "1E2.0E-2", "1E5.1E-2", "1E8.1E-2", "5E0.0E-2", "5E3.0E-2", "5E5.5E-2", "5E7.5E-2", "9E0.0E-2", "9E4.0E-2", "9E5.9E-2", "9E6.9E-2", "-1E0.0E-2", "-1E2.0E-2", "-1E5.1E-2", "-1E8.1E-2", "-5E0.0E-2", "-5E3.0E-2", "-5E5.5E-2", "-5E7.5E-2", "-9E0.0E-2", "-9E4.0E-2", "-9E5.9E-2", "-9E6.9E-2", "0E3.4E+0", "0E4.6E+0", "0E4.3E+0", "0E4.6E+0", "0E4.3E+0", "-0E3.4E+0", "-0E4.6E+0", "-0E4.3E+0", "-0E4.6E+0", "-0E4.3E+0"]);
		check_numeric_format("\\E00.0E+0", numbers,
				["E00.0E+0", "E01.0E+0", "E11.0E+0", "E15.0E+0", "E19.0E+0", "E02.0E+2", "E02.2E+2", "E02.5E+2", "E02.8E+2", "E12.3E+2", "-E01.0E+0", "-E11.0E+0", "-E15.0E+0", "-E19.0E+0", "-E02.0E+2", "-E02.2E+2", "-E02.5E+2", "-E02.8E+2", "-E98.8E+2", "E10.0E-2", "E12.0E-2", "E15.1E-2", "E18.1E-2", "E50.0E-2", "E53.0E-2", "E55.5E-2", "E57.5E-2", "E90.0E-2", "E94.0E-2", "E95.9E-2", "E96.9E-2", "-E10.0E-2", "-E12.0E-2", "-E15.1E-2", "-E18.1E-2", "-E50.0E-2", "-E53.0E-2", "-E55.5E-2", "-E57.5E-2", "-E90.0E-2", "-E94.0E-2", "-E95.9E-2", "-E96.9E-2", "E03.4E+0", "E04.6E+0", "E04.3E+0", "E04.6E+0", "E04.3E+0", "-E03.4E+0", "-E04.6E+0", "-E04.3E+0", "-E04.6E+0", "-E04.3E+0"]);
		check_numeric_format("\\E0\\E0.0E+0", numbers,
				["E0E0.0E+0", "E0E1.0E+0", "E1E1.0E+0", "E1E5.0E+0", "E1E9.0E+0", "E0E2.0E+2", "E0E2.2E+2", "E0E2.5E+2", "E0E2.8E+2", "E1E2.3E+2", "-E0E1.0E+0", "-E1E1.0E+0", "-E1E5.0E+0", "-E1E9.0E+0", "-E0E2.0E+2", "-E0E2.2E+2", "-E0E2.5E+2", "-E0E2.8E+2", "-E9E8.8E+2", "E1E0.0E-2", "E1E2.0E-2", "E1E5.1E-2", "E1E8.1E-2", "E5E0.0E-2", "E5E3.0E-2", "E5E5.5E-2", "E5E7.5E-2", "E9E0.0E-2", "E9E4.0E-2", "E9E5.9E-2", "E9E6.9E-2", "-E1E0.0E-2", "-E1E2.0E-2", "-E1E5.1E-2", "-E1E8.1E-2", "-E5E0.0E-2", "-E5E3.0E-2", "-E5E5.5E-2", "-E5E7.5E-2", "-E9E0.0E-2", "-E9E4.0E-2", "-E9E5.9E-2", "-E9E6.9E-2", "E0E3.4E+0", "E0E4.6E+0", "E0E4.3E+0", "E0E4.6E+0", "E0E4.3E+0", "-E0E3.4E+0", "-E0E4.6E+0", "-E0E4.3E+0", "-E0E4.6E+0", "-E0E4.3E+0"]);
		check_numeric_format("0.0\\E0E+0", numbers,
				["0.0E0E+0", "1.0E0E+0", "1.1E0E+1", "1.5E0E+1", "1.9E0E+1", "2.0E0E+2", "2.2E0E+2", "2.5E0E+2", "2.8E0E+2", "1.2E3E+3", "-1.0E0E+0", "-1.1E0E+1", "-1.5E0E+1", "-1.9E0E+1", "-2.0E0E+2", "-2.2E0E+2", "-2.5E0E+2", "-2.8E0E+2", "-9.8E8E+3", "1.0E0E-1", "1.2E0E-1", "1.5E1E-1", "1.8E1E-1", "5.0E0E-1", "5.3E0E-1", "5.5E5E-1", "5.7E5E-1", "9.0E0E-1", "9.4E0E-1", "9.5E9E-1", "9.6E9E-1", "-1.0E0E-1", "-1.2E0E-1", "-1.5E1E-1", "-1.8E1E-1", "-5.0E0E-1", "-5.3E0E-1", "-5.5E5E-1", "-5.7E5E-1", "-9.0E0E-1", "-9.4E0E-1", "-9.5E9E-1", "-9.6E9E-1", "3.4E0E+0", "4.5E6E+0", "4.3E2E+0", "4.5E7E+0", "4.3E2E+0", "-3.4E0E+0", "-4.5E6E+0", "-4.3E2E+0", "-4.5E7E+0", "-4.3E2E+0"]);
		check_numeric_format("0.\\E00E+0", numbers,
				["0.E00E+0", "1.E00E+0", "1.E10E+1", "1.E50E+1", "1.E90E+1", "2.E00E+2", "2.E20E+2", "2.E50E+2", "2.E80E+2", "1.E23E+3", "-1.E00E+0", "-1.E10E+1", "-1.E50E+1", "-1.E90E+1", "-2.E00E+2", "-2.E20E+2", "-2.E50E+2", "-2.E80E+2", "-9.E88E+3", "1.E00E-1", "1.E20E-1", "1.E51E-1", "1.E81E-1", "5.E00E-1", "5.E30E-1", "5.E55E-1", "5.E75E-1", "9.E00E-1", "9.E40E-1", "9.E59E-1", "9.E69E-1", "-1.E00E-1", "-1.E20E-1", "-1.E51E-1", "-1.E81E-1", "-5.E00E-1", "-5.E30E-1", "-5.E55E-1", "-5.E75E-1", "-9.E00E-1", "-9.E40E-1", "-9.E59E-1", "-9.E69E-1", "3.E40E+0", "4.E56E+0", "4.E32E+0", "4.E57E+0", "4.E32E+0", "-3.E40E+0", "-4.E56E+0", "-4.E32E+0", "-4.E57E+0", "-4.E32E+0"]);
		check_numeric_format("0.\\E0\\E0E+0", numbers,
				["0.E0E0E+0", "1.E0E0E+0", "1.E1E0E+1", "1.E5E0E+1", "1.E9E0E+1", "2.E0E0E+2", "2.E2E0E+2", "2.E5E0E+2", "2.E8E0E+2", "1.E2E3E+3", "-1.E0E0E+0", "-1.E1E0E+1", "-1.E5E0E+1", "-1.E9E0E+1", "-2.E0E0E+2", "-2.E2E0E+2", "-2.E5E0E+2", "-2.E8E0E+2", "-9.E8E8E+3", "1.E0E0E-1", "1.E2E0E-1", "1.E5E1E-1", "1.E8E1E-1", "5.E0E0E-1", "5.E3E0E-1", "5.E5E5E-1", "5.E7E5E-1", "9.E0E0E-1", "9.E4E0E-1", "9.E5E9E-1", "9.E6E9E-1", "-1.E0E0E-1", "-1.E2E0E-1", "-1.E5E1E-1", "-1.E8E1E-1", "-5.E0E0E-1", "-5.E3E0E-1", "-5.E5E5E-1", "-5.E7E5E-1", "-9.E0E0E-1", "-9.E4E0E-1", "-9.E5E9E-1", "-9.E6E9E-1", "3.E4E0E+0", "4.E5E6E+0", "4.E3E2E+0", "4.E5E7E+0", "4.E3E2E+0", "-3.E4E0E+0", "-4.E5E6E+0", "-4.E3E2E+0", "-4.E5E7E+0", "-4.E3E2E+0"]);
		check_numeric_format("0.0E+0\\E", numbers,
				["0.0E+0E", "1.0E+0E", "1.1E+1E", "1.5E+1E", "1.9E+1E", "2.0E+2E", "2.2E+2E", "2.5E+2E", "2.8E+2E", "1.2E+3E", "-1.0E+0E", "-1.1E+1E", "-1.5E+1E", "-1.9E+1E", "-2.0E+2E", "-2.2E+2E", "-2.5E+2E", "-2.8E+2E", "-9.9E+3E", "1.0E-1E", "1.2E-1E", "1.5E-1E", "1.8E-1E", "5.0E-1E", "5.3E-1E", "5.6E-1E", "5.8E-1E", "9.0E-1E", "9.4E-1E", "9.6E-1E", "9.7E-1E", "-1.0E-1E", "-1.2E-1E", "-1.5E-1E", "-1.8E-1E", "-5.0E-1E", "-5.3E-1E", "-5.6E-1E", "-5.8E-1E", "-9.0E-1E", "-9.4E-1E", "-9.6E-1E", "-9.7E-1E", "3.4E+0E", "4.6E+0E", "4.3E+0E", "4.6E+0E", "4.3E+0E", "-3.4E+0E", "-4.6E+0E", "-4.3E+0E", "-4.6E+0E", "-4.3E+0E"]);
		check_numeric_format("0.0\\EE+0", numbers,
				["0.0EE+0", "1.0EE+0", "1.1EE+1", "1.5EE+1", "1.9EE+1", "2.0EE+2", "2.2EE+2", "2.5EE+2", "2.8EE+2", "1.2EE+3", "-1.0EE+0", "-1.1EE+1", "-1.5EE+1", "-1.9EE+1", "-2.0EE+2", "-2.2EE+2", "-2.5EE+2", "-2.8EE+2", "-9.9EE+3", "1.0EE-1", "1.2EE-1", "1.5EE-1", "1.8EE-1", "5.0EE-1", "5.3EE-1", "5.6EE-1", "5.8EE-1", "9.0EE-1", "9.4EE-1", "9.6EE-1", "9.7EE-1", "-1.0EE-1", "-1.2EE-1", "-1.5EE-1", "-1.8EE-1", "-5.0EE-1", "-5.3EE-1", "-5.6EE-1", "-5.8EE-1", "-9.0EE-1", "-9.4EE-1", "-9.6EE-1", "-9.7EE-1", "3.4EE+0", "4.6EE+0", "4.3EE+0", "4.6EE+0", "4.3EE+0", "-3.4EE+0", "-4.6EE+0", "-4.3EE+0", "-4.6EE+0", "-4.3EE+0"]);
		check_numeric_format("0.0\\EE+0\\E", numbers,
				["0.0EE+0E", "1.0EE+0E", "1.1EE+1E", "1.5EE+1E", "1.9EE+1E", "2.0EE+2E", "2.2EE+2E", "2.5EE+2E", "2.8EE+2E", "1.2EE+3E", "-1.0EE+0E", "-1.1EE+1E", "-1.5EE+1E", "-1.9EE+1E", "-2.0EE+2E", "-2.2EE+2E", "-2.5EE+2E", "-2.8EE+2E", "-9.9EE+3E", "1.0EE-1E", "1.2EE-1E", "1.5EE-1E", "1.8EE-1E", "5.0EE-1E", "5.3EE-1E", "5.6EE-1E", "5.8EE-1E", "9.0EE-1E", "9.4EE-1E", "9.6EE-1E", "9.7EE-1E", "-1.0EE-1E", "-1.2EE-1E", "-1.5EE-1E", "-1.8EE-1E", "-5.0EE-1E", "-5.3EE-1E", "-5.6EE-1E", "-5.8EE-1E", "-9.0EE-1E", "-9.4EE-1E", "-9.6EE-1E", "-9.7EE-1E", "3.4EE+0E", "4.6EE+0E", "4.3EE+0E", "4.6EE+0E", "4.3EE+0E", "-3.4EE+0E", "-4.6EE+0E", "-4.3EE+0E", "-4.6EE+0E", "-4.3EE+0E"]);
		check_numeric_format("0\"E+\"0.0E+0", numbers,
				["0E+0.0E+0", "0E+1.0E+0", "1E+1.0E+0", "1E+5.0E+0", "1E+9.0E+0", "0E+2.0E+2", "0E+2.2E+2", "0E+2.5E+2", "0E+2.8E+2", "1E+2.3E+2", "-0E+1.0E+0", "-1E+1.0E+0", "-1E+5.0E+0", "-1E+9.0E+0", "-0E+2.0E+2", "-0E+2.2E+2", "-0E+2.5E+2", "-0E+2.8E+2", "-9E+8.8E+2", "1E+0.0E-2", "1E+2.0E-2", "1E+5.1E-2", "1E+8.1E-2", "5E+0.0E-2", "5E+3.0E-2", "5E+5.5E-2", "5E+7.5E-2", "9E+0.0E-2", "9E+4.0E-2", "9E+5.9E-2", "9E+6.9E-2", "-1E+0.0E-2", "-1E+2.0E-2", "-1E+5.1E-2", "-1E+8.1E-2", "-5E+0.0E-2", "-5E+3.0E-2", "-5E+5.5E-2", "-5E+7.5E-2", "-9E+0.0E-2", "-9E+4.0E-2", "-9E+5.9E-2", "-9E+6.9E-2", "0E+3.4E+0", "0E+4.6E+0", "0E+4.3E+0", "0E+4.6E+0", "0E+4.3E+0", "-0E+3.4E+0", "-0E+4.6E+0", "-0E+4.3E+0", "-0E+4.6E+0", "-0E+4.3E+0"]);
		check_numeric_format("\"E+\"00.0E+0", numbers,
				["E+00.0E+0", "E+01.0E+0", "E+11.0E+0", "E+15.0E+0", "E+19.0E+0", "E+02.0E+2", "E+02.2E+2", "E+02.5E+2", "E+02.8E+2", "E+12.3E+2", "-E+01.0E+0", "-E+11.0E+0", "-E+15.0E+0", "-E+19.0E+0", "-E+02.0E+2", "-E+02.2E+2", "-E+02.5E+2", "-E+02.8E+2", "-E+98.8E+2", "E+10.0E-2", "E+12.0E-2", "E+15.1E-2", "E+18.1E-2", "E+50.0E-2", "E+53.0E-2", "E+55.5E-2", "E+57.5E-2", "E+90.0E-2", "E+94.0E-2", "E+95.9E-2", "E+96.9E-2", "-E+10.0E-2", "-E+12.0E-2", "-E+15.1E-2", "-E+18.1E-2", "-E+50.0E-2", "-E+53.0E-2", "-E+55.5E-2", "-E+57.5E-2", "-E+90.0E-2", "-E+94.0E-2", "-E+95.9E-2", "-E+96.9E-2", "E+03.4E+0", "E+04.6E+0", "E+04.3E+0", "E+04.6E+0", "E+04.3E+0", "-E+03.4E+0", "-E+04.6E+0", "-E+04.3E+0", "-E+04.6E+0", "-E+04.3E+0"]);
		check_numeric_format("\"E+\"0\"E+\"0.0E+0", numbers,
				["E+0E+0.0E+0", "E+0E+1.0E+0", "E+1E+1.0E+0", "E+1E+5.0E+0", "E+1E+9.0E+0", "E+0E+2.0E+2", "E+0E+2.2E+2", "E+0E+2.5E+2", "E+0E+2.8E+2", "E+1E+2.3E+2", "-E+0E+1.0E+0", "-E+1E+1.0E+0", "-E+1E+5.0E+0", "-E+1E+9.0E+0", "-E+0E+2.0E+2", "-E+0E+2.2E+2", "-E+0E+2.5E+2", "-E+0E+2.8E+2", "-E+9E+8.8E+2", "E+1E+0.0E-2", "E+1E+2.0E-2", "E+1E+5.1E-2", "E+1E+8.1E-2", "E+5E+0.0E-2", "E+5E+3.0E-2", "E+5E+5.5E-2", "E+5E+7.5E-2", "E+9E+0.0E-2", "E+9E+4.0E-2", "E+9E+5.9E-2", "E+9E+6.9E-2", "-E+1E+0.0E-2", "-E+1E+2.0E-2", "-E+1E+5.1E-2", "-E+1E+8.1E-2", "-E+5E+0.0E-2", "-E+5E+3.0E-2", "-E+5E+5.5E-2", "-E+5E+7.5E-2", "-E+9E+0.0E-2", "-E+9E+4.0E-2", "-E+9E+5.9E-2", "-E+9E+6.9E-2", "E+0E+3.4E+0", "E+0E+4.6E+0", "E+0E+4.3E+0", "E+0E+4.6E+0", "E+0E+4.3E+0", "-E+0E+3.4E+0", "-E+0E+4.6E+0", "-E+0E+4.3E+0", "-E+0E+4.6E+0", "-E+0E+4.3E+0"]);
		check_numeric_format("0.0\"E+\"0E+0", numbers,
				["0.0E+0E+0", "1.0E+0E+0", "1.1E+0E+1", "1.5E+0E+1", "1.9E+0E+1", "2.0E+0E+2", "2.2E+0E+2", "2.5E+0E+2", "2.8E+0E+2", "1.2E+3E+3", "-1.0E+0E+0", "-1.1E+0E+1", "-1.5E+0E+1", "-1.9E+0E+1", "-2.0E+0E+2", "-2.2E+0E+2", "-2.5E+0E+2", "-2.8E+0E+2", "-9.8E+8E+3", "1.0E+0E-1", "1.2E+0E-1", "1.5E+1E-1", "1.8E+1E-1", "5.0E+0E-1", "5.3E+0E-1", "5.5E+5E-1", "5.7E+5E-1", "9.0E+0E-1", "9.4E+0E-1", "9.5E+9E-1", "9.6E+9E-1", "-1.0E+0E-1", "-1.2E+0E-1", "-1.5E+1E-1", "-1.8E+1E-1", "-5.0E+0E-1", "-5.3E+0E-1", "-5.5E+5E-1", "-5.7E+5E-1", "-9.0E+0E-1", "-9.4E+0E-1", "-9.5E+9E-1", "-9.6E+9E-1", "3.4E+0E+0", "4.5E+6E+0", "4.3E+2E+0", "4.5E+7E+0", "4.3E+2E+0", "-3.4E+0E+0", "-4.5E+6E+0", "-4.3E+2E+0", "-4.5E+7E+0", "-4.3E+2E+0"]);
		check_numeric_format("0.\"E+\"00E+0", numbers,
				["0.E+00E+0", "1.E+00E+0", "1.E+10E+1", "1.E+50E+1", "1.E+90E+1", "2.E+00E+2", "2.E+20E+2", "2.E+50E+2", "2.E+80E+2", "1.E+23E+3", "-1.E+00E+0", "-1.E+10E+1", "-1.E+50E+1", "-1.E+90E+1", "-2.E+00E+2", "-2.E+20E+2", "-2.E+50E+2", "-2.E+80E+2", "-9.E+88E+3", "1.E+00E-1", "1.E+20E-1", "1.E+51E-1", "1.E+81E-1", "5.E+00E-1", "5.E+30E-1", "5.E+55E-1", "5.E+75E-1", "9.E+00E-1", "9.E+40E-1", "9.E+59E-1", "9.E+69E-1", "-1.E+00E-1", "-1.E+20E-1", "-1.E+51E-1", "-1.E+81E-1", "-5.E+00E-1", "-5.E+30E-1", "-5.E+55E-1", "-5.E+75E-1", "-9.E+00E-1", "-9.E+40E-1", "-9.E+59E-1", "-9.E+69E-1", "3.E+40E+0", "4.E+56E+0", "4.E+32E+0", "4.E+57E+0", "4.E+32E+0", "-3.E+40E+0", "-4.E+56E+0", "-4.E+32E+0", "-4.E+57E+0", "-4.E+32E+0"]);
		check_numeric_format("0.\"E+\"0\"E+\"0E+0", numbers,
				["0.E+0E+0E+0", "1.E+0E+0E+0", "1.E+1E+0E+1", "1.E+5E+0E+1", "1.E+9E+0E+1", "2.E+0E+0E+2", "2.E+2E+0E+2", "2.E+5E+0E+2", "2.E+8E+0E+2", "1.E+2E+3E+3", "-1.E+0E+0E+0", "-1.E+1E+0E+1", "-1.E+5E+0E+1", "-1.E+9E+0E+1", "-2.E+0E+0E+2", "-2.E+2E+0E+2", "-2.E+5E+0E+2", "-2.E+8E+0E+2", "-9.E+8E+8E+3", "1.E+0E+0E-1", "1.E+2E+0E-1", "1.E+5E+1E-1", "1.E+8E+1E-1", "5.E+0E+0E-1", "5.E+3E+0E-1", "5.E+5E+5E-1", "5.E+7E+5E-1", "9.E+0E+0E-1", "9.E+4E+0E-1", "9.E+5E+9E-1", "9.E+6E+9E-1", "-1.E+0E+0E-1", "-1.E+2E+0E-1", "-1.E+5E+1E-1", "-1.E+8E+1E-1", "-5.E+0E+0E-1", "-5.E+3E+0E-1", "-5.E+5E+5E-1", "-5.E+7E+5E-1", "-9.E+0E+0E-1", "-9.E+4E+0E-1", "-9.E+5E+9E-1", "-9.E+6E+9E-1", "3.E+4E+0E+0", "4.E+5E+6E+0", "4.E+3E+2E+0", "4.E+5E+7E+0", "4.E+3E+2E+0", "-3.E+4E+0E+0", "-4.E+5E+6E+0", "-4.E+3E+2E+0", "-4.E+5E+7E+0", "-4.E+3E+2E+0"]);
		check_numeric_format("0.0E+0\"E+\"", numbers,
				["0.0E+0E+", "1.0E+0E+", "1.1E+1E+", "1.5E+1E+", "1.9E+1E+", "2.0E+2E+", "2.2E+2E+", "2.5E+2E+", "2.8E+2E+", "1.2E+3E+", "-1.0E+0E+", "-1.1E+1E+", "-1.5E+1E+", "-1.9E+1E+", "-2.0E+2E+", "-2.2E+2E+", "-2.5E+2E+", "-2.8E+2E+", "-9.9E+3E+", "1.0E-1E+", "1.2E-1E+", "1.5E-1E+", "1.8E-1E+", "5.0E-1E+", "5.3E-1E+", "5.6E-1E+", "5.8E-1E+", "9.0E-1E+", "9.4E-1E+", "9.6E-1E+", "9.7E-1E+", "-1.0E-1E+", "-1.2E-1E+", "-1.5E-1E+", "-1.8E-1E+", "-5.0E-1E+", "-5.3E-1E+", "-5.6E-1E+", "-5.8E-1E+", "-9.0E-1E+", "-9.4E-1E+", "-9.6E-1E+", "-9.7E-1E+", "3.4E+0E+", "4.6E+0E+", "4.3E+0E+", "4.6E+0E+", "4.3E+0E+", "-3.4E+0E+", "-4.6E+0E+", "-4.3E+0E+", "-4.6E+0E+", "-4.3E+0E+"]);
		check_numeric_format("0.0\\E\"E+\"+0", numbers,
				["0.0EE++0", "1.0EE++0", "1.1EE++1", "1.5EE++1", "1.9EE++1", "2.0EE++2", "2.2EE++2", "2.5EE++2", "2.8EE++2", "1.2EE++3", "-1.0EE++0", "-1.1EE++1", "-1.5EE++1", "-1.9EE++1", "-2.0EE++2", "-2.2EE++2", "-2.5EE++2", "-2.8EE++2", "-9.9EE++3", "1.0EE+-1", "1.2EE+-1", "1.5EE+-1", "1.8EE+-1", "5.0EE+-1", "5.3EE+-1", "5.6EE+-1", "5.8EE+-1", "9.0EE+-1", "9.4EE+-1", "9.6EE+-1", "9.7EE+-1", "-1.0EE+-1", "-1.2EE+-1", "-1.5EE+-1", "-1.8EE+-1", "-5.0EE+-1", "-5.3EE+-1", "-5.6EE+-1", "-5.8EE+-1", "-9.0EE+-1", "-9.4EE+-1", "-9.6EE+-1", "-9.7EE+-1", "3.4EE++0", "4.6EE++0", "4.3EE++0", "4.6EE++0", "4.3EE++0", "-3.4EE++0", "-4.6EE++0", "-4.3EE++0", "-4.6EE++0", "-4.3EE++0"]);
		check_numeric_format("0.0\\E\"E+\"+0\"E+\"", numbers,
				["0.0EE++0E+", "1.0EE++0E+", "1.1EE++1E+", "1.5EE++1E+", "1.9EE++1E+", "2.0EE++2E+", "2.2EE++2E+", "2.5EE++2E+", "2.8EE++2E+", "1.2EE++3E+", "-1.0EE++0E+", "-1.1EE++1E+", "-1.5EE++1E+", "-1.9EE++1E+", "-2.0EE++2E+", "-2.2EE++2E+", "-2.5EE++2E+", "-2.8EE++2E+", "-9.9EE++3E+", "1.0EE+-1E+", "1.2EE+-1E+", "1.5EE+-1E+", "1.8EE+-1E+", "5.0EE+-1E+", "5.3EE+-1E+", "5.6EE+-1E+", "5.8EE+-1E+", "9.0EE+-1E+", "9.4EE+-1E+", "9.6EE+-1E+", "9.7EE+-1E+", "-1.0EE+-1E+", "-1.2EE+-1E+", "-1.5EE+-1E+", "-1.8EE+-1E+", "-5.0EE+-1E+", "-5.3EE+-1E+", "-5.6EE+-1E+", "-5.8EE+-1E+", "-9.0EE+-1E+", "-9.4EE+-1E+", "-9.6EE+-1E+", "-9.7EE+-1E+", "3.4EE++0E+", "4.6EE++0E+", "4.3EE++0E+", "4.6EE++0E+", "4.3EE++0E+", "-3.4EE++0E+", "-4.6EE++0E+", "-4.3EE++0E+", "-4.6EE++0E+", "-4.3EE++0E+"]);
	});

});
