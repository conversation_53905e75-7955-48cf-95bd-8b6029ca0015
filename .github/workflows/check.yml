name: check
on: [push]
jobs:
  code-style:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Ruby 2.7
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: 2.7
      - name: Run rake task
        run: rake check_source_files
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Use Node.js 14
        uses: actions/setup-node@v1
        with:
            node-version: 14
      - name: Run unit tests
        run: |
          npm install -g grunt-cli node-qunit-puppeteer
          npm install --prefix build
          grunt --gruntfile build/Gruntfile.js develop
          node-qunit-puppeteer tests/common/api/api.html
          node-qunit-puppeteer tests/cell/spreadsheet-calculation/FormulaTests.html
          node-qunit-puppeteer tests/cell/spreadsheet-calculation/PivotTests.html
          node-qunit-puppeteer tests/cell/spreadsheet-calculation/CopyPasteTests.html
          node-qunit-puppeteer tests/word/unit-tests/paragraphContentPos.html
          node-qunit-puppeteer tests/word/document-calculation/paragraph.html
          node-qunit-puppeteer tests/word/document-calculation/table/correctBadTable.html
          node-qunit-puppeteer tests/word/document-calculation/table/flowTablePosition.html
          node-qunit-puppeteer tests/word/document-calculation/textShaper/textShaper.html
          node-qunit-puppeteer tests/word/forms/forms.html
          node-qunit-puppeteer tests/word/forms/complexForm.html
          node-qunit-puppeteer tests/word/numbering/numberingApplicator.html
          node-qunit-puppeteer tests/word/api/api.html
          node-qunit-puppeteer tests/word/api/textInput.html
          node-qunit-puppeteer tests/word/plugins/pluginsApi.html
  builder-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Use Node.js 12
        uses: actions/setup-node@v1
        with:
            node-version: 12
      - name: Run builder tests
        run: |
           npm install -g grunt-cli
           npm install --prefix build
           grunt --level=WHITESPACE_ONLY --base build --gruntfile build/Gruntfile.js
           docker run -v $PWD/deploy/sdkjs/common:/opt/onlyoffice/documentbuilder/sdkjs/common \
                      -v $PWD/deploy/sdkjs/word:/opt/onlyoffice/documentbuilder/sdkjs/word \
                      -v $PWD/deploy/sdkjs/cell:/opt/onlyoffice/documentbuilder/sdkjs/cell \
                      -v $PWD/deploy/sdkjs/slide:/opt/onlyoffice/documentbuilder/sdkjs/slide \
                      onlyofficeqa/doc-builder-testing:next-release rake rspec_critical
